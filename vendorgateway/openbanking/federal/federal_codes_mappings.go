package federal

import (
	"encoding/csv"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/epifi/be-common/pkg/logger"

	employmentPb "github.com/epifi/gamma/api/employment"
	types "github.com/epifi/gamma/api/typesv2"

	"go.uber.org/zap"
)

/*
V2 api documentations
https://docs.google.com/spreadsheets/d/1OeR3rOiIVueFSIQv6m4SRlrH1A7aNLPP/edit?usp=sharing&ouid=102530266168786225163&rtpof=true&sd=true
https://docs.google.com/spreadsheets/d/1zVbb5hEgPMR4NCWwPPlLuGcytKgk6zCt9xBJKcKljIw/edit?usp=sharing
*/

const (
	DefaultEmployment = "Salaried"
)

// City, State, Country mapping
var (
	cityCodeMapping    map[string]string
	stateCodeMapping   map[string]string
	countryCodeMapping map[string]string
)

// Nominee, Guardian mapping
var (

	// map of all relation type to guardian code supported by federal.
	// guardian code are the codes accepted by federal for a guardian type
	relationTypeToGuardianCodeMap = map[types.RelationType]string{
		types.RelationType_COURT_APPOINTED:   "C",
		types.RelationType_DEFACTO_GUARDIAN:  "D",
		types.RelationType_DE_FACTO_GUARDIAN: "D",
		types.RelationType_FATHER:            "F",
		types.RelationType_MOTHER:            "M",
		types.RelationType_OTHERS:            "O",
	}

	QualificationToQualificationString = map[types.Qualification]string{
		types.Qualification_CHARTED_ACCOUNTANTS:                          "CA",
		types.Qualification_DIPLOMA_HOLDERS:                              "DIP",
		types.Qualification_DOCTORAL:                                     "DR",
		types.Qualification_ENGINEERING_GRADUATE:                         "ENGG",
		types.Qualification_ENGINEERING_GRADUATE_FROM_PREMIER_INSTITUTES: "ENGP",
		types.Qualification_GRADUATE:                                     "G",
		types.Qualification_NON_MATRICULATE:                              "NONM",
		types.Qualification_POSTGRADUATE:                                 "PG",
		types.Qualification_QUALIFICATION_UNSPECIFIED:                    "NA",
		types.Qualification_UNDERGRADUATE:                                "UG",
		types.Qualification_NON_LITERATE:                                 "NONLT",
		types.Qualification_PROFESSIONAL_DEGREE_OR_DIPLOMA:               "PRFDP",
	}

	OccTypeToOccupationString = map[employmentPb.OccupationType]string{
		employmentPb.OccupationType_OCCUPATION_TYPE_LEGAL_AND_JUDICIARY:            "ADVOC",
		employmentPb.OccupationType_OCCUPATION_TYPE_SOFTWARE_AND_IT:                "ITENG",
		employmentPb.OccupationType_OCCUPATION_TYPE_ENGINEERING:                    "ENGIN",
		employmentPb.OccupationType_OCCUPATION_TYPE_HEALTHCARE:                     "MEDRE",
		employmentPb.OccupationType_OCCUPATION_TYPE_ACADEMIA:                       "TEACH",
		employmentPb.OccupationType_OCCUPATION_TYPE_BANKING:                        "BANKR",
		employmentPb.OccupationType_OCCUPATION_TYPE_CHARTERED_ACCOUNTANT:           "CHRTA",
		employmentPb.OccupationType_OCCUPATION_TYPE_PUBLIC_SERVICES:                "CVLSR",
		employmentPb.OccupationType_OCCUPATION_TYPE_MERCHANT_AND_TRADE:             "MERCH",
		employmentPb.OccupationType_OCCUPATION_TYPE_NEWS_AND_MEDIA:                 "JOURN",
		employmentPb.OccupationType_OCCUPATION_TYPE_BUSINESS:                       "BUSIN",
		employmentPb.OccupationType_OCCUPATION_TYPE_AVIATION:                       "AIRHO",
		employmentPb.OccupationType_OCCUPATION_TYPE_REAL_ESTATE_AND_INFRASTRUCTURE: "REB",
		employmentPb.OccupationType_OCCUPATION_TYPE_DEFENCE_AND_LAW_ENFORCEMENT:    "LEADF",
		employmentPb.OccupationType_OCCUPATION_TYPE_MARKETING_AND_SALES:            "MKTEX",
		employmentPb.OccupationType_OCCUPATION_TYPE_OTHERS:                         "OTHERS",
		employmentPb.OccupationType_OCCUPATION_TYPE_ENTERTAINMENT:                  "ENTIN",
		employmentPb.OccupationType_OCCUPATION_TYPE_CRYPTO_TRADING:                 "VIRCE",
		employmentPb.OccupationType_OCCUPATION_TYPE_LUXURY_CAR_DEALER:              "LUXUR",
		employmentPb.OccupationType_OCCUPATION_TYPE_SCRAP_DEALER:                   "SCRAP",
		employmentPb.OccupationType_OCCUPATION_TYPE_STUDENT:                        "STUDE",
		employmentPb.OccupationType_OCCUPATION_TYPE_HOMEMAKER:                      "HSEWF",
		employmentPb.OccupationType_OCCUPATION_TYPE_RETIRED:                        "RETIR",
		employmentPb.OccupationType_OCCUPATION_TYPE_JUDGE:                          "JUDGE",
		employmentPb.OccupationType_OCCUPATION_TYPE_ADVOCATE:                       "ADVOC",
		employmentPb.OccupationType_OCCUPATION_TYPE_FUND_MANAGEMENT:                "PROIN",
		employmentPb.OccupationType_OCCUPATION_TYPE_SELF_EMPLOYED:                  "SELFE",
		employmentPb.OccupationType_OCCUPATION_TYPE_DIPLOMAT:                       "DIPLO",
		employmentPb.OccupationType_OCCUPATION_TYPE_AGRICULTURE:                    "AGRI",
		employmentPb.OccupationType_OCCUPATION_TYPE_VIRTUAL_CURRENCY_DEALER:        "VIRCD",
		employmentPb.OccupationType_OCCUPATION_TYPE_ART_ANTIQUES_DEALER:            "DLANQ",
		employmentPb.OccupationType_OCCUPATION_TYPE_ARM_ARMAMENTS_DEALER:           "DLARM",
		employmentPb.OccupationType_OCCUPATION_TYPE_GOLD_PRECIOUS_STONE_DEALER:     "GOLDE",
		employmentPb.OccupationType_OCCUPATION_TYPE_PAWN_BROKER:                    "PAWNB",
		employmentPb.OccupationType_OCCUPATION_TYPE_UNEMPLOYED:                     "UNEMP",
		employmentPb.OccupationType_OCCUPATION_TYPE_POLITICIAN_OR_STATESMAN:        "POLST",
		employmentPb.OccupationType_OCCUPATION_TYPE_PRIVATE_SECTOR:                 "PVTSC",
		employmentPb.OccupationType_OCCUPATION_TYPE_BUREAUCRAT:                     "BURCR",
		employmentPb.OccupationType_OCCUPATION_TYPE_FINANCIAL_SECTOR:               "FINSC",
		employmentPb.OccupationType_OCCUPATION_TYPE_GOVERNMENT:                     "GOVT",
		employmentPb.OccupationType_OCCUPATION_TYPE_MEDIA:                          "MEDIA",
		employmentPb.OccupationType_OCCUPATION_TYPE_NGO:                            "NGO",
		employmentPb.OccupationType_OCCUPATION_TYPE_PUBLIC_SECTOR:                  "PUBSC",
		employmentPb.OccupationType_OCCUPATION_TYPE_LLP:                            "LLP",
		employmentPb.OccupationType_OCCUPATION_TYPE_PARTNERSHIP:                    "PRTNR",
		employmentPb.OccupationType_OCCUPATION_TYPE_PROPRIETORSHIP:                 "PROPT",
		employmentPb.OccupationType_OCCUPATION_TYPE_PRIVATE_LIMITED:                "PVTLT",
		employmentPb.OccupationType_OCCUPATION_TYPE_PUBLIC_LIMITED:                 "PUBLT",
		employmentPb.OccupationType_OCCUPATION_TYPE_TRUST:                          "TRUST",
		employmentPb.OccupationType_OCCUPATION_TYPE_SOCIETY:                        "SOCTY",
		employmentPb.OccupationType_OCCUPATION_TYPE_MULTINATIONAL:                  "MULTN",
		employmentPb.OccupationType_OCCUPATION_TYPE_PROFESSIONAL_INTERMEDIARIES:    "PROIN",
	}

	OccCodeToOccType = map[string]employmentPb.OccupationType{
		"ITENG":  employmentPb.OccupationType_OCCUPATION_TYPE_SOFTWARE_AND_IT,
		"ENGIN":  employmentPb.OccupationType_OCCUPATION_TYPE_ENGINEERING,
		"MEDRE":  employmentPb.OccupationType_OCCUPATION_TYPE_HEALTHCARE,
		"TEACH":  employmentPb.OccupationType_OCCUPATION_TYPE_ACADEMIA,
		"BANKR":  employmentPb.OccupationType_OCCUPATION_TYPE_BANKING,
		"CHRTA":  employmentPb.OccupationType_OCCUPATION_TYPE_CHARTERED_ACCOUNTANT,
		"CVLSR":  employmentPb.OccupationType_OCCUPATION_TYPE_PUBLIC_SERVICES,
		"MERCH":  employmentPb.OccupationType_OCCUPATION_TYPE_MERCHANT_AND_TRADE,
		"JOURN":  employmentPb.OccupationType_OCCUPATION_TYPE_NEWS_AND_MEDIA,
		"BUSIN":  employmentPb.OccupationType_OCCUPATION_TYPE_BUSINESS,
		"AIRHO":  employmentPb.OccupationType_OCCUPATION_TYPE_AVIATION,
		"REB":    employmentPb.OccupationType_OCCUPATION_TYPE_REAL_ESTATE_AND_INFRASTRUCTURE,
		"LEADF":  employmentPb.OccupationType_OCCUPATION_TYPE_DEFENCE_AND_LAW_ENFORCEMENT,
		"MKTEX":  employmentPb.OccupationType_OCCUPATION_TYPE_MARKETING_AND_SALES,
		"OTHERS": employmentPb.OccupationType_OCCUPATION_TYPE_OTHERS,
		"OTHER":  employmentPb.OccupationType_OCCUPATION_TYPE_OTHERS,
		"OTHRS":  employmentPb.OccupationType_OCCUPATION_TYPE_OTHERS,
		"ENTIN":  employmentPb.OccupationType_OCCUPATION_TYPE_ENTERTAINMENT,
		"VIRCE":  employmentPb.OccupationType_OCCUPATION_TYPE_CRYPTO_TRADING,
		"LUXUR":  employmentPb.OccupationType_OCCUPATION_TYPE_LUXURY_CAR_DEALER,
		"SCRAP":  employmentPb.OccupationType_OCCUPATION_TYPE_SCRAP_DEALER,
		"STUDE":  employmentPb.OccupationType_OCCUPATION_TYPE_STUDENT,
		"HSEWF":  employmentPb.OccupationType_OCCUPATION_TYPE_HOMEMAKER,
		"RETIR":  employmentPb.OccupationType_OCCUPATION_TYPE_RETIRED,
		"JUDGE":  employmentPb.OccupationType_OCCUPATION_TYPE_JUDGE,
		"ADVOC":  employmentPb.OccupationType_OCCUPATION_TYPE_ADVOCATE,
		"PROIN":  employmentPb.OccupationType_OCCUPATION_TYPE_FUND_MANAGEMENT,
		"SELFE":  employmentPb.OccupationType_OCCUPATION_TYPE_SELF_EMPLOYED,
		"DIPLO":  employmentPb.OccupationType_OCCUPATION_TYPE_DIPLOMAT,
		"AGRI":   employmentPb.OccupationType_OCCUPATION_TYPE_AGRICULTURE,
		"VIRCD":  employmentPb.OccupationType_OCCUPATION_TYPE_VIRTUAL_CURRENCY_DEALER,
		"DLANQ":  employmentPb.OccupationType_OCCUPATION_TYPE_ART_ANTIQUES_DEALER,
		"DLARM":  employmentPb.OccupationType_OCCUPATION_TYPE_ARM_ARMAMENTS_DEALER,
		"GOLDE":  employmentPb.OccupationType_OCCUPATION_TYPE_GOLD_PRECIOUS_STONE_DEALER,
		"PAWNB":  employmentPb.OccupationType_OCCUPATION_TYPE_PAWN_BROKER,
		"PVTSC":  employmentPb.OccupationType_OCCUPATION_TYPE_PRIVATE_SECTOR,
	}

	// https://docs.google.com/spreadsheets/d/1Lf9TUV4lFMB5dj440kJRdvcGSdPGuNVn/edit?gid=1630926951#gid=1630926951
	CategoaryMapping = map[types.Category]string{
		types.Category_CATEGORY_UNSPECIFIED:          "NA",
		types.Category_CATEGORY_NOT_APPLICABLE:       "NA",
		types.Category_CATEGORY_OTHER_BACKWARD_CASTE: "OBC",
		types.Category_CATEGORY_SCHEDULE_CASTE:       "SC",
		types.Category_CATEGORY_SCHEDULE_TRIBE:       "ST",
		types.Category_CATEGORY_GENERAL:              "GN",
	}

	PepCategoryMapping = map[types.PepCategory]string{
		types.PepCategory_PEP_CATEGORY_UNSPECIFIED:              "NA",
		types.PepCategory_PEP_CATEGORY_GOVERNMENT_FUNCTION:      "GOVT",
		types.PepCategory_PEP_CATEGORY_POLITICAL_PARTY_POSITION: "POLST",
		types.PepCategory_PEP_CATEGORY_RELATED_TO_PEP:           "RELPEP",
	}
	EmploymentTypeMapping = map[employmentPb.EmploymentType]string{
		employmentPb.EmploymentType_SELF_EMPLOYED:               "Self Employed",
		employmentPb.EmploymentType_SALARIED:                    "Salaried",
		employmentPb.EmploymentType_RETIRED:                     "Retired",
		employmentPb.EmploymentType_OTHERS:                      "DefaultEmployment",
		employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED: DefaultEmployment,
		employmentPb.EmploymentType_BUSINESS_OWNER:              "Self Employed",
		employmentPb.EmploymentType_FREELANCER:                  "Self Employed",
		employmentPb.EmploymentType_WORKING_PROFESSIONAL:        "Self Employed",
		employmentPb.EmploymentType_STUDENT:                     "Unemployed",
		employmentPb.EmploymentType_HOMEMAKER:                   "Housewife",
		employmentPb.EmploymentType_SELF_EMPLOYED_PROFESSIONAL:  "Self Employed Professional",
		employmentPb.EmploymentType_POLITICIAN_OR_STATESMAN:     "Politician/Statesman",
	}
)

func LoadAddressCodeMapping(configDir string, cityCsvPath string, stateCsvPath string, countryCsvPath string) {
	cityCodeMapping = make(map[string]string)
	stateCodeMapping = make(map[string]string)
	countryCodeMapping = make(map[string]string)

	if err := parseToMap(filepath.Join(configDir, cityCsvPath), cityCodeMapping); err != nil {
		logger.Panic("Failed to open city codes mapping file for Federal", zap.Error(err))
	}
	if err := parseToMap(filepath.Join(configDir, stateCsvPath), stateCodeMapping); err != nil {
		logger.Panic("Failed to open state codes mapping file for Federal", zap.Error(err))
	}
	if err := parseToMap(filepath.Join(configDir, countryCsvPath), countryCodeMapping); err != nil {
		logger.Panic("Failed to open country codes mapping file for Federal", zap.Error(err))
	}
}

func parseToMap(fileName string, mapName map[string]string) error {
	f, err := os.Open(filepath.Clean(fileName))
	if err != nil {
		return err
	}
	defer func() {
		if err = f.Close(); err != nil {
			logger.ErrorNoCtx("Failed to close file here")
		}
	}()

	lines, err := csv.NewReader(f).ReadAll()
	if err != nil {
		return err
	}

	var key, value string
	for _, line := range lines {
		key = line[0]
		value = line[1]
		mapName[key] = value
	}
	return nil
}

// todo (NRI) : move these behind an interface
func GetCountryCode(country string) (string, error) {
	if val, found := countryCodeMapping[strings.ToUpper(country)]; found {
		return val, nil
	}
	logger.ErrorNoCtx("Country code not found for given country")
	return "", errors.New("country code not found")
}

func GetStateCode(state string) (string, error) {
	if val, found := stateCodeMapping[strings.ToUpper(state)]; found {
		return val, nil
	}
	logger.ErrorNoCtx("State code not found for given state")
	return "", errors.New("state code not found")
}

func GetCityCode(city string) (string, error) {
	if val, found := cityCodeMapping[strings.ToUpper(city)]; found {
		return val, nil
	}
	logger.ErrorNoCtx("City code not found for given city")
	return "", errors.New("city code not found")
}

func GetTitleFromGender(gender string) string {
	titleMapping := map[string]string{
		"FEMALE": "Ms",
		"MALE":   "Mr",
	}
	s, ok := titleMapping[gender]
	if !ok {
		return "Ms"
	}
	return s
}

func GetGenderCode(gender string) string {
	genderMapping := map[string]string{
		"FEMALE":      "F",
		"MALE":        "M",
		"TRANSGENDER": "T",
	}
	s, ok := genderMapping[gender]
	if !ok {
		return "O"
	}
	return s
}
func GetMaritalStatusCode(maritalStatus string) string {
	maritalStatusMapping := map[string]string{
		"MARRIED": "MARD",
		"SINGLE":  "SING",
		"NA":      "NA",
	}
	s, ok := maritalStatusMapping[maritalStatus]
	if !ok {
		return "NA"
	}
	return s
}

// method to get get relation to guardian code
func GetGuardianRelationshipCode(relType types.RelationType) (string, error) {
	if value, ok := relationTypeToGuardianCodeMap[relType]; !ok {
		return "", fmt.Errorf("no such guardian code for type %s", relType.String())
	} else {
		return value, nil
	}
}
