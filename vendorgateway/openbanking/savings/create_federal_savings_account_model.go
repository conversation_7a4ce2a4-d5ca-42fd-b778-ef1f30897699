package savings

// bank has added DFDL validation and expects the fields in the request should follow a particular order as below
type CreateAccountReqInVendorSpecifiedOrder struct {
	RespUrl           string         `json:"RespUrl"`
	SenderCode        string         `json:"SenderCode"`
	ServiceAccessId   string         `json:"ServiceAccessId"`
	ServiceAccessCode string         `json:"ServiceAccessCode"`
	DeviceId          string         `json:"DeviceId"`
	UserProfileId     string         `json:"UserProfileId"`
	DeviceToken       string         `json:"DeviceToken"`
	RequestId         string         `json:"RequestId"`
	SchemCode         string         `json:"SchemCode"`
	SolId             string         `json:"SolId"`
	CustId            string         `json:"CustId"`
	CustomerName      string         `json:"CustomerName"`
	PreopenKitFlag    string         `json:"PreopenKitFlag"`
	AccountNo         string         `json:"AccountNo"`
	AccOpenDate       string         `json:"AccOpenDate"`
	ModeOfOperation   string         `json:"ModeOfOperation"`
	MobileNumber      string         `json:"Mobile_Number"`
	EmailId           string         `json:"EmailId"`
	NoOfCustomers     string         `json:"NoOfCustomers"`
	NomineeOptedFlag  string         `json:"NomineeOptedFlag"`
	SourceOfFunds     string         `json:"SourceOfFunds"`
	ScholarshipFlag   string         `json:"ScholarshipFlag"`
	DBT_Flag          string         `json:"DBT_Flag"`
	AnnualTxnVolume   string         `json:"AnnualTxnVolume"`
	AccPurpose        string         `json:"AccPurpose"`
	Nominee_Details   NomineeDetails `json:"NomineeDetails"`
	CustIds           string         `json:"CustIds"`
}

type NomineeDetails struct {
	NomineeName        string          `json:"NomineeName"`
	NomineeRelation    string          `json:"NomineeRelation"`
	NomineeAge         string          `json:"NomineeAge"`
	NomineeDob         string          `json:"NomineeDOB"`
	NomineeMinorFlag   string          `json:"NomineeMinorFlag"`
	NomineeAddress     string          `json:"NomineeAddress"`
	NomineeCityCode    string          `json:"NomineeCitCode"`
	NomineeStateCode   string          `json:"NomineeStaCode"`
	NomineeCountryCode string          `json:"NomineeConCode"`
	NomineePinCode     string          `json:"NomineepinCode"`
	GuardianDetails    GuardianDetails `json:"GuardianDetails"`
}

type GuardianDetails struct {
	GuardianName        string `json:"GuardianName"`
	GuardianCode        string `json:"GuardianCode"`
	GuardianAddress     string `json:"GuardianAddress"`
	GuardianCityCode    string `json:"GuardianCitCode"`
	GuardianStateCode   string `json:"GuardianStaCode"`
	GuardianCountryCode string `json:"GuardianConCode"`
	GuardianPinCode     string `json:"GuardianPinCode"`
}
