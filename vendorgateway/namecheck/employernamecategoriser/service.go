package employernamematch

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/pkg/errors"

	"github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
)

// Service provides an implementation of the employer name check gRPC service
type Service struct {
	Handler *vendorapi.HTTPRequestHandler
	conf    *config.Config
}

// NewService creates and returns an initialized instance of the employernamecategoriser.Service
func NewService(h *vendorapi.HTTPRequestHandler, conf *config.Config) *Service {
	return &Service{Handler: h, conf: conf}
}

func (s *Service) requestFactoryMap() map[commonvgpb.Vendor]vendorapi.SyncRequestFactory {
	return map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
		commonvgpb.Vendor_IN_HOUSE: s.NewInhouseRequest,
	}
}

func (s *Service) EmployerNameCategoriser(ctx context.Context, req *employernamecategoriser.EmployerNameCategoriserRequest) (*employernamecategoriser.EmployerNameCategoriserResponse, error) {
	vendorReq, err := vendorapi.NewVendorRequest(req, s.requestFactoryMap())
	if err != nil {
		return nil, errors.Wrap(err, "failed to create new vendor request")
	}
	res, handlerErr := s.Handler.Handle(ctx, vendorReq)
	if handlerErr != nil {
		return nil, errors.Wrap(handlerErr, "failed to handle categoriser for employer name")
	}
	return res.(*employernamecategoriser.EmployerNameCategoriserResponse), nil
}
