// nolint:dupl
package setu

import (
	"context"
	"fmt"
	"net/http"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	setuRechargePb "github.com/epifi/gamma/api/vendors/setu/recharge"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
)

type FetchPlansRequest struct {
	Conf              *genconf.Config
	ProductInstanceId string
	BaseUrl           string
	EndpointUri       string
	Method            string
	Req               *rechargeVgPb.FetchPlansRequest
	ctx               context.Context
	AccessToken       string
}

func (p *FetchPlansRequest) Marshal() ([]byte, error) {
	customer := p.Req.GetMobileNumber()
	operator := p.Req.GetOperator().ToVendorString()
	location := p.Req.GetLocation()
	billParams := []*setuRechargePb.BillParameter{
		{Name: BillParameterMobileNumber, Value: fmt.Sprintf("%d", customer.GetNationalNumber())},
		{Name: BillParameterOperator, Value: operator},
		{Name: BillParameterLocation, Value: location},
	}
	vendorReq := &setuRechargePb.FetchPlansRequest{
		Customer: &setuRechargePb.FetchPlansRequest_Customer{
			Mobile:         fmt.Sprintf("%d", customer.GetNationalNumber()),
			BillParameters: billParams,
		},
	}

	marshaller := protojson.MarshalOptions{EmitUnpopulated: true}
	return marshaller.Marshal(vendorReq)
}

func (p *FetchPlansRequest) HTTPMethod() string {
	return p.Method
}

func (p *FetchPlansRequest) URL() string {
	return fmt.Sprintf("%s/%s", p.BaseUrl, p.EndpointUri)
}

func (p *FetchPlansRequest) Add(req *http.Request) *http.Request {
	req.Header.Add("X-PRODUCT-INSTANCE-ID", p.ProductInstanceId)
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", p.AccessToken))
	req.Header.Add("Accept", "*/*")
	req.Header.Add("Content-Type", "application/json")
	return req
}

func (p *FetchPlansRequest) GetResponse() vendorapi.Response {
	return &FetchPlansResponse{ctx: p.ctx}
}

type FetchPlansResponse struct {
	ctx context.Context
}

func (p *FetchPlansResponse) Unmarshal(b []byte) (proto.Message, error) {
	resp := &setuRechargePb.FetchPlansResponse{}
	unmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if errUnmarshal := unmarshaller.Unmarshal(b, resp); errUnmarshal != nil {
		return &rechargeVgPb.FetchPlansResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in unmarshalling fetch plans response : %v", errUnmarshal)),
		}, nil
	}

	// fallback handling for error message if vendor api returns 200 http status code but sends error message
	if resp.GetError() != nil {
		return p.handleErrorMessage(resp)
	}

	// Success case
	if resp.GetSuccess() {
		var plans []*rechargeVgPb.Plan
		if resp.GetData() != nil {
			for _, plan := range resp.GetData() {
				plans = append(plans, &rechargeVgPb.Plan{
					Talktime: plan.GetTalktime(),
					PlanName: plan.GetPlanName(),
					Amount:   moneyPkg.FromPaisa(plan.GetAmount()),

					Validity:        plan.GetValidity(),
					PlanDescription: plan.GetPlanDescription(),
					ServiceProvider: plan.GetServiceProvider(),
				})
			}
		}
		return &rechargeVgPb.FetchPlansResponse{
			Status:  rpc.StatusOk(),
			Success: true,
			Plans:   plans,
			TraceId: resp.GetTraceId(),
		}, nil
	}

	// Fallback: unknown error
	return &rechargeVgPb.FetchPlansResponse{
		Status: rpc.StatusUnknown(),
	}, nil
}

func (p *FetchPlansResponse) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	if len(b) == 0 || httpStatus == http.StatusServiceUnavailable {
		return &rechargeVgPb.FetchPlansResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("%d", httpStatus)),
		}, nil
	}

	resp := &setuRechargePb.FetchPlansResponse{}
	unmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if errUnmarshal := unmarshaller.Unmarshal(b, resp); errUnmarshal != nil {
		return &rechargeVgPb.FetchPlansResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in unmarshalling fetch plans response : %v", errUnmarshal)),
		}, nil
	}
	return p.handleErrorMessage(resp)
}

// nolint:unparam
func (p *FetchPlansResponse) handleErrorMessage(resp *setuRechargePb.FetchPlansResponse) (*rechargeVgPb.FetchPlansResponse, error) {
	vgResp := &rechargeVgPb.FetchPlansResponse{
		Success: resp.GetSuccess(),
		TraceId: resp.GetTraceId(),
	}
	switch resp.GetError().GetCode() {
	case InvalidInputParameter:
		vgResp.Status = rpc.StatusInvalidArgumentWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason())
	case InvalidRequest, InvalidProductInstanceId, ApiConnectivityError, InternalServerError:
		vgResp.Status = rpc.StatusInternalWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason())
	default:
		vgResp.Status = rpc.StatusUnknownWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason())
	}
	return vgResp, nil
}
