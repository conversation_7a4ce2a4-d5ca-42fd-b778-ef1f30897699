// nolint:dupl
package setu

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"time"

	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	commonv2 "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	setuRechargePb "github.com/epifi/gamma/api/vendors/setu/recharge"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
)

type InitiateRechargeRequest struct {
	Conf              *genconf.Config
	ProductInstanceId string
	BaseUrl           string
	EndpointUri       string
	Method            string
	Req               *rechargeVgPb.InitiateRechargeRequest
	ctx               context.Context
	AccessToken       string
}

func (p *InitiateRechargeRequest) Marshal() ([]byte, error) {
	paymentDetails := p.Req.GetPaymentDetails()

	// https://github.com/golang/protobuf/issues/1414
	// using int32 since int64 is getting marshalled to a string
	if paymentDetails.GetAmount().GetUnits()*100 > math.MaxInt32 {
		return nil, fmt.Errorf("amount in paise exceeds maximum limit: %d", paymentDetails.GetAmount().GetUnits())
	}

	amountInPaise, toPaiseErr := moneyPkg.ToPaise(paymentDetails.GetAmount())
	if toPaiseErr != nil {
		return nil, fmt.Errorf("error converting amount to paise: %w", toPaiseErr)
	}

	vendorReq := &setuRechargePb.RechargeRequestRequest{
		Customer: &setuRechargePb.RechargeRequestRequest_Customer{
			BillParameters: []*setuRechargePb.BillParameter{
				{
					Name:  BillParameterMobileNumber,
					Value: fmt.Sprintf("%d", p.Req.GetRechargeMobileNumber().GetNationalNumber()),
				},
				{
					Name:  BillParameterOperator,
					Value: p.Req.GetOperator().ToVendorString(),
				},
				{
					Name:  BillParameterLocation,
					Value: p.Req.GetLocation(),
				},
			},
			Mobile: fmt.Sprintf("%d", p.Req.GetCustomerMobileNumber().GetNationalNumber()),
		},
		PaymentDetails: &setuRechargePb.RechargeRequestRequest_PaymentDetails{
			Amount:        int32(amountInPaise), // nolint:gosec
			Mode:          paymentDetails.GetMode().ToVendorString(),
			PaymentParams: []*setuRechargePb.RechargeRequestRequest_PaymentDetails_PaymentParam{},
			PaymentRefId:  paymentDetails.GetPaymentRefId(),
			Timestamp:     paymentDetails.GetTimestamp().AsTime().In(datetime.IST).Format(time.RFC3339),
		},
		RefId: p.Req.GetRefId(),
	}

	marshaller := protojson.MarshalOptions{EmitUnpopulated: true}
	return marshaller.Marshal(vendorReq)
}

func (p *InitiateRechargeRequest) HTTPMethod() string {
	return p.Method
}

func (p *InitiateRechargeRequest) URL() string {
	return fmt.Sprintf("%s/%s", p.BaseUrl, p.EndpointUri)
}

func (p *InitiateRechargeRequest) Add(req *http.Request) *http.Request {
	req.Header.Add("X-PRODUCT-INSTANCE-ID", p.ProductInstanceId)
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", p.AccessToken))
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")
	return req
}

func (p *InitiateRechargeRequest) GetResponse() vendorapi.Response {
	return &InitiateRechargeResponse{ctx: p.ctx}
}

type InitiateRechargeResponse struct {
	ctx context.Context
}

func (p *InitiateRechargeResponse) Unmarshal(b []byte) (proto.Message, error) {
	resp := &setuRechargePb.RechargeRequestResponse{}
	unmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if errUnmarshal := unmarshaller.Unmarshal(b, resp); errUnmarshal != nil {
		return &rechargeVgPb.InitiateRechargeResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in unmarshalling initiate recharge response : %v", errUnmarshal)),
		}, nil
	}

	if resp.GetError() != nil {
		return p.handleErrorMessage(resp)
	}

	if resp.GetSuccess() {
		data := resp.GetData()
		details := data.GetDetails()
		mobileNumber, intParseErr := strconv.ParseUint(details.GetMobileNumber(), 10, 64)
		if intParseErr != nil {
			// gracefully handling parse error since Setu is returning a invalid mobile number in response in UAT env.
			logger.ErrorNoCtx("error in parsing mobile number from response", zap.Error(intParseErr))
			// return &rechargeVgPb.InitiateRechargeResponse{
			// 	Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in parsing mobile number: %v", intParseErr)),
			// }, nil
		}

		return &rechargeVgPb.InitiateRechargeResponse{
			Status:  rpc.StatusOk(),
			Success: true,
			Data: &rechargeVgPb.RechargeDetails{
				MobileNumber: &commonv2.PhoneNumber{NationalNumber: mobileNumber},
				Provider:     rechargeVgPb.GetOperatorEnumForVendorString(details.GetProvider()),
				IsPostpaid:   details.GetIsPostpaid(),
				IsSpecial:    details.GetIsSpecial(),
				Amount: &gmoney.Money{
					CurrencyCode: moneyPkg.RupeeCurrencyCode,
					Units:        data.GetAmount(),
				},
				TransactionRefId: data.GetTransactionRefId(),
				Status:           rechargeVgPb.GetRechargeStatusEnumFromVendorString(data.GetStatus()),
				OperatorRefId:    data.GetOperatorRefId(),
			},
			TraceId: resp.GetTraceId(),
		}, nil
	}

	return &rechargeVgPb.InitiateRechargeResponse{
		Status: rpc.StatusUnknown(),
	}, nil
}

func (p *InitiateRechargeResponse) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	if len(b) == 0 || httpStatus == http.StatusServiceUnavailable {
		return &rechargeVgPb.InitiateRechargeResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("%d", httpStatus)),
		}, nil
	}

	resp := &setuRechargePb.RechargeRequestResponse{}
	unmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if errUnmarshal := unmarshaller.Unmarshal(b, resp); errUnmarshal != nil {
		return &rechargeVgPb.InitiateRechargeResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in unmarshalling initiate recharge response : %v", errUnmarshal)),
		}, nil
	}
	return p.handleErrorMessage(resp)
}

// nolint:unparam
func (p *InitiateRechargeResponse) handleErrorMessage(resp *setuRechargePb.RechargeRequestResponse) (*rechargeVgPb.InitiateRechargeResponse, error) {
	vgResp := &rechargeVgPb.InitiateRechargeResponse{
		Success: resp.GetSuccess(),
		TraceId: resp.GetTraceId(),
	}
	switch resp.GetError().GetCode() {
	case InvalidProductInstanceId, ApiConnectivityError, InternalServerError:
		vgResp.Status = rpc.StatusInternalWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason())
	case DuplicatePaymentRefId:
		vgResp.Status = rpc.StatusAlreadyExistsWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason())
	case InputParamsInvalid:
		vgResp.Status = rpc.StatusInvalidArgumentWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason())
	default:
		vgResp.Status = rpc.StatusUnknownWithDebugMsg(resp.GetError().GetCode() + "-" + resp.GetError().GetFailureReason())
	}
	return vgResp, nil
}
