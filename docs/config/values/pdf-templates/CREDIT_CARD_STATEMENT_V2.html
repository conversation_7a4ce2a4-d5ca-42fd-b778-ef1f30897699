<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>Epifi - Credit Card Statement</title>
	<!-- <link rel="stylesheet" href="./stylesheets/index_v5.css"> -->
	<link rel="stylesheet" href="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/css/font.css" rel="preload"/>
	<link rel="stylesheet" href="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/css/index_v5.css" rel="preload"/>
	<style type="text/css">
		.issuer-detail.mt-80 {
			margin-top: 20px;
		}
		.text-center {
			text-align: center;
		}
		.mr-15 {
			margin-right: 15px;
		}
		.block {
			display: block;
		}
		.hcr-r2 .cr {
			margin-top: 8px;
		}
		.frccwrapper {
			display: flex;
			display: -webkit-flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}
		.frcc-items {
			align-self: center;
			justify-self: center;
		}
		.fi-logo {
			margin-bottom: 5px;
		}
		.hrc-r1_v2 {
			padding: 23px 40px 22px 52px;
		}


	</style>
</head>

<body>
<div id="target">Loading...</div>
<div id="extra-page"></div>
<!-- <div id="debug-page"></div> -->

<!-- Primary Header Template -->
<script id="primary-header-template" type="x-tmpl-mustache">
            <div class="hcr" id="user-info">
                <div class="hcr-r1">
                    <img
                        class="logo"
                        src={{{ CreditCardProgramLogo }}}
                        alt="card-logo"
                    />
                    <div class="wr1">
                        <span class="text-center">
                            <div class="h1 block">Credit Card Statement</div>
                            <div class="block">
                                <span class="l3--v1">{{ FromDate.day }}</span>
                                <span class="l3--v1">{{ FromDate.month }}</span>
                                <span class="l3--v1">{{ FromDate.year }}</span>
                                <span class="l3--v1">&nbsp;to&nbsp;</span>
                                <span class="l3--v1">{{ ToDate.day }}</span>
                                <span class="l3--v1">{{ ToDate.month }}</span>
                                <span class="l3--v1">{{ ToDate.year }}</span>
                            </div>
                        </span>
                    </div>
                    <div class="wr2 frccwrapper">
                        <img
                            class="fi-logo frcc-items mr-15"
                            src={{{ FiBrandLogo }}}
                            alt="epifi"
                        />
                        <img
                            class="logo frcc-items"
                            src={{{ PartnerBankLogo }}}
                            alt="federal bank"
                        />
                    </div>
                </div>
                <div class="hcr-r2">
                    <div class="h1">{{ UserDetails.Name }}</div>
                    <div class="cr">
                        <div class='mr-20 address-section'>
                            <span class="h2">ADDRESS</span>
                            <div class="l3--v1 lb line-clamp-3 he-50">{{ UserDetails.Address }}</div>
                        </div>
                        <div class='mr-20'>
                            <span class="h2">PHONE NO</span>
                            <div class="l3--v1 lb">{{ UserDetails.PhoneNumber }}</div>
                        </div>
                        <div class='mr-20'>
                            <span class="h2">EMAIL</span>
                            <div class="l3--v1 lb">{{ UserDetails.Email }}</div>
                        </div>
                        <div class='credit-card-number-block'>
                            <div class="h2">CREDIT CARD NO</div>
                            <div class="l4--v1 lb">{{ UserDetails.MaskedCreditCardNumber }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </script>

<!-- Secondary Header Template -->
<script id="secondary-header-template" type="x-tmpl-mustache">
        <div class="hcr-r1 hrc-r1_v2">
            <img
                class="logo"
                src={{{ CreditCardProgramLogo }}}
                alt="card-logo"
            />
            <div class="wr1">
                <span class="text-center">
                    <div class="h1 block">Credit Card Statement</div>
                    <div class="block">
                        <span class="l3--v1">{{ FromDate.day }}</span>
                        <span class="l3--v1">{{ FromDate.month }}</span>
                        <span class="l3--v1">{{ FromDate.year }}</span>
                        <span class="l3--v1">&nbsp;to&nbsp;</span>
                        <span class="l3--v1">{{ ToDate.day }}</span>
                        <span class="l3--v1">{{ ToDate.month }}</span>
                        <span class="l3--v1">{{ ToDate.year }}</span>
                    </div>
                </span>
            </div>
            <div class="wr2 frccwrapper">
                <img
                    class="fi-logo frcc-items mr-15"
                    src={{{ FiBrandLogo }}}
                    alt="epifi"
                />
                <img
                    class="logo frcc-items"
                    src={{{ PartnerBankLogo }}}
                    alt="federal bank"
                />
            </div>
        </div>
    </script>
<!-- Footer Template -->
<script id="footer-template" type="x-tmpl-mustache">
            <div class="fcr">
                <div class="cr cr1">
                    <div class="wr l6--v2">
                        <img
                            class="logo"
                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/day.svg"
                            alt="logo"
                        />
                        <div class="legend">5AM - 6PM</div>
                    </div>
                    <div class="wr l6--v2">
                        <img
                            class="logo"
                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/night.svg"
                            alt="logo"
                        />
                        <div class="legend">6PM - 5AM</div>
                    </div>
                    <div class="wr l6--v2">
                        <img
                            class="logo"
                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/debit.svg"
                            alt="logo"
                        />
                        <div class="legend">Debit</div>
                    </div>
                    <div class="wr l6--v2">
                        <img
                            class="logo"
                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/credit.svg"
                            alt="logo"
                        />
                        <div class="legend">Credit</div>
                    </div>
                    <div class="wr l6--v3">
                        <img
                            class="logo"
                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/automated.svg"
                            alt="logo"
                        />
                        <div class="legend">Automated</div>
                    </div>
                </div>
                <div class="cr cr2" {{#lastPage}}style="max-height:38px"{{/lastPage}}>
                    <div class="wr1">
                        <img
                            class="logo"
                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/partnership-federal-logo_white.svg"
                            alt="logo"
                        />
                        <div class="h2--v1 mr1">CONTACT US</div>
                        <div class="l6--v1 mr2">080-********</div>
                        <div class="h2--v1">PAGE {{ Page }} OF {{ Length }}</div>
                    </div>
                    <!-- <div class="wr2">
                        <span class="h2--v1">CONTACT US</span>
                        <span class="l6--v1 mr1">080-********</span>
                        <span class="h2--v1">PAGE {{ Page }} OF {{ Length }}</span>
                    </div> -->
                </div>
            </div>
        </script>

<!-- Content Template -->
<script id="content-template" type="x-tmpl-mustache">
            <div>
                <div class="{{ Class }}">
                    {{#HasTxnSummaryData}}
                    <div class='ju-sb'>
                        <div class='wd-36_per'>
                            <div class='mb-30'>
                                <div class='summary-block bg--light-grey'>
                                    <div class='card mb-20'>
                                        <div class='mr-20'>
                                            <img src='https://epifi-icons.s3.ap-south-1.amazonaws.com/assets/svgs/statement-sign.svg' alt='statement' />
                                        </div>
                                        <div>
                                            <span class='h2--v3'>STATEMENT DATE</span>
                                            <br />
                                            <span class='h1--v3'>{{ TxnSummaryData.StatementDate }}</span>
                                        </div>
                                    </div>
                                    <div class='card mb-20'>
                                        <div class='mr-20'>
                                            <img src='https://epifi-icons.s3.ap-south-1.amazonaws.com/assets/svgs/payment-sign.svg' alt='statement' />
                                        </div>
                                        <div>
                                            <span class='h2--v3'>PAYMENT DUE DATE</span>
                                            <br />
                                            <span class='h1--v3'>{{ TxnSummaryData.PaymentDueDate }}</span>
                                        </div>
                                    </div>
                                    <div class='card'>
                                        <div class='mr-20'>
                                            <img src='https://epifi-icons.s3.ap-south-1.amazonaws.com/assets/svgs/rupees-sign.svg' alt='statement' />
                                        </div>
                                        <div>
                                            <span class='h2--v3'>AVAILABLE LIMIT</span>
                                            <br />
                                            <span class='h1--v3'>₹{{ TxnSummaryData.AvailableLimit.Amount.units }}.{{TxnSummaryData.AvailableLimit.Amount.decimals}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{#hasRewardsSummaryData}}
                            <div class='mb-24'>
                                <div class='rewards-block bg--light-grey'>
                                    <div class='h1--v1 ta-center rewards-block-title'>{{ RewardsSummaryData.title }}</div>
                                    <div class='top-merchants-section mt-20 mb-20'>
                                        {{#RewardsSummaryData.merchantWiseRewardInfo}}
                                        <div class='fl ju-sb'>
                                            <div class='fl al-ce'>
                                                <div class='mr-8'>
                                                    <img src={{ merchantIconUrl }} alt='rewards logo' class='rewards-logo' />
                                                </div>
                                                <div class='wd-80' >
                                                    <span class='h2--v5'>{{ title }}</span>
                                                </div>
                                            </div>
                                            <div class='fl al-ce'>
                                                {{#useV2}}
                                                <img
                                                    class="logo mr-4"
                                                    src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-points.svg"
                                                    alt="fi points"
                                                />
                                                {{/useV2}}
                                                {{#useV1}}
                                                <img
                                                    class="logo mr-4"
                                                    src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-points.svg"
                                                    alt="fi points"
                                                />
                                                {{/useV1}}
                                                {{^useV2}}{{^useV1}}
                                                <img
                                                    class="logo mr-4"
                                                    src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-coins.svg"
                                                    alt="fi coins"
                                                />
                                                {{/useV1}}{{/useV2}}
                                                <div class="h2--v2">{{ totalRewardCoinsEarned.units }}</div>
                                            </div>
                                        </div>
                                        {{/RewardsSummaryData.merchantWiseRewardInfo}}
                                    </div>
                                    <div class='l6 ta-center'>{{ RewardsSummaryData.bottomText }}</div>
                                </div>
                            </div>
                            {{/hasRewardsSummaryData}}
                            <div>
                                <div class='fi-coins-block bg--light-grey'>
                                    <div class='ta-center h2--v3'>{{ RewardsSummaryData.rewardCoinsSummary.Title }}</div>
                                    <div class='ju-ce al-ce mb-10 mt-6'>
                                        {{#useV2}}
                                        <img
                                            class="logo mr-4"
                                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-points.svg"
                                            alt="fi points"
                                        />
                                        {{/useV2}}
                                        {{#useV1}}
                                        <img
                                            class="logo mr-4"
                                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-points.svg"
                                            alt="fi points"
                                        />
                                        {{/useV1}}
                                        {{^useV2}}{{^useV1}}
                                        <img
                                            class="logo mr-4"
                                            src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-coins.svg"
                                            alt="fi coins"
                                        />
                                        {{/useV1}}{{/useV2}}
                                        <div class="h2--v2">{{ RewardsSummaryData.rewardCoinsSummary.TotalRewardCoinsEarned.units }}</div>
                                    </div>
                                </div>
                            </div>
                            {{#showProTipInReward}}
                            <div class='pro-tip-v2 bg--light-grey'>
                                <div>
                                    <span class='h2--v4'>{{ TipsAndInformationData.Title }}:</span>
                                    {{#TipsAndInformationData.InformationText}}
                                    <span class='l6 pro-tip-subtitle'>
                                        {{Title}}
                                            {{#TextWithLinkAndValues}}
                                             <a class='link' href={{Link}} > {{Title}} </a>
                                        {{/TextWithLinkAndValues}}
                                    </span>
                                    <br />
                                    <br />
                                    {{/TipsAndInformationData.InformationText}}
                                </div>
                            </div>
                            <br>
                            <br>
                            {{/showProTipInReward}}
                        </div>
                        <div class='wd-62_per mb-40'>
                            <div class="txnsmrywr">
                                <div class='fl amount-block'>
                                    <div class='cards mr-20'>
                                        <div class="l6--v6">
                                            TOTAL AMOUNT DUE
                                            <br/>
                                        </div>
                                        <div class="h1--v4">₹{{ TxnSummaryData.TotalAmountDue.Amount.units }}.{{ TxnSummaryData.TotalAmountDue.Amount.decimals }}</div>
                                    </div>
                                    <div class='cards'>
                                        <div class="l6--v6">
                                            MINIMUM AMOUNT DUE
                                            <br/>
                                        </div>
                                        <div class="h1--v4">₹{{ TxnSummaryData.MinAmountDue.Amount.units }}.{{ TxnSummaryData.MinAmountDue.Amount.decimals }}</div>
                                    </div>
                                </div>
                                <div class="l6--v6 mb-20">
                                    Minimum amount due is 5% of purchases + 100% of active EMIs and all card related charges including any over limit spend. Interest will be applied if your total amount due is not paid.
                                </div>
                                <div class='bor-grey'></div>
                                <div class='mt-20'>
                                    <div class='ju-sb al-ce mb-20'>
                                        <div class='wd-200' >
                                            <div class="h1--v3">Opening Balance</div>
                                            <div class="l6">on {{ FromDate.day }} {{ FromDate.monthShortStr }} {{ FromDate.year }}</div>
                                        </div>
                                        <div class="wr">
                                            <div class="h2--v2">₹</div>
                                            <div class="h2--v2">{{ TxnSummaryData.OpeningBalance.Amount.units }}</div>
                                            <div class="h2--v2">.{{ TxnSummaryData.OpeningBalance.Amount.decimals }}</div>
                                        </div>
                                    </div>
                                    <div class='ju-sb al-ce mb-20'>
                                        <div class='wd-200' >
                                            <div class="h1--v3">Spends</div>
                                        </div>
                                        <div class="wr">
                                            <div class="h2--v2">₹</div>
                                            <div class="h2--v2">{{ TxnSummaryData.Spends.Amount.units }}</div>
                                            <div class="h2--v2">.{{ TxnSummaryData.Spends.Amount.decimals }}</div>
                                        </div>
                                    </div>
                                    <div class='ju-sb al-ce mb-20'>
                                        <div class='wd-200' >
                                            <div class="h1--v3">Fees, Taxes and Others</div>
                                            <div class="l6">Including taxes on fees & charges at 18%</div>
                                        </div>
                                        <div class="wr">
                                            <div class="h2--v2">₹</div>
                                            <div class="h2--v2">{{ TxnSummaryData.Fees.Amount.units }}</div>
                                            <div class="h2--v2">.{{ TxnSummaryData.Fees.Amount.decimals }}</div>
                                        </div>
                                    </div>
                                    <div class='ju-sb al-ce mb-20'>
                                        <div class='wd-200' >
                                            <div class="h1--v3">Repayments & Refunds</div>
                                        </div>
                                        <div class="wr">
                                            <div class="h2--v2">₹</div>
                                            <div class="h2--v2">{{ TxnSummaryData.RepaymentsAndRefunds.Amount.units }}</div>
                                            <div class="h2--v2">.{{ TxnSummaryData.RepaymentsAndRefunds.Amount.decimals }}</div>
                                        </div>
                                    </div>
                                    <div class='ju-sb al-ce'>
                                        <div class='wd-200' >
                                            <div class="h1--v3">Spend converted to EMI</div>
                                        </div>
                                        <div class="wr">
                                            <div class="h2--v2">₹</div>
                                            <div class="h2--v2">{{ TxnSummaryData.SpendsConvertedToEmi.Amount.units }}</div>
                                            <div class="h2--v2">.{{ TxnSummaryData.SpendsConvertedToEmi.Amount.decimals }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{#hasRewardsSummaryData}}
                            <div class='fl al-ce bg--light-grey pro-tip-box'>
                                <div class='mr-20'>
                                    <img src={{ TipsAndInformationData.IconUrl }} alt='suggestion' width='24px' height='24px' />
                                </div>
                                <div class='wd-90_per'>
                                    <span class='h2--v4'>{{ TipsAndInformationData.Title }}:</span>
                                    {{#TipsAndInformationData.InformationText}}
                                    <span class='l6 pro-tip-subtitle'>
                                        {{Title}}
                                            {{#TextWithLinkAndValues}}
                                             <a class='link' href={{Link}} > {{Title}} </a>
                                        {{/TextWithLinkAndValues}}
                                    </span>
                                    <br />
                                    <br />
                                    {{/TipsAndInformationData.InformationText}}
                                </div>
                            </div>
                            {{/hasRewardsSummaryData}}
                        </div>
                    </div>
                    {{/HasTxnSummaryData}}
                    {{#HasTxns}}
                    <div id="transaction-Details" class='mb-40' >
                        <div class="hwr">
                            <div class="wr1">
                                <div class="h1--v1">Date</div>
                                <div class="l6">Day/Night</div>
                            </div>
                            <div class="wr2">
                                <div class="h1--v1">Transaction Details</div>
                                <div class="l6">Place • Payment Method • Cateogry</div>
                            </div>
                            {{#useV2}}
                            <div class="wr3">
                                <div class="h1--v1">Fi-Points</div>
                                <div class="l6">12 Fi-Points = ₹3</div>
                            </div>
                            {{/useV2}}
                            {{#useV1}}
                            <div class="wr3">
                                <div class="h1--v1">Fi-Points <span style="background-color: #FFFCEB; color: #98712F; padding: 2px 6px; font-size: 10px; border-radius: 4px; display: inline-block; vertical-align: middle;">NEW</span></div>
                                <div class="l6">12 Fi-Points = ₹3</div>
                            </div>
                            <div class="wr3">
                                <div class="h1--v1">Fi-Coins</div>
                                <div class="l6">100 Fi-Coins = ₹3</div>
                            </div>
                            {{/useV1}}
                            {{#useV0}}
                            <div class="wr3">
                                <div class="h1--v1">Fi-Coins</div>
                                <div class="l6">100 Fi-Coins = ₹3</div>
                            </div>
                            {{/useV0}}
                            <div class="wr4">
                                <div class="h1--v1">Amount</div>
                            </div>
                        </div>
                        {{#RenderRewardsData}}
                        {{#ExtraRewardsInfo}}
                        <div class="txnwr">
                            <div class="cl">
                                <div class="wr1">
                                </div>
                                <div class="wr2">
                                    <img
                                        class="rewardlogo mr-4 va-middle"
                                        src={{ RewardTypeLogo }}
                                        alt="fi coins"
                                    />
                                    <span class="h2--v2 va-middle">{{ Text }}</span>
                                </div>
                                {{#useV2}}
                                <div class="wr3">
                                    <img
                                        class="logo mr-4"
                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-points.svg"
                                        alt="fi points"
                                    />
                                    <div class="h2--v2">{{ RewardCoinsEarned.units }}</div>
                                </div>
                                {{/useV2}}
                                {{#useV1}}
                                <div class="wr3">
                                    <img
                                        class="logo mr-4"
                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-points.svg"
                                        alt="fi points"
                                    />
                                    <div class="h2--v2">{{ RewardCoinsEarned.units }}</div>
                                </div>
                                <div class="wr3">
                                    <div class="h2--v2 ml-4"> - </div>
                                </div>
                                {{/useV1}}
                                {{#useV0}}
                                <div class="wr3">
                                    <img
                                        class="logo mr-4"
                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-coins.svg"
                                        alt="fi coins"
                                    />
                                    <div class="h2--v2">{{ RewardCoinsEarned.units }}</div>
                                </div>
                                {{/useV0}}
                                <div class="wr4">
                                </div>
                            </div>
                        </div>
                        {{/ExtraRewardsInfo}}
                        {{/RenderRewardsData}}
                        {{#firstPagefooter}}
                          <div class='nxt-page l6--v5' >View all your transactions on the next page</div>
                        {{/firstPagefooter}}
                        <div>
                            {{#TxnData}}
                                {{^hasPageLengthOne}}
                                <div class="cwr">
                                    <div class="wr1 l4">
                                        {{name}}
                                    </div>
                                    <div class="wr2"></div>
                                </div>
                                {{/hasPageLengthOne}}
                                {{^hasPageLengthOne}}
                                <div class="txnwr">
                                    {{#data}}
                                        <div class="cl">
                                            <div class="wr1">
                                                {{#IsDayIcon}}
                                                    <img
                                                        class="logo"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/day.svg"
                                                        alt="day"
                                                    />
                                                {{/IsDayIcon}}
                                                {{#IsNightIcon}}
                                                    <img
                                                        class="logo"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/night.svg"
                                                        alt="night"
                                                    />
                                                {{/IsNightIcon}}
                                            </div>
                                            <div class="vertical-bar"></div>
                                            <div class="wr2">
                                                <div class="h1--v3">{{ MerchantName }}</div>
                                                <div class="l6--v5 desc">
                                                    {{ TxnDescription }}
                                                </div>
                                            </div>
                                            {{#useV2}}
                                            <div class="wr3">
                                                {{ #hasRewardPoints }}
                                                    <img
                                                        class="logo mr-4"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-points.svg"
                                                        alt="fi points"
                                                    />
                                                    <div class="h2--v2">{{ RewardPoints }}</div>
                                                {{ /hasRewardPoints }}
                                                {{ ^hasRewardPoints }}
                                                    <div class="h2--v2 ml-4"> - </div>
                                                {{ /hasRewardPoints }}
                                            </div>
                                            {{/useV2}}
                                            {{#useV1}}
                                            <div class="wr3">
                                                {{ #hasRewardPoints }}
                                                    <img
                                                        class="logo mr-4"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-points.svg"
                                                        alt="fi points"
                                                    />
                                                    <div class="h2--v2">{{ RewardPoints }}</div>
                                                {{ /hasRewardPoints }}
                                                {{ ^hasRewardPoints }}
                                                    <div class="h2--v2 ml-4"> - </div>
                                                {{ /hasRewardPoints }}
                                            </div>
                                            <div class="wr3">
                                                {{ #hasRewardCoins }}
                                                    <img
                                                        class="logo mr-4"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-coins.svg"
                                                        alt="fi coins"
                                                    />
                                                    <div class="h2--v2">{{ RewardCoins.units }}</div>
                                                {{ /hasRewardCoins }}
                                                {{ ^hasRewardCoins }}
                                                    <div class="h2--v2 ml-4"> - </div>
                                                {{ /hasRewardCoins }}
                                            </div>
                                            {{/useV1}}
                                            {{#useV0}}
                                            <div class="wr3">
                                                {{ #hasRewardCoins }}
                                                    <img
                                                        class="logo mr-4"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/fi-coins.svg"
                                                        alt="fi coins"
                                                    />
                                                    <div class="h2--v2">{{ RewardCoins.units }}</div>
                                                {{ /hasRewardCoins }}
                                                {{ ^hasRewardCoins }}
                                                    <div class="h2--v2 ml-4"> - </div>
                                                {{ /hasRewardCoins }}
                                            </div>
                                            {{/useV0}}

                                            <div class="wr4">
                                                <div class="wr">
                                                    <div class="h2--v2">₹{{ Amount.units }}</div>
                                                    <div class="h2--v2">.{{ Amount.decimals }}</div>
                                                </div>
                                                {{#IsDebit}}
                                                    <img
                                                        class="arrow-icon"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/debit.svg"
                                                        alt="db"
                                                    />
                                                {{/IsDebit}}
                                                {{#IsCredit}}
                                                    <img
                                                        class="arrow-icon"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/credit.svg"
                                                        alt="cr"
                                                    />
                                                {{/IsCredit}}
                                                {{#IsAutomated}}
                                                    <img
                                                        class="arrow-icon"
                                                        src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/automated.svg"
                                                        alt="automated"
                                                    />
                                                {{/IsAutomated}}
                                            </div>
                                        </div>
                                    {{/data}}
                                </div>
                                {{/hasPageLengthOne}}
                            {{/TxnData}}
                        </div>
                    </div>
                    {{/HasTxns}}
                    {{^HasTxns}}
                        {{ #showNoTxnUi }}
                            <div class="notxnwr">
                                <div class="wr1">
                                    <span class="h1--v3">It looks like you have not made</span>
                                    <br />
                                    <span class="h1--v3">any transactions on Fi between</span>
                                    <br />
                                    <span class="h1--v1">{{ FromDate.day }} {{ FromDate.month }} {{ FromDate.year }} to {{ ToDate.day }} {{ ToDate.month }} {{ ToDate.year }}</span>
                                </div>
                                <img
                                    class="logo"
                                    src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/pngs/<EMAIL>"
                                    alt="no-txns"
                                />
                            </div>
                        {{ /showNoTxnUi }}
                    {{/HasTxns}}
                    {{#pageLength}}
                        {{#hasEmiSummary}}
                        <div class=''>
                            <div class='title'>EMI Summary</div>
                            <div class='emi-summary'>
                                <div class='emi-due bg--light-grey p-20'>
                                    <div class='h2--v6'>DUE THIS MONTH</div>
                                    <div class='h1--v1 mt-6' >₹{{ EmiSummary.DueAmount.units }}.{{EmiSummary.DueAmount.decimals}}</div>
                                    <div class='h2--v6 mt-24'>ACTIVE EMIs</div>
                                    <div class='h1--v1 mt-6' >{{ EmiSummary.NoOfActiveEmi }}</div>
                                </div>
                                <div class='emi-detail bg--light-grey p-20'>
                                    <table class='table' >
                                        <tr class='table-head' >
                                            <th class='h2--v6 mt-20 mb-20'>NAME</th>
                                            <th class='h2--v6 mt-20 mb-20'>INSTALLMENTS</th>
                                            <th class='h2--v6 mt-20'>MONTHLY INSTALLMENTS</th>
                                        </tr>
                                        {{#EmiSummary.EmiDetails}}
                                        <tr class="mb-40">
                                            <td class='h1--v1 pt-15'>{{MerchantName}}</td>
                                            <td class='h1--v1 pt-15'>{{ InstallmentNumber }} of {{TotalInstallment}}</td>
                                            <td class='h1--v1 pt-15'>₹{{ Amount.units }}.{{Amount.decimals}}</td>
                                        </tr>
                                        {{/EmiSummary.EmiDetails}}
                                    </table>
                                </div>
                            </div>
                        </div>
                        {{/hasEmiSummary}}
                    {{/pageLength}}
                </div>
            </div>
        </script>
		<script id="second-last-page-template" type="x-tmpl-mustache">
			<table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; width: 100%; height: 100vh; margin: 0; padding: 0;">
			<tr>
			  <td align="center" style="padding: 0; width: 100%; height: 100vh;">
				<table border="0" cellpadding="0" cellspacing="0" class="main-container" style="border-collapse: collapse; width: 100%; height: 100vh; background-color: #ffffff; margin: 0; padding: 0;">
				  <tr style="height: 100%;">
					<td class="content-section" style="padding: 10px 20px 0 20px; width: 100%; height: 100%; vertical-align: top;">
					  <!-- Ways to Pay Section -->
					  <div class="tag" style="display: block; width: 100px; background-color: #f5f5f5; border-radius: 50px; padding: 0px 7px; margin-bottom: 0px;">
						<span class="tag-text" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #636363; font-size: 12px; letter-spacing: 0.3px;">WAYS TO PAY</span>
					  </div>

					  <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin-top: 10px;">
						<tr>
						  <td style="width: 38%; vertical-align: top; padding-right: 0px;">
							<!-- Fi App - Image and Description in same row -->
							<table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
							  <tr>
								<td style="width: 160px; vertical-align: top; padding-right: 10px;">
								   <img src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/fi-app.png" alt="IMPS/NEFT" class="payment-icon" style="width: 120px; height: 100px; border-radius: 8px; display: block; margin: 0 auto; " />
								</td>
								<td style="vertical-align: top; padding-top: 35px;">
								  <div class="content-heading" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #004e2d; font-size: 14px; line-height: 18px; margin-bottom: 6px;">Fi App</div>
								  <p class="content-subtext" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #6a6d70; font-size: 10px; line-height: 16px; margin: 0;">Tap on 'Pay' on your<br/>credit card page</p>
								</td>
							  </tr>
							</table>
						  </td>

						  <td style="width: 1%; vertical-align: top;">
							<div class="vertical-divider" style="width: 1px; background-color: #e0e0e0; height: 100px;"></div>
						  </td>

						  <td style="width: 54%; vertical-align: top; padding-left: 0px;">
							<!-- IMPS/NEFT - Image and Description in same row -->
							<table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
							  <tr>
								<td style="width: 170px; vertical-align: top; padding-right: 6px;">
								  <img src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/bank-logo.png" alt="IMPS/NEFT" class="payment-icon" style="width: 150px; height: 120px; border-radius: 8px; display: block; margin: 0 auto;" />
								</td>
								<td style="vertical-align: top; padding-top: 0px;">
								  <div class="content-heading" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #004e2d; font-size: 14px; line-height: 18px; margin-bottom: 4px;">IMPS/NEFT transfer</div>
								  <p class="content-subtext" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #6a6d70; font-size: 10px; line-height: 12px; margin: 0;">
									<span style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">Account Number</span> will be your 16-digit credit card number
									<span style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">OR</span> 91(your mobile number)(last 4 digits of card).
									<span style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">IFSC FDRL00CARDS.</span>
									<br />
									<span style="display: block; margin-top: 4px;">
									  <span style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">Example:</span>
									  if your mobile number is <span style="color: #e8ad62;">**********</span> and your credit card number is 4123 •••• ••••
									  <span style="color: #d65779;">7865</span>, then the a/c number would be
									  <span style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">91</span><span style="color: #e8ad62;">**********</span><span style="color: #d65779;">7865</span>
									</span>
								  </p>
								</td>
							  </tr>
							</table>
						  </td>
						</tr>
					  </table>

					  <div>
						<p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px;margin-top:0px;margin-bottom:0px;">
						  You may make payment through third party applications, however, cardholders should exercise due caution and and refrain from making payments through modes other than those authorised by bank. Federal Bank and/or Fi does not take responsibility for the success of payments through such third party applications.
						</p>
					  </div>

					  <div style="margin-top: 13px;">
						<div class="tag" style="background-color: #f5f5f5; border-radius: 50px; padding: 0px 7px; display: inline-block; margin-bottom: 0px;">
						  <span class="tag-text" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #636363; font-size: 12px; letter-spacing: 0.3px;">IMPORTANT INFORMATION ABOUT YOUR CREDIT CARD</span>
						</div>

						<ul class="info-list" style="margin: 12px 0; padding-left: 20px;">
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							In case your credit card is lost or stolen, freeze or block your card immediately from the <span style="color: #00b899;">Fi app</span> or contact Federal Bank's customer care
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							Please note that all rewards will be converted to rewards will be credited within 10 days of bill generation.
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							Rewards awarded to you may be different than what is shown in the bill in case of any transaction cancellations/reversals/refunds. In these cases, rewards may be deducted/reversed from your rewards balance after bill generation.
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							Transactions are classified as weekend/weekday depending on the date & time of transaction completion.
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							Only transactions available in the statement will get rewarded, rewards for any transactions done before the bill generation and are not available in the statement will be adjusted in the next billing cycle.
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							<span style="color: #6a6d70;">There is a maximum monthly cap to the rewards you can earn in a billing cycle. For more details on this,</span> refer to <span style="color: #00b899; text-decoration: underline;">MITC.</span>
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							Making only the minimum payment every month would result in the repayment stretching over months / years with consequential compounded interest payment on your outstanding balance. Interest-free credit period is suspended if any balance of the previous month's bill is outstanding. Late payment charges may be applied on each statement based on the total amount due at that time, if at least the minimum amount due is not paid every month
						  </li>
						</ul>
					  </div>

					  <div style="margin-top: 10px;">
						<div class="tag" style="background-color: #f5f5f5; border-radius: 50px; padding: 0px 7px; display: inline-block; margin-bottom: 0px;">
						  <span class="tag-text" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #636363; font-size: 12px; letter-spacing: 0.3px;">IMPORTANT INFORMATION ON DISPUTED TRANSACTIONS</span>
						</div>

						<ul class="info-list" style="margin: 12px 0; padding-left: 20px;">
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							As per VISA guidelines, all card holders should provide a duly filled in Cardholder Dispute Form (CDF) mentioning the details of the disputed transaction which will enable/authorize the bank to investigate with the respective Merchant/Member Bank. Please get in touch with the bank customer support channels to raise a dispute and provide necessary details.
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							Transaction dispute needs to be reported in writing within 60 days from the statement date in which the said transaction appears.
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #6a6d70; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							When a dispute is raised for a transaction, depending on the nature of the dispute, a temporary credit to the extent of the amount disputed may be given and an investigation is initiated. This temporary credit will nullify the effect of the disputed transaction on the total outstanding. This is to ensure that you are not adversely impacted during the period of investigation.
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #6a6d70; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							Based on the outcome of the investigation, the temporary credit may be made permanent resulting in no liability from your end for the transaction or reversed, wherein the amount under dispute will be debited to your account and payable by you. We will contact you with details, if the temporary credit given to the card account is reversed.
						  </li>
						  <li style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-bottom: 0px; list-style-type: disc;">
							You will receive written communication advising you about the out-come of your dispute.
						  </li>
						</ul>
					  </div>

					  <div style="margin-top: 10px;">
						<div class="tag" style="background-color: #f5f5f5; border-radius: 50px; padding: 0px 7px; display: inline-block; margin-bottom: 12px;">
						  <span class="tag-text" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #636363; font-size: 12px; letter-spacing: 0.3px;">GRIEVANCE REDRESSAL</span>
						</div>

						<p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-top: 6px;margin-bottom:0px;">
						  For further assistance on this or any other matters related to your credit card, reach out to <span style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">Federal Banks's customer care</span>:
						</p>

						<p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-top: 0px;">
						  Phone: <span style="color: #00b899;">***********</span><br/>
						  Email: <span style="color: #00b899;"><EMAIL></span>
						</p>

						<p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-top: 28px;margin-bottom:0px;">
						  For any issues related to the <span style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">Fi App</span> contact:
						</p>

						<p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin-top: 0px;">
						  Phone: <span style="color: #00b899;">080-********</span><br/>
						  Email: <span style="color: #00b899;"><EMAIL></span>
						</p>
					  </div>
					</td>
				  </tr>
				</table>
			  </td>
			</tr>
			</table>
		</script>
        <script id="last-page-template" type="x-tmpl-mustache">
            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; width: 100%; height: 100vh; margin: 0; padding: 0;">
            <tr>
            <td align="center" style="padding: 0; width: 100%;">
              <table border="0" cellpadding="0" cellspacing="0" class="main-container" style="border-collapse: collapse; width: 100%; height: 886px; background-color: #ffffff; margin: 0; padding: 0;">
                <tr style="height: 100%;">
                  <td class="content-section" style="padding: 20px 40px; width: 100%; height: 100%; vertical-align: top;">

                    <!-- Grievance Redressal Content -->
                    <div style="margin-bottom: 20px;">
                      <p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin: 0;">
                        If you are not happy with the resolution, please contact <span class="content-text-bold" style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">Nodal Officer</span> for Grievance Redressal:
                      </p>

                      <p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin: 4px 0 0 0;">
                        Name: Nikhil A, Associate Vice President<br/>
                        The Federal Bank Ltd. 2nd Floor, Municipal Building, Aluva, Ernakulam, Kerala, India, 683101<br/>
                        Phone: <span class="content-text-bold" style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">0484-2866511</span><br/>
                        Email: <span class="link-text-underline" style="color: #00b899; text-decoration: underline;"><EMAIL></span>
                      </p>
                    </div>

                    <div style="margin-bottom: 20px;">
                      <p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin: 0;">
                        If your complaint has not been handled properly or there has been a delay in resolving the issue to your satisfaction, please escalate to our <span class="content-text-bold" style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">Principal Nodal Officer</span>
                      </p>

                      <p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin: 4px 0 0 0;">
                        Name: Minimole Liz Thomas, Head – Service Quality Department<br/>
                        The Federal Bank Ltd. Federal Towers, Aluva, Ernakulam, Kerala, India, 683101<br/>
                        Phone: <span class="content-text-bold" style="font-family: 'Inter', Arial, sans-serif; font-weight: 700;">0484-2626366</span><br/>
                        Email: <span class="link-text-underline" style="color: #00b899; text-decoration: underline;"><EMAIL></span>
                      </p>
                    </div>

                    <div style="margin-bottom: 20px;">
                      <p class="content-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #636363; font-size: 12px; line-height: 16px; margin: 0;">
                        If you are still not satisfied with the resolution of your complaint you can approach Banking Ombudsman. Please take note that the first point for redressal of complaints is the bank itself. <span class="content-text-bold" style="font-family: 'Inter', Arial, sans-serif; font-weight: 700; color: #636363;">The complainants may approach Reserve Bank Integrated Ombudsman</span> through <span class="link-text-underline" style="color: #00b899; text-decoration: underline;">this link</span> <span class="highlight-gray" style="color: #6a6d70;">(or)</span> <span class="content-text-bold" style="font-family: 'Inter', Arial, sans-serif; font-weight: 700; color: #6a6d70;">Write to CRPC to this address:</span> <span class="highlight-gray" style="color: #6a6d70;">Reserve Bank of India, 4th floor, Sector 17, Chandigarh, 160017 RBI Contact Centre – 14448</span>
                      </p>
                    </div>

                    <div style="margin-bottom: 20px;">
                      <p class="link-text" style="font-family: 'Inter', Arial, sans-serif; font-weight: 400; color: #00b899; font-size: 12px; line-height: 16px; margin: 0;">
                        To know more, read Federal Bank's <span class="link-text-underline" style="text-decoration: underline;">Most Important Terms & Conditions (MITC)</span>, <span class="link-text-underline" style="text-decoration: underline;">Key Fact Statement (KFS)</span> and <span class="link-text-underline" style="text-decoration: underline;">Terms & Conditions.</span>
                      </p>
                    </div>

                    <!-- Issuer Details Box -->
                    <div class="issuer-details-box" style="background-color: #f5f5f5; border-radius: 15px; padding: 24px; margin-top: 24px;">
                      <div class="tag" style="background-color: #ffffff; border-radius: 6px; padding: 2px 4px; display: inline-block; margin-bottom: 12px;">
                        <span class="tag-text" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #6a6d70; font-size: 10px; letter-spacing: 0;">ISSUER DETAILS</span>
                      </div>

                      <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                        <tr>
                          <td style="width: 50%; vertical-align: top; padding-right: 60px;">
                            <p class="issuer-details-text" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #6A6D70; font-size: 13px; line-height: 16px; margin: 0;">
                              The Federal Bank Limited<br/>
                              GSTN: 32AABCT0020H3Z3<br/>
                              Parackal Towers, Thottakkatukara, Aluva,<br/>
                              Ernakulam, Kerala - 683101
                            </p>
                          </td>
                          <td style="width: 50%; vertical-align: top;">
                            <p class="issuer-details-text" style="font-family: 'Gilroy', Arial, sans-serif; font-weight: 600; color: #6a6d70; font-size: 13px; line-height: 16px; margin: 0;">
                              Place of Service: Kerala<br/>
                              State code: 32<br/>
                              HSN - 997114 - Financial and Related Services
                            </p>
                          </td>
                        </tr>
                      </table>
                    </div>

                  </td>
                </tr>
              </table>
            </td>
            </tr>
            </table>
        </script>
<script id="fees-breakdown-template" type="x-tmpl-mustache">
            <div class='ccr--v1' >
                {{#hasFeeBreakDownComponents}}
					<div class='bor-grey mb-20' ></div>
					<div>
						<div class='title'>{{FeeBreakDown.Title }}</div>
						<div class='bg--light-grey fees-section'>
							<div>
								{{#FeeBreakDown.FeeBreakDownComponents}}
								<div class='fl al-ce ju-sb'>
									<div class='h1--v3'>{{ FeeType }}</div>
									<div class='fl al-ce'>
										{{#isFeeAmountTypePlus}}
										<img
											src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/plus.svg"
											alt="plus minus icon"
											class="plus-minus-icon"
											width="20px"
											height="20px"
										/>
										{{/isFeeAmountTypePlus}}
										{{#isFeeAmountTypeMinus}}
										<img
											src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/assets/svgs/minus.svg"
											alt="plus minus icon"
											width="20px"
											height="20px"
										/>
										{{/isFeeAmountTypeMinus}}
										<div class='h1--v3'>₹{{ Amount.units }}</div>
									</div>
								</div>
								{{/FeeBreakDown.FeeBreakDownComponents}}
							</div>
						</div>
					</div>
				{{/hasFeeBreakDownComponents}}
			</div>
        </script>
<script id="debug-page-template" type="x-tmpl-mustache">
            {{#messages}}
                <div>{{value}}</div>
            {{/messages}}
        </script>

<script src="https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/mustache.js"></script>
<script>
	/**
	 * Returns the month string label
	 * @example
	 * getMonthStr(1)
	 * returns "January"
	 *
	 * @param {number} month number with 1 to 12
	 * @returns {string} Returns the month string label
	 */
	function getMonthStr(month, shortStr) {
		var monthList = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
		var shortStrMonthList = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

		if (shortStr) {
			return shortStrMonthList[month - 1];
		}

		return monthList[month - 1];
	}

	/**
	 * Returns the formatted amount
	 * @example
	 * getFormattedAmount({ units: 1000000, Nanos: 250000000 })
	 * returns { units: 10,00,000, decimals: 25 }
	 *
	 * @param {object} amount object with units and units
	 * @returns {object} Returns the formatted amount
	 */
	/*
	function getFormattedAmount(amount) {
		if (!amount) return {};

		var nanos = amount.units;
		nanos = (nanos / 1e9).toFixed(2).slice(-2);

		return {
			units: amount.units.toLocaleString("en-IN"),
			decimals: nanos
		};
	}
	*/
	function getFormattedAmount(amount) {
		if (!amount) return {};
		var nanos = "00";

		if (amount.nanos) {
			nanos = amount.nanos;
			nanos = (nanos / 1e9).toFixed(2).slice(-2);
		}

		var modifiedUnits = "0";

		if (amount.units) {
			modifiedUnits = amount.units.toString();
			modifiedUnits = modifiedUnits.replace(/\B(?=(\d{2})*(\d{3})(?!\d))/g, ",");
		}

		return {
			units: modifiedUnits,
			decimals: nanos
		};
	}

	/**
	 * Returns the hour 12 str - am/pm
	 * @example
	 * getFormattedHour12(1605917420)
	 * returns "am"
	 *
	 * @param {number} ts seconds
	 * @returns {string} Returns the hour 12 str - am/pm
	 */
	/*
	function getFormattedHour12(ts) {
		var d = new Date(ts * 1000);
		var hour12 = d.toLocaleTimeString([], { timeZone: "IST", hour12: true }).slice(-2);

		return hour12;
	}
	*/
	function getFormattedHour12(ts) {
		var d = new Date(ts * 1000);
		var hours = d.getHours();

		if (hours >= 12) {
			return "pm";
		}

		return "am";
	}

	/**
	 * Returns the 2-digit hours
	 * @example
	 * getFormattedHours(1605917420)
	 * returns "05"
	 *
	 * @param {number} ts seconds
	 * @returns {string} Returns the 2-digit hours
	 */
	/*
	function getFormattedHours(ts) {
		var d = new Date(ts * 1000);
		var hours = d.toLocaleTimeString([], { timeZone: "IST", hour12: true, hour: "2-digit" }).slice(0, 2);

		return hours;
	}
	*/

	function getFormattedHours(ts) {
		var d = new Date(ts * 1000);
		var hours = d.getHours();

		if (hours >= 12) {
			hours = hours - 12;
		}

		return addPrefixZero(hours);
	}

	// add zero for numbers less than 10
	function addPrefixZero(str) {
		var modifiedStr = ("0" + str).slice(-2);

		return modifiedStr;
	}

	/**
	 * Returns the formatted date & month str
	 * @example
	 * getFormattedDateAndMonth(1605917420)
	 * returns "21 Nov"
	 *
	 * @param {number} ts seconds
	 * @returns {string} Returns the formatted date & month str
	 */
	function getFormattedDateAndMonth(ts) {
		var d = new Date(ts * 1000);

		var date = addPrefixZero(d.getDate());
		var monthList = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

		var month = monthList[d.getMonth()];
		var dateAndMonth = date + " " + month;

		return dateAndMonth;
	}

	function getIconObj(ts, hasDayIcon, hasNightIcon) {
		var hours = getFormattedHours(ts);
		var ampm = getFormattedHour12(ts);

		if (!hasDayIcon && !hasNightIcon) {
			if ((hours >= "05" && ampm === "am") || (hours < "06" && ampm === "pm")) {
				return {
					dayIcon: true,
					nightIcon: false,
				};
			} else {
				return {
					dayIcon: false,
					nightIcon: true,
				};
			}
		} else if (!hasDayIcon) {
			if ((hours >= "05" && ampm === "am") || (hours < "06" && ampm === "pm")) {
				return {
					dayIcon: true,
					nightIcon: false,
				};
			}
		} else if (!hasNightIcon) {
			if ((hours >= "06" && ampm === "pm")) {
				return {
					dayIcon: false,
					nightIcon: true,
				};
			}
		}

		return {
			dayIcon: false,
			nightIcon: false,
		};
	}

	function sortTxnData(txnData) {
		var txnDataKeys = Object.keys(txnData);

		var sortedKeys = txnDataKeys.sort(function (a, b) {
			// Turn your strings into dates, and then subtract them
			// to get a value that is either negative, positive, or zero.
			return new Date(txnData[b].timeInMs) - new Date(txnData[a].timeInMs);
		});
		console.log(sortedKeys);

		var sortedTxnData = [];

		sortedKeys.forEach(function (item) {
			sortedTxnData.push({
				name: item,
				data: txnData[item].data
			})
		})

		return sortedTxnData;
	}

	function isEmptyObject(obj) {
		// not using check Object.keys(obj).length === 0; as that's valid only for ECMA 5+
		return JSON.stringify(obj) === '{}';
	}
</script>
<script>
	var init = (function () {
		// required if we send data as stringified JSON
		// var statement = '${data}';

		//  both txn & txn summary json
		// var statement =JSON.stringify({
		// "TransactionModelVersion": "V1",
		// 	"UserDetails":
		// 		{
		// 			"Name": "Jolly Joseph",
		// 			"PhoneNumber": **********,
		// 			"Email": "<EMAIL>",
		// 			"Address": "421, Fi Apartments, Bangalore, Karnataka, INDIA - 686502",
		// 			"MaskedCreditCardNumber": "•••• •••• •••• 4390"
		// 		},
		// 	"FiBrandLogo": "https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/svgs/logo_v2.svg",
		// 	"PartnerBankLogo": "https://epifi-icons.s3.ap-south-1.amazonaws.com/statement/svgs/federal-logo_v2.svg",
		// 	"CreditCardProgramLogo": "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/statement_amplifi_logo.svg",
		// 	"FromDate":
		// 		{
		// 			"year": 2023,
		// 			"month": 3,
		// 			"day": 2
		// 		},
		// 	"ToDate":
		// 		{
		// 			"year": 2023,
		// 			"month": 3,
		// 			"day": 29
		// 		},
		// 	"Summary":
		// 		{
		// 			"StatementDate":
		// 				{
		// 					"year": 2023,
		// 					"month": 3,
		// 					"day": 30
		// 				},
		// 			"PaymentDueDate":
		// 				{
		// 					"year": 2023,
		// 					"month": 4,
		// 					"day": 16
		// 				},
		// 			"AvailableLimit":
		// 				{
		// 					"currency_code": "INR",
		// 					"units": 216178,
		// 					"nanos": *********
		// 				},
		// 			"TotalAmountDue":
		// 				{
		// 					"currency_code": "INR",
		// 					"units": 32046,
		// 					"nanos": *********
		// 				},
		// 			"MinAmountDue":
		// 				{
		// 					"currency_code": "INR",
		// 					"units": 1602,
		// 					"nanos": 320000000
		// 				},
		// 			"OpeningBalance":
		// 				{
		// 					"currency_code": "INR"
		// 				},
		// 			"Spends":
		// 				{
		// 					"currency_code": "INR",
		// 					"units": 40775
		// 				},
		// 			"Interest": null,
		// 			"Fees":
		// 				{
		// 					"currency_code": "INR"
		// 				},
		// 			"RepaymentsAndRefunds":
		// 				{
		// 					"currency_code": "INR",
		// 					"units": 695312222,
		// 					"nanos": *********
		// 				},
		// 			"SpendsConvertedToEmi":
		// 				{
		// 					"currency_code": "INR"
		// 				}
		// 		},
		// 	"TransactionDetails":
		// 		[
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 250
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1679984912
		// 					},
		// 				"MerchantName": "ABCRESTAURANTCOIMBATORE               IN",
		// 				"MerchantLocation": "IN",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Debit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 100,
		// 				"RewardPoints": 25
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 15000
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1679995217
		// 					},
		// 				"MerchantName": "555ELECTRONICSTRIVANDRAM              IN",
		// 				"MerchantLocation": "IN",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Debit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 100,
		// 				"RewardPoints": 25
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 250
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680068096
		// 					},
		// 				"MerchantName": "",
		// 				"MerchantLocation": "",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 1255
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680083723
		// 					},
		// 				"MerchantName": "555ELECTRONICSTRIVANDRAM              IN",
		// 				"MerchantLocation": "IN",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Debit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 100,
		// 				"RewardPoints": 25
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 2000
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680083815
		// 					},
		// 				"MerchantName": "555ELECTRONICSTRIVANDRAM              IN",
		// 				"MerchantLocation": "IN",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Debit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 100,
		// 				"RewardPoints": 25
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 22000
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680083956
		// 					},
		// 				"MerchantName": "555ELECTRONICSTRIVANDRAM              IN",
		// 				"MerchantLocation": "IN",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Debit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 100,
		// 				"RewardPoints": 25
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 270
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680084007
		// 					},
		// 				"MerchantName": "ABCRESTAURANTCOIMBATORE               IN",
		// 				"MerchantLocation": "IN",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Debit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 100,
		// 				"RewardPoints": 25
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 1000,
		// 						"nanos": 280000000
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680093903
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "nt",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 2000,
		// 						"nanos": 830000000
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680094007
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "nt",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 2000,
		// 						"nanos": 830000000
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680096809
		// 					},
		// 				"MerchantName": "Repayment",
		// 				"MerchantLocation": "nt",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 200,
		// 						"nanos": 830000000
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680096820
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "nt",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 1700,
		// 						"nanos": 830000000
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680096830
		// 					},
		// 				"MerchantName": "Repayment",
		// 				"MerchantLocation": "nt",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 270
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680102981
		// 					},
		// 				"MerchantName": "",
		// 				"MerchantLocation": "",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 1255
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680103081
		// 					},
		// 				"MerchantName": "",
		// 				"MerchantLocation": "",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},{
		// 			"Amount":
		// 				{
		// 					"currency_code": "INR",
		// 					"units": 50
		// 				},
		// 			"Date":
		// 				{
		// 					"seconds": 1680106034
		// 				},
		// 			"MerchantName": "Others",
		// 			"MerchantLocation": "NK",
		// 			"TransactionOrigin": "",
		// 			"Category": "",
		// 			"TransactionType": "Credit",
		// 			"PaymentMethod": "Mobile",
		// 			"RewardCoins": 0,
		// 			"RewardPoints": 0
		// 		},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			},{
		// 			"Amount":
		// 				{
		// 					"currency_code": "INR",
		// 					"units": 50
		// 				},
		// 			"Date":
		// 				{
		// 					"seconds": 1680106034
		// 				},
		// 			"MerchantName": "Others",
		// 			"MerchantLocation": "NK",
		// 			"TransactionOrigin": "",
		// 			"Category": "",
		// 			"TransactionType": "Credit",
		// 			"PaymentMethod": "Mobile",
		// 			"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 		},
		// 			{
		// 				"Amount":
		// 					{
		// 						"currency_code": "INR",
		// 						"units": 50
		// 					},
		// 				"Date":
		// 					{
		// 						"seconds": 1680106034
		// 					},
		// 				"MerchantName": "Others",
		// 				"MerchantLocation": "NK",
		// 				"TransactionOrigin": "",
		// 				"Category": "",
		// 				"TransactionType": "Credit",
		// 				"PaymentMethod": "Mobile",
		// 				"RewardCoins": 0,
		// 				"RewardPoints": 0
		// 			}
		// 		],
		// 	"RewardsSummary":
		// 		{
		// 			"Title": "2x rewards",
		// 			"MerchantWiseRewardInfo":
		// 				[
		//
		// 				],
		// 			"BottomText": "We don't endorse these brands & vice versa. These brand logos are for representation & information purposes only.",
		// 			"RewardCoinsSummary":
		// 				{
		// 					"Title": "FI-COINS EARNED",
		// 					"TotalRewardCoinsEarned": 600,
		// 					"TotalFiCoinsText": "xyz",
		// 					"TotalFiCoins": 100
		// 				}
		// 		},
		// 	"ExtraRewardsInfo":
		// 		[
		// 			{
		// 				"Text": "Extra Rewards",
		// 				"RewardCoinsEarned": 12000,
		// 				"RewardTypeLogo": "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/2x_statement_extra_rewards.png"
		// 			},
		// 			{
		// 				"Text": "Extra Rewards",
		// 				"RewardCoinsEarned": 36000,
		// 				"RewardTypeLogo": "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/5x_statement_extra_rewards.png"
		// 			}
		// 		],
		// 	"TipsAndInformationSection":
		// 		{
		// 			"IconUrl": "https://epifi-icons.s3.ap-south-1.amazonaws.com/credit_card_images/Bulb_4x.png",
		// 			"Title": "Pro tip",
		// 			"InformationText":
		// 				[
		// 					{
		// 						"Title": "Want to improve your credit score? Pay your bills before the due date.To reduce late payment charges, pay more than the minimum due.",
		// 						"TextWithLinkAndValues": null
		// 					},
		// 					{
		// 						"Title": "Paying only the minimum monthly due would stretch repayment over many months/years. And each time, your interest payments get compounded & pile on your outstanding balance. See a sample calculation for interest applied when only minimum amount due is paid, in the MITC.",
		// 						"TextWithLinkAndValues":
		// 							[
		// 								{
		// 									"Title": "MITC",
		// 									"Value": "MITC",
		// 									"Link": "https://fi.money/credit-card/important-T&Cs",
		// 									"Type": ""
		// 								}
		// 							]
		// 					}
		// 				]
		// 		},
		// 	"FeeBreakDown":
		// 		{
		// 			"Title": "FEE BREAKDOWN",
		// 			"FeeBreakDownComponents":
		// 				[
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},{
		// 					"FeeType": "Interest charges",
		// 					"Amount": { "currency_code": "INR", "units": 100 },
		// 					"FeeAmountType": "PLUS"
		// 				},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					},
		// 					{
		// 						"FeeType": "Cash withdrawal fees ",
		// 						"Amount": { "currency_code": "INR", "units": 500 },
		// 						"FeeAmountType": "MINUS"
		// 					},
		// 					{
		// 						"FeeType": "Interest charges",
		// 						"Amount": { "currency_code": "INR", "units": 100 },
		// 						"FeeAmountType": "PLUS"
		// 					}
		// 				]
		// 		},
		// 	"ContactUsDetails":
		// 		{
		// 			"Title": "IMPORTANT INFORMATION",
		// 			"CustomerCareDetails":
		// 				[
		// 					{
		// 						"Title": "In case your credit card is lost or stolen, freeze or block your card immediately from the Fi app or contact Fi's customer care.",
		// 						"CustomerCareOrganisation": "Fi app",
		// 						"Email": "",
		// 						"Phone": "",
		// 						"Recommended": false,
		// 						"WebText": "",
		// 						"WebLinkText": "",
		// 						"WebLink": "",
		// 						"TextWithLinkAndValues": null
		// 					},
		// 					{
		// 						"Title": "For further assistance on this or any other matters related to your credit card, reach out to Fi's Customer Care:",
		// 						"CustomerCareOrganisation": "Fi's Customer Care:",
		// 						"Email": "",
		// 						"Phone": "",
		// 						"Recommended": false,
		// 						"WebText": "",
		// 						"WebLinkText": "",
		// 						"WebLink": "",
		// 						"TextWithLinkAndValues":
		// 							[
		// 								{
		// 									"Title": "Email",
		// 									"Value": "<EMAIL>",
		// 									"Link": "www.google.com",
		// 									"Type": "mailto"
		// 								},
		// 								{
		// 									"Title": "Phone",
		// 									"Value": "***********",
		// 									"Link": "",
		// 									"Type": "tel"
		// 								}
		// 							]
		// 					},
		// 					{
		// 						"Title": "For credit card grievances that need to be addressed by Federal Bank's Principal Nodal officer for Grievance Redressal: ",
		// 						"CustomerCareOrganisation": "Federal Bank's Principal Nodal officer",
		// 						"Email": "<EMAIL>",
		// 						"Phone": "",
		// 						"Recommended": false,
		// 						"WebText": "",
		// 						"WebLinkText": "",
		// 						"WebLink": "",
		// 						"TextWithLinkAndValues":
		// 							[
		// 								{
		// 									"Title": "Name",
		// 									"Value": "Minimole Liz Thomas, Head – Service Quality Department",
		// 									"Link": "",
		// 									"Type": ""
		// 								},
		// 								{
		// 									"Title": "Email",
		// 									"Value": "<EMAIL>",
		// 									"Link": "",
		// 									"Type": ""
		// 								}
		// 							]
		// 					}
		// 				],
		// 			"PostalAddress": "Postal address: CEO's Secretariat, Federal Bank Limited, Federal Towers, Bank Junction, Aluva, Kerala, 6831010.",
		// 			"TnCSection":
		// 				{
		// 					"Text": "To know more, read Federal Bank's Most Important Terms & Conditions, Key Fact Statement and Terms & Conditions.",
		// 					"TextWithLinkAndValues":
		// 						[
		// 							{
		// 								"Title": "Most Important Terms & Conditions",
		// 								"Value": "Most Important Terms & Conditions",
		// 								"Link": "https://fi.money/credit-card/T&Cs",
		// 								"Type": ""
		// 							},
		// 							{
		// 								"Title": "Key Fact Statement",
		// 								"Value": "Key Fact Statement",
		// 								"Link": "https://fi.money/credit-card/key-fact-statement",
		// 								"Type": ""
		// 							},
		// 							{
		// 								"Title": "Terms & Conditions.",
		// 								"Value": "Terms & Conditions.",
		// 								"Link": "https://fi.money/credit-card/T&Cs",
		// 								"Type": ""
		// 							}
		// 						]
		// 				},
		// 			"IssuerDetails":
		// 				{
		// 					"Title": "ISSUER DETAILS",
		// 					"LeftTextWithLinkAndValues":
		// 						[
		// 							{
		// 								"Title": "The federal Bank Limited",
		// 								"Value": "",
		// 								"Link": ""
		// 							},
		// 							{
		// 								"Title": "GSTN",
		// 								"Value": "32AABCT0020H3Z3",
		// 								"Link": ""
		// 							},
		// 							{
		// 								"Title": "Parackal Towers, Thottakkatukara, Aluva,\n\t\tErnakulam, Kerala - 683101",
		// 								"Value": "",
		// 								"Link": ""
		// 							}
		// 						],
		// 					"RightTextWithLinkAndValues":
		// 						[
		// 							{
		// 								"Title": "Place Of Service:",
		// 								"Value": "Kerala",
		// 								"Link": ""
		// 							},
		// 							{
		// 								"Title": "State Code:",
		// 								"Value": "32",
		// 								"Link": ""
		// 							},
		// 							{
		// 								"Title": "HSN",
		// 								"Value": "- 997114 - Financial and Related Services",
		// 								"Link": ""
		// 							}
		// 						]
		// 				}
		// 		},
		// 	"EmiSummary":
		// 		{
		// 			"NoOfActiveEmi": 10,
		// 			"DueAmount": { "currency_code": "INR", "units": 100, "nanos": 1000000000 },
		// 			"EmiDetails":
		// 				[
		// 					{
		// 						"MerchantName": "testing",
		// 						"InstallmentNumber":12,
		// 						"TotalInstallment":21,
		// 						"Amount":{ "currency_code": "INR", "units": 100, "nanos": 1000000000 }
		// 					},
		// 					{
		// 						"MerchantName": "testing",
		// 						"InstallmentNumber":12,
		// 						"TotalInstallment":21,
		// 						"Amount":{ "currency_code": "INR", "units": 100, "nanos": 1000000000 }
		// 					},
		// 					{
		// 						"MerchantName": "testing",
		// 						"InstallmentNumber":12,
		// 						"TotalInstallment":21,
		// 						"Amount":{ "currency_code": "INR", "units": 100, "nanos": 1000000000 }
		// 					},
		// 					{
		// 						"MerchantName": "testing",
		// 						"InstallmentNumber":12,
		// 						"TotalInstallment":21,
		// 						"Amount":{ "currency_code": "INR", "units": 100, "nanos": 1000000000 }
		// 					}
		// 				]
		// 		}
		// })
		//
		// var json = JSON.parse(statement);
		// Note: ${data} is a template variable that gets replaced at runtime
		var json = ${data};

		if (isEmptyObject(json)) {
			// print undefined as BE looks for this log and returns error to caller
			console.log(undefined);
			return;
		}

					// Use TransactionModelVersion to determine how to render TransactionDetails
			var transactions = json.TransactionDetails;
			var modelVersion = json.TransactionModelVersion || "V0"; // Default to V0 if not specified
			var useV2 = modelVersion === "V2"; // Flag to check if we're using V2
			var useV1 = modelVersion === "V1"; // Flag to check if we're using V1
			var useV0 = modelVersion === "V0"; // Flag to check if we're using V0 (legacy)

		var fromDateMonth = getMonthStr(json.FromDate.month);
		var fromDateMonthShortStr = getMonthStr(json.FromDate.month, true);
		var toDateMonth = getMonthStr(json.ToDate.month);
		var toDateMonthShortStr = getMonthStr(json.ToDate.month, true);

		json.FromDate.month = fromDateMonth;
		json.ToDate.month = toDateMonth;

		json.FromDate.monthShortStr = fromDateMonthShortStr;
		json.ToDate.monthShortStr = toDateMonthShortStr;

		var txnSummaryData = {};
		var rewardsSummaryData = {};
		var txnSummaryJSON = json.Summary;
		var rewardsSummaryJSON = json.RewardsSummary;
		var tipsAndInformationData = json.TipsAndInformationSection;
		var extraRewardsInfo = json.ExtraRewardsInfo;
		var feeBreakDown = json.FeeBreakDown;
		var customerCareDetails = json.ContactUsDetails.CustomerCareDetails;
		var hasTxnSummaryData = true;
		var hasRewardsSummaryData = true;
		var showProTipInReward = false;
		var ContactUsDetails = json.ContactUsDetails;
		var tncSectionDetails = json.ContactUsDetails.TnCSection;
		var hasFeeBreakDownComponents = feeBreakDown.FeeBreakDownComponents.length > 0 || false
		var EmiSummary = json.EmiSummary;

		extraRewardsInfo.forEach(function (item) {
			item.RewardCoinsEarned = getFormattedAmount({ "units": item.RewardCoinsEarned, "nanos": 0 })
		})


		if (Object.keys(txnSummaryJSON).length) {
			var statementDate = txnSummaryJSON.StatementDate;
			var paymentDueDate = txnSummaryJSON.PaymentDueDate;
			var availableLimit = txnSummaryJSON.AvailableLimit;
			var openingBalance = txnSummaryJSON.OpeningBalance;
			var spends = txnSummaryJSON.Spends;
			var interest = txnSummaryJSON.Interest;
			var fees = txnSummaryJSON.Fees;
			var repaymentsAndRefunds = txnSummaryJSON.RepaymentsAndRefunds;
			var spendsConvertedToEmi = txnSummaryJSON.SpendsConvertedToEmi;
			var totalAmountDue = txnSummaryJSON.TotalAmountDue;
			var minAmountDue = txnSummaryJSON.MinAmountDue;

			if (statementDate) {
				var statementMonth = getMonthStr(statementDate.month);
				txnSummaryData.StatementDate = statementDate.day + ' ' + statementMonth + ' ' + statementDate.year
			}

			if (paymentDueDate) {
				var paymentDueMonth = getMonthStr(paymentDueDate.month);
				txnSummaryData.PaymentDueDate = paymentDueDate.day + ' ' + paymentDueMonth + ' ' + paymentDueDate.year
			}

			if (availableLimit) {
				txnSummaryData.AvailableLimit = {
					Amount: getFormattedAmount(availableLimit)
				};
			}

			if (openingBalance) {
				txnSummaryData.OpeningBalance = {
					Amount: getFormattedAmount(openingBalance)
				};
			}

			if (spends) {
				txnSummaryData.Spends = {
					Amount: getFormattedAmount(spends)
				};
			}

			if (interest) {
				txnSummaryData.Interest = {
					Amount: getFormattedAmount(interest)
				};
			}

			if (fees) {
				txnSummaryData.Fees = {
					Amount: getFormattedAmount(fees)
				};
			}

			if (repaymentsAndRefunds) {
				txnSummaryData.RepaymentsAndRefunds = {
					Amount: getFormattedAmount(repaymentsAndRefunds)
				};
			}

			if (spendsConvertedToEmi) {
				txnSummaryData.SpendsConvertedToEmi = {
					Amount: getFormattedAmount(spendsConvertedToEmi)
				};
			}

			if (totalAmountDue) {
				txnSummaryData.TotalAmountDue = {
					Amount: getFormattedAmount(totalAmountDue)
				};
			}

			if (minAmountDue) {
				txnSummaryData.MinAmountDue = {
					Amount: getFormattedAmount(minAmountDue)
				};
			}
		} else {
			hasTxnSummaryData = false;
		}

		rewardsSummaryData.title = rewardsSummaryJSON.Title;
		rewardsSummaryData.bottomText = rewardsSummaryJSON.BottomText;
		rewardsSummaryData.rewardCoinsSummary = rewardsSummaryJSON.RewardCoinsSummary
		rewardsSummaryData.rewardCoinsSummary.TotalRewardCoinsEarned = getFormattedAmount({ "units": rewardsSummaryData.rewardCoinsSummary.TotalRewardCoinsEarned })

		if (rewardsSummaryJSON.MerchantWiseRewardInfo.length > 0) {

			var merchantWiseRewardInfo = [];

			rewardsSummaryJSON.MerchantWiseRewardInfo.forEach(function (item) {
				var modifiedData = {};

				modifiedData.title = item.MerchantDisplayName;
				modifiedData.merchantIconUrl = item.MerchantIconUrl;
				modifiedData.totalRewardCoinsEarned = getFormattedAmount({ "units": item.TotalRewardCoinsEarned })
				modifiedData.amount = getFormattedAmount(item.TotalAmountSpent);
				merchantWiseRewardInfo.push(modifiedData);
			})

			rewardsSummaryData.merchantWiseRewardInfo = merchantWiseRewardInfo;
		} else {
			hasRewardsSummaryData = false;
			showProTipInReward = true;
		}

		feeBreakDown.FeeBreakDownComponents.forEach((function (item) {
			if (item.FeeAmountType === 'PLUS') {
				item.isFeeAmountTypePlus = true;
			} else {
				item.isFeeAmountTypeMinus = true;
			}
		}));

		var txnData = {};

		// format txn details according to the rendered html
		transactions && transactions.forEach(function (item) {
			var modifiedItem = {};

			modifiedItem.Amount = getFormattedAmount(item.Amount);

						if (useV2) {
				// For V2: handle RewardPoints only
				modifiedItem.RewardPoints = item.RewardPoints || 0;
				modifiedItem.hasRewardPoints = !!item.RewardPoints;
			} else if (useV1) {
				// For V1: handle both RewardCoins and RewardPoints
				if (typeof item.RewardCoins === 'number') {
					// If RewardCoins is a number, format it
					modifiedItem.RewardCoins = getFormattedAmount({ "units": item.RewardCoins });
				} else {
					// If RewardCoins is already an object, use it directly
					modifiedItem.RewardCoins = getFormattedAmount(item.RewardCoins || { "units": 0 });
				}
				modifiedItem.hasRewardCoins = !!parseInt(modifiedItem.RewardCoins.units);

				// Also handle RewardPoints for V1 format
				modifiedItem.RewardPoints = item.RewardPoints || 0;
				modifiedItem.hasRewardPoints = !!item.RewardPoints;
			} else if (useV0) {
				// For V0 (legacy): handle only RewardCoins
				if (typeof item.RewardCoins === 'number') {
					// If RewardCoins is a number, format it
					modifiedItem.RewardCoins = getFormattedAmount({ "units": item.RewardCoins });
				} else {
					// If RewardCoins is already an object, use it directly
					modifiedItem.RewardCoins = getFormattedAmount(item.RewardCoins || { "units": 0 });
				}
				modifiedItem.hasRewardCoins = !!parseInt(modifiedItem.RewardCoins.units);
			}

			modifiedItem.TxnDescription = function () {
				var str = "";

				if (item.MerchantLocation) {
					str += item.MerchantLocation;
				}

				if (item.PaymentMethod) {
					if (item.MerchantLocation) {
						str += " • " + item.PaymentMethod;
					} else {
						str += item.PaymentMethod;
					}
				}

				if (item.Category) {
					if (item.MerchantLocation) {
						str += " • " + item.Category;
					} else if (item.PaymentMethod) {
						str += " • " + item.Category;
					} else {
						str += item.Category;
					}
				}

				return str;
			};

			if (item.TransactionType) {
				var txnType = item.TransactionType.toLowerCase();

				modifiedItem.IsDebit = txnType === "debit";
				modifiedItem.IsCredit = txnType === "credit";
				modifiedItem.IsAutomated = txnType === "automated";
			} else if (item.AmountBadge) {
				var amountBadge = item.AmountBadge.toLowerCase();

				modifiedItem.IsDebit = amountBadge === "debit";
				modifiedItem.IsCredit = amountBadge === "credit";
				modifiedItem.IsAutomated = amountBadge === "automated";
			}

			modifiedItem.MerchantName = item.MerchantName;

			var tsSeconds = item.Date.seconds + 19800; // offset 5.5 * 60 * 60
			var dateAndMonth = getFormattedDateAndMonth(tsSeconds);
			var hasDayIcon = false;
			var hasNightIcon = false;
			var iconObj;

			if (txnData[dateAndMonth]) {
				hasDayIcon = txnData[dateAndMonth].hasDayIcon;
				hasNightIcon = txnData[dateAndMonth].hasNightIcon;
			}

			iconObj = getIconObj(tsSeconds, hasDayIcon, hasNightIcon);

			if (iconObj.dayIcon) {
				hasDayIcon = true;
				modifiedItem.IsDayIcon = true;
			} else if (iconObj.nightIcon) {
				hasNightIcon = true;
				modifiedItem.IsNightIcon = true;
			}

			if (!txnData[dateAndMonth]) {
				txnData[dateAndMonth] = { data: [modifiedItem], hasDayIcon: hasDayIcon, hasNightIcon: hasNightIcon };
			} else {
				txnData[dateAndMonth].data.push(modifiedItem);
				txnData[dateAndMonth].hasDayIcon = hasDayIcon;
				txnData[dateAndMonth].hasNightIcon = hasNightIcon;
			}
			txnData[dateAndMonth].timeInMs = tsSeconds * 1000;
		});

		// debugger;
		json.TxnData = sortTxnData(txnData);

		// prepare the page txn data to make it render on the html
		var pageData = [{ page: 1, data: [{ name: "", data: [] }], hasValidData: true }];
		var currIdx = 0;
		var innerIdx = -1;
		var height = hasTxnSummaryData ? 90 : 684; // total height - 1120, header - 208, footer - 160, txn summary - 662, left content = 684 (1120 - 208 - 160 - 662)
		var currHeight = 0;

		json.TxnData.forEach(function (item) {
			innerIdx += 1;
			currHeight += 40 + 42;

			if (currHeight > height) {
				currIdx += 1;
				height = 812; // total height - 1120, header - 80, footer - 160, content top padding & header - 32 + 36, left content = 812 (1120 - 80 - 160 - 32 - 36)
				innerIdx = 0;
				currHeight = 40 + 42;
			}

			if (!pageData[currIdx]) {
				pageData[currIdx] = { page: currIdx + 1, data: [{ name: "", data: [] }] };
				pageData[currIdx].hasValidData = true;
			}

			pageData[currIdx].data[innerIdx] = {
				name: item.name,
				data: [item.data[0]]
			};

			item.data.forEach(function (idata, sidx) {
				if (sidx !== 0) {
					currHeight += 54;

					if (currHeight > height) {
						currIdx += 1;
						height = 812;
						innerIdx = 0;
						currHeight = 40 + 42;
					}

					if (!pageData[currIdx]) {
						pageData[currIdx] = { page: currIdx + 1, data: [{ name: item.name, data: [] }] };
						pageData[currIdx].hasValidData = true;
					}

					pageData[currIdx].data[innerIdx].data.push(idata);
				}
			})
		});

		///// Logic to start transaction data from page 2nd ////

		// Removing last data from the page 1st transaction
		var firstPageTransaction = pageData[0].data.pop();

		// Adding empty object at the place from where we remove the transaction data
		pageData[0].data.push({ name: '', data: [{}] });

		/**
		 * Checking if date in removeData(firstPageTransaction) transaction is same in other page transaction date
		 */
		if (pageData.length === 1) {
			pageData.push({ page: 2, data: [firstPageTransaction], hasValidData: true });
		} else if (pageData.length > 1) {
			if (firstPageTransaction.name === pageData[1].data[0].name) {
				pageData[1].data[0].data.unshift(firstPageTransaction.data[0]);
			} else {
				pageData[1].data.unshift(firstPageTransaction);

			}
		}
		///// Logic End ////

		// need to create new page if data is greater than 9
		var len = pageData.length;
		var rendered = "";
		var extrapage = "";
		var lastPageData = pageData[pageData.length - 1];

		// if (getTransactionsInPage(lastPageData) > 9) {
		//           extractRemainingTransactionsInPage(pageData, lastPageData, 9);
		//           len = pageData.length;
		// }

		// Create new page for EMI summary if it exists
		var hasEmiSummary = false;
		var emiSummaryDetails =EmiSummary.EmiDetails;

		if (Object.keys(EmiSummary).length) {
			EmiSummary.DueAmount = getFormattedAmount(EmiSummary.DueAmount)
		}

		if (emiSummaryDetails && emiSummaryDetails.length > 0) {
			emiSummaryDetails.forEach((function (item) {
				item.Amount = getFormattedAmount(item.Amount)
			}));
			hasEmiSummary = true;
			var newObj = {};
			len += 1;
			newObj.page = len;
			// even if there is no txn for the current page, do not show the no txn UI
			newObj.showNoTxnUi = false;
			newObj.data = [{ name: '', data: [] }];
			pageData.push(newObj);
		}

		var renderHeader, contentData;

		pageData.forEach(function (item, idx) {
			var headerTemplate;
			var hasTxns = !!item.data[0].data.length;
			var hasValidData = item.hasValidData;
			var hasPageLengthOne = item.page === 1;

			var showNoTxnUi = true;

			if (item.showNoTxnUi !== undefined) showNoTxnUi = item.showNoTxnUi;

			contentData = {
				TxnSummaryData: txnSummaryData,
				HasTxnSummaryData: hasTxnSummaryData && idx === 0 && hasTxns,
				TxnData: item.data,
				HasTxns: hasTxns,
				showNoTxnUi: showNoTxnUi,
				FromDate: json.FromDate,
				ToDate: json.ToDate,
				RenderRewardsData: item.page === 1,
				RewardsSummaryData: rewardsSummaryData,
				hasRewardsSummaryData: hasRewardsSummaryData,
				TipsAndInformationData: tipsAndInformationData,
				ExtraRewardsInfo: extraRewardsInfo,
				FeeBreakDown: feeBreakDown,
				CustomerCareDetails: customerCareDetails,
				pageLength: item.page === pageData.length,
				hasPageLengthOne: hasPageLengthOne,
				ContactUsDetails: ContactUsDetails,
				TncSection: getTncText(tncSectionDetails),
				showProTipInReward: showProTipInReward,
				EmiSummary: EmiSummary,
				hasEmiSummary: hasEmiSummary,
				hasFeeBreakDownComponents: hasFeeBreakDownComponents,
				firstPagefooter: hasPageLengthOne && showProTipInReward,
				useV2: useV2,
				useV1: useV1,
				useV0: useV0
			};

			if (idx === 0) {
				headerTemplate = document.getElementById("primary-header-template").innerHTML;
				contentData.Class = hasTxnSummaryData ? "ccr" : "ccr ccr--v2";
			} else {
				headerTemplate = document.getElementById("secondary-header-template").innerHTML;
				contentData.Class = "ccr ccr--v1";
			}

			var contentTemplate = document.getElementById("content-template").innerHTML;
			var footerTemplate = document.getElementById("footer-template").innerHTML;

			renderHeader = Mustache.render(headerTemplate, {
                UserDetails: json.UserDetails,
				FromDate: json.FromDate,
				ToDate: json.ToDate,
				FiBrandLogo: json.FiBrandLogo,
				PartnerBankLogo: json.PartnerBankLogo,
				CreditCardProgramLogo: json.CreditCardProgramLogo,
			});
			var renderFooter = Mustache.render(footerTemplate, { Page: item.page, Length: pageData.length + 3 });
			var renderContent = Mustache.render(contentTemplate, contentData);

			rendered += renderHeader + renderContent + renderFooter;
		});

		document.getElementById("target").innerHTML = rendered;

		var topElement = document.querySelector('#extra-page .issuer-adress');
		var fcrElement = document.querySelector('#extra-page .fcr');
		var bottomElement = fcrElement && fcrElement.children && fcrElement.children[0];
		var footerTemplate = document.getElementById("footer-template").innerHTML;
		var debugMsgs = [];

		console.log(topElement, bottomElement);

		// debugMsgs.push({
		//     value : JSON.stringify(topElement) + ' class = ' + document.querySelector('#extra-page .issuer-detail').className
		// });
		// debugMsgs.push({
		//     value : JSON.stringify(bottomElement) + ' class = ' + document.querySelector('#extra-page .fcr').children[0].className
		// })
		var bottomsTop = bottomElement ? getTopDistance(bottomElement) : 0;
		var topTop = topElement ? getTopDistance(topElement) : 0;
		var topsBottom = topTop + (topElement ? topElement.offsetHeight : 0);
		debugMsgs.push({ value: JSON.stringify(topElement ? topElement.getBoundingClientRect() : null) });

		debugMsgs.push({
			value : '1st -> bottom' + topsBottom
		})
		debugMsgs.push({
			value : '2nd -> top' + bottomsTop
		})

        var feeBreakDownTemplate = document.getElementById("fees-breakdown-template").innerHTML;
        var feeBreakDownRenderedContent = Mustache.render(feeBreakDownTemplate, contentData)
        var feesPage = renderHeader + feeBreakDownRenderedContent + Mustache.render(footerTemplate, { Page: pageData.length + 1, Length: pageData.length + 3 });

        var secondLastPageTemplate = document.getElementById("second-last-page-template").innerHTML;
        var secondLastPageContent = Mustache.render(secondLastPageTemplate, contentData)
        var secondLastPage = renderHeader + secondLastPageContent + Mustache.render(footerTemplate, { Page: pageData.length + 2, Length: pageData.length + 3, lastPage : true });

        var lastPageTemplate = document.getElementById("last-page-template").innerHTML;
        var lastPageContent = Mustache.render(lastPageTemplate, contentData)
        var lastPage = renderHeader + lastPageContent + Mustache.render(footerTemplate, { Page: pageData.length + 3, Length: pageData.length + 3, lastPage : true });

        extrapage = feesPage + secondLastPage + lastPage;

        document.getElementById("extra-page").innerHTML = extrapage

		// var debugDiv = document.getElementById('debug-page');
		// var debugTemplate = document.getElementById('debug-page-template').innerHTML;
		// debugDiv.innerHTML = renderHeader + Mustache.render(debugTemplate, {messages : debugMsgs}) + Mustache.render(footerTemplate, { Page: pageData.length + 3, Length: pageData.length + 3 });;


	});

	var domC = 'DOMContentLoaded'
	var onLoad = 'load'

	window.addEventListener(domC, init);

	function getTransactionsInPage(currentPageData) {
		var count = 0;
		currentPageData.data.forEach( function(dateItem) {
			count += dateItem.data.length;
		});
		return count;
	}

	function extractRemainingTransactionsInPage(pageData, currentPageData, fromIndex) {
		var count = 0;
		for (var index = 0; index < currentPageData.data.length; index++) {
			var dateItem = currentPageData.data[index];
			var updatedCount = count + dateItem.data.length;
			if (updatedCount >= fromIndex) {
				var slicedData = dateItem.data.splice( -(updatedCount - fromIndex) );
				var newPageData = [{
					name : dateItem.name,
					data : slicedData
				}];
				newPageData = newPageData.concat(currentPageData.data.splice(index + 1));
				pageData.push({
					hasValidData : true,
					page : pageData.length,
					data : newPageData
				});
				break;
			}
			count = updatedCount;
		}
	}

	function getTopDistance (elem) {

		// Set our distance placeholder
		var distance = 0;

		// Loop up the dom
		do {
			// Increase our distance counter
			distance += elem.offsetTop;

			// Set the element to it's parent
			elem = elem.offsetParent;

		} while (elem);
		distance = distance < 0 ? 0 : distance;
		return distance;
	}

	function getTncText(tncSectionDetails) {
		var finalText = tncSectionDetails.Text;
		if (tncSectionDetails.TextWithLinkAndValues.length > 0) {
			tncSectionDetails.TextWithLinkAndValues.forEach (function (textWithLinkAndValue) {
				var linkTemplate = "<a class='link' href='url' >urlText</a>"
				linkTemplate = linkTemplate.replace("url",textWithLinkAndValue.Link)
				linkTemplate = linkTemplate.replace("urlText",textWithLinkAndValue.Value)
				finalText = finalText.replace(textWithLinkAndValue.Title,linkTemplate)
			})
		}
		var tncText = "<div class='l6--v7'>" + finalText + "</div>"
		console.log(tncText);
		return tncText;
	}

</script>
</body>

</html>
