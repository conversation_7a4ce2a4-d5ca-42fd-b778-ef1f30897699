package dao_test

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"

	biometricsPb "github.com/epifi/gamma/api/auth/biometrics"
	"github.com/epifi/gamma/auth/biometrics/dao"
)

var (
	biometricsDao dao.BiometricsDao
	biometrics1   = &biometricsPb.Biometrics{
		ActorId: "actor-1",
		BiometricInfo: &biometricsPb.BiometricInfo{
			BiometricId: "biometric-id-1",
			AppPlatform: commontypes.Platform_IOS,
			DeviceId:    "device-id-1",
			AppVersion:  222,
		},
		Status:    biometricsPb.BiometricStatus_BIOMETRIC_STATUS_REVOKED,
		SubStatus: biometricsPb.SubStatus_SUB_STATUS_UNSPECIFIED,
	}

	biometrics2 = &biometricsPb.Biometrics{
		ActorId: "actor-2",
		BiometricInfo: &biometricsPb.BiometricInfo{
			BiometricId: "biometric-id-2",
			AppPlatform: commontypes.Platform_IOS,
			DeviceId:    "device-id-2",
			AppVersion:  222,
		},
		Status:     biometricsPb.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
		VerifiedAt: timestampPb.New(time.Now()),
		SubStatus:  biometricsPb.SubStatus_SUB_STATUS_UNSPECIFIED,
	}

	biometrics3 = &biometricsPb.Biometrics{
		ActorId: "actor-2",
		BiometricInfo: &biometricsPb.BiometricInfo{
			BiometricId: "biometric-id-3",
			AppPlatform: commontypes.Platform_IOS,
			DeviceId:    "device-id-3",
			AppVersion:  222,
		},
		Status:     biometricsPb.BiometricStatus_BIOMETRIC_STATUS_VERIFIED,
		VerifiedAt: timestampPb.New(time.Now()),
		SubStatus:  biometricsPb.SubStatus_SUB_STATUS_UNSPECIFIED,
	}
)

func TestBiometricsPgdb_Create(t *testing.T) {
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, daoTestSuite.db, daoTestSuite.dbName, affectedTestTables)
	biometricsDao = dao.NewBiometricsPgdb(daoTestSuite.db, idgen.NewDomainIdGenerator(idgen.NewClock()))
	type args struct {
		ctx        context.Context
		biometrics *biometricsPb.Biometrics
	}
	tests := []struct {
		name    string
		args    args
		want    *biometricsPb.Biometrics
		wantErr bool
	}{
		{
			name: "Successful biometrics creation",
			args: args{
				ctx:        context.Background(),
				biometrics: biometrics1,
			},
			want:    biometrics1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := biometricsDao.Create(tt.args.ctx, tt.args.biometrics)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				got.Id = tt.args.biometrics.GetId()
				// timestamp nanos gives precision for 9 digits
				// but pgdb stores ony 6 digits
				// adding this to avoid unnecessary failures
				got.VerifiedAt = tt.want.GetVerifiedAt()
			}

			got.CreatedAt = nil
			got.UpdatedAt = nil
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBiometricsPgdb_GetByActorId(t *testing.T) {
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, daoTestSuite.db, daoTestSuite.dbName, affectedTestTables)
	biometricsDao = dao.NewBiometricsPgdb(daoTestSuite.db, idgen.NewDomainIdGenerator(idgen.NewClock()))
	if err := insertBiometricsPgdb(biometrics2); err != nil {
		t.Errorf("error in creating biometrics: (%v)", err)
	}
	if err := insertBiometricsPgdb(biometrics3); err != nil {
		t.Errorf("error in creating biometrics: (%v)", err)
	}

	type args struct {
		ctx     context.Context
		actorId string
		fields  []biometricsPb.BiometricsFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *biometricsPb.Biometrics
		wantErr bool
		err     error
	}{
		{
			name: "Successful GetByActorId()",
			args: args{
				ctx:     context.Background(),
				actorId: biometrics2.GetActorId(),
			},
			want:    biometrics3,
			wantErr: false,
		},
		{
			name: "Record not found",
			args: args{
				ctx:     context.Background(),
				actorId: "invalid-actor-id",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := biometricsDao.GetByActorId(tt.args.ctx, tt.args.actorId, tt.args.fields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.err)
				}
			}

			if got != nil {
				got.CreatedAt = nil
				got.UpdatedAt = nil
				// timestamp nanos gives precision for 9 digits
				// but pgdb stores ony 6 digits
				// adding this to avoid unnecessary failures
				got.VerifiedAt = tt.want.GetVerifiedAt()
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func insertBiometricsPgdb(models ...*biometricsPb.Biometrics) error {
	for _, m := range models {
		got, err := biometricsDao.Create(context.Background(), m)
		if err != nil {
			return err
		}
		m.Id = got.Id
	}
	return nil
}

func TestBiometricsPgdb_GetById(t *testing.T) {
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, daoTestSuite.db, daoTestSuite.dbName, affectedTestTables)
	biometricsDao = dao.NewBiometricsPgdb(daoTestSuite.db, idgen.NewDomainIdGenerator(idgen.NewClock()))
	if err := insertBiometricsPgdb(biometrics2); err != nil {
		t.Errorf("error in creating biometrics: (%v)", err)
	}

	type args struct {
		ctx     context.Context
		actorId string
		fields  []biometricsPb.BiometricsFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *biometricsPb.Biometrics
		wantErr bool
		err     error
	}{
		{
			name: "Successful GetById()",
			args: args{
				ctx:     context.Background(),
				actorId: biometrics2.GetId(),
			},
			want:    biometrics2,
			wantErr: false,
		},
		{
			name: "Record not found",
			args: args{
				ctx:     context.Background(),
				actorId: "invalid-id",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := biometricsDao.GetById(tt.args.ctx, tt.args.actorId, tt.args.fields)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}

			if got != nil {
				got.CreatedAt = nil
				got.UpdatedAt = nil
				// timestamp nanos gives precision for 9 digits
				// but pgdb stores ony 6 digits
				// adding this to avoid unnecessary failures
				got.VerifiedAt = tt.want.GetVerifiedAt()
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBiometricsPgdb_Update(t *testing.T) {
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, daoTestSuite.db, daoTestSuite.dbName, affectedTestTables)
	biometricsDao = dao.NewBiometricsPgdb(daoTestSuite.db, idgen.NewDomainIdGenerator(idgen.NewClock()))
	if err := insertBiometricsPgdb(biometrics2); err != nil {
		t.Errorf("error in creating biometrics: (%v)", err)
	}
	biometrics1.Id = "id-1"

	type args struct {
		ctx         context.Context
		biometrics  *biometricsPb.Biometrics
		updateMasks []biometricsPb.BiometricsFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		err     error
	}{
		{
			name: "Successful update",
			args: args{
				ctx:        context.Background(),
				biometrics: biometrics2,
				updateMasks: []biometricsPb.BiometricsFieldMask{
					biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_VERIFIED_AT,
				},
			},
			wantErr: false,
			err:     nil,
		},
		{
			name: "empty field masks",
			args: args{
				ctx:        context.Background(),
				biometrics: biometrics2,
			},
			wantErr: true,
		},
		{
			name: "incorrect id",
			args: args{
				ctx:        context.Background(),
				biometrics: biometrics1,
				updateMasks: []biometricsPb.BiometricsFieldMask{
					biometricsPb.BiometricsFieldMask_BIOMETRICS_FIELD_MASK_VERIFIED_AT,
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRowNotUpdated,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := biometricsDao.Update(tt.args.ctx, tt.args.biometrics, tt.args.updateMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Update() error = %v, wantErr %v", err, tt.err)
				}
			}
		})
	}
}

func TestBiometricsPgdb_DeleteById(t *testing.T) {
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, daoTestSuite.db, daoTestSuite.dbName, affectedTestTables)
	biometricsDao = dao.NewBiometricsPgdb(daoTestSuite.db, idgen.NewDomainIdGenerator(idgen.NewClock()))
	if err := insertBiometricsPgdb(biometrics2); err != nil {
		t.Errorf("error in creating biometrics: (%v)", err)
	}

	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "record not found error",
			args: args{
				ctx: context.Background(),
				id:  "actor-not-exists",
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				id:  biometrics2.GetId(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := biometricsDao.DeleteById(tt.args.ctx, tt.args.id)
			t.Logf("DeleteById(%q) returned error: %v (wantErr: %v)", tt.args.id, err, tt.wantErr)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteById() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
