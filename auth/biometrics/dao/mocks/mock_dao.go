// Code generated by MockGen. DO NOT EDIT.
// Source: ./dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	biometrics "github.com/epifi/gamma/api/auth/biometrics"
	gomock "github.com/golang/mock/gomock"
)

// MockBiometricsDao is a mock of BiometricsDao interface.
type MockBiometricsDao struct {
	ctrl     *gomock.Controller
	recorder *MockBiometricsDaoMockRecorder
}

// MockBiometricsDaoMockRecorder is the mock recorder for MockBiometricsDao.
type MockBiometricsDaoMockRecorder struct {
	mock *MockBiometricsDao
}

// NewMockBiometricsDao creates a new mock instance.
func NewMockBiometricsDao(ctrl *gomock.Controller) *MockBiometricsDao {
	mock := &MockBiometricsDao{ctrl: ctrl}
	mock.recorder = &MockBiometricsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBiometricsDao) EXPECT() *MockBiometricsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockBiometricsDao) Create(ctx context.Context, biometric *biometrics.Biometrics) (*biometrics.Biometrics, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, biometric)
	ret0, _ := ret[0].(*biometrics.Biometrics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockBiometricsDaoMockRecorder) Create(ctx, biometric interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockBiometricsDao)(nil).Create), ctx, biometric)
}

// DeleteById mocks base method.
func (m *MockBiometricsDao) DeleteById(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteById", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteById indicates an expected call of DeleteById.
func (mr *MockBiometricsDaoMockRecorder) DeleteById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteById", reflect.TypeOf((*MockBiometricsDao)(nil).DeleteById), ctx, id)
}

// GetByActorId mocks base method.
func (m *MockBiometricsDao) GetByActorId(ctx context.Context, actorId string, fields []biometrics.BiometricsFieldMask) (*biometrics.Biometrics, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId, fields)
	ret0, _ := ret[0].(*biometrics.Biometrics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockBiometricsDaoMockRecorder) GetByActorId(ctx, actorId, fields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockBiometricsDao)(nil).GetByActorId), ctx, actorId, fields)
}

// GetById mocks base method.
func (m *MockBiometricsDao) GetById(ctx context.Context, id string, fields []biometrics.BiometricsFieldMask) (*biometrics.Biometrics, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id, fields)
	ret0, _ := ret[0].(*biometrics.Biometrics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockBiometricsDaoMockRecorder) GetById(ctx, id, fields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockBiometricsDao)(nil).GetById), ctx, id, fields)
}

// Update mocks base method.
func (m *MockBiometricsDao) Update(ctx context.Context, biometrics *biometrics.Biometrics, updateMasks []biometrics.BiometricsFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, biometrics, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockBiometricsDaoMockRecorder) Update(ctx, biometrics, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockBiometricsDao)(nil).Update), ctx, biometrics, updateMasks)
}
