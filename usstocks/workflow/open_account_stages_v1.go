//nolint:dupl,funlen
package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"

	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	usstocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/usstocks"
	usStocksActivityPb "github.com/epifi/gamma/api/usstocks/activity"
	payloadPb "github.com/epifi/gamma/api/usstocks/payload"
	workflow2 "github.com/epifi/gamma/api/usstocks/workflow"
	"github.com/epifi/gamma/usstocks/proxy"
)

func collectPoaAndPoiStageV1(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_COLLECT_POA_AND_POI
	return collectDataAndValidate(ctx, wfReqID, wfProcessingParams, wfReq, stage)
}

func collectDisclosuresV1(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_COLLECT_DISCLOSURES
	return collectDataAndValidate(ctx, wfReqID, wfProcessingParams, wfReq, stage)
}

func collectEmploymentDetailsV1(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_COLLECT_EMPLOYMENT_DETAILS
	return collectDataAndValidate(ctx, wfReqID, wfProcessingParams, wfReq, stage)
}

func collectAgreementsV1(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_COLLECT_AGREEMENTS
	return collectDataAndValidate(ctx, wfReqID, wfProcessingParams, wfReq, stage)
}

func collectInvestmentInterestV1(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_COLLECT_INVESTMENT_INTEREST
	return collectDataAndValidate(ctx, wfReqID, wfProcessingParams, wfReq, stage)
}

func collectRiskDisclosure(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_COLLECT_RISK_DISCLOSURE
	return collectDataAndValidate(ctx, wfReqID, wfProcessingParams, wfReq, stage)
}

func performWaitForDocuments(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_WAIT_FOR_DOCUMENTS_UPLOAD
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))

	var workflowStageStatus stagePb.Status
	// Step 1: initiate workflow stage COMPLIANT_DOCUMENT_UPLOAD
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	resp := &usStocksActivityPb.CheckDocumentsResponse{}
	checkDocumentsFuture, err := activityPkg.ExecuteAsync(ctx, usstocksNs.CheckDocuments, &usStocksActivityPb.CheckDocumentsRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Ownership:   USSAlpacaOwnership,
		},
		ActorId: wfReq.GetActorId(),
	}, resp)
	if err != nil {
		return workflowStageStatus, err
	}

	checkDocumentsFuture.AddFutureHandler(func(checkDocErr error, resp *usStocksActivityPb.CheckDocumentsResponse) {
		if checkDocErr != nil {
			workflowStageStatus = stagePb.Status_FAILED
		} else {
			workflowStageStatus = stagePb.Status_SUCCESSFUL
		}
	})

	for workflowStageStatus == stagePb.Status_STATUS_UNSPECIFIED {

		payload := &payloadPb.ProxySignalRequest{}
		// TODO(Mihir): Take timeout config value as part of workflow request header
		checkDocumentsFuture.AddToSelector(ctx, workflow.NewSelector(ctx)).
			AddReceive(workflow.GetSignalChannel(ctx, string(usstocksNs.UsBrokerAccountOpeningProxyRequestSignal)), func(c workflow.ReceiveChannel, more bool) {
				lg.Info("received signal", zap.String(logger.WORKFLOW_STAGE, stage.String()))
				c.Receive(ctx, &payload)
				lg.Debug("-------payload received:-----", zap.Any("payload", payload))
				var nextAction *deeplink.Deeplink
				nextAction, _, err = getNextAction(ctx, wfReq, wfProcessingParams, payload, stage)
				if err != nil {
					err = proxy.SendResponse(ctx, payload.GetCallingWorkflowId(), &payloadPb.ProxySignalResponse{
						Error: err.Error(),
					})
					if err != nil {
						lg.Error("error sending check docs next action error response to proxy-workflow", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
					}
				} else {
					err = proxy.SendResponse(ctx, payload.GetCallingWorkflowId(), &payloadPb.ProxySignalResponse{
						Deeplink: nextAction,
					})
					if err != nil {
						lg.Error("error sending check docs next action deeplink response to proxy-workflow", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
					}
				}
			}).
			Select(ctx)
	}

	// Step 3: update workflow stage with success
	if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, actErr)
	}

	lg.Info("completing usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.STAGE_STATUS, workflowStageStatus.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	return workflowStageStatus, nil
}

func uploadPanManually(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest, isCheckDocumentsStepComplete bool) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_MANUAL_PAN_UPLOAD

	v := workflow.GetVersion(ctx, "wait_for_sending_notification_async", workflow.DefaultVersion, 1)

	if v == 1 && isCheckDocumentsStepComplete {
		_, _ = sendNotification(ctx, wfProcessingParams.GetClientReqId().GetId(), usstocks.UsStockNotificationType_US_STOCK_NOTIFICATION_TYPE_MANUAL_PAN_REUPLOAD)
	}
	return collectDataAndValidate(ctx, wfReqID, wfProcessingParams, wfReq, stage)
}

func collectDataAndValidate(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest, stage workflowPb.Stage) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	// Step 1: initiate workflow stage
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_BLOCKED, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	// Step 2: wait for signal for successful data collection
	workflowStageStatus := communicateWithProxy(ctx, wfProcessingParams, wfReq, stage)

	// Step 3: update workflow stage with success
	if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, actErr)
	}

	lg.Info("completing usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.STAGE_STATUS, workflowStageStatus.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	return workflowStageStatus, nil
}

func communicateWithProxy(ctx workflow.Context, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest, stage workflowPb.Stage) stagePb.Status {
	lg := workflow.GetLogger(ctx)
	var workflowStageStatus stagePb.Status
	// Loop until we receive a valid size
loop:
	for {
		payload := &payloadPb.ProxySignalRequest{}
		workflow.NewSelector(ctx).
			AddReceive(workflow.GetSignalChannel(ctx, string(usstocksNs.UsBrokerAccountOpeningProxyRequestSignal)), func(c workflow.ReceiveChannel, more bool) {
				lg.Info("received signal", zap.String(logger.WORKFLOW_STAGE, stage.String()))
				c.Receive(ctx, &payload)
				lg.Debug("-------payload received:-----", zap.String("payload", payload.String()))
				workflowStageStatus = stagePb.Status_SUCCESSFUL
			}).
			// TODO(Mihir): Take timeout config value as part of workflow request header
			AddFuture(workflow.NewTimer(ctx, 6*7*24*time.Hour), func(f workflow.Future) {
				workflowStageStatus = stagePb.Status_MANUAL_INTERVENTION
			}).
			Select(ctx)

		lg.Debug("-------payload:-----", zap.Any(logger.PAYLOAD, payload), zap.String(logger.WORKFLOW_STAGE, stage.String()))
		switch {
		case payload.GetRequestType() == payloadPb.ProxySignalRequest_REQUEST_TYPE_NEXT_ACTION:
			nextAction, stepStatus, err := getNextAction(ctx, wfReq, wfProcessingParams, payload, stage)
			if err != nil {
				lg.Error("error getting next action", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
				err = proxy.SendResponse(ctx, payload.GetCallingWorkflowId(), &payloadPb.ProxySignalResponse{
					Error: err.Error(),
				})
				if err != nil {
					lg.Error("error sending next action error response to proxy-workflow", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
				}
				continue loop
			}
			err = proxy.SendResponse(ctx, payload.GetCallingWorkflowId(), &payloadPb.ProxySignalResponse{
				Deeplink: nextAction,
			})
			if err != nil {
				lg.Error("error sending next action deeplink response to proxy-workflow", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
			}
			if nextAction == nil {
				lg.Info("next action deeplink is nil", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
				break loop
			}
			if stepStatus == usStocksActivityPb.OnboardingStepStatus_ONBOARDING_STEP_STATUS_SKIPPED {
				// stage is marked as canceled as workflow need not perform this step due to some external factors
				// for example if user was not 1 year old user will be taken to sof flow
				// if user drops off and come back after user becomes a year old we will not need to perform sof step
				lg.Info("step is skipped", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
				workflowStageStatus = stagePb.Status_CANCELED
				break loop
			}
			continue loop
		case payload.GetRequestType() == payloadPb.ProxySignalRequest_REQUEST_TYPE_COLLECT_DATA:
			resp := &usStocksActivityPb.ValidateAndSaveDataResponse{}
			req := &usStocksActivityPb.ValidateAndSaveDataRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
					Ownership:   USSAlpacaOwnership,
				},
				CurrentStage:       stage,
				ActorId:            wfReq.GetActorId(),
				Disclosures:        payload.GetDisclosures(),
				Agreements:         payload.GetAgreements(),
				InvestmentInterest: payload.GetInvestmentInterest(),
				OnboardingStep:     payload.GetOnboardingStep(),
			}
			var err error
			version := workflow.GetVersion(ctx, "add-local-activity-execution-for-sync-proxy", workflow.DefaultVersion, 1)
			switch version {
			case workflow.DefaultVersion:
				err = activityPkg.Execute(ctx, usstocksNs.ValidateAndSaveData, resp, req)
			default:
				err = activityPkg.ExecuteLocally(ctx, usstocksNs.ValidateAndSaveData, resp, req)
			}
			if err != nil {
				lg.Error("error validating and saving data", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
				err = proxy.SendResponse(ctx, payload.GetCallingWorkflowId(), &payloadPb.ProxySignalResponse{
					Error: err.Error(),
				})
				if err != nil {
					lg.Error("error sending data collection error response to proxy-workflow", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
				}
				continue loop
			}
			err = proxy.SendResponse(ctx, payload.GetCallingWorkflowId(), &payloadPb.ProxySignalResponse{
				Deeplink: resp.GetDeeplink(),
			})
			if err != nil {
				lg.Error("error sending data collection deeplink response to proxy-workflow", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
				continue loop
			}
			if resp.GetDeeplink() != nil {
				lg.Info("deeplink received", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()), zap.String(logger.SCREEN, resp.GetDeeplink().GetScreen().String()))
				continue loop
			}
			workflowStageStatus = stagePb.Status_SUCCESSFUL
			break loop
		case errors.Is(ctx.Err(), workflow.ErrCanceled):
			return 0
		default:
			lg.Error("incorrect request type", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.REQUEST_TYPE, payload.GetRequestType().String()),
				zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
			err := proxy.SendResponse(ctx, payload.GetCallingWorkflowId(), &payloadPb.ProxySignalResponse{
				Error: "incorrect request type",
			})
			if err != nil {
				lg.Error("error sending incorrect request type error response to proxy-workflow", zap.Error(err), zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()))
			}
			continue loop
		}
	}
	return workflowStageStatus
}

func getNextAction(ctx workflow.Context, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest, wfProcessingParams *workflowPb.ProcessingParams, payload *payloadPb.ProxySignalRequest, stage workflowPb.Stage) (*deeplink.Deeplink, usStocksActivityPb.OnboardingStepStatus, error) {
	lg := workflow.GetLogger(ctx)
	platform := payload.GetPlatform()
	appVersion := payload.GetAppVersion()
	if appVersion == 0 {
		appVersion = wfReq.GetAppVersion()
	}
	if platform == commontypes.Platform_PLATFORM_UNSPECIFIED {
		platform = wfReq.GetPlatform()
	}
	resp := &usStocksActivityPb.GetNextOnboardingActionResponse{}
	req := &usStocksActivityPb.GetNextOnboardingActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Ownership:   USSAlpacaOwnership,
		},
		CurrentStage: stage,
		Platform:     platform,
		AppVersion:   appVersion,
		ActorId:      wfReq.GetActorId(),
	}
	var err error
	lg.Info("getting next onboarding action", zap.String(logger.APP_PLATFORM, platform.String()), zap.Uint32(logger.APP_VERSION_CODE, appVersion))
	version := workflow.GetVersion(ctx, "add-local-activity-execution-for-sync-proxy", workflow.DefaultVersion, 1)
	switch version {
	case workflow.DefaultVersion:
		err = activityPkg.Execute(ctx, usstocksNs.GetNextOnboardingAction, resp, req)
	default:
		err = activityPkg.ExecuteLocally(ctx, usstocksNs.GetNextOnboardingAction, resp, req)
	}
	if err != nil {
		lg.Error("error in getting next action", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.ACTOR_ID_V2, wfReq.GetActorId()), zap.Error(err))
	}
	return resp.GetDeeplink(), resp.GetStepStatus(), err
}

// performAccountOpeningStage performs following activities
//  1. CreateAccountWithVendor - initialize account creation with vendor
//  2. UploadKyc - upload kyc details to vendor
//  3. GetAccountStatus - poll account status with vendor
//
// nolint:funlen
func performAccountOpeningStageV1(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_ACCOUNT_OPENING_WITH_VENDOR
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	// Step 1: initiate workflow stage ACCOUNT_OPENING_WITH_VENDOR
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	// Step 2: initiate account creation process with vendor
	resp := &usStocksActivityPb.CreateAccountWithVendorResponse{}
	err := activityPkg.Execute(ctx, usstocksNs.CreateAccountWithVendor, resp, &usStocksActivityPb.CreateAccountWithVendorRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Ownership:   USSAlpacaOwnership,
		},
	})
	workflowStageStatus := stagePb.GetWorkflowStageStatusForErr(err)
	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		workflow.GetLogger(ctx).Error("activity failed", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, wfProcessingParams.GetClientReqId().GetId()))
	}

	// Step 3: update workflow stage
	if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, actErr)
	}

	lg.Info("completing usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.STAGE_STATUS, workflowStageStatus.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	return workflowStageStatus, nil
}

// performAccountOpeningStage performs following activities
//  1. CreateAccountWithVendor - initialize account creation with vendor
//  2. UploadKyc - upload kyc details to vendor
//  3. GetAccountStatus - poll account status with vendor
//
// nolint:funlen
func performKycUploadStage(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_UPLOAD_KYC_DATA
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	// Step 1: initiate workflow stage ACCOUNT_OPENING_WITH_VENDOR
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	resp := &usStocksActivityPb.UploadKycResponse{}
	// Step 2: upload user's kyc data
	err := activityPkg.Execute(ctx, usstocksNs.UploadKyc, resp, &usStocksActivityPb.UploadKycRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Ownership:   USSAlpacaOwnership,
		},
	})
	workflowStageStatus := stagePb.GetWorkflowStageStatusForErr(err)
	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		workflow.GetLogger(ctx).Error("activity failed", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, wfProcessingParams.GetClientReqId().GetId()))
	}

	// Step 3: update workflow stage
	if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, actErr)
	}

	lg.Info("completing usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.STAGE_STATUS, workflowStageStatus.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	return workflowStageStatus, nil
}

// performAccountOpeningStage performs following activities
//  1. CreateAccountWithVendor - initialize account creation with vendor
//  2. UploadKyc - upload kyc details to vendor
//  3. GetAccountStatus - poll account status with vendor
//
// nolint:funlen
func performAccountStatusPollingStage(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_POLL_ACCOUNT_STATUS
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	// Step 1: initiate workflow stage ACCOUNT_OPENING_WITH_VENDOR
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	// Step 2: poll account status from vendor
	resp := &usStocksActivityPb.GetAccountStatusResponse{}
	err := activityPkg.Execute(ctx, usstocksNs.GetAccountStatus, resp, &usStocksActivityPb.GetAccountStatusRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Ownership:   USSAlpacaOwnership,
		},
	})
	workflowStageStatus := stagePb.GetWorkflowStageStatusForErr(err)
	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		workflow.GetLogger(ctx).Error("activity failed", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, wfProcessingParams.GetClientReqId().GetId()))
	}

	// Step 3: update workflow stage
	if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, actErr)
	}

	lg.Info("completing usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.STAGE_STATUS, workflowStageStatus.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	return workflowStageStatus, nil
}

func performPanManualReviewStage(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_MANUAL_PAN_REVIEW
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	// Step 1: initiate workflow stage
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_BLOCKED, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	asyncCtx, cancelFn := workflow.NewDisconnectedContext(ctx)
	defer cancelFn()

	workflow.Go(asyncCtx, func(ctx workflow.Context) {
		_ = communicateWithProxy(ctx, wfProcessingParams, wfReq, stage)
	})

	var workflowStageStatus stagePb.Status
	// Step 2: wait for signal for successful review action
	for workflowStageStatus == stagePb.Status_STATUS_UNSPECIFIED {
		v := workflow.GetVersion(ctx, "manual-pan-review-remove-dependency-of-signal", workflow.DefaultVersion, 1)
		if v == workflow.DefaultVersion {
			reviewActionSignalPayload := &payloadPb.PanManualReviewActionSignalPayload{}
			workflow.NewSelector(ctx).
				AddReceive(workflow.GetSignalChannel(ctx, string(usstocksNs.UsBrokerAccountOpeningPanManualReviewActionSignal)), func(c workflow.ReceiveChannel, more bool) {
					lg.Info("received PanManualReviewActionSignal")
					var b []byte
					c.Receive(ctx, &b)
					if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, reviewActionSignalPayload); err != nil {
						workflowStageStatus = stagePb.Status_FAILED
						lg.Error("error in unmarshalling PanManualReviewActionSignal", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, wfProcessingParams.GetClientReqId().GetId()))
						return
					}
					lg.Debug("-------payload received:-----", zap.Any("payload", string(b)))
					resp := &usStocksActivityPb.GetPanManualReviewStatusResponse{}
					err := activityPkg.Execute(ctx, usstocksNs.GetPanManualReviewStatus, resp, &usStocksActivityPb.GetPanManualReviewStatusRequest{
						RequestHeader: &activityPb.RequestHeader{
							ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
							Ownership:   USSAlpacaOwnership,
						},
					})
					workflowStageStatus = stagePb.GetWorkflowStageStatusForErr(err)
				}).
				Select(ctx)
		} else {
			resp := &usStocksActivityPb.GetPanManualReviewStatusResponse{}
			err := activityPkg.Execute(ctx, usstocksNs.GetPanManualReviewStatus, resp, &usStocksActivityPb.GetPanManualReviewStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
					Ownership:   USSAlpacaOwnership,
				},
			})
			workflowStageStatus = stagePb.GetWorkflowStageStatusForErr(err)
		}
	}
	// Step 3: update workflow stage with success
	if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, actErr)
	}

	lg.Info("completing usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.STAGE_STATUS, workflowStageStatus.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	return workflowStageStatus, nil
}

func collectSourceOfFundsV1(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_COLLECT_SOURCE_OF_FUNDS
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	// Step 1: initiate workflow stage
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_BLOCKED, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStage, err)
	}

	asyncCtx, cancelFn := workflow.NewDisconnectedContext(ctx)
	defer cancelFn()

	workflow.Go(asyncCtx, func(ctx workflow.Context) {
		_ = communicateWithProxy(ctx, wfProcessingParams, wfReq, stage)
	})

	var workflowStageStatus stagePb.Status
	// Step 2: wait for signal for successful collect source of fund
	for workflowStageStatus == stagePb.Status_STATUS_UNSPECIFIED {
		collectSofSignalPayload := &payloadPb.CollectSourceOfFundsSignalPayload{}
		workflow.NewSelector(ctx).
			AddReceive(workflow.GetSignalChannel(ctx, string(usstocksNs.UsBrokerAccountOpeningCollectSourceOfFunds)), func(c workflow.ReceiveChannel, more bool) {
				lg.Info("received CollectSourceOfFunds signal")
				var b []byte
				c.Receive(ctx, &b)
				if err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, collectSofSignalPayload); err != nil {
					workflowStageStatus = stagePb.Status_MANUAL_INTERVENTION
					lg.Error("error in unmarshalling CollectSourceOfFundsSignalPayload", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, wfProcessingParams.GetClientReqId().GetId()))
					return
				}
				lg.Debug("-------payload received:-----", zap.Any("payload", collectSofSignalPayload))
				workflowStageStatus = stagePb.Status_SUCCESSFUL
			}).
			Select(ctx)
	}
	// Step 3: update workflow stage with success
	if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStageStatus, actErr)
	}

	lg.Info("completing usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.STAGE_STATUS, workflowStageStatus.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	return workflowStageStatus, nil
}

func collectSourceOfFundsV2(ctx workflow.Context, wfReqID string, wfProcessingParams *workflowPb.ProcessingParams, wfReq *workflow2.UsBrokerAccountOpeningWorkflowRequest) (stagePb.Status, error) {
	stage := workflowPb.Stage_US_BROKER_ACCOUNT_OPENING_COLLECT_SOURCE_OF_FUNDS
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))

	// Step 1: initiate workflow stage with initiated status
	var workflowStageStatus stagePb.Status
	if err := initiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED, wfProcessingParams.GetClientReqId().GetId(), wfReqID); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	// Step 2: communicate with sync proxy in async
	asyncCtx, cancelFn := workflow.NewDisconnectedContext(ctx)
	defer cancelFn()

	workflow.Go(asyncCtx, func(ctx workflow.Context) {
		_ = communicateWithProxy(ctx, wfProcessingParams, wfReq, stage)
	})

	// Step 3: Execute GetSOFStatusForOnboarding activity in async
	sofStatusResp := &usStocksActivityPb.GetSOFStatusForOnboardingResponse{}
	sofStatusFuture, err := activityPkg.ExecuteAsync(ctx, usstocksNs.GetSOFStatusForOnboarding, &usStocksActivityPb.GetSOFStatusForOnboardingRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
			Ownership:   USSAlpacaOwnership,
		},
		ActorId: wfReq.GetActorId(),
	}, sofStatusResp)
	if err != nil {
		lg.Error("failed to start async GetSOFStatusForOnboarding activity", zap.Error(err))
		workflowStageStatus = celestial.GetWorkflowStageStatusForErr(err)
		if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, actErr)
		}
		return workflowStageStatus, nil
	}
	// Add future handler for activity completion
	sofStatusFuture.AddFutureHandler(func(getErr error, resp *usStocksActivityPb.GetSOFStatusForOnboardingResponse) {
		if getErr != nil {
			lg.Error("GetSOFStatusForOnboarding activity failed", zap.Error(getErr))
			workflowStageStatus = celestial.GetWorkflowStageStatusForErr(getErr)
		} else {
			lg.Info("GetSOFStatusForOnboarding activity completed successfully")
			workflowStageStatus = stagePb.Status_SUCCESSFUL
		}
	})

	// Step 4: Also waiting for sof collection signal along with activity
	signalPayload := &payloadPb.CollectSourceOfFundsSignalPayload{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, usstocksNs.UsBrokerAccountOpeningCollectSourceOfFunds, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, resp *payloadPb.CollectSourceOfFundsSignalPayload) {
		if getErr != nil {
			lg.Error("failed to get UsBrokerAccountOpeningCollectSourceOfFunds signal", zap.Error(getErr))
			workflowStageStatus = celestial.GetWorkflowStageStatusForErr(getErr)
		} else {
			lg.Info("UsBrokerAccountOpeningCollectSourceOfFunds signal received successfully")
			workflowStageStatus = stagePb.Status_SUCCESSFUL
		}
	})
	err = epifitemporal.ReceiveSignalWithFuture(ctx, sofStatusFuture, sigChannel, 30*time.Minute)
	if err != nil {
		lg.Error("failed to receive signal for UsBrokerAccountOpeningCollectSourceOfFunds", zap.Error(err))
		workflowStageStatus = celestial.GetWorkflowStageStatusForErr(err)
	}

	// Step 5: Update workflow stage status
	if actErr := updateWorkflowStage(ctx, stage, workflowStageStatus, wfProcessingParams.GetClientReqId().GetId(), wfReqID); actErr != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, errors.Wrapf(actErr, "error in updating workflow stage %s status %s", stage, workflowStageStatus)
	}

	lg.Info("completing usstocks-onboarding stage", zap.String(logger.WORKFLOW_STAGE, stage.String()), zap.String(logger.STAGE_STATUS, workflowStageStatus.String()), zap.String(logger.WORKFLOW_REQ_ID, wfReqID))
	return workflowStageStatus, nil
}
