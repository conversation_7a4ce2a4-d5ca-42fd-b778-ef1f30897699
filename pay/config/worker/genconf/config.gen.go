// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	v "github.com/epifi/be-common/pkg/cfg/v2"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	genconfig "github.com/epifi/gamma/order/config/genconf"
	server "github.com/epifi/gamma/pay/config/server"
	genserver "github.com/epifi/gamma/pay/config/server/genconf"
	worker "github.com/epifi/gamma/pay/config/worker"
	pay "github.com/epifi/gamma/pkg/pay"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to indicate whether to enable resource provider based db instance provision or whether to
	// go with a static db instance
	_EnableEntitySegregation           uint32
	_IsGSTReportedWithNewInvoiceNumber uint32
	_Application                       *gencfg.TemporalWorkerApplication
	_OffAppUpiApplication              *gencfg.TemporalWorkerApplication
	_BillpayApplication                *gencfg.TemporalWorkerApplication
	_InternationalFundTransfer         *InternationalFundTransfer
	_BillpayParams                     *BillpayParams
	_OrderCacheConfig                  *genconfig.OrderCacheConfig
	_PayOrderCacheConfig               *genserver.PayOrderCacheConfig
	_PayTransactionCacheConfig         *genserver.PayTransactionCacheConfig
	_PgParams                          *genserver.PgParams
	_Server                            *cfg.ServerPorts
	_RazorPayResponseCodesJson         string
	_WorkflowParamsList                v.WorkflowParamsList
	_DefaultActivityParamsList         v.ActivityParamsList
	_PausedWorkflowList                v.PausedWorkflowsList
	_DbConfigMap                       cfg.DbConfigMap
	_UsecaseDbConfigMap                cfg.UseCaseDbConfigMap
	_BillpayPGDB                       *cfg.DB
	_AWS                               *cfg.AWS
	_OrderUpdatePublisher              *cfg.SnsPublisher
	_TxnDetailedStatusUpdatePublisher  *cfg.SnsPublisher
	_WorkflowUpdatePublisher           *cfg.SnsPublisher
	_Tracing                           *cfg.Tracing
	_Profiling                         *cfg.Profiling
	_Secrets                           *cfg.Secrets
	_ExecutionReportGenerationParams   *worker.ExecutionReportGenerationParams
	_FundTransferParams                *worker.FundTransferParams
	_PgProgramToAuthSecretMap          map[string]*server.PgProgramToAuthSecret
	_LockRedisStore                    *cfg.RedisOptions
	_PaymentEnquiryParams              *pay.PaymentEnquiryParams
}

// flag to indicate whether to enable resource provider based db instance provision or whether to
// go with a static db instance
func (obj *Config) EnableEntitySegregation() bool {
	if atomic.LoadUint32(&obj._EnableEntitySegregation) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) IsGSTReportedWithNewInvoiceNumber() bool {
	if atomic.LoadUint32(&obj._IsGSTReportedWithNewInvoiceNumber) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) Application() *gencfg.TemporalWorkerApplication {
	return obj._Application
}
func (obj *Config) OffAppUpiApplication() *gencfg.TemporalWorkerApplication {
	return obj._OffAppUpiApplication
}
func (obj *Config) BillpayApplication() *gencfg.TemporalWorkerApplication {
	return obj._BillpayApplication
}
func (obj *Config) InternationalFundTransfer() *InternationalFundTransfer {
	return obj._InternationalFundTransfer
}
func (obj *Config) BillpayParams() *BillpayParams {
	return obj._BillpayParams
}
func (obj *Config) OrderCacheConfig() *genconfig.OrderCacheConfig {
	return obj._OrderCacheConfig
}
func (obj *Config) PayOrderCacheConfig() *genserver.PayOrderCacheConfig {
	return obj._PayOrderCacheConfig
}
func (obj *Config) PayTransactionCacheConfig() *genserver.PayTransactionCacheConfig {
	return obj._PayTransactionCacheConfig
}
func (obj *Config) PgParams() *genserver.PgParams {
	return obj._PgParams
}
func (obj *Config) Server() *cfg.ServerPorts {
	return obj._Server
}
func (obj *Config) RazorPayResponseCodesJson() string {
	return obj._RazorPayResponseCodesJson
}
func (obj *Config) WorkflowParamsList() v.WorkflowParamsList {
	return obj._WorkflowParamsList
}
func (obj *Config) DefaultActivityParamsList() v.ActivityParamsList {
	return obj._DefaultActivityParamsList
}
func (obj *Config) PausedWorkflowList() v.PausedWorkflowsList {
	return obj._PausedWorkflowList
}
func (obj *Config) DbConfigMap() cfg.DbConfigMap {
	return obj._DbConfigMap
}
func (obj *Config) UsecaseDbConfigMap() cfg.UseCaseDbConfigMap {
	return obj._UsecaseDbConfigMap
}
func (obj *Config) BillpayPGDB() *cfg.DB {
	return obj._BillpayPGDB
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) OrderUpdatePublisher() *cfg.SnsPublisher {
	return obj._OrderUpdatePublisher
}
func (obj *Config) TxnDetailedStatusUpdatePublisher() *cfg.SnsPublisher {
	return obj._TxnDetailedStatusUpdatePublisher
}
func (obj *Config) WorkflowUpdatePublisher() *cfg.SnsPublisher {
	return obj._WorkflowUpdatePublisher
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) ExecutionReportGenerationParams() *worker.ExecutionReportGenerationParams {
	return obj._ExecutionReportGenerationParams
}
func (obj *Config) FundTransferParams() *worker.FundTransferParams {
	return obj._FundTransferParams
}
func (obj *Config) PgProgramToAuthSecretMap() map[string]*server.PgProgramToAuthSecret {
	return obj._PgProgramToAuthSecretMap
}
func (obj *Config) LockRedisStore() *cfg.RedisOptions {
	return obj._LockRedisStore
}
func (obj *Config) PaymentEnquiryParams() *pay.PaymentEnquiryParams {
	return obj._PaymentEnquiryParams
}

type InternationalFundTransfer struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableFederalSherlock uint32
	_SherlockHost          string
	_SherlockHostMutex     *sync.RWMutex
	// This is the domain of the federal sherlock as part of IFT migration
	_FederalSherlockHost            string
	_FederalSherlockHostMutex       *sync.RWMutex
	_DocumentsBucketName            string
	_ForexRateReportSlackChannelId  string
	_ReportTcsChargesFromApi        bool
	_IsSofAnalysisFlowActive        bool
	_IsGstReportingViaApiFlowActive bool
}

func (obj *InternationalFundTransfer) EnableFederalSherlock() bool {
	if atomic.LoadUint32(&obj._EnableFederalSherlock) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *InternationalFundTransfer) SherlockHost() string {
	obj._SherlockHostMutex.RLock()
	defer obj._SherlockHostMutex.RUnlock()
	return obj._SherlockHost
}

// This is the domain of the federal sherlock as part of IFT migration
func (obj *InternationalFundTransfer) FederalSherlockHost() string {
	obj._FederalSherlockHostMutex.RLock()
	defer obj._FederalSherlockHostMutex.RUnlock()
	return obj._FederalSherlockHost
}
func (obj *InternationalFundTransfer) DocumentsBucketName() string {
	return obj._DocumentsBucketName
}
func (obj *InternationalFundTransfer) ForexRateReportSlackChannelId() string {
	return obj._ForexRateReportSlackChannelId
}
func (obj *InternationalFundTransfer) ReportTcsChargesFromApi() bool {
	return obj._ReportTcsChargesFromApi
}
func (obj *InternationalFundTransfer) IsSofAnalysisFlowActive() bool {
	return obj._IsSofAnalysisFlowActive
}
func (obj *InternationalFundTransfer) IsGstReportingViaApiFlowActive() bool {
	return obj._IsGstReportingViaApiFlowActive
}

type BillpayParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RechargePoolAccountActorId      string
	_RechargePoolAccountActorIdMutex *sync.RWMutex
	_RechargePoolAccountPiId         string
	_RechargePoolAccountPiIdMutex    *sync.RWMutex
}

func (obj *BillpayParams) RechargePoolAccountActorId() string {
	obj._RechargePoolAccountActorIdMutex.RLock()
	defer obj._RechargePoolAccountActorIdMutex.RUnlock()
	return obj._RechargePoolAccountActorId
}
func (obj *BillpayParams) RechargePoolAccountPiId() string {
	obj._RechargePoolAccountPiIdMutex.RLock()
	defer obj._RechargePoolAccountPiIdMutex.RUnlock()
	return obj._RechargePoolAccountPiId
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["isgstreportedwithnewinvoicenumber"] = _obj.SetIsGSTReportedWithNewInvoiceNumber
	_Application, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._Application = _Application
	helper.AddFieldSetters("application", _fieldSetters, _setters)
	_OffAppUpiApplication, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._OffAppUpiApplication = _OffAppUpiApplication
	helper.AddFieldSetters("offappupiapplication", _fieldSetters, _setters)
	_BillpayApplication, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._BillpayApplication = _BillpayApplication
	helper.AddFieldSetters("billpayapplication", _fieldSetters, _setters)
	_InternationalFundTransfer, _fieldSetters := NewInternationalFundTransfer()
	_obj._InternationalFundTransfer = _InternationalFundTransfer
	helper.AddFieldSetters("internationalfundtransfer", _fieldSetters, _setters)
	_BillpayParams, _fieldSetters := NewBillpayParams()
	_obj._BillpayParams = _BillpayParams
	helper.AddFieldSetters("billpayparams", _fieldSetters, _setters)
	_OrderCacheConfig, _fieldSetters := genconfig.NewOrderCacheConfig()
	_obj._OrderCacheConfig = _OrderCacheConfig
	helper.AddFieldSetters("ordercacheconfig", _fieldSetters, _setters)
	_PayOrderCacheConfig, _fieldSetters := genserver.NewPayOrderCacheConfig()
	_obj._PayOrderCacheConfig = _PayOrderCacheConfig
	helper.AddFieldSetters("payordercacheconfig", _fieldSetters, _setters)
	_PayTransactionCacheConfig, _fieldSetters := genserver.NewPayTransactionCacheConfig()
	_obj._PayTransactionCacheConfig = _PayTransactionCacheConfig
	helper.AddFieldSetters("paytransactioncacheconfig", _fieldSetters, _setters)
	_PgParams, _fieldSetters := genserver.NewPgParams()
	_obj._PgParams = _PgParams
	helper.AddFieldSetters("pgparams", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["isgstreportedwithnewinvoicenumber"] = _obj.SetIsGSTReportedWithNewInvoiceNumber
	_Application, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._Application = _Application
	helper.AddFieldSetters("application", _fieldSetters, _setters)
	_OffAppUpiApplication, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._OffAppUpiApplication = _OffAppUpiApplication
	helper.AddFieldSetters("offappupiapplication", _fieldSetters, _setters)
	_BillpayApplication, _fieldSetters := gencfg.NewTemporalWorkerApplication()
	_obj._BillpayApplication = _BillpayApplication
	helper.AddFieldSetters("billpayapplication", _fieldSetters, _setters)
	_InternationalFundTransfer, _fieldSetters := NewInternationalFundTransfer()
	_obj._InternationalFundTransfer = _InternationalFundTransfer
	helper.AddFieldSetters("internationalfundtransfer", _fieldSetters, _setters)
	_BillpayParams, _fieldSetters := NewBillpayParams()
	_obj._BillpayParams = _BillpayParams
	helper.AddFieldSetters("billpayparams", _fieldSetters, _setters)
	_OrderCacheConfig, _fieldSetters := genconfig.NewOrderCacheConfig()
	_obj._OrderCacheConfig = _OrderCacheConfig
	helper.AddFieldSetters("ordercacheconfig", _fieldSetters, _setters)
	_PayOrderCacheConfig, _fieldSetters := genserver.NewPayOrderCacheConfig()
	_obj._PayOrderCacheConfig = _PayOrderCacheConfig
	helper.AddFieldSetters("payordercacheconfig", _fieldSetters, _setters)
	_PayTransactionCacheConfig, _fieldSetters := genserver.NewPayTransactionCacheConfig()
	_obj._PayTransactionCacheConfig = _PayTransactionCacheConfig
	helper.AddFieldSetters("paytransactioncacheconfig", _fieldSetters, _setters)
	_PgParams, _fieldSetters := genserver.NewPgParams()
	_obj._PgParams = _PgParams
	helper.AddFieldSetters("pgparams", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*worker.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *worker.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableentitysegregation":
		return obj.SetEnableEntitySegregation(v.EnableEntitySegregation, true, nil)
	case "isgstreportedwithnewinvoicenumber":
		return obj.SetIsGSTReportedWithNewInvoiceNumber(v.IsGSTReportedWithNewInvoiceNumber, true, nil)
	case "application":
		return obj._Application.Set(v.Application, true, path)
	case "offappupiapplication":
		return obj._OffAppUpiApplication.Set(v.OffAppUpiApplication, true, path)
	case "billpayapplication":
		return obj._BillpayApplication.Set(v.BillpayApplication, true, path)
	case "internationalfundtransfer":
		return obj._InternationalFundTransfer.Set(v.InternationalFundTransfer, true, path)
	case "billpayparams":
		return obj._BillpayParams.Set(v.BillpayParams, true, path)
	case "ordercacheconfig":
		return obj._OrderCacheConfig.Set(v.OrderCacheConfig, true, path)
	case "payordercacheconfig":
		return obj._PayOrderCacheConfig.Set(v.PayOrderCacheConfig, true, path)
	case "paytransactioncacheconfig":
		return obj._PayTransactionCacheConfig.Set(v.PayTransactionCacheConfig, true, path)
	case "pgparams":
		return obj._PgParams.Set(v.PgParams, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *worker.Config, dynamic bool, path []string) (err error) {

	err = obj.SetEnableEntitySegregation(v.EnableEntitySegregation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsGSTReportedWithNewInvoiceNumber(v.IsGSTReportedWithNewInvoiceNumber, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Application.Set(v.Application, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OffAppUpiApplication.Set(v.OffAppUpiApplication, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BillpayApplication.Set(v.BillpayApplication, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InternationalFundTransfer.Set(v.InternationalFundTransfer, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BillpayParams.Set(v.BillpayParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderCacheConfig.Set(v.OrderCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PayOrderCacheConfig.Set(v.PayOrderCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PayTransactionCacheConfig.Set(v.PayTransactionCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PgParams.Set(v.PgParams, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *worker.Config) error {

	obj._Server = v.Server
	obj._RazorPayResponseCodesJson = v.RazorPayResponseCodesJson
	obj._WorkflowParamsList = v.WorkflowParamsList
	obj._DefaultActivityParamsList = v.DefaultActivityParamsList
	obj._PausedWorkflowList = v.PausedWorkflowList
	obj._DbConfigMap = v.DbConfigMap
	obj._UsecaseDbConfigMap = v.UsecaseDbConfigMap
	obj._BillpayPGDB = v.BillpayPGDB
	obj._AWS = v.AWS
	obj._OrderUpdatePublisher = v.OrderUpdatePublisher
	obj._TxnDetailedStatusUpdatePublisher = v.TxnDetailedStatusUpdatePublisher
	obj._WorkflowUpdatePublisher = v.WorkflowUpdatePublisher
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._Secrets = v.Secrets
	obj._ExecutionReportGenerationParams = v.ExecutionReportGenerationParams
	obj._FundTransferParams = v.FundTransferParams
	obj._PgProgramToAuthSecretMap = v.PgProgramToAuthSecretMap
	obj._LockRedisStore = v.LockRedisStore
	obj._PaymentEnquiryParams = v.PaymentEnquiryParams
	return nil
}

func (obj *Config) SetEnableEntitySegregation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableEntitySegregation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEntitySegregation")
	}
	return nil
}
func (obj *Config) SetIsGSTReportedWithNewInvoiceNumber(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.IsGSTReportedWithNewInvoiceNumber", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsGSTReportedWithNewInvoiceNumber, 1)
	} else {
		atomic.StoreUint32(&obj._IsGSTReportedWithNewInvoiceNumber, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsGSTReportedWithNewInvoiceNumber")
	}
	return nil
}

func NewInternationalFundTransfer() (_obj *InternationalFundTransfer, _setters map[string]dynconf.SetFunc) {
	_obj = &InternationalFundTransfer{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablefederalsherlock"] = _obj.SetEnableFederalSherlock
	_setters["sherlockhost"] = _obj.SetSherlockHost
	_obj._SherlockHostMutex = &sync.RWMutex{}
	_setters["federalsherlockhost"] = _obj.SetFederalSherlockHost
	_obj._FederalSherlockHostMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *InternationalFundTransfer) Init() {
	newObj, _ := NewInternationalFundTransfer()
	*obj = *newObj
}

func (obj *InternationalFundTransfer) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InternationalFundTransfer) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*worker.InternationalFundTransfer)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InternationalFundTransfer) setDynamicField(v *worker.InternationalFundTransfer, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablefederalsherlock":
		return obj.SetEnableFederalSherlock(v.EnableFederalSherlock, true, nil)
	case "sherlockhost":
		return obj.SetSherlockHost(v.SherlockHost, true, nil)
	case "federalsherlockhost":
		return obj.SetFederalSherlockHost(v.FederalSherlockHost, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InternationalFundTransfer) setDynamicFields(v *worker.InternationalFundTransfer, dynamic bool, path []string) (err error) {

	err = obj.SetEnableFederalSherlock(v.EnableFederalSherlock, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSherlockHost(v.SherlockHost, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFederalSherlockHost(v.FederalSherlockHost, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InternationalFundTransfer) setStaticFields(v *worker.InternationalFundTransfer) error {

	obj._DocumentsBucketName = v.DocumentsBucketName
	obj._ForexRateReportSlackChannelId = v.ForexRateReportSlackChannelId
	obj._ReportTcsChargesFromApi = v.ReportTcsChargesFromApi
	obj._IsSofAnalysisFlowActive = v.IsSofAnalysisFlowActive
	obj._IsGstReportingViaApiFlowActive = v.IsGstReportingViaApiFlowActive
	return nil
}

func (obj *InternationalFundTransfer) SetEnableFederalSherlock(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.EnableFederalSherlock", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableFederalSherlock, 1)
	} else {
		atomic.StoreUint32(&obj._EnableFederalSherlock, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableFederalSherlock")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetSherlockHost(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.SherlockHost", reflect.TypeOf(val))
	}
	obj._SherlockHostMutex.Lock()
	defer obj._SherlockHostMutex.Unlock()
	obj._SherlockHost = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SherlockHost")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetFederalSherlockHost(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.FederalSherlockHost", reflect.TypeOf(val))
	}
	obj._FederalSherlockHostMutex.Lock()
	defer obj._FederalSherlockHostMutex.Unlock()
	obj._FederalSherlockHost = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FederalSherlockHost")
	}
	return nil
}

func NewBillpayParams() (_obj *BillpayParams, _setters map[string]dynconf.SetFunc) {
	_obj = &BillpayParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["rechargepoolaccountactorid"] = _obj.SetRechargePoolAccountActorId
	_obj._RechargePoolAccountActorIdMutex = &sync.RWMutex{}
	_setters["rechargepoolaccountpiid"] = _obj.SetRechargePoolAccountPiId
	_obj._RechargePoolAccountPiIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *BillpayParams) Init() {
	newObj, _ := NewBillpayParams()
	*obj = *newObj
}

func (obj *BillpayParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *BillpayParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*worker.BillpayParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *BillpayParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *BillpayParams) setDynamicField(v *worker.BillpayParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "rechargepoolaccountactorid":
		return obj.SetRechargePoolAccountActorId(v.RechargePoolAccountActorId, true, nil)
	case "rechargepoolaccountpiid":
		return obj.SetRechargePoolAccountPiId(v.RechargePoolAccountPiId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *BillpayParams) setDynamicFields(v *worker.BillpayParams, dynamic bool, path []string) (err error) {

	err = obj.SetRechargePoolAccountActorId(v.RechargePoolAccountActorId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRechargePoolAccountPiId(v.RechargePoolAccountPiId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *BillpayParams) setStaticFields(v *worker.BillpayParams) error {

	return nil
}

func (obj *BillpayParams) SetRechargePoolAccountActorId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *BillpayParams.RechargePoolAccountActorId", reflect.TypeOf(val))
	}
	obj._RechargePoolAccountActorIdMutex.Lock()
	defer obj._RechargePoolAccountActorIdMutex.Unlock()
	obj._RechargePoolAccountActorId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "RechargePoolAccountActorId")
	}
	return nil
}
func (obj *BillpayParams) SetRechargePoolAccountPiId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *BillpayParams.RechargePoolAccountPiId", reflect.TypeOf(val))
	}
	obj._RechargePoolAccountPiIdMutex.Lock()
	defer obj._RechargePoolAccountPiIdMutex.Unlock()
	obj._RechargePoolAccountPiId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "RechargePoolAccountPiId")
	}
	return nil
}
