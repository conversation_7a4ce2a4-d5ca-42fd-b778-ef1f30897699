package serverhelper

import (
	"strings"

	casbinLib "github.com/casbin/casbin/v2"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"

	casbinPb "github.com/epifi/gamma/api/casbin"
	"github.com/epifi/gamma/casbin/config"
)

func InitCxPolicies(conf *config.Config, enforcer *casbinLib.SyncedEnforcer) {
	initPolicies(conf, enforcer)
	initGroupingPolicies(conf, enforcer)
}

func getCasbinAccessLevelFromConfig(role string) casbinPb.AccessLevel {
	// As we have changed config parser to koanf from viper now the keys are case-sensitive
	// But in this switch case we have hardcoded the lower case values of the key, which doesn't match with the config
	// Hence, comparing the values with ToLower value of key
	switch strings.ToLower(role) {
	case "admin":
		return casbinPb.AccessLevel_ADMIN
	case "qa":
		return casbinPb.AccessLevel_QA
	case "agent":
		return casbinPb.AccessLevel_AGENT
	case "developer":
		return casbinPb.AccessLevel_DEVELOPER
	case "viewlivenessvideo":
		return casbinPb.AccessLevel_VIEW_LIVENESS_VIDEO
	case "wealthinsights":
		return casbinPb.AccessLevel_WEALTH_INSIGHTS
	case "superadmin":
		return casbinPb.AccessLevel_SUPER_ADMIN
	case "waitlistapprover":
		return casbinPb.AccessLevel_WAITLIST_APPROVER
	case "waitlistadminapprover":
		return casbinPb.AccessLevel_WAITLIST_ADMIN_APPROVER
	case "qalead":
		return casbinPb.AccessLevel_QA_LEAD
	case "federalagent":
		return casbinPb.AccessLevel_FEDERAL_AGENT
	case "riskops":
		return casbinPb.AccessLevel_RISK_OPS
	case "fitadmin":
		return casbinPb.AccessLevel_FIT_ADMIN
	case "adminrestricted":
		return casbinPb.AccessLevel_ADMIN_RESTRICTED
	case "bizadmin":
		return casbinPb.AccessLevel_BIZ_ADMIN
	case "bizadminrestricted":
		return casbinPb.AccessLevel_BIZ_ADMIN_RESTRICTED
	case "wealthdeveloper":
		return casbinPb.AccessLevel_WEALTH_DEVELOPER
	case "wealthops":
		return casbinPb.AccessLevel_WEALTH_OPS
	case "recurringpaymentdev":
		return casbinPb.AccessLevel_RECURRING_PAYMENT_DEV
	case "webdeveloper":
		return casbinPb.AccessLevel_WEB_DEVELOPER
	case "salarywhitelistb2b":
		return casbinPb.AccessLevel_SALARY_WHITELIST_B2B
	case "rewardsadmin":
		return casbinPb.AccessLevel_REWARDS_ADMIN
	case "accountops":
		return casbinPb.AccessLevel_ACCOUNT_OPS
	case "salarydataops":
		return casbinPb.AccessLevel_SALARY_DATA_OPS
	case "salaryadmin":
		return casbinPb.AccessLevel_SALARY_ADMIN
	case "riskopsadmin":
		return casbinPb.AccessLevel_RISK_OPS_ADMIN
	case "dataops":
		return casbinPb.AccessLevel_DATA_OPS
	case "federalinwardremitter":
		return casbinPb.AccessLevel_FEDERAL_INWARD_REMITTER
	case "federaloutwardremitter":
		return casbinPb.AccessLevel_FEDERAL_OUTWARD_REMITTER
	case "federalmasterremitter":
		return casbinPb.AccessLevel_FEDERAL_MASTER_REMITTER
	case "federalmasterremittanceops":
		return casbinPb.AccessLevel_FEDERAL_MASTER_REMITTANCE_OPS
	case "jumpsensitivevendorresponse":
		return casbinPb.AccessLevel_JUMP_SENSITIVE_VENDOR_RESPONSE
	case "jumpops":
		return casbinPb.AccessLevel_JUMP_OPS
	case "riskeng":
		return casbinPb.AccessLevel_RISK_ENG
	case "federalloanlivenessreviewer":
		return casbinPb.AccessLevel_FEDERAL_LOAN_LIVENESS_REVIEWER
	case "usstocksops":
		return casbinPb.AccessLevel_US_STOCKS_OPS
	case "kycanalyst":
		return casbinPb.AccessLevel_KYC_ANALYST
	case "financeadmin":
		return casbinPb.AccessLevel_FINANCE_ADMIN
	case "usstockssensitivedata":
		return casbinPb.AccessLevel_USSTOCKS_SENSITIVE_DATA
	case "mutualfundssensitivedata":
		return casbinPb.AccessLevel_MUTUAL_FUNDS_SENSITIVE_DATA
	case "wealthonboardingsensitivedata":
		return casbinPb.AccessLevel_WEALTH_ONBOARDING_SENSITIVE_DATA
	case "useroutcall":
		return casbinPb.AccessLevel_USER_OUTCALL
	case "kycagentadmin":
		return casbinPb.AccessLevel_KYC_AGENT_ADMIN
	case "dataretriever":
		return casbinPb.AccessLevel_DATA_RETRIEVER
	case "segmentationadmin":
		return casbinPb.AccessLevel_SEGMENTATION_ADMIN
	case "cxsupportllmtester":
		return casbinPb.AccessLevel_CX_SUPPORT_LLM_TESTER
	case "vkyc_call_agent":
		return casbinPb.AccessLevel_VKYC_CALL_AGENT
	case "vkyc_call_auditor":
		return casbinPb.AccessLevel_VKYC_CALL_AUDITOR
	case "stockguardianvkyccallagent":
		return casbinPb.AccessLevel_STOCK_GUARDIAN_VKYC_CALL_AGENT
	case "stockguardianvkyccallauditor":
		return casbinPb.AccessLevel_STOCK_GUARDIAN_VKYC_CALL_AUDITOR
	case "stockguardianlendingops":
		return casbinPb.AccessLevel_STOCK_GUARDIAN_LENDING_OPS
	case "stockguardiandeveloper":
		return casbinPb.AccessLevel_STOCK_GUARDIAN_DEVELOPER
	case "loansuseroutcall":
		return casbinPb.AccessLevel_LOANS_USER_OUTCALL
	case "escalations":
		return casbinPb.AccessLevel_ESCALATIONS
	}
	return casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED
}

// nolint:funlen
func initPolicies(conf *config.Config, enforcer *casbinLib.SyncedEnforcer) {
	curRoleToRpcMap := extractCurrentRoleToResourceMap(conf)
	prevRoleToRpcMap := extractPreviousRoleToResourceMap(enforcer)
	permissionListToDelete := getObsoletePoliciesList(prevRoleToRpcMap, curRoleToRpcMap)
	for _, perm := range permissionListToDelete {
		ok, err := enforcer.RemovePolicy(perm.GetAccessLevel(), perm.GetResource())
		logger.InfoNoCtx("remove obsolete policy from casbin", zap.Any("permission", perm), zap.Bool("ok", ok), zap.Error(err))
	}

	for accessLevel, resourceList := range curRoleToRpcMap {
		for _, resource := range resourceList {
			ok, err := enforcer.AddPolicy(accessLevel.String(), resource)
			if err != nil {
				panic(err)
			}
			if !ok {
				logger.InfoNoCtx("policy already exists", zap.Any("Access Level", accessLevel), zap.Any("Resource", resource), zap.Error(err))
			}
		}
	}
}

func extractPreviousRoleToResourceMap(enforcer *casbinLib.SyncedEnforcer) map[casbinPb.AccessLevel][]string {
	prevRoleToRpcMap := make(map[casbinPb.AccessLevel][]string)
	// remove policies which do not exist in config but exist in DB
	previousPolicies := enforcer.GetPolicy()
	for _, policy := range previousPolicies {
		roleEnum := casbinPb.AccessLevel(casbinPb.AccessLevel_value[policy[0]])
		// if it cannot be cast to enum, do not consider for deleting it since it can be en email too
		if roleEnum != casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED {
			prevRoleToRpcMap[roleEnum] = append(prevRoleToRpcMap[roleEnum], policy[1])
		}
	}
	return prevRoleToRpcMap
}

func extractCurrentRoleToResourceMap(conf *config.Config) map[casbinPb.AccessLevel][]string {
	curRoleToRpcMap := make(map[casbinPb.AccessLevel][]string)
	for key, value := range conf.RoleToRpcMap {
		accessLvl := getCasbinAccessLevelFromConfig(key)
		if accessLvl == casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED {
			logger.ErrorNoCtx("invalid role found in casbin config", zap.String("Role", strings.ToLower(key)))
			continue
		}
		curRoleToRpcMap[accessLvl] = value
	}

	for role, serviceList := range conf.RoleToServiceEntity {
		accessLvl := getCasbinAccessLevelFromConfig(role)
		if accessLvl == casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED {
			logger.ErrorNoCtx("invalid role found in casbin config", zap.String("Role", strings.ToLower(role)))
			continue
		}
		for service, entityList := range serviceList {
			for _, entity := range entityList {
				dbStateResource := db_state.NewEntityServiceCombination(service, entity)
				curRoleToRpcMap[accessLvl] = append(curRoleToRpcMap[accessLvl], dbStateResource.GetResourceString())
			}
		}
	}
	return curRoleToRpcMap
}

// nolint:funlen
func initGroupingPolicies(conf *config.Config, enforcer *casbinLib.SyncedEnforcer) {
	curGroupedRolesMap := make(map[casbinPb.AccessLevel][]casbinPb.AccessLevel)
	for key, value := range conf.GroupedRolesMap {
		accessLvl := getCasbinAccessLevelFromConfig(key)
		if accessLvl == casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED {
			logger.ErrorNoCtx("invalid role found in casbin config", zap.String("Role", strings.ToLower(key)))
			continue
		}
		curGroupedRolesMap[accessLvl] = getEnumRoleListFromString(value)
	}
	prevGroupedRolesMap := make(map[casbinPb.AccessLevel][]casbinPb.AccessLevel)
	// This returns all grouping policies with email to role mapping
	// We need to only consider groupings where parent is one of those in enums
	previousGroupingPolicy := enforcer.GetGroupingPolicy()
	for _, groupingPolicy := range previousGroupingPolicy {
		parentRoleEnum := casbinPb.AccessLevel(casbinPb.AccessLevel_value[groupingPolicy[0]])
		childrenRoleEnum := casbinPb.AccessLevel(casbinPb.AccessLevel_value[groupingPolicy[1]])
		// if it cannot be cast to enum, do not consider for deleting it since it can be en email too
		if parentRoleEnum != casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED && childrenRoleEnum != casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED {
			prevGroupedRolesMap[parentRoleEnum] = append(prevGroupedRolesMap[parentRoleEnum], childrenRoleEnum)
		}
	}
	groupingPermissionListToDelete := getObsoleteGroupingPoliciesList(prevGroupedRolesMap, curGroupedRolesMap)
	for _, perm := range groupingPermissionListToDelete {
		ok, err := enforcer.RemoveGroupingPolicy(perm.GetAccessLevel(), perm.GetResource())
		logger.InfoNoCtx("remove obsolete policy from casbin", zap.Any("permission", perm), zap.Bool("ok", ok), zap.Error(err))
	}
	for parentRole, childrenRoleList := range curGroupedRolesMap {
		for _, childrenRole := range childrenRoleList {
			ok, err := enforcer.AddGroupingPolicy(parentRole.String(), childrenRole.String())
			if err != nil {
				panic(err)
			}
			if !ok {
				logger.InfoNoCtx("grouping policy already exists", zap.Any("Parent Role", parentRole.String()), zap.Any("Children Role", childrenRole.String()), zap.Error(err))
			}
		}
	}
}

func getObsoletePoliciesList(prevMap map[casbinPb.AccessLevel][]string, curMap map[casbinPb.AccessLevel][]string) []*casbinPb.Permission {
	var permissionList []*casbinPb.Permission
	for prevRole, prevRpcList := range prevMap {
		for _, prevRpc := range prevRpcList {
			// check if this RPC exists in that prevRole or not in current map
			found := false
			curRpcList, ok := curMap[prevRole]
			if ok {
				for _, curRpc := range curRpcList {
					if prevRpc == curRpc {
						found = true
					}
				}
			}
			if !found {
				permissionList = append(permissionList, &casbinPb.Permission{
					AccessLevel: prevRole.String(),
					Resource:    prevRpc,
				})
			}
		}
	}
	return permissionList
}

func getObsoleteGroupingPoliciesList(prevMap map[casbinPb.AccessLevel][]casbinPb.AccessLevel, curMap map[casbinPb.AccessLevel][]casbinPb.AccessLevel) []*casbinPb.Permission {
	var permissionList []*casbinPb.Permission
	for prevRole, prevChildrenList := range prevMap {
		for _, prevChildren := range prevChildrenList {
			// check if this children exists in that parent or not in current map
			found := false
			curChildrenList, ok := curMap[prevRole]
			if ok {
				for _, curChildren := range curChildrenList {
					if prevChildren == curChildren {
						found = true
					}
				}
			}
			if !found {
				permissionList = append(permissionList, &casbinPb.Permission{
					AccessLevel: prevRole.String(),
					Resource:    prevChildren.String(),
				})
			}
		}
	}
	return permissionList
}

// nolint:funlen
func getEnumRoleListFromString(roleList []string) []casbinPb.AccessLevel {
	var enumRoleList []casbinPb.AccessLevel
	for _, role := range roleList {
		switch role {
		case "ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_ADMIN)
		case "QA":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_QA)
		case "AGENT":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_AGENT)
		case "DEVELOPER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_DEVELOPER)
		case "VIEW_LIVENESS_VIDEO":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_VIEW_LIVENESS_VIDEO)
		case "SUPER_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_SUPER_ADMIN)
		case "WAITLIST_APPROVER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_WAITLIST_APPROVER)
		case "WAITLIST_ADMIN_APPROVER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_WAITLIST_ADMIN_APPROVER)
		case "QA_LEAD":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_QA_LEAD)
		case "FEDERAL_AGENT":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_FEDERAL_AGENT)
		case "RISK_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_RISK_OPS)
		case "FIT_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_FIT_ADMIN)
		case "ADMIN_RESTRICTED":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_ADMIN_RESTRICTED)
		case "BIZ_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_BIZ_ADMIN)
		case "BIZ_ADMIN_RESTRICTED":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_BIZ_ADMIN_RESTRICTED)
		case "WEALTH_DEVELOPER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_WEALTH_DEVELOPER)
		case "WEALTH_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_WEALTH_OPS)
		case "RECURRING_PAYMENT_DEV":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_RECURRING_PAYMENT_DEV)
		case "WEB_DEVELOPER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_WEB_DEVELOPER)
		case "SALARY_WHITELIST_B2B":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_SALARY_WHITELIST_B2B)
		case "REWARDS_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_REWARDS_ADMIN)
		case "ACCOUNT_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_ACCOUNT_OPS)
		case "SALARY_DATA_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_SALARY_DATA_OPS)
		case "SALARY_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_SALARY_ADMIN)
		case "RISK_OPS_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_RISK_OPS_ADMIN)
		case "DATA_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_DATA_OPS)
		case "FEDERAL_INWARD_REMITTER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_FEDERAL_INWARD_REMITTER)
		case "FEDERAL_MASTER_REMITTANCE_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_FEDERAL_MASTER_REMITTANCE_OPS)
		case "FEDERAL_OUTWARD_REMITTER_AGENT":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_FEDERAL_OUTWARD_REMITTER)
		case "FEDERAL_MASTER_REMITTER_AGENT":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_FEDERAL_MASTER_REMITTER)
		case "JUMP_SENSITIVE_VENDOR_RESPONSE":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_JUMP_SENSITIVE_VENDOR_RESPONSE)
		case "JUMP_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_JUMP_OPS)
		case "RISK_ENG":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_RISK_ENG)
		case "FEDERAL_LOAN_LIVENESS_REVIEWER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_FEDERAL_LOAN_LIVENESS_REVIEWER)
		case "US_STOCKS_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_US_STOCKS_OPS)
		case "KYC_ANALYST":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_KYC_ANALYST)
		case "FINANCE_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_FINANCE_ADMIN)
		case "USSTOCKS_SENSITIVE_DATA":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_USSTOCKS_SENSITIVE_DATA)
		case "MUTUAL_FUNDS_SENSITIVE_DATA":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_MUTUAL_FUNDS_SENSITIVE_DATA)
		case "WEALTH_ONBOARDING_SENSITIVE_DATA":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_WEALTH_ONBOARDING_SENSITIVE_DATA)
		case "USER_OUTCALL":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_USER_OUTCALL)
		case "KYC_AGENT_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_KYC_AGENT_ADMIN)
		case "DATA_RETRIEVER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_DATA_RETRIEVER)
		case "SEGMENTATION_ADMIN":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_SEGMENTATION_ADMIN)
		case "CX_SUPPORT_LLM_TESTER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_CX_SUPPORT_LLM_TESTER)
		case "VKYC_CALL_AGENT":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_VKYC_CALL_AGENT)
		case "VKYC_CALL_AUDITOR":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_VKYC_CALL_AUDITOR)
		case "STOCK_GUARDIAN_VKYC_CALL_AGENT":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_STOCK_GUARDIAN_VKYC_CALL_AGENT)
		case "STOCK_GUARDIAN_VKYC_CALL_AUDITOR":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_STOCK_GUARDIAN_VKYC_CALL_AUDITOR)
		case "STOCK_GUARDIAN_LENDING_OPS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_STOCK_GUARDIAN_LENDING_OPS)
		case "STOCK_GUARDIAN_DEVELOPER":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_STOCK_GUARDIAN_DEVELOPER)
		case "WEALTH_INSIGHTS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_WEALTH_INSIGHTS)
		case "ESCALATIONS":
			enumRoleList = append(enumRoleList, casbinPb.AccessLevel_ESCALATIONS)
		}
	}
	return enumRoleList
}
