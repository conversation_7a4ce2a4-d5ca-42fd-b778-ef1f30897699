package process_amc_eligible_users

import (
	"archive/zip"
	"bufio"
	"bytes"
	"context"
	"fmt"
	"io"
	"strings"

	"github.com/pkg/errors"
	"github.com/slack-go/slack"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/be-common/pkg/epifitemporal"

	cardActivityPb "github.com/epifi/gamma/api/card/activity/processamceligibleusers"
	commsPb "github.com/epifi/gamma/api/comms"
)

const (
	fileGenDateStringLayout = "02_Jan_2006"
)

var (
	fiPocEmails = []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"}
)

func (p *Processor) PostProcessAmcFile(ctx context.Context, req *cardActivityPb.PostProcessAmcFileRequest) (*cardActivityPb.PostProcessAmcFileResponse, error) {
	var (
		res = &cardActivityPb.PostProcessAmcFileResponse{}
	)

	lg := activity.GetLogger(ctx)
	fileContents, err := p.getSuccessFileContentsZip(ctx, req.GetBatchS3PathDetails())
	if err != nil {
		lg.Error("error fetching successful files", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	fileDateString := dateTimePkg.DateToString(req.GetFileGenDate(), fileGenDateStringLayout, dateTimePkg.IST)
	err = p.sendEmail(ctx, fileContents, fileDateString)
	if err != nil {
		lg.Error("error sending success file to bank", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	err = p.uploadFailureFilesToSlack(ctx, req.GetBatchS3PathDetails())
	if err != nil {
		// not returning error here since this should not cause an email resend
		lg.Error("error in sending failure files to slack", zap.Error(err))
	}
	return res, nil
}

func (p *Processor) getSuccessFileContentsZip(ctx context.Context, s3PathDetails []*cardActivityPb.BatchS3PathDetail) ([]byte, error) {
	lg := activity.GetLogger(ctx)

	buffer := &bytes.Buffer{}
	writer := bufio.NewWriter(buffer)

	zipWriter := zip.NewWriter(writer)

	for _, s3PathDetail := range s3PathDetails {
		lg.Info("fetching amc eligible users file from s3 for email zip creation", zap.String("eligibleUsersS3Path", s3PathDetail.GetEligibleUsersS3Path()))

		successCsvFileContent, err := p.dcDocsS3Client.Read(ctx, s3PathDetail.GetEligibleUsersS3Path())
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error fetching file from s3 path: %s", s3PathDetail.GetEligibleUsersS3Path()))
		}
		fileName := p.getFileName(s3PathDetail.GetEligibleUsersS3Path())
		zipFile, err := zipWriter.Create(fileName + ".csv")
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error creating zip file for: %s", fileName))
		}
		if _, err = io.Copy(zipFile, bytes.NewReader(successCsvFileContent)); err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error copying zip file contents for: %s", fileName))
		}
	}
	err := zipWriter.Close()
	if err != nil {
		return nil, errors.Wrap(err, "error in closing zip file writer")
	}

	return buffer.Bytes(), nil
}

func (p *Processor) sendEmail(ctx context.Context, fileContentZip []byte, fileGenDate string) error {
	emailMessage := p.getEmailMessage(fileContentZip, fileGenDate)
	emailResp, err := p.commsClient.SendMessage(ctx, emailMessage)
	if te := epifigrpc.RPCError(emailResp, err); te != nil {
		return errors.Wrap(te, "error dispatching email")
	}
	return nil
}

func (p *Processor) getFileName(s3Path string) string {
	fileNameWithAttachment := s3Path[strings.LastIndex(s3Path, "/")+1:]
	return fileNameWithAttachment[:strings.LastIndex(fileNameWithAttachment, ".")]
}

func (p *Processor) getEmailMessage(fileContent []byte, fileDateString string) *commsPb.SendMessageRequest {
	zipFileName := "dc_amc_charges_" + fileDateString + ".zip"
	return &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_GUARANTEED,
		Medium:         commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{EmailId: p.conf.AmcConfig.RecipientEmailId},
		Message: &commsPb.SendMessageRequest_Email{Email: &commsPb.EmailMessage{
			FromEmailId:   "<EMAIL>",
			FromEmailName: "Fi Money",
			Destination: &commsPb.EmailMessage_Destination{
				CcAddresses: p.getClosedCaptioningAddress(),
			},
			EmailOption: &commsPb.EmailOption{
				Option: &commsPb.EmailOption_DebitCardAmcChargesReportEmail{
					DebitCardAmcChargesReportEmail: &commsPb.DebitCardAmcChargesReportEmail{
						EmailType: commsPb.EmailType_DEBIT_CARD_AMC_CHARGES_REPORT_EMAIL,
						Option: &commsPb.DebitCardAmcChargesReportEmail_DebitCardAmcChargesReportEmailOptionV1{
							DebitCardAmcChargesReportEmailOptionV1: &commsPb.DebitCardAmcChargesReportEmailOptionV1{
								TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
								Heading:         "DEBIT CARD AMC CHARGES",
								Description:     "Please find attached herewith debit card amc charges files",
								FileGenDate:     fileDateString,
								IconUrl:         "https://epifi-icons.pointz.in/credit_card_images/cc_comms_green_tick",
								BackgroundColor: "#ECEEF0",
							},
						},
					},
				},
			},
			Attachment: []*commsPb.EmailMessage_Attachment{
				{
					FileContent:    fileContent,
					FileName:       zipFileName,
					Disposition:    commsPb.Disposition_ATTACHMENT,
					AttachmentType: "ZIP",
				},
			},
		}},
	}
}

func (p *Processor) uploadFailureFilesToSlack(ctx context.Context, s3PathDetails []*cardActivityPb.BatchS3PathDetail) error {
	for _, s3PathDetail := range s3PathDetails {
		failureCsvContents, err := p.dcDocsS3Client.Read(ctx, s3PathDetail.GetFailedUsersS3Path())
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("error reading file from s3 %s", s3PathDetail.GetFailedUsersS3Path()))
		}

		csvReader := bytes.NewReader(failureCsvContents)
		params := slack.UploadFileV2Parameters{
			Reader:   csvReader,
			FileSize: csvReader.Len(),
			Filename: p.getFileName(s3PathDetail.GetFailedUsersS3Path()),
			Title:    "Dc AMC Failures",
			Channel:  p.conf.AmcConfig.SlackChannelId,
		}
		_, err = p.slackClient.UploadFileV2(params)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("error in uploading amc failure file to slack: %s", s3PathDetail.GetFailedUsersS3Path()))
		}
	}
	return nil
}

func (p *Processor) getClosedCaptioningAddress() []*commsPb.EmailMessage_Destination_EmailAddress {
	var ccAddress []*commsPb.EmailMessage_Destination_EmailAddress
	for _, email := range fiPocEmails {
		ccAddress = append(ccAddress, &commsPb.EmailMessage_Destination_EmailAddress{EmailId: email})
	}
	return ccAddress
}
