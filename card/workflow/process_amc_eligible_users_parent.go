// nolint:funlen
package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	cardNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/card"
	workflowPkg "github.com/epifi/be-common/pkg/epifitemporal/workflow"
	"github.com/epifi/be-common/pkg/logger"

	cardActPb "github.com/epifi/gamma/api/card/activity/processamceligibleusers"
	cardWorkflowPayload "github.com/epifi/gamma/api/card/workflow/payload"
)

func ProcessAmcEligibleUsersParent(ctx workflow.Context, _ *workflowPb.Request) error {
	var (
		err error
	)
	lg := workflow.GetLogger(ctx)

	payload := &cardWorkflowPayload.ProcessAmcEligibleUsers{}
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, payload)
	if err != nil {
		lg.Error("activity failed", zap.String(logger.ACTIVITY, string(epifitemporal.GetWorkflowProcessingParamsV2)), zap.Error(err))
		return err
	}

	requestHeader := &activityPb.RequestHeader{
		Payload:     wfProcessingParams.GetPayload(),
		ClientReqId: wfProcessingParams.GetClientReqId().GetId(),
		Ownership:   commontypes.Ownership_EPIFI_TECH,
	}

	// -------------- FETCH AMC USERS LIST STAGE -----------------------------------
	amcUsersResponse, err := performFetchAmcUsersList(ctx, requestHeader, payload)
	if err != nil {
		lg.Error("fetch amc users list stage failure", zap.Error(err))
		return err
	}

	// --------------- BATCH WORKFLOW INITIATION STAGE  --------------------
	batchS3PathDetails, err := performBatchChildWorkflowInitStage(ctx, payload, amcUsersResponse)
	if err != nil {
		lg.Error("batch workflow initiation stage failure", zap.Error(err))
		return err
	}

	// --------------- POST PROCESS AMC FILE STAGE --------------------
	err = postProcessAmcFileStage(ctx, requestHeader, payload, batchS3PathDetails)
	if err != nil {
		lg.Error("create and upload final file stage failure", zap.Error(err))
		return err
	}
	return nil
}

func performFetchAmcUsersList(ctx workflow.Context, requestHeader *activityPb.RequestHeader, payload *cardWorkflowPayload.ProcessAmcEligibleUsers) (*cardActPb.GetAmcUserBaseResponse, error) {
	response := &cardActPb.GetAmcUserBaseResponse{}
	err := celestialPkg.InitiateWorkflowStage(ctx, cardNs.GetAmcUserBaseStage, stagePb.Status_INITIATED)
	if err != nil {
		return nil, err
	}
	activityErr := activityPkg.Execute(ctx, cardNs.GetAmcUserBase, response, &cardActPb.GetAmcUserBaseRequest{
		RequestHeader: requestHeader,
		S3Path:        payload.GetS3Path(),
		FileGenDate:   payload.GetFileGenDate(),
	})
	err = publishCelestialStageEvent(ctx, activityErr, cardNs.GetAmcUserBaseStage)
	if err != nil {
		return nil, err
	}
	if activityErr != nil {
		return nil, activityErr
	}
	return response, nil
}

func performBatchChildWorkflowInitStage(ctx workflow.Context, payload *cardWorkflowPayload.ProcessAmcEligibleUsers, amcUserBaseResponse *cardActPb.GetAmcUserBaseResponse) ([]*cardActPb.BatchS3PathDetail, error) {
	lg := workflow.GetLogger(ctx)
	err := celestialPkg.InitiateWorkflowStage(ctx, cardNs.AmcBatchChildWorkflowStage, stagePb.Status_INITIATED)
	if err != nil {
		return nil, err
	}
	childWorkflowFutures := make([]workflow.ChildWorkflowFuture, 0)
	batchS3PathDetails := make([]*cardActPb.BatchS3PathDetail, 0)
	batchNumber := 1
	timeUnix := time.Now().Unix()
	initiateNextBatchWorkflow := true
	for initiateNextBatchWorkflow {
		startRow := (batchNumber - 1) * int(amcUserBaseResponse.GetBatchSize())
		endRow := min(len(amcUserBaseResponse.GetCardIds()), startRow+int(amcUserBaseResponse.GetBatchSize()))
		eligibleUsersBatchPath := fmt.Sprintf(amcUserBaseResponse.GetEligibleUsersS3PathFormat(), timeUnix, batchNumber)
		failedUsersBatchPath := fmt.Sprintf(amcUserBaseResponse.GetFailedUsersS3PathFormat(), timeUnix, batchNumber)

		lg.Info("initiating ProcessAmcEligibleUsersChild workflow", zap.Int("batchNumber", batchNumber),
			zap.String("eligibleUsersBatchS3Path", eligibleUsersBatchPath),
			zap.String("failedUsersBatchS3Path", failedUsersBatchPath))
		childCtx, _ := workflow.NewDisconnectedContext(ctx)
		wfPayload := &cardWorkflowPayload.ProcessAmcEligibleUsersBatch{
			CardIds:             amcUserBaseResponse.GetCardIds()[startRow:endRow],
			EligibleUsersS3Path: eligibleUsersBatchPath,
			FailedUsersS3Path:   failedUsersBatchPath,
			BatchNumber:         int32(batchNumber),
			FileGenDate:         payload.GetFileGenDate(),
			UseBaseFileS3Path:   payload.GetS3Path(),
		}
		future, initWorkflowErr := workflowPkg.ExecuteChildWorkflowAsync(childCtx, "",
			cardNs.ProcessAmcEligibleUsersChild, wfPayload)
		if initWorkflowErr != nil {
			err = publishCelestialStageEvent(ctx, initWorkflowErr, cardNs.AmcBatchChildWorkflowStage)
			if err != nil {
				return nil, err
			}
			return nil, initWorkflowErr
		}
		err = workflow.Sleep(ctx, 1*time.Second)
		if err != nil {
			return nil, err
		}
		childWorkflowFutures = append(childWorkflowFutures, future)
		batchS3PathDetails = append(batchS3PathDetails, &cardActPb.BatchS3PathDetail{
			EligibleUsersS3Path: eligibleUsersBatchPath,
			FailedUsersS3Path:   failedUsersBatchPath,
			BatchNumber:         int32(batchNumber),
		})
		batchNumber += 1
		if endRow == len(amcUserBaseResponse.GetCardIds()) {
			initiateNextBatchWorkflow = false
		}
	}

	for _, future := range childWorkflowFutures {
		futureErr := future.Get(ctx, nil)
		if futureErr != nil {
			err = publishCelestialStageEvent(ctx, futureErr, cardNs.AmcBatchChildWorkflowStage)
			if err != nil {
				return nil, err
			}
			return nil, futureErr
		}
	}

	err = publishCelestialStageEvent(ctx, nil, cardNs.AmcBatchChildWorkflowStage)
	if err != nil {
		return nil, err
	}
	return batchS3PathDetails, nil

}

func postProcessAmcFileStage(ctx workflow.Context, requestHeader *activityPb.RequestHeader, payload *cardWorkflowPayload.ProcessAmcEligibleUsers, batchS3PathDetails []*cardActPb.BatchS3PathDetail) error {

	err := celestialPkg.InitiateWorkflowStage(ctx, cardNs.PostProcessAmcFileStage, stagePb.Status_INITIATED)
	if err != nil {
		return err
	}
	activityErr := activityPkg.Execute(ctx, cardNs.PostProcessAmcFile, &cardActPb.PostProcessAmcFileResponse{}, &cardActPb.PostProcessAmcFileRequest{
		RequestHeader:      requestHeader,
		BatchS3PathDetails: batchS3PathDetails,
		FileGenDate:        payload.GetFileGenDate(),
	})
	err = publishCelestialStageEvent(ctx, activityErr, cardNs.PostProcessAmcFileStage)
	if err != nil {
		return err
	}
	return nil
}
