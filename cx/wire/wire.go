//go:build wireinject
// +build wireinject

//go:generate wire
package wire

import (
	"context"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	stsv2 "github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	developer2 "github.com/epifi/gamma/api/accounts/developer"
	insightsActions "github.com/epifi/gamma/cx/developer/actions/processor/wealth/networth/insights"

	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"

	"github.com/epifi/gamma/risk/accountstatus"

	escalation_event "github.com/epifi/gamma/cx/consumer/escalation"

	"github.com/epifi/gamma/cx/escalations"
	dao24 "github.com/epifi/gamma/cx/escalations/dao"

	awsConfigOP "github.com/aws/aws-sdk-go-v2/config"

	dcProfilePb "github.com/epifi/gamma/api/cx/data_collector/profile"
	irPb "github.com/epifi/gamma/api/inapphelp/issue_reporting"
	"github.com/epifi/gamma/api/nudge/journey"
	leaPb "github.com/epifi/gamma/api/risk/lea"
	whitelistPb "github.com/epifi/gamma/api/risk/whitelist"
	salaryEstDevPb "github.com/epifi/gamma/api/salaryestimation/developer"
	sgKycApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	sgLmsPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	tspDbState "github.com/epifi/gamma/api/tspuser"
	vgNcPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgScienapticPb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	"github.com/epifi/gamma/cx/call/blocker"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/factory"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator/impl"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/providers"
	cxvkyccall "github.com/epifi/gamma/cx/data_collector/vkyccall"
	"github.com/epifi/gamma/cx/data_collector/vkyccall/serviceprovider"
	"github.com/epifi/gamma/cx/developer/actions/processor/stockguardian"
	"github.com/epifi/gamma/cx/developer/actions/processor/userrisk"
	cxactionvkyccall "github.com/epifi/gamma/cx/developer/actions/processor/vkyccall"
	"github.com/epifi/gamma/cx/issue_category"
	"github.com/epifi/gamma/cx/issue_category/issue_category_id_fetcher"
	"github.com/epifi/gamma/cx/sherlock_sop"
	dao21 "github.com/epifi/gamma/cx/sherlock_sop/dao"
	"github.com/epifi/gamma/cx/sherlock_sop/sop_helper"
	cxSGPb "github.com/epifi/gamma/cx/stockguardian/kyc"
	"github.com/epifi/gamma/cx/ticket/csat"
	comms2 "github.com/epifi/gamma/cx/ticket/csat/comms"
	"github.com/epifi/gamma/cx/ticket/csat/token_manager"
	dao23 "github.com/epifi/gamma/inapphelp/issue_reporting/dao"
	fittt3 "github.com/epifi/gamma/pkg/fittt"
	sherlockDbState "github.com/epifi/gamma/sherlock/dev/dbstate"
	"github.com/epifi/gamma/sherlock/dev/dbstate/omegle"
	"github.com/epifi/gamma/sherlock/dev/dbstate/vkyccall"
	verifiPkg "github.com/epifi/gamma/verifi/pkg"

	"github.com/slack-go/slack"

	dao20 "github.com/epifi/gamma/actor_activity/dao"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	dao22 "github.com/epifi/gamma/cx/call_ivr/dao"
	riskChart "github.com/epifi/gamma/cx/risk_ops/chart"
	"github.com/epifi/gamma/cx/sherlock_scripts/script_helper"
	mfpayDao "github.com/epifi/gamma/investment/mutualfund/payment_handler/dao"
	"github.com/epifi/gamma/pkg/strapi"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/ratelimiter"

	watsonInfoProviderPb "github.com/epifi/gamma/api/cx/error_activity/watson_info_provider"
	stageWiseCommsPb "github.com/epifi/gamma/api/cx/manual_ticket_stage_wise_comms"
	folioPb "github.com/epifi/gamma/api/investment/mutualfund/foliodetails"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	employernamecategoriserPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	"github.com/epifi/gamma/cx/consumer/s3_event"
	"github.com/epifi/gamma/cx/developer/actions/processor/casper"
	cache2 "github.com/epifi/gamma/cx/dispute/dao/cache"
	"github.com/epifi/gamma/cx/error_activity"
	dao19 "github.com/epifi/gamma/cx/error_activity/dao"
	"github.com/epifi/gamma/cx/error_activity/trigger_processor"
	watsonInfoProvider "github.com/epifi/gamma/cx/error_activity/watson_info_provider"
	"github.com/epifi/gamma/cx/issue_config"
	"github.com/epifi/gamma/cx/manual_ticket_stage_wise_comms"
	"github.com/epifi/gamma/varys/logsource"

	"google.golang.org/protobuf/encoding/protojson"

	sqsPkg "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"

	monorailPayload "github.com/epifi/be-common/api/pkg/monorail/payload"
	pkgMonorailApiWrapper "github.com/epifi/be-common/pkg/monorail/api_wrapper"

	dbStatePb "github.com/epifi/gamma/api/cx/developer/db_state"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	p2pIncidentManagerPb "github.com/epifi/gamma/api/p2pinvestment/incidentmanager"
	airflowPayload "github.com/epifi/gamma/api/pkg/airflow/payload"
	palSherlockBannersPb "github.com/epifi/gamma/api/preapprovedloan/sherlock_banners"
	"github.com/epifi/gamma/api/product"
	vgP2pPb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/handler"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/repository"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/usecase"
	"github.com/epifi/gamma/cx/data_collector/user_requests/account_details_collector"
	manager2 "github.com/epifi/gamma/cx/issue_category/manager"
	dao18 "github.com/epifi/gamma/cx/issue_config/dao"
	"github.com/epifi/gamma/cx/risk_ops/review/watchlist"
	pkgAirflowApiWrapper "github.com/epifi/gamma/pkg/airflow/wrapper"

	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	"github.com/google/wire"
	temporalWorkflowPb "go.temporal.io/api/workflowservice/v1"
	"gorm.io/gorm"

	awss3 "github.com/epifi/be-common/pkg/aws/v2/s3"

	dao2 "github.com/epifi/gamma/cx/issue_category/dao"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	rlwireset "github.com/epifi/be-common/pkg/ratelimiter/wire"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	celestialPb "github.com/epifi/be-common/api/celestial"

	monorailApi "github.com/epifi/be-common/pkg/monorail/api_wrapper"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/accounts/statement"
	actorPb "github.com/epifi/gamma/api/actor"
	actorDeveloper "github.com/epifi/gamma/api/actor/developer"
	aaClient "github.com/epifi/gamma/api/actor_activity"
	alfredPb "github.com/epifi/gamma/api/alfred"
	alfredDeveloper "github.com/epifi/gamma/api/alfred/developer"
	amlPb "github.com/epifi/gamma/api/aml"
	amlDeveloper "github.com/epifi/gamma/api/aml/developer"
	analyserDeveloper "github.com/epifi/gamma/api/analyser/developer"
	authPb "github.com/epifi/gamma/api/auth"
	authDeveloper "github.com/epifi/gamma/api/auth/developer"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	livenessDeveloper "github.com/epifi/gamma/api/auth/liveness/developer"
	"github.com/epifi/gamma/api/auth/location"
	authOrchDeveloper "github.com/epifi/gamma/api/auth/orchestrator/developer"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	bankCustDeveloper "github.com/epifi/gamma/api/bankcust/developer"
	ccPb "github.com/epifi/gamma/api/card/control"
	cardCxPb "github.com/epifi/gamma/api/card/cx"
	cardDeveloper "github.com/epifi/gamma/api/card/developer"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	casbinPb "github.com/epifi/gamma/api/casbin"
	casbinDeveloper "github.com/epifi/gamma/api/casbin/developer"
	casperPb "github.com/epifi/gamma/api/casper"
	casperDeveloper "github.com/epifi/gamma/api/casper/developer"
	discountsPb "github.com/epifi/gamma/api/casper/discounts"
	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	"github.com/epifi/gamma/api/casper/redemption"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	categorizerDeveloper "github.com/epifi/gamma/api/categorizer/developer"
	celestialDeveloper "github.com/epifi/gamma/api/celestial/developer"
	cmsPb "github.com/epifi/gamma/api/cms"
	cmsDeveloper "github.com/epifi/gamma/api/cms/developer"
	collectionDeveloperPb "github.com/epifi/gamma/api/collection/developer"
	commsPb "github.com/epifi/gamma/api/comms"
	commsDeveloper "github.com/epifi/gamma/api/comms/developer"
	commsDeveloperActionsPb "github.com/epifi/gamma/api/comms/developer/actions"
	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	upPb "github.com/epifi/gamma/api/comms/user_preference"
	caPb "github.com/epifi/gamma/api/connected_account"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	caDeveloper "github.com/epifi/gamma/api/connected_account/developer"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportPb "github.com/epifi/gamma/api/creditreportv2/developer"
	chatPb "github.com/epifi/gamma/api/cx/chat"
	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	salaryb2bPb "github.com/epifi/gamma/api/cx/data_collector/salaryprogram/salaryb2b"
	cxDeveloper "github.com/epifi/gamma/api/cx/developer"
	issueResolutionFeedbackPb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	userIssueInfoPb "github.com/epifi/gamma/api/cx/user_issue_info"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	mockWatsonClient "github.com/epifi/gamma/api/cx/watson/mock_client"
	depositPb "github.com/epifi/gamma/api/deposit"
	depositDeveloper "github.com/epifi/gamma/api/deposit/developer"
	depositWatsonPb "github.com/epifi/gamma/api/deposit/watson"
	"github.com/epifi/gamma/api/employment"
	employmentDeveloper "github.com/epifi/gamma/api/employment/developer"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBillPb "github.com/epifi/gamma/api/firefly/billing"
	ccCxPb "github.com/epifi/gamma/api/firefly/cx"
	ffDeveloper "github.com/epifi/gamma/api/firefly/developer"
	ffLmsPb "github.com/epifi/gamma/api/firefly/lms"
	ffPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	fitttPb "github.com/epifi/gamma/api/fittt"
	devconsolepb "github.com/epifi/gamma/api/fittt/devconsole"
	fitttDeveloper "github.com/epifi/gamma/api/fittt/developer"
	schedulerpb "github.com/epifi/gamma/api/fittt/scheduler"
	sportspb "github.com/epifi/gamma/api/fittt/sports"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	heDeveloper "github.com/epifi/gamma/api/health_engine/developer"
	inapphelpDeveloper "github.com/epifi/gamma/api/inapphelp/developer"
	inappHelpServingPb "github.com/epifi/gamma/api/inapphelp/faq/serving"
	servingPb "github.com/epifi/gamma/api/inapphelp/faq/serving"
	mediaPb "github.com/epifi/gamma/api/inapphelp/media"
	inappReferralPb "github.com/epifi/gamma/api/inappreferral"
	inappreferralDeveloper "github.com/epifi/gamma/api/inappreferral/developer"
	seasonsPb "github.com/epifi/gamma/api/inappreferral/season"
	"github.com/epifi/gamma/api/insights"
	"github.com/epifi/gamma/api/insights/accessinfo"
	insightsDeveloper "github.com/epifi/gamma/api/insights/developer"
	emailParserPb "github.com/epifi/gamma/api/insights/emailparser"
	invAggrPb "github.com/epifi/gamma/api/investment/aggregator"
	dynUIElPb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	investmentCatalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	investDeveloper "github.com/epifi/gamma/api/investment/mutualfund/developer"
	investmentOrder "github.com/epifi/gamma/api/investment/mutualfund/order"
	fgPb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	mfOps "github.com/epifi/gamma/api/investment/mutualfund/order/operations"
	prhPb "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler"
	rfPb "github.com/epifi/gamma/api/investment/mutualfund/order/reverse_feed"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	reconciliationPb "github.com/epifi/gamma/api/investment/mutualfund/reconciliation"
	investmentWatsonPb "github.com/epifi/gamma/api/investment/watson"
	kycPb "github.com/epifi/gamma/api/kyc"
	agentPb "github.com/epifi/gamma/api/kyc/agent"
	kycDeveloper "github.com/epifi/gamma/api/kyc/developer"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	devLeadsPb "github.com/epifi/gamma/api/leads/developer"
	merchantDeveloper "github.com/epifi/gamma/api/merchant/developer"
	"github.com/epifi/gamma/api/nudge"
	nudgeDeveloper "github.com/epifi/gamma/api/nudge/developer"
	"github.com/epifi/gamma/api/order"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/cx"
	orderDeveloper "github.com/epifi/gamma/api/order/developer"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	reconPb "github.com/epifi/gamma/api/order/recon"
	p2investmentPb "github.com/epifi/gamma/api/p2pinvestment"
	p2pCxPb "github.com/epifi/gamma/api/p2pinvestment/cx"
	p2pDeveloper "github.com/epifi/gamma/api/p2pinvestment/developer"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment/developer"
	"github.com/epifi/gamma/api/pan"
	panDeveloper "github.com/epifi/gamma/api/pan/developer"
	payPb "github.com/epifi/gamma/api/pay"
	payCxPb "github.com/epifi/gamma/api/pay/cx"
	payDeveloper "github.com/epifi/gamma/api/pay/developer"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	payFileGeneratorPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	payIncidentManagerPb "github.com/epifi/gamma/api/pay/payincidentmanager"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	piDeveloper "github.com/epifi/gamma/api/paymentinstrument/developer"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	preapprovedloanCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	plDeveloper "github.com/epifi/gamma/api/preapprovedloan/developer"
	preApprovedLoanDevPb "github.com/epifi/gamma/api/preapprovedloan/developer"
	questDeveloper "github.com/epifi/gamma/api/quest/developer"
	questManagerPb "github.com/epifi/gamma/api/quest/manager"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	recurrDeveloper "github.com/epifi/gamma/api/recurringpayment/developer"
	enachDeveloper "github.com/epifi/gamma/api/recurringpayment/enach/developer"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/campaigncomm"
	rewardsDeveloper "github.com/epifi/gamma/api/rewards/developer"
	rewardSimulatorPb "github.com/epifi/gamma/api/rewards/generator"
	luckydrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	riskPb "github.com/epifi/gamma/api/risk"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	riskDeveloper "github.com/epifi/gamma/api/risk/developer"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/risk/redlist"
	rmsDeveloper "github.com/epifi/gamma/api/rms/developer"
	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/salaryprogram"
	salaryCxPb "github.com/epifi/gamma/api/salaryprogram/cx"
	spDeveloper "github.com/epifi/gamma/api/salaryprogram/developer"
	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	salaryReferralsPb "github.com/epifi/gamma/api/salaryprogram/referrals"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsDeveloper "github.com/epifi/gamma/api/savings/developer"
	"github.com/epifi/gamma/api/savings/extacct"
	savingsWatsonClient "github.com/epifi/gamma/api/savings/watson"
	"github.com/epifi/gamma/api/screener"
	screenerDeveloper "github.com/epifi/gamma/api/screener/developer"
	searchPb "github.com/epifi/gamma/api/search"
	searchDevPb "github.com/epifi/gamma/api/search/developer"
	indexerPb "github.com/epifi/gamma/api/search/indexer"
	securitiesPb "github.com/epifi/gamma/api/securities/developer"
	segmentPb "github.com/epifi/gamma/api/segment"
	segmentConsumerPb "github.com/epifi/gamma/api/segment/consumer"
	segmentDeveloper "github.com/epifi/gamma/api/segment/developer"
	simulatorWcPb "github.com/epifi/gamma/api/simulator/cx/watson_client"
	preApprovedLoanSimPb "github.com/epifi/gamma/api/simulator/lending/preapprovedloan"
	accountsDevPb "github.com/epifi/gamma/api/simulator/openbanking/accounts/developer"
	profileevaluatorSimPb "github.com/epifi/gamma/api/simulator/profileevaluator"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	tieringDeveloper "github.com/epifi/gamma/api/tiering/developer"
	timelinePb "github.com/epifi/gamma/api/timeline"
	timelineDeveloper "github.com/epifi/gamma/api/timeline/developer"
	developerClient "github.com/epifi/gamma/api/tspuser/developer"
	types "github.com/epifi/gamma/api/typesv2"
	upcomingtxnDeveloper "github.com/epifi/gamma/api/upcomingtransactions/developer"
	upiPb "github.com/epifi/gamma/api/upi"
	cx2 "github.com/epifi/gamma/api/upi/cx"
	upiDeveloper "github.com/epifi/gamma/api/upi/developer"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	userPb "github.com/epifi/gamma/api/user"
	userDeveloper "github.com/epifi/gamma/api/user/developer"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	location2 "github.com/epifi/gamma/api/user/location"
	onboarding2 "github.com/epifi/gamma/api/user/onboarding"
	onboardingWatsonClient "github.com/epifi/gamma/api/user/onboarding/watson"
	"github.com/epifi/gamma/api/useractions"
	accountManagerPb "github.com/epifi/gamma/api/usstocks/account"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usstockDeveloper "github.com/epifi/gamma/api/usstocks/developer"
	ussOperationsPb "github.com/epifi/gamma/api/usstocks/operations"
	orderManagerPb "github.com/epifi/gamma/api/usstocks/order"
	usstocksOrderManagerPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	vgAaPb "github.com/epifi/gamma/api/vendorgateway/aa"
	vgLeadsquaredPb "github.com/epifi/gamma/api/vendorgateway/crm/leadsquared"
	vgSenseforthPb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/livechatfallback/senseforth"
	"github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"
	vgFcPb "github.com/epifi/gamma/api/vendorgateway/cx/freshchat"
	"github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/api/vendorgateway/cx/ozonetel"
	ekycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/api/vendorgateway/extvalidate"
	fennelPb "github.com/epifi/gamma/api/vendorgateway/fennel"
	namecheckVgPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgAccountsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	dispute2 "github.com/epifi/gamma/api/vendorgateway/openbanking/dispute"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	stocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	"github.com/epifi/gamma/api/vendormapping"
	vmDeveloper "github.com/epifi/gamma/api/vendormapping/developer"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	wonbDeveloper "github.com/epifi/gamma/api/wealthonboarding/developer"
	"github.com/epifi/gamma/cx/activity"
	"github.com/epifi/gamma/cx/admin_actions"
	helper5 "github.com/epifi/gamma/cx/admin_actions/helper"
	processor6 "github.com/epifi/gamma/cx/admin_actions/processor"
	"github.com/epifi/gamma/cx/app_log"
	appLogDao "github.com/epifi/gamma/cx/app_log/dao"
	dao7 "github.com/epifi/gamma/cx/app_log/dao"
	"github.com/epifi/gamma/cx/audit_log"
	dao5 "github.com/epifi/gamma/cx/audit_log/dao"
	"github.com/epifi/gamma/cx/call"
	ozonetel_consumer "github.com/epifi/gamma/cx/call/consumer"
	processor8 "github.com/epifi/gamma/cx/call/consumer/processor"
	dao12 "github.com/epifi/gamma/cx/call/dao"
	helper11 "github.com/epifi/gamma/cx/call/helper"
	"github.com/epifi/gamma/cx/call_ivr"
	dao25 "github.com/epifi/gamma/cx/call_ivr/dao"
	"github.com/epifi/gamma/cx/call_routing"
	"github.com/epifi/gamma/cx/call_routing/dao"
	helper9 "github.com/epifi/gamma/cx/call_routing/helper"
	"github.com/epifi/gamma/cx/call_routing/helper/priority_helper"
	"github.com/epifi/gamma/cx/chat"
	"github.com/epifi/gamma/cx/chat/bot/livechatfallback"
	botWorkflow "github.com/epifi/gamma/cx/chat/bot/workflow"
	"github.com/epifi/gamma/cx/chat/bot/workflow/execute_action_processor"
	"github.com/epifi/gamma/cx/chat/bot/workflow/fetch_data_processor"
	chatConsumer "github.com/epifi/gamma/cx/chat/consumer"
	processor11 "github.com/epifi/gamma/cx/chat/consumer/processor"
	dao4 "github.com/epifi/gamma/cx/chat/dao"
	helper8 "github.com/epifi/gamma/cx/chat/helper"
	"github.com/epifi/gamma/cx/config"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	cxWorkerConfig "github.com/epifi/gamma/cx/config/worker"
	cxConnectedAccount "github.com/epifi/gamma/cx/connected_account"
	"github.com/epifi/gamma/cx/consumer"
	"github.com/epifi/gamma/cx/consumer/contact_event"
	"github.com/epifi/gamma/cx/consumer/create_ticket_event"
	"github.com/epifi/gamma/cx/consumer/ticket_event"
	ticketEventHelper "github.com/epifi/gamma/cx/consumer/ticket_event/helper"
	"github.com/epifi/gamma/cx/consumer/update_ticket_event"
	"github.com/epifi/gamma/cx/consumer/update_ticket_event/issue_resolution_feedback"
	processor10 "github.com/epifi/gamma/cx/consumer/update_ticket_event/issue_resolution_feedback/processor"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration"
	citConsumer "github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer"
	citConsumerProcessor "github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer/processor"
	citDao "github.com/epifi/gamma/cx/crm_issue_tracker_integration/dao"
	citHelper "github.com/epifi/gamma/cx/crm_issue_tracker_integration/helper"
	citTicketTranslator "github.com/epifi/gamma/cx/crm_issue_tracker_integration/ticket_translator"
	citMonorailDescriptionProcessor "github.com/epifi/gamma/cx/crm_issue_tracker_integration/ticket_translator/monorail_description"
	"github.com/epifi/gamma/cx/customer_auth"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	dao3 "github.com/epifi/gamma/cx/customer_auth/dao"
	"github.com/epifi/gamma/cx/customer_auth/verifier"
	"github.com/epifi/gamma/cx/data_collector/account"
	"github.com/epifi/gamma/cx/data_collector/alfred"
	"github.com/epifi/gamma/cx/data_collector/card"
	"github.com/epifi/gamma/cx/data_collector/comms"
	"github.com/epifi/gamma/cx/data_collector/firefly"
	"github.com/epifi/gamma/cx/data_collector/fittt"
	helper3 "github.com/epifi/gamma/cx/data_collector/helper"
	cxInvexstment "github.com/epifi/gamma/cx/data_collector/investment/mutualfund"
	"github.com/epifi/gamma/cx/data_collector/investment/usstocks"
	"github.com/epifi/gamma/cx/data_collector/kyc"
	"github.com/epifi/gamma/cx/data_collector/onboarding"
	"github.com/epifi/gamma/cx/data_collector/p2pinvestment"
	internationalFundTransferCxDataCollector "github.com/epifi/gamma/cx/data_collector/pay/internationalfundtransfer"
	"github.com/epifi/gamma/cx/data_collector/payment_instruments"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan"
	"github.com/epifi/gamma/cx/data_collector/profile"
	"github.com/epifi/gamma/cx/data_collector/referrals"
	"github.com/epifi/gamma/cx/data_collector/rewards"
	riskOpsWealth "github.com/epifi/gamma/cx/data_collector/risk_ops_wealth"
	"github.com/epifi/gamma/cx/data_collector/salarydataops"
	"github.com/epifi/gamma/cx/data_collector/salaryprogram/salaryb2b"
	"github.com/epifi/gamma/cx/data_collector/tiering"
	"github.com/epifi/gamma/cx/data_collector/transaction"
	userReq "github.com/epifi/gamma/cx/data_collector/user_requests"
	"github.com/epifi/gamma/cx/data_collector/wealth_onboarding"
	"github.com/epifi/gamma/cx/developer/actions"
	consumer5 "github.com/epifi/gamma/cx/developer/actions/consumer"
	"github.com/epifi/gamma/cx/developer/actions/consumer/event_processors"
	devActionProcessors "github.com/epifi/gamma/cx/developer/actions/processor"
	"github.com/epifi/gamma/cx/developer/actions/processor/deposits"
	ffDevAction "github.com/epifi/gamma/cx/developer/actions/processor/firefly"
	fittt2 "github.com/epifi/gamma/cx/developer/actions/processor/fittt"
	"github.com/epifi/gamma/cx/developer/actions/processor/internationalfundtransfer"
	"github.com/epifi/gamma/cx/developer/actions/processor/lending"
	mutualfund2 "github.com/epifi/gamma/cx/developer/actions/processor/mutualfund"
	p2pinvestment2 "github.com/epifi/gamma/cx/developer/actions/processor/p2pinvestment"
	devActionReferralProcessors "github.com/epifi/gamma/cx/developer/actions/processor/referrals"
	usstocks2 "github.com/epifi/gamma/cx/developer/actions/processor/usstocks"
	"github.com/epifi/gamma/cx/developer/actions/processor/wealthonboarding"
	developer "github.com/epifi/gamma/cx/developer/cx_db_states"
	processor5 "github.com/epifi/gamma/cx/developer/cx_db_states/processor"
	"github.com/epifi/gamma/cx/developer/db_states"
	"github.com/epifi/gamma/cx/developer/db_states/collector"
	helper4 "github.com/epifi/gamma/cx/developer/helper"
	"github.com/epifi/gamma/cx/developer/ticket_summary"
	"github.com/epifi/gamma/cx/dispute"
	consumer2 "github.com/epifi/gamma/cx/dispute/consumer"
	dao6 "github.com/epifi/gamma/cx/dispute/dao"
	helper2 "github.com/epifi/gamma/cx/dispute/helper"
	"github.com/epifi/gamma/cx/dispute/job"
	processor2 "github.com/epifi/gamma/cx/dispute/job/processor"
	"github.com/epifi/gamma/cx/dispute/job/reverse_update_processor"
	"github.com/epifi/gamma/cx/dispute/processor"
	helper6 "github.com/epifi/gamma/cx/dispute/processor/helper"
	"github.com/epifi/gamma/cx/dispute/questionnaire_helper"
	"github.com/epifi/gamma/cx/federal"
	"github.com/epifi/gamma/cx/fittt/devconsole"
	"github.com/epifi/gamma/cx/helper"
	"github.com/epifi/gamma/cx/inapphelp_feedback_engine_clients/feedback_subscription"
	"github.com/epifi/gamma/cx/interceptor"
	"github.com/epifi/gamma/cx/internal"
	internal_celestial "github.com/epifi/gamma/cx/internal/celestial"
	internal_irf "github.com/epifi/gamma/cx/internal/issue_resolution_feedback"
	watson2 "github.com/epifi/gamma/cx/internal/watson"
	"github.com/epifi/gamma/cx/internal/watson/activity_helper"
	watsonComms "github.com/epifi/gamma/cx/internal/watson/comms"
	irfService "github.com/epifi/gamma/cx/issue_resolution_feedback"
	dao14 "github.com/epifi/gamma/cx/issue_resolution_feedback/dao"
	dao15 "github.com/epifi/gamma/cx/issue_resolution_feedback/dao"
	"github.com/epifi/gamma/cx/landing_page"
	lpAct "github.com/epifi/gamma/cx/landing_page/sherlock_activity_generator"
	"github.com/epifi/gamma/cx/liveness_video"
	"github.com/epifi/gamma/cx/payout"
	consumer4 "github.com/epifi/gamma/cx/payout/consumer"
	dao8 "github.com/epifi/gamma/cx/payout/dao"
	processor3 "github.com/epifi/gamma/cx/payout/processor"
	"github.com/epifi/gamma/cx/payout/processor/cash"
	"github.com/epifi/gamma/cx/payout/processor/fi_coins"
	dao10 "github.com/epifi/gamma/cx/priority_routing/dao"
	"github.com/epifi/gamma/cx/priority_routing/priority_routing_helper"
	"github.com/epifi/gamma/cx/priority_routing/routing_engine"
	processor7 "github.com/epifi/gamma/cx/priority_routing/routing_engine/processor"
	"github.com/epifi/gamma/cx/rate_limit"
	"github.com/epifi/gamma/cx/risk_ops"
	"github.com/epifi/gamma/cx/risk_ops/products"
	"github.com/epifi/gamma/cx/risk_ops/review"
	reviewActions "github.com/epifi/gamma/cx/risk_ops/review/actions"
	"github.com/epifi/gamma/cx/risk_ops/review/annotations"
	"github.com/epifi/gamma/cx/risk_ops/review/comments"
	"github.com/epifi/gamma/cx/risk_ops/transaction_review/actor"
	"github.com/epifi/gamma/cx/risk_ops/transaction_review/builder"
	"github.com/epifi/gamma/cx/risk_ops/transaction_review/categorizer"
	"github.com/epifi/gamma/cx/risk_ops/transaction_review/fetcher"
	transaction2 "github.com/epifi/gamma/cx/risk_ops/transaction_review/transaction"
	"github.com/epifi/gamma/cx/sherlock_auth"
	"github.com/epifi/gamma/cx/sherlock_banners"
	sbCollector "github.com/epifi/gamma/cx/sherlock_banners/collector"
	dao13 "github.com/epifi/gamma/cx/sherlock_banners/dao"
	sbHelper "github.com/epifi/gamma/cx/sherlock_banners/helper"
	"github.com/epifi/gamma/cx/sherlock_feedback"
	dao16 "github.com/epifi/gamma/cx/sherlock_feedback/dao"
	sherlockScripts "github.com/epifi/gamma/cx/sherlock_scripts"
	"github.com/epifi/gamma/cx/sherlock_user"
	dao11 "github.com/epifi/gamma/cx/sherlock_user/dao"
	provisioner2 "github.com/epifi/gamma/cx/sherlock_user/provisioner"
	sherlockUserWrapper "github.com/epifi/gamma/cx/sherlock_user/sherlock_user_wrapper"
	"github.com/epifi/gamma/cx/sprinklr"
	dao17 "github.com/epifi/gamma/cx/sprinklr/dao"
	"github.com/epifi/gamma/cx/ticket"
	consumer6 "github.com/epifi/gamma/cx/ticket/consumer"
	dao9 "github.com/epifi/gamma/cx/ticket/dao"
	helper7 "github.com/epifi/gamma/cx/ticket/helper"
	processor9 "github.com/epifi/gamma/cx/ticket/processor"
	"github.com/epifi/gamma/cx/user_issue_info"
	"github.com/epifi/gamma/cx/validation"
	"github.com/epifi/gamma/cx/watson"
	watsonCollector "github.com/epifi/gamma/cx/watson/collector"
	watsonConsumer "github.com/epifi/gamma/cx/watson/consumer"
	helper10 "github.com/epifi/gamma/cx/watson/consumer/helper"
	watsonTicketEventConsumer "github.com/epifi/gamma/cx/watson/consumer/ticket_event"
	watsonDao "github.com/epifi/gamma/cx/watson/dao"
	watsonHelper "github.com/epifi/gamma/cx/watson/helper"
	watsonIngestEvent "github.com/epifi/gamma/cx/watson/ingest_event"
	"github.com/epifi/gamma/cx/watson/mock_client"
	cxTypes "github.com/epifi/gamma/cx/wire/types"
	wireTypes "github.com/epifi/gamma/cx/wire/types"
	"github.com/epifi/gamma/featurestore"
	featureStoreWire "github.com/epifi/gamma/featurestore/wire"
	iftFileGen "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseConf "github.com/epifi/gamma/pkg/feature/release/config"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	devActionRegistryProcessors "github.com/epifi/gamma/scripts/dev_actions/actions"

	"github.com/opensearch-project/opensearch-go"

	investmentAnalyserPb "github.com/epifi/gamma/api/analyser/investment"
	consentPb "github.com/epifi/gamma/api/consent"
	epfPb "github.com/epifi/gamma/api/insights/epf"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	npsPb "github.com/epifi/gamma/api/nps/developer"
	spDynamicElementPb "github.com/epifi/gamma/api/salaryprogram/dynamic_ui_element"
	sgApplicationPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	sgApiGwDbsPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/dbstate"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	escalFederalVgPb "github.com/epifi/gamma/api/vendorgateway/cx/federal"
	rpFederalPb "github.com/epifi/gamma/api/vendornotification/openbanking/recurringpayment/federal"
	devActionInsights "github.com/epifi/gamma/cx/developer/actions/processor/insights"
	osPkg "github.com/epifi/gamma/pkg/opensearch"
)

func InitializeAuthenticationService(ctx context.Context, conf *config.Config) *sherlock_auth.CognitoAuthService {
	wire.Build(
		getCognitoIDPClient,
		getCognitoUserPoolId,
		getAuthValidationConfig,
		sherlock_auth.NewCognitoAuthService,
	)
	return &sherlock_auth.CognitoAuthService{}
}

func InitializeAuthorizationService(casbinClient casbinPb.CasbinClient) *sherlock_auth.CasbinAuthorizationService {
	wire.Build(
		sherlock_auth.NewCasbinAuthorizationService,
	)
	return &sherlock_auth.CasbinAuthorizationService{}
}

func InitSherlockActorActivityService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, authClient authPb.AuthClient,
	actorActivityClient aaClient.ActorActivityClient, actorClient actorPb.ActorClient, piClient piPb.PiClient, usersClient userPb.UsersClient,
	pClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient, savingsClient savingsPb.SavingsClient,
	depositClient depositPb.DepositClient, investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient,
	p2pInvestmentClient p2investmentPb.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient,
	usstocksOrderManagerClient orderManagerPb.OrderManagerClient, accountBalanceClinet accountBalancePb.BalanceClient, ffClient ffPb.FireflyClient) *handler.SherlockActorActivityHandler {
	wire.Build(
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(usecase.NewSherlockActorActivityServiceImpl, wire.Bind(new(usecase.SherlockActorActivity), new(*usecase.SherlockActorActivityServiceImpl))),
		wire.NewSet(repository.NewSherlockActorActivityRepoImpl, wire.Bind(new(repository.SherlockActorActivityRepository), new(*repository.SherlockActorActivityRepoImpl))),
		wire.NewSet(helper3.NewDataCollectorHelper, wire.Bind(new(helper3.IDataCollectorHelper), new(*helper3.DataCollectorHelper))),
		handler.NewSherlockActorActivityHandler,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &handler.SherlockActorActivityHandler{}
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeProfileService(conf *config.Config, db cmdTypes.SherlockPGDB, savingsClient savingsPb.SavingsClient,
	userClient userPb.UsersClient, actorClient actorPb.ActorClient, cxS3Client cxTypes.CxS3Client, ticketServiceClient ticketPb.TicketClient,
	vendorMappingClient vendormapping.VendorMappingServiceClient, cxConf *cxGenConf.Config,
	externalAccountsClient extacct.ExternalAccountsClient, tieringClient beTieringPb.TieringClient, bcClient bankCustPb.BankCustomerServiceClient, onbClient onboarding2.OnboardingClient,
	commsClient commsPb.CommsClient, operationalStatusClient operStatusPb.OperationalStatusServiceClient, chatClient chatPb.ChatsClient, piClient piPb.PiClient, pClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient,
	depositClient depositPb.DepositClient, investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient,
	p2pInvestmentClient p2investmentPb.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient,
	usstocksOrderManagerClient orderManagerPb.OrderManagerClient, accountBalanceClinet accountBalancePb.BalanceClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient, accountPiRelationClient accountPIPb.AccountPIRelationClient,
	payClient payPb.PayClient, payCxClient payCxPb.CXClient, orderCxClient cx.CXClient, ffClient ffPb.FireflyClient) *profile.Service {
	wire.Build(
		helper.NewCustomerIdentifier,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		cxTypes.CxS3ClientProvider,
		profile.NewService,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(helper3.NewDataCollectorHelper, wire.Bind(new(helper3.IDataCollectorHelper), new(*helper3.DataCollectorHelper))),
		wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier)),
		wire.NewSet(helper3.NewTransactionsDataCollectorHelper, wire.Bind(new(helper3.ITransactionsDataCollectorHelper), new(*helper3.TransactionsDataCollectorHelper))),
	)
	return &profile.Service{}
}

func InitializeCustomerIdentifier(userClient userPb.UsersClient, actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient, vmClient vendormapping.VendorMappingServiceClient) *helper.CustomerIdentifier {
	wire.Build(
		helper.NewCustomerIdentifier,
	)
	return &helper.CustomerIdentifier{}
}

func IntializeCustomerCardService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB,
	userClient userPb.UsersClient, cpClient cardPb.CardProvisioningClient, ccClient ccPb.CardControlClient, tieringClient beTieringPb.TieringClient,
	payClient payPb.PayClient) *card.Service {
	wire.Build(
		card.NewService,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &card.Service{}
}

func InitializeAccountService(db cmdTypes.SherlockPGDB, extAcctClient extacct.ExternalAccountsClient,
	commsClient commsPb.CommsClient, cxConfig *config.Config, genConf *cxGenConf.Config,
	vgDepositClient vgDepositPb.DepositClient, bcClient bankCustPb.BankCustomerServiceClient, watsonClient watsonPb.WatsonClient,
	ticketClient ticketPb.TicketClient, actorClient actorPb.ActorClient, piClient piPb.PiClient, usersClient userPb.UsersClient,
	pClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient, savingsClient savingsPb.SavingsClient,
	depositClient depositPb.DepositClient, investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient,
	p2pInvestmentClient p2investmentPb.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient,
	usstocksOrderManagerClient orderManagerPb.OrderManagerClient, accountBalanceClinet accountBalancePb.BalanceClient, authClient authPb.AuthClient, ffClient ffPb.FireflyClient, saClosureClient saClosurePb.SavingsAccountClosureClient) *account.Service {
	wire.Build(
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		account.NewService,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(helper3.NewDataCollectorHelper, wire.Bind(new(helper3.IDataCollectorHelper), new(*helper3.DataCollectorHelper))),
	)
	return &account.Service{}
}
func InitializeWatsonClientService() *mock_client.MockWatsonClientService {
	wire.Build(mock_client.NewMockWatsonClientService)
	return &mock_client.MockWatsonClientService{}
}

func WatsonIncidentInfoCollectorFactoryProvider(
	simulatorWatsonClientClient simulatorWcPb.WatsonClientClient,
	mockCxWatsonClient mockWatsonClient.MockWatsonClientServiceClient,
	watsonSavingsClient savingsWatsonClient.WatsonClient,
	watsonOnboardingClient onboardingWatsonClient.WatsonClient,
	payIncidentManagerClient payIncidentManagerPb.PayIncidentManagerClient,
	investmentWatsonClient investmentWatsonPb.WatsonClient,
	watsonDepositsClient depositWatsonPb.WatsonClient,
	p2pWatsonClient p2pIncidentManagerPb.IncidentManagerClient,
	errorActivityWatsonClient watsonInfoProviderPb.WatsonInfoProviderClient,
	manualTicketStageWiseCommsClient stageWiseCommsPb.ManualTicketStageWiseCommsClient,
) watsonCollector.IWatsonIncidentInfoCollectorFactory {
	WatsonIncidentInfoCollectorFactory := watsonCollector.NewWatsonIncidentInfoCollectorFactory()
	// To register use: WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.Service_<service>, <service>Client)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_SIMULATOR_GRPC_SERVICE, simulatorWatsonClientClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_CX_SERVICE, mockCxWatsonClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_SAVINGS_SERVICE, watsonSavingsClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_ONBOARDING_SERVICE, watsonOnboardingClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_ORDER_SERVICE, payIncidentManagerClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_INVESTMENT_SERVICE, investmentWatsonClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_DEPOSIT_SERVICE, watsonDepositsClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_P2P_INVESTMENT_SERVICE, p2pWatsonClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_ERROR_ACTIVITY_SERVICE, errorActivityWatsonClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, manualTicketStageWiseCommsClient)
	return WatsonIncidentInfoCollectorFactory
}

func InitializeWatsonService(db cmdTypes.SherlockPGDB, incidentReportPub cxTypes.WatsonIncidentReportingPublisher,
	incidentResolvePub cxTypes.WatsonIncidentResolutionPublisher,
	simulatorWatsonClientClient simulatorWcPb.WatsonClientClient,
	mockCxWatsonClient mockWatsonClient.MockWatsonClientServiceClient,
	watsonSavingsClient savingsWatsonClient.WatsonClient,
	watsonOnboardingClient onboardingWatsonClient.WatsonClient,
	payIncidentManagerClient payIncidentManagerPb.PayIncidentManagerClient,
	investmentWatsonClient investmentWatsonPb.WatsonClient,
	watsonDepositsClient depositWatsonPb.WatsonClient,
	p2pWatsonClient p2pIncidentManagerPb.IncidentManagerClient,
	watsonClient watsonPb.WatsonClient, conf *config.Config,
	ticketEventForWatsonPub cxTypes.WatsonTicketEventPublisher,
	errorActivityWatsonClient watsonInfoProviderPb.WatsonInfoProviderClient,
	manualTicketStageWiseCommsClient stageWiseCommsPb.ManualTicketStageWiseCommsClient) *watson.Service {
	wire.Build(
		WatsonIncidentInfoCollectorFactoryProvider,
		watsonIngestEvent.NewReportEvent,
		watsonIngestEvent.NewResolveEvent,
		wire.NewSet(watsonIngestEvent.NewIngestEventFactory, wire.Bind(new(watsonIngestEvent.IIngestEventFactory), new(*watsonIngestEvent.IngestEventFactory))),
		wire.NewSet(watsonDao.NewIncidentDao, wire.Bind(new(watsonDao.IIncidentDao), new(*watsonDao.IncidentDao))),
		wire.NewSet(watsonDao.NewIncidentTicketDetailDao, wire.Bind(new(watsonDao.IIncidentTicketDetailDao), new(*watsonDao.IncidentTicketDetailDao))),
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		wire.NewSet(manager2.NewIssueCategoryManagerImpl, wire.Bind(new(manager2.IssueCategoryManager), new(*manager2.IssueCategoryManagerImpl))),
		watson2.NewWatsonHelper,
		watson2.NewWatsonHelperV2,
		wire.NewSet(watson2.NewHelperFactoryImpl, wire.Bind(new(watson2.HelperFactory), new(*watson2.HelperFactoryImpl))),
		watsonHelper.NewValidator,
		watsonHelper.NewValidatorV2,
		wire.NewSet(watsonHelper.NewHelperFactoryImpl, wire.Bind(new(watsonHelper.RequestValidatorFactory), new(*watsonHelper.RequestValidatorFactoryImpl))),
		watson.NewService,
		getWatsonConfig,
	)
	return &watson.Service{}
}

func InitializeKycService(kycClient kycPb.KycClient, vkycClient vkycPb.VKYCClient,
	config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, livenessClient livenessPb.LivenessClient,
	actorClient actorPb.ActorClient, usersClient userPb.UsersClient, obClient onboarding2.OnboardingClient,
	bcClient bankCustPb.BankCustomerServiceClient, panClient pan.PanClient, compClient compliancePb.ComplianceClient) *kyc.Service {
	wire.Build(
		kyc.NewService,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		getKYCConfig,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &kyc.Service{}
}

func InitializeTransactionService(conf *config.Config, orderTxnClient cx.CXClient,
	genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, txnCategorizerClient categorizerPb.TxnCategorizerClient,
	payCxClient payCxPb.CXClient, payClient payPb.PayClient, accountPiClient accountPIPb.AccountPIRelationClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient, actorClient actorPb.ActorClient, piClient piPb.PiClient,
	usersClient userPb.UsersClient, pClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient, savingsClient savingsPb.SavingsClient,
	depositClient depositPb.DepositClient, investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient,
	p2pInvestmentClient p2investmentPb.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient,
	usstocksOrderManagerClient orderManagerPb.OrderManagerClient, accountBalanceClinet accountBalancePb.BalanceClient,
	client recurringPaymentPb.RecurringPaymentServiceClient, ffClient ffPb.FireflyClient) *transaction.Service {
	wire.Build(
		getTransactionConf,
		getOrderConfig,
		transaction.NewService,
		wire.NewSet(helper3.NewTransactionsDataCollectorHelper, wire.Bind(new(helper3.ITransactionsDataCollectorHelper), new(*helper3.TransactionsDataCollectorHelper))),
		wire.NewSet(helper3.NewDataCollectorHelper, wire.Bind(new(helper3.IDataCollectorHelper), new(*helper3.DataCollectorHelper))),
		helper3.WireTxnCategoryHelperSet,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &transaction.Service{}
}

func InitializeUserRequestsService(conf *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient,
	savingsClient savingsPb.SavingsClient, statementClient statement.AccountStatementClient, eventBroker events.Broker, commsClient commsPb.CommsClient) *userReq.Service {
	wire.Build(
		account_details_collector.NewSavingsAccountCollector,
		wire.NewSet(account_details_collector.NewAccountDetailsFactory, wire.Bind(new(account_details_collector.AccountDetailsFactory),
			new(*account_details_collector.AccountDetailsFactoryImpl))),
		userReq.NewUserRequestsService,
		getSherlockUserRequestsConfig,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &userReq.Service{}
}

func InitializeTicketValidation(freshdeskClient freshdesk.FreshdeskClient, redisClient cxTypes.CxRedisStore, conf *config.Config) *validation.TicketValidation {
	wire.Build(
		validation.NewTicketValidation,
		NewRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &validation.TicketValidation{}
}

func InitializeCustomerAuth(commsClient commsPb.CommsClient, db cmdTypes.SherlockPGDB, confg *cxGenConf.Config, conf *config.Config,
	userClient userPb.UsersClient, chatsClient chatPb.ChatsClient,
	ticketPub cxTypes.FreshdeskTicketPublisher, orderClient order.OrderServiceClient, bcClient bankCustPb.BankCustomerServiceClient,
	onboardingClient onboarding2.OnboardingClient, redisClient cxTypes.CxRedisStore) *customer_auth.CustomerAuth {
	wire.Build(
		customer_auth.NewCustomerAuth,
		verifier.NewDOBVerifier,
		verifier.NewMobilePromptVerifier,
		verifier.NewPANVerifier,
		verifier.NewEmailVerifier,
		verifier.NewPinCodeVerifier,
		verifier.NewTxnVerifier,
		verifier.NewFathersNameVerifier,
		verifier.NewMothersNameVerifier,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		NewRedisClient,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		getTransactionConf,
		getCustomerAuthConf,
		wire.NewSet(customer_auth.NewVerificationFactory, wire.Bind(new(customer_auth.IVerificationFactory), new(*customer_auth.VerificationFactory))),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCallbackResponseDAO, wire.Bind(new(dao3.ICustomerAuthenticationCallbackResponsesDAO), new(*dao3.CallbackResponseDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewAuthFactorRetryLogsDAO, wire.Bind(new(dao3.IAuthFactorRetryLogsDao), new(*dao3.AuthFactorRetryLogsDAO))),
		wire.NewSet(dao4.NewFreshdeskUpdateEventDAO, wire.Bind(new(dao4.IFreshdeskUpdateEventDAO), new(*dao4.FreshdeskUpdateEventDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &customer_auth.CustomerAuth{}
}

func InitializeAuthEngine(db cmdTypes.SherlockPGDB, genConf *cxGenConf.Config, authFactorRetryLimit *config.AuthFactorRetryLimit,
	client userPb.UsersClient) *auth_engine.AuthEngine {
	wire.Build(
		auth_engine.NewAuthEngine,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &auth_engine.AuthEngine{}
}

func InitializeLandingPageService(userClient userPb.UsersClient, actorClient actorPb.ActorClient,
	ozonetelClient ozonetel.OzonetelClient, chatClient chatPb.ChatsClient,
	db cmdTypes.SherlockPGDB, authClient authPb.AuthClient,
	vmClient vendormapping.VendorMappingServiceClient, conf *config.Config, genConf *cxGenConf.Config,
	customerProfileClient dcProfilePb.CustomerProfileClient) *landing_page.Service {
	wire.Build(
		wire.NewSet(dao11.NewSherlockUserInfoDao, wire.Bind(new(dao11.ISherlockUserInfoDao), new(*dao11.SherlockUserInfoDao))),
		wire.NewSet(dao11.NewSherlockUserRoleDao, wire.Bind(new(dao11.ISherlockUserRoleDao), new(*dao11.SherlockUserRoleDao))),
		wire.NewSet(sherlockUserWrapper.NewSherlockUserDao, wire.Bind(new(sherlockUserWrapper.ISherlockUser), new(*sherlockUserWrapper.SherlockUser))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		wire.NewSet(dao12.NewCallDetailsDao, wire.Bind(new(dao12.ICallDetailsDao), new(*dao12.CallDetailsDao))),
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		wire.NewSet(dao23.NewUserQueryLogDao, wire.Bind(new(dao23.UserQueryLogDao), new(*dao23.UserQueryLogDaoImpl))),
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		lpAct.NewUserProfileGenerator,
		wire.NewSet(lpAct.NewUserDetailsGridGeneratorFactoryImpl, wire.Bind(new(lpAct.UserDetailsGridGeneratorFactory), new(*lpAct.UserDetailsGridGeneratorFactoryImpl))),
		landing_page.NewService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &landing_page.Service{}
}

func InitializeChatInitInformationService(db cmdTypes.SherlockPGDB, ticketPub cxTypes.FreshdeskTicketPublisher, gconf *cxGenConf.Config, conf *config.Config,
	vmClient vendormapping.VendorMappingServiceClient, redisClient cxTypes.CxRedisStore, onbClient onboarding2.OnboardingClient, actorClient actorPb.ActorClient, userClient userPb.UsersClient,
	salaryProgramClient salaryprogram.SalaryProgramClient, authClient authPb.AuthClient, userGroupClient userGroupPb.GroupClient, freshChatClient vgFcPb.FreshchatClient, fdClient freshdesk.FreshdeskClient,
	eventBroker events.Broker, savingsClient savingsPb.SavingsClient, operationalStatusClient operStatusPb.OperationalStatusServiceClient, balanceClient accountBalancePb.BalanceClient, chatClient chatPb.ChatsClient, vgNuggetClient nugget.NuggetChatbotServiceClient) *chat.Service {
	wire.Build(
		processor7.NewLowPriorityRuleProcessor,
		processor7.NewHighPriorityRuleProcessor,
		processor7.NewCurrentlyOnbRuleProcessor,
		processor7.NewSalaryProgramUsersRuleProcessor,
		chat.NewService,
		idgen.NewClock,
		idgen.WireSet,
		wire.NewSet(dao4.NewFreshdeskUpdateEventDAO, wire.Bind(new(dao4.IFreshdeskUpdateEventDAO), new(*dao4.FreshdeskUpdateEventDAO))),
		wire.NewSet(routing_engine.NewRoutingEngine, wire.Bind(new(routing_engine.IRoutingEngine), new(*routing_engine.RoutingEngine))),
		wire.NewSet(priority_routing_helper.NewPriorityDataHelper, wire.Bind(new(priority_routing_helper.IHelper), new(*priority_routing_helper.Helper))),
		wire.NewSet(dao10.NewUserPriorityPropertiesDao, wire.Bind(new(dao10.IUserPriorityPropertiesDAO), new(*dao10.UserPriorityPropertiesDao))),
		wire.NewSet(processor7.NewRuleFactory, wire.Bind(new(processor7.IFactory), new(*processor7.Factory))),
		release.EvaluatorWireSet,
		wire.NewSet(helper8.NewFreshchatUserMappingHelper, wire.Bind(new(helper8.IFreshchatUserMappingHelper), new(*helper8.FreshchatUserMappingHelper))),
		wire.NewSet(dao4.NewFreshchatUserMappingDao, wire.Bind(new(dao4.IFreshchatUserMappingDao), new(*dao4.FreshchatUserMappingDao))),
		getPriorityRoutingConfig,
		getDynFeatureReleaseConfig,
		IsNewOperationalStatusAPIEnabled,
		accountstatus.ProvideFetcherImplementation,
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
	)
	return &chat.Service{}
}

func InitializeAuditLogService(db cmdTypes.SherlockPGDB, conf *config.Config) *audit_log.Service {
	wire.Build(
		audit_log.NewAuditLogService,
		getAuditLog,
		wire.NewSet(dao5.NewAuditLogDao, wire.Bind(new(dao5.IAuditLogDao), new(*dao5.AuditLogDao))),
	)
	return &audit_log.Service{}
}

// config: {"s3Client": "S3EventConsumerConfig().BucketName()"}
func InitializeConsumerService(db cmdTypes.SherlockPGDB, fdClient freshdesk.FreshdeskClient, contactPub cxTypes.FreshdeskContactPublisher,
	userClient userPb.UsersClient, actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient, vmClient vendormapping.VendorMappingServiceClient,
	ticketClient ticketPb.TicketClient, conf *config.Config, genConf *cxGenConf.Config,
	addNotePub cxTypes.DisputeAddNoteTicketPublisher, bcClient bankCustPb.BankCustomerServiceClient, onboardingClient onboarding2.OnboardingClient, updateTicketPub cxTypes.UpdateTicketPublisher,
	disputeExtPub cxTypes.DisputeExternalPublisher, pClient paymentPb.PaymentClient, piClient piPb.PiClient, orderClient orderPb.OrderServiceClient,
	userGroupClient userGroupPb.GroupClient, createDisputeTicketPub cxTypes.DisputeCreateTicketPublisher, savingsClient savingsPb.SavingsClient,
	commsClient commsPb.CommsClient, vgDisputeClient dispute2.DisputeClient, s3Client awss3.S3Client, createTicketEventPublisher cxTypes.CreateTicketEventPublisher, federalVgClient escalFederalVgPb.FederalEscalationServiceClient) *consumer.Service {
	wire.Build(
		consumer.NewService,
		s3_event.SummarizationOutputProcessorWireSet,
		s3_event.DsModelProcessorFactoryWireSet,
		consumer.NewEventFactory,
		ticket_event.NewUpdateCfService,
		ticket_event.NewUpdateRequesterService,
		ticket_event.NewCreateTicketService,
		ticket_event.NewUpdateTicketService,
		ticket_event.NewUpdateRequesterForSourceService,
		contact_event.NewDeleteContactService,
		update_ticket_event.NewTicketRawBulkUpdateProcessor,
		update_ticket_event.NewCallDetailsTicketUpdateProcessor,
		update_ticket_event.NewIssueResolutionFeedbackTicketUpdateProcessor,
		update_ticket_event.NewExpectedResolutionDateUpdateProcessor,
		update_ticket_event.NewDisputeDetailsUpdateProcessor,
		update_ticket_event.NewSherlockTicketInfoUpdateProcessor,
		update_ticket_event.NewUpdateTicketFromSprinklrEventProcessor,
		create_ticket_event.NewCreateTicketFromSprinklrEventProcessor,
		update_ticket_event.NewTicketRawUpdateEventProcessor,
		create_ticket_event.NewDMPCorrespondenceCreateTicketEventProcessor,
		update_ticket_event.NewCxTicketUpdateEventProcessor,
		create_ticket_event.NewAddPrivateNoteEventProcessor,
		create_ticket_event.NewCxTicketEventProcessor,
		wire.NewSet(issue_resolution_feedback.NewIssueResolutionFeedbackTicketUpdateFactory, wire.Bind(new(issue_resolution_feedback.IIssueResolutionFeedbackTicketUpdateFactory), new(*issue_resolution_feedback.IssueResolutionFeedbackTicketUpdateFactory))),
		processor10.NewDisputeFeedbackTicketUpdateProcessor,
		wire.NewSet(dao9.NewBulkTicketJobDao, wire.Bind(new(dao9.IBulkTicketJobDao), new(*dao9.BulkTicketJobDao))),
		wire.NewSet(dao9.NewTicketFailureLogDao, wire.Bind(new(dao9.ITicketFailureLogDao), new(*dao9.TicketFailureLogDao))),
		wire.NewSet(dao17.NewSprinklrCaseDetailsDao, wire.Bind(new(dao17.ISprinklrCaseDetailsDao), new(*dao17.SprinklrCaseDetailsDao))),
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		wire.Bind(new(consumer.IUpdateTicketFactory), new(*consumer.EventFactory)),
		wire.Bind(new(consumer.ICreateTicketFactory), new(*consumer.EventFactory)),
		wire.NewSet(dao4.NewFreshdeskUpdateEventDAO, wire.Bind(new(dao4.IFreshdeskUpdateEventDAO), new(*dao4.FreshdeskUpdateEventDAO))),
		wire.NewSet(dao11.NewSherlockUserInfoDao, wire.Bind(new(dao11.ISherlockUserInfoDao), new(*dao11.SherlockUserInfoDao))),
		wire.NewSet(dao11.NewSherlockUserRoleDao, wire.Bind(new(dao11.ISherlockUserRoleDao), new(*dao11.SherlockUserRoleDao))),
		wire.NewSet(sherlockUserWrapper.NewSherlockUserDao, wire.Bind(new(sherlockUserWrapper.ISherlockUser), new(*sherlockUserWrapper.SherlockUser))),
		wire.NewSet(dao6.NewDisputeIdToTicketIdMappingDao, wire.Bind(new(dao6.IDisputeIdToTicketIdMappingDao), new(*dao6.DisputeIdToTicketIdMappingDao))),
		wire.NewSet(dao6.NewDisputeTicketLogDao, wire.Bind(new(dao6.IDisputeTicketLogDao), new(*dao6.DisputeTicketLogDao))),
		wire.NewSet(ticketEventHelper.NewFreshdeskAutomationHelper, wire.Bind(new(ticketEventHelper.IFreshdeskAutomationHelper), new(*ticketEventHelper.FreshdeskAutomationHelper))),
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		getCallConfig,
		getFreshChatConfig,
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		helper2.NewDisputeHelper,
		getDynDispute,
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(dao6.NewDisputeConfigDao, wire.Bind(new(dao6.IDisputeConfigDao), new(*dao6.DisputeConfigDao))),
		wire.NewSet(dao6.NewFederalDmpDisputeDetailDao, wire.Bind(new(dao6.IFederalDmpDisputeDetailDao), new(*dao6.FederalDmpDisputeDetailDao))),
		wire.Bind(new(helper2.IDisputeHelper), new(*helper2.DisputeHelper)),
		escalation_event.NewEscalationEventProcessor,
		escalation_event.NewEscalationCreationEventProcessor,
		wire.NewSet(dao24.NewEscalationUpdateDao, wire.Bind(new(dao24.EscalationUpdateDAO), new(*dao24.EscalationUpdateDao))),
		wire.NewSet(dao24.NewEscalationDao, wire.Bind(new(dao24.EscalationDAO), new(*dao24.EscalationDao))),
		wire.NewSet(dao24.NewEscalationAttachmentDao, wire.Bind(new(dao24.EscalationAttachmentDAO), new(*dao24.EscalationAttachmentDao))),
	)
	return &consumer.Service{}
}

func InitializeOnboardingService(kycClient kycPb.KycClient, dbconn cmdTypes.SherlockPGDB,
	livenessClient livenessPb.LivenessClient, obClient onboarding2.OnboardingClient,
	conf *config.Config, userGroupClient userGroupPb.GroupClient, authClient authPb.AuthClient,
	actorClient actorPb.ActorClient, usersClient userPb.UsersClient, gconf *cxGenConf.Config,
	productClient product.ProductClient, salaryClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient) *onboarding.Service {
	wire.Build(
		onboarding.NewService,
		getOnboardingStageDetailsMapping,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &onboarding.Service{}
}

func InitializeWealthOnboardingService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient, wonbClient woPb.WealthOnboardingClient, woCxClient woCxPb.WealthCxServiceClient) *wealth_onboarding.Service {
	wire.Build(
		wealth_onboarding.NewService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &wealth_onboarding.Service{}
}

func InitializeInvestmentService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient,
	catalogClient investmentCatalogPb.CatalogManagerClient, rmsClient manager.RuleManagerClient, orderManagerClient investmentOrder.OrderManagerClient,
	paymentHandlerClient phPb.PaymentHandlerClient, wobClient woPb.WealthOnboardingClient) *cxInvexstment.Service {

	wire.Build(
		cxInvexstment.NewInvestmentService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &cxInvexstment.Service{}
}

func InitializeUsStockService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient,
	celestialClient celestialPb.CelestialClient, accountManagerClient accountManagerPb.AccountManagerClient,
	OrderManagerClient orderManagerPb.OrderManagerClient, PortfolioManagerClient portfolio.PortfolioManagerClient) *usstocks.Service {
	wire.Build(
		usstocks.NewUsStocksInvestmentService,
		getUsStocksOpsConfig,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &usstocks.Service{}
}

func InitializeAuthCallbackService(db cmdTypes.SherlockPGDB, conf *config.Config, genConf *cxGenConf.Config,
	freshdeskClient freshdesk.FreshdeskClient, redisClient cxTypes.CxRedisStore, userClient userPb.UsersClient) *customer_auth.AuthCallback {
	wire.Build(
		customer_auth.NewAuthCallback,
		getCustomerAuthConf,
		getAuthFactoryRetryLimit,
		getSherlockConf,
		getSecrets,
		auth_engine.NewAuthEngine,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		validation.NewTicketValidation,
		NewRedisClient,
		getHttpClientForSherlock,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wire.Bind(new(validation.ITicketValidation), new(*validation.TicketValidation)),
		wire.Bind(new(customer_auth.IHttpClient), new(*http.Client)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCallbackResponseDAO, wire.Bind(new(dao3.ICustomerAuthenticationCallbackResponsesDAO), new(*dao3.CallbackResponseDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewAuthFactorRetryLogsDAO, wire.Bind(new(dao3.IAuthFactorRetryLogsDao), new(*dao3.AuthFactorRetryLogsDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &customer_auth.AuthCallback{}
}

func InitializePiService(piClient piPb.PiClient, acPiClient accountPIPb.AccountPIRelationClient,
	upiClient upiPb.UPIClient, cxUpiClient cx2.UpiCXClient, db cmdTypes.SherlockPGDB, conf *config.Config, genConf *cxGenConf.Config,
	userClient userPb.UsersClient) *payment_instruments.Service {
	wire.Build(
		payment_instruments.NewService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &payment_instruments.Service{}
}

func InitializeRewardsService(db cmdTypes.SherlockPGDB, conf *config.Config, genConf *cxGenConf.Config,
	rewardOffersClient rewardOfferPb.RewardOffersClient, offersClient casperPb.OfferListingServiceClient,
	rewardsClient rewardsPb.RewardsGeneratorClient, offerRedemptionClient redemption.OfferRedemptionServiceClient,
	exchangerOffersClient exchangerPb.ExchangerOfferServiceClient, actorServiceClient actorPb.ActorClient,
	usersClient userPb.UsersClient, rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient, externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient) *rewards.Service {
	wire.Build(
		rewards.NewService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &rewards.Service{}
}

func InitializeDisputeHelper(db cmdTypes.SherlockPGDB, disputeExtPub cxTypes.DisputeExternalPublisher, pClient paymentPb.PaymentClient,
	piClient piPb.PiClient, orderClient orderPb.OrderServiceClient, actorClient actorPb.ActorClient,
	userClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient, gconf *cxGenConf.Config,
	createDisputeTicketPub cxTypes.DisputeCreateTicketPublisher, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient, savingsClient savingsPb.SavingsClient,
	commsClient commsPb.CommsClient, vgDisputeClient dispute2.DisputeClient) *helper2.DisputeHelper {
	wire.Build(
		helper2.NewDisputeHelper,
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		getDynDispute,
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(dao6.NewDisputeConfigDao, wire.Bind(new(dao6.IDisputeConfigDao), new(*dao6.DisputeConfigDao))),
		wire.NewSet(dao6.NewFederalDmpDisputeDetailDao, wire.Bind(new(dao6.IFederalDmpDisputeDetailDao), new(*dao6.FederalDmpDisputeDetailDao))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
	)
	return &helper2.DisputeHelper{}
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeDisputeService(db cmdTypes.SherlockPGDB,
	disPub cxTypes.DisputePublisher, aClient actorPb.ActorClient, conf *config.Config, genConfig *cxGenConf.Config, broker events.Broker, upiClient upiPb.UPIClient,
	usersClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient,
	updateTicketPub cxTypes.DisputeUpdateTicketPublisher, disputeCreateTicketPub cxTypes.DisputeCreateTicketPublisher, createTicketPub cxTypes.CreateTicketPublisher,
	chatClient chatPb.ChatsClient, vmClient vendormapping.VendorMappingServiceClient,
	vgDisputeClient dispute2.DisputeClient, cxS3Client cxTypes.CxS3Client, freshdeskUpdateTicketPub cxTypes.UpdateTicketPublisher, bcClient bankCustPb.BankCustomerServiceClient,
	disputeExtPub cxTypes.DisputeExternalPublisher, pClient paymentPb.PaymentClient,
	piClient piPb.PiClient, orderClient orderPb.OrderServiceClient, savingsClient savingsPb.SavingsClient, commsClient commsPb.CommsClient, redisClient cxTypes.CxRedisStore) *dispute.Service {
	wire.Build(
		NewRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wire.NewSet(cache2.NewDisputeIdempotencyCacheImpl, wire.Bind(new(cache2.DisputeIdempotency), new(*cache2.DisputeIdempotencyCacheImpl))),
		dispute.NewService,
		processor.NewEscalateDisputeToNPCI,
		processor.NewEscalateDisputeToFreshDesk,
		processor.NewFederalBankDisputeProcessor,
		cxTypes.CxS3ClientProvider,
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		getFeatureReleaseConfig,
		getDynDispute,
		getDispute,
		wire.NewSet(helper6.NewUDIRHelper, wire.Bind(new(helper6.IUDIRHelper), new(*helper6.UDIRHelper))),
		wire.NewSet(dao6.NewDisputeConfigDao, wire.Bind(new(dao6.IDisputeConfigDao), new(*dao6.DisputeConfigDao))),
		wire.NewSet(dao6.NewDisputeIdToTicketIdMappingDao, wire.Bind(new(dao6.IDisputeIdToTicketIdMappingDao), new(*dao6.DisputeIdToTicketIdMappingDao))),
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(dao6.NewFederalDmpDisputeDetailDao, wire.Bind(new(dao6.IFederalDmpDisputeDetailDao), new(*dao6.FederalDmpDisputeDetailDao))),
		wire.NewSet(dao6.NewDisputeCorrespondenceDetailDao, wire.Bind(new(dao6.IDisputeCorrespondenceDetailDao), new(*dao6.DisputeCorrespondenceDetailDao))),
		wire.NewSet(dao6.NewDisputeDocumentDetailDao, wire.Bind(new(dao6.IDisputeDocumentDetailDao), new(*dao6.DisputeDocumentDetailDao))),
		wire.NewSet(questionnaire_helper.NewQuestionnaireHelper, wire.Bind(new(questionnaire_helper.IQuestionnaireHelper), new(*questionnaire_helper.QuestionnaireHelper))),
		wire.NewSet(dispute.NewDisputeEscalationFactory, wire.Bind(new(dispute.IDisputeEscalationFactory), new(*dispute.DisputeEscalationFactory))),
		release.StaticConfEvaluatorWireSet,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		helper2.NewDisputeHelper,
		wire.Bind(new(helper2.IDisputeHelper), new(*helper2.DisputeHelper)),
	)
	return &dispute.Service{}
}

func InitializeDisputeConsumerService(db cmdTypes.SherlockPGDB,
	fClient freshdesk.FreshdeskClient, updateTicketPub cxTypes.DisputeUpdateTicketPublisher, createTicketPub cxTypes.DisputeCreateTicketPublisher,
	userClient userPb.UsersClient, addNotePub cxTypes.DisputeAddNoteTicketPublisher,
	actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient, upiClient upiPb.UPIClient,
	vmClient vendormapping.VendorMappingServiceClient, genConfig *cxGenConf.Config, vgDisputeClient dispute2.DisputeClient,
	bcClient bankCustPb.BankCustomerServiceClient, ticketClient ticketPb.TicketClient,
	disputeExtPub cxTypes.DisputeExternalPublisher, pClient paymentPb.PaymentClient,
	piClient piPb.PiClient, orderClient orderPb.OrderServiceClient, userGroupClient userGroupPb.GroupClient,
	savingsClient savingsPb.SavingsClient, commsClient commsPb.CommsClient, redisClient cxTypes.CxRedisStore) *consumer2.DisputeConsumerSvc {
	wire.Build(
		getDynDispute,
		NewRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wire.NewSet(cache2.NewDisputeIdempotencyCacheImpl, wire.Bind(new(cache2.DisputeIdempotency), new(*cache2.DisputeIdempotencyCacheImpl))),
		consumer2.NewDisputeConsumerSvc,
		processor.NewEscalateDisputeToNPCI,
		processor.NewEscalateDisputeToFreshDesk,
		processor.NewFederalBankDisputeProcessor,
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		wire.NewSet(dispute.NewDisputeEscalationFactory, wire.Bind(new(dispute.IDisputeEscalationFactory), new(*dispute.DisputeEscalationFactory))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(dao6.NewFederalDmpDisputeDetailDao, wire.Bind(new(dao6.IFederalDmpDisputeDetailDao), new(*dao6.FederalDmpDisputeDetailDao))),
		wire.NewSet(dao6.NewDisputeConfigDao, wire.Bind(new(dao6.IDisputeConfigDao), new(*dao6.DisputeConfigDao))),
		wire.NewSet(dao6.NewDisputeTicketLogDao, wire.Bind(new(dao6.IDisputeTicketLogDao), new(*dao6.DisputeTicketLogDao))),
		wire.NewSet(helper6.NewUDIRHelper, wire.Bind(new(helper6.IUDIRHelper), new(*helper6.UDIRHelper))),
		helper2.NewDisputeHelper,
		wire.Bind(new(helper2.IDisputeHelper), new(*helper2.DisputeHelper)),
	)
	return &consumer2.DisputeConsumerSvc{}
}

func CxCacheStorageProvider(redisStore cxTypes.CxRedisStore) cxTypes.CxCacheStore {
	cacheStorage := cache.NewRedisCacheStorage(redisStore)
	return cacheStorage
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeAppLogService(actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient, redisClient cxTypes.CxRedisStore,
	commsClient commsPb.CommsClient, conf *config.Config, cxS3Client cxTypes.CxS3Client,
	genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient) *app_log.Service {
	wire.Build(
		cxTypes.CxS3ClientProvider,
		app_log.NewAppLogService,
		wire.NewSet(dao7.NewAppLogDao, wire.Bind(new(dao7.IAppLogDao), new(*dao7.AppLogDao))),
		wire.NewSet(dao7.NewActorLogMappingDao, wire.Bind(new(dao7.IActorLogMappingDao), new(*dao7.ActorLogMappingDao))),
		wire.NewSet(dao7.NewLogIdDao, wire.Bind(new(dao7.ILogIdMap), new(*dao7.LogIdDao))),
		getAppLogsNotificationContent,
		getDynAppLog,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		CxCacheStorageProvider,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
	)
	return &app_log.Service{}
}

func InitializeCommsService(commsClient commsPb.CommsClient, conf *config.Config,
	genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient) *comms.Service {
	wire.Build(
		comms.NewService,
		getCommsConf,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &comms.Service{}
}

// config: {"s3Client": "Dispute().S3BucketName()"}
func InitializeDisputeJobService(
	userClient userPb.UsersClient, actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient,
	db cmdTypes.SherlockPGDB, fClient freshdesk.FreshdeskClient, updateTicketPub cxTypes.DisputeUpdateTicketPublisher, vgDisputeClient dispute2.DisputeClient,
	s3Client cxTypes.Disputes3Client, genConfig *cxGenConf.Config, sClient savingsPb.SavingsClient,
	commsClient commsPb.CommsClient, ticketClient ticketPb.TicketClient,
	issueClient issueResolutionFeedbackPb.IssueResolutionFeedbackServiceClient, updateTicketPublisher cxTypes.UpdateTicketPublisher,
	disputeExtPub cxTypes.DisputeExternalPublisher, pClient paymentPb.PaymentClient,
	piClient piPb.PiClient, orderClient orderPb.OrderServiceClient, userGroupClient userGroupPb.GroupClient,
	createDisputeTicketPub cxTypes.DisputeCreateTicketPublisher) *job.Service {
	wire.Build(
		cxTypes.Disputes3ClientProvider,
		getDynDispute,
		job.NewService,
		job.NewProcessorFactory,
		job.NewReverseUpdateProcessorFactory,
		reverse_update_processor.NewFreshdeskReverseUpdateProcessor,
		reverse_update_processor.NewFederalBankReverseUpdateProcessor,
		processor2.NewFreshdeskProcessor,
		processor2.NewSftpProcessor,
		wire.NewSet(dao6.NewFederalDmpDisputeDetailDao, wire.Bind(new(dao6.IFederalDmpDisputeDetailDao), new(*dao6.FederalDmpDisputeDetailDao))),
		wire.NewSet(dao6.NewDisputeIdToTicketIdMappingDao, wire.Bind(new(dao6.IDisputeIdToTicketIdMappingDao), new(*dao6.DisputeIdToTicketIdMappingDao))),
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(dao6.NewDisputeNotificationLogDao, wire.Bind(new(dao6.IDisputeNotificationLogDao), new(*dao6.DisputeNotificationLogDao))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		helper2.NewDisputeHelper,
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		wire.NewSet(dao6.NewDisputeConfigDao, wire.Bind(new(dao6.IDisputeConfigDao), new(*dao6.DisputeConfigDao))),
		wire.Bind(new(helper2.IDisputeHelper), new(*helper2.DisputeHelper)),
	)
	return &job.Service{}
}

func InitializeTicketSummaryService(fClient freshdesk.FreshdeskClient, chatClient chatPb.ChatsClient,
	actorClient actorPb.ActorClient, userClient userPb.UsersClient,
	vmClient vendormapping.VendorMappingServiceClient) *ticket_summary.TicketSummaryService {
	wire.Build(
		ticket_summary.NewTicketSummaryService,
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
	)
	return &ticket_summary.TicketSummaryService{}
}

func InitializeDbStatesService(commsDbStateClient commsDeveloper.CommsDbStatesClient,
	devActorEntityClient actorDeveloper.DevActorClient,
	devDepositClient depositDeveloper.DevDepositClient,
	devKycClient kycDeveloper.DevKYCClient,
	devUserClient userDeveloper.DevUserClient,
	orderDbStateClient orderDeveloper.DevClient,
	piDbStateClient piDeveloper.DevPaymentIntrumentClient,
	devCardClient cardDeveloper.CardDbStatesClient,
	devLivenessClient livenessDeveloper.DevLivenessClient,
	devSavingsClient savingsDeveloper.SavingsDbStatesClient,
	upiDbStateClient upiDeveloper.DevClient,
	devInapphelpClient inapphelpDeveloper.DevInapphelpClient,
	devCxClient cxDeveloper.DevCXClient,
	casbinDevClient casbinDeveloper.DevCasbinClient,
	insightsDevClient insightsDeveloper.DevInsightsClient,
	categorizerDevClient categorizerDeveloper.DevCategorizerClient,
	rewardsDevClient rewardsDeveloper.RewardsDevClient,
	devAuthClient authDeveloper.DevAuthClient,
	vmDevClient vmDeveloper.DevVendorMappingClient,
	timelineDevClient timelineDeveloper.DevTimelineClient,
	casperDevClient casperDeveloper.CasperDevClient,
	rmsDevClient rmsDeveloper.RMSDbStatesClient,
	devMerchantClient merchantDeveloper.DevMerchantClient,
	fitttDevClient fitttDeveloper.FITTTDbStatesClient,
	caDevClient caDeveloper.DevConnectedAccClient,
	devInappreferralClient inappreferralDeveloper.DevInAppReferralClient,
	woOnbDevClient wonbDeveloper.DevWealthOnboardingClient,
	investmentDBStateClient investDeveloper.MutualFundDbStatesClient,
	devrecurringPaymentClient recurrDeveloper.RecurringPaymentDevClient,
	enachDevClient enachDeveloper.EnachDevClient,
	devP2PInvestmentClient p2pDeveloper.DevP2PInvestmentClient,
	devSegmentClient segmentDeveloper.SegmentDbStatesClient,
	devNudgeClient nudgeDeveloper.NudgeDbStatesClient,
	salaryProgramDevClient spDeveloper.SalaryProgramDevClient,
	analyserDevClient analyserDeveloper.DevAnalyserClient,
	preApprovedDevClient plDeveloper.DevPreApprovedLoanClient,
	ccDevClient ffDeveloper.DevFireflyClient,
	celestialDevClient celestialDeveloper.DeveloperClient,
	riskDevClient riskDeveloper.DeveloperClient,
	scrnrDevClient screenerDeveloper.DeveloperClient,
	devAuthOrchClient authOrchDeveloper.DevOrchestratorClient,
	payDevClient payDeveloper.DevClient,
	usStocksDBStatesClient usstockDeveloper.DBStateClient,
	tieringDevClient tieringDeveloper.TieringDevServiceClient,
	amlDevClient amlDeveloper.AmlDevServiceClient,
	alfredDevClient alfredDeveloper.DeveloperClient,
	panDevClient panDeveloper.DeveloperClient,
	questDevClient questDeveloper.QuestDbStatesClient,
	upcomingTxnsDevClient upcomingtxnDeveloper.DevUpcomingTransactionsClient,
	healthEngineDevClient heDeveloper.HealthEngineDevClient,
	cmsDevClient cmsDeveloper.CmsDevClient,
	devBcClient bankCustDeveloper.DevBankCustClient,
	devOmegleClientToSGApiGatewayServer verifiPkg.DevOmegleClientToSGApiGatewayServer,
	devOmegleClientToOnboardingServer verifiPkg.DevOmegleClientToOnboardingServer,
	devVkycCallClientToSGApiGatewayServer verifiPkg.DevVkycCallClientToSGApiGatewayServer,
	devVkycCallClientToOnboardingServer verifiPkg.DevVkycCallClientToOnboardingServer,
	devEmpClient employmentDeveloper.DevEmploymentClient,
	collectionDeveloperClient collectionDeveloperPb.DeveloperClient,
	sgApiGwDbsClient sgApiGwDbsPb.DBStateServiceClient,
	tspUserServiceClient tspDbState.TspUserServiceClient,
	devClient developerClient.DeveloperClient,
	cxConf *cxGenConf.Config,
	casbinClient casbinPb.CasbinClient,
	leadClient devLeadsPb.DevLeadClient,
	crDevClient creditReportPb.DevCreditReportClient,
	accountsDevClient developer2.AccountsDbStatesClient,
	npsDevClient npsPb.NpsDbStatesClient,
	securitiesDevClient securitiesPb.SecuritiesDevClient,
	salaryEstDevClient salaryEstDevPb.DevSalaryEstimationClient,
) *db_states.Service {
	wire.Build(
		vkyccall.EpifiTechVkycCallCollectorWireSet,
		vkyccall.StockguardianVkycCallCollectorWireSet,
		omegle.EpifiTechOmegleCollectorWireSet,
		omegle.StockguardianOmegleCollectorWireSet,
		sherlockDbState.StockguardianApiGatewayDbStateWireSet,
		getDBStateCollectorFactory,
		db_states.NewDbStatesServer,
	)
	return &db_states.Service{}
}

func InitializePayoutService(db cmdTypes.SherlockPGDB, accountPiRelationClient accountPIPb.AccountPIRelationClient,
	orderServiceClient order.OrderServiceClient, gconf *cxGenConf.Config, actorClient actorPb.ActorClient,
	timelineClient timelinePb.TimelineServiceClient, piClient piPb.PiClient,
	payoutStatusCheckPublisher cxTypes.PayoutStatusCheckPublisher, payClient payPb.PayClient, rewardsClient rewardsPb.RewardsGeneratorClient) *payout.Service {
	wire.Build(
		getDynPayout,
		cxTypes.PayoutStatusCheckPublisherProvider,
		payout.NewPayoutService,
		wire.NewSet(dao8.NewPayoutRequestDAO, wire.Bind(new(dao8.IPayoutRequestDAO), new(*dao8.PayoutRequestDAO))),
		cash.NewCashPayoutProcessor,
		fi_coins.NewFiCoinsPayoutProcessor,
		wire.NewSet(processor3.NewPayoutFactory, wire.Bind(new(processor3.IFactory), new(*processor3.Factory))),
	)
	return &payout.Service{}
}

func InitializePayoutConsumerService(db cmdTypes.SherlockPGDB, orderServiceClient order.OrderServiceClient,
	gconf *cxGenConf.Config, payoutStatusCheckPublisher cxTypes.PayoutStatusCheckPublisher, payClient payPb.PayClient, rewardsClient rewardsPb.RewardsGeneratorClient) *consumer4.Service {
	wire.Build(
		getDynPayout,
		cxTypes.PayoutStatusCheckPublisherProvider,
		consumer4.NewPayoutConsumerService,
		wire.NewSet(dao8.NewPayoutRequestDAO, wire.Bind(new(dao8.IPayoutRequestDAO), new(*dao8.PayoutRequestDAO))),
	)
	return &consumer4.Service{}
}

func uploadCreditMISToVendorPublisherProvider(ctx context.Context, conf *config.Config, sqsClient *sqs.Client) cxTypes.UploadCreditMISToVendorPublisher {
	if !cfg.IsProdEnv(conf.Application.Environment) {
		pub, err := sqsPkg.NewPublisherWithConfig(ctx, conf.UploadCreditMISToVendorPublisher, sqsClient, nil)
		if err != nil {
			panic(err)
		}
		return pub
	}
	return nil
}

// config: {"cxS3Client": "CxS3Config().BucketName", "epifiIconsS3Client": "EpifiIconS3Config().BucketName", "dataS3Client": "DataS3Config().BucketName", "federalEscalationAttachmentsS3Client": "FederalEscalationConfig().FederalEscalationAttachmentBucketName()"}
func InitializeDevActionsService(
	ctx context.Context,
	sqsClient *sqs.Client,
	chatClient chatPb.ChatsClient,
	kycClient kycPb.KycClient,
	livenessClient livenessPb.LivenessClient,
	userClient userPb.UsersClient,
	savingsClient savingsPb.SavingsClient,
	vmClient vendormapping.VendorMappingServiceClient,
	cardClient cardPb.CardProvisioningClient,
	rewardOfferClient rewardOfferPb.RewardOffersClient,
	luckyDrawSvcClient luckydrawPb.LuckyDrawServiceClient,
	userGroupClient userGroupPb.GroupClient,
	onbClient onboarding2.OnboardingClient,
	authClient authPb.AuthClient,
	indexerClient indexerPb.IndexerClient,
	searchClient searchPb.ActionBarClient,
	emailParserClient emailParserPb.ConsumerServiceClient,
	accountPiClient accountPIPb.AccountPIRelationClient,
	offerCatalogClient casperPb.OfferCatalogServiceClient,
	offerInventoryClient casperPb.OfferInventoryServiceClient,
	offerListingClient casperPb.OfferListingServiceClient,
	actorClient actorPb.ActorClient,
	reconClient reconPb.LedgerReconciliationClient,
	epClient emailParserPb.EmailParserClient,
	accessinfoClient accessinfo.AccessInfoClient,
	rmsClient manager.RuleManagerClient,
	sportsClient sportspb.SportsManagerClient,
	schedulerClient schedulerpb.SchedulerServiceClient,
	vkycClient vkycPb.VKYCClient,
	upClient upPb.UserPreferenceClient,
	orderClient orderPb.OrderServiceClient,
	rmsEventPublisher cxTypes.RMSEventPublisher,
	commsClient commsPb.CommsClient,
	rewardsCampaignCommClient campaigncomm.RewardsCampaignCommClient,
	rewardsClient rewardsPb.RewardsGeneratorClient,
	statementClient statement.AccountStatementClient,
	offerRedemptionClient redemption.OfferRedemptionServiceClient,
	devActionDelayPublisher cxTypes.DevActionDelayPublisher,
	casbinClient casbinPb.CasbinClient,
	connectedAccClient caPb.ConnectedAccountClient,
	paymentClient paymentPb.PaymentClient,
	woClient woPb.WealthOnboardingClient,
	inAppReferralClient inappReferralPb.InAppReferralClient,
	inAppHelpServingClient inappHelpServingPb.ServeFAQClient,
	vgAaClient vgAaPb.AccountAggregatorClient,
	inAppHelpMediaClient mediaPb.InAppHelpMediaClient,
	investmentsCatalogClient investmentCatalogPb.CatalogManagerClient,
	investmentOrderManagerClient investmentOrder.OrderManagerClient,
	exchangerOfferSvcClient exchangerPb.ExchangerOfferServiceClient,
	searchDevClient searchDevPb.DevSearchClient,
	depositClient depositPb.DepositClient,
	wOnbCxClient woCxPb.WealthCxServiceClient,
	fileGeneratorClient fgPb.FileGeneratorClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	cxS3Client cxTypes.CxS3Client,
	cxConf *config.Config,
	inappTargetedCommsClient tcPb.InAppTargetedCommsClient,
	mfprerequisiteHandlerClient prhPb.PrerequisiteHandlerClient,
	mfOpsClient mfOps.OrderOperationsClient,
	insightsClient insights.InsightsClient,
	devP2PInvestmentClient p2pPb.DevP2PInvestmentClient,
	reverseFeedManagerClient rfPb.ReverseFeedManagerClient,
	seasonsClient seasonsPb.SeasonServiceClient,
	segmentClient segmentPb.SegmentationServiceClient,
	mfVGClient mutualfund.MutualFundClient,
	upiClient upiPb.UPIClient,
	dbconn cmdTypes.SherlockPGDB,
	segmentConsumerClient segmentConsumerPb.ConsumerClient,
	ticketClient ticketPb.TicketClient,
	mfReconciliationServiceClient reconciliationPb.ReconciliationServiceClient,
	ccCxClient ccCxPb.CxClient,
	preapprovedloanCxClient preapprovedloanCxPb.CxClient,
	preApprovedLoanSimClient preApprovedLoanSimPb.PreApprovedLoanClient,
	preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient,
	preapprovedloanDevClient preApprovedLoanDevPb.DevPreApprovedLoanClient,
	profileEvaluatorClient profileevaluatorSimPb.ProfileEvaluatorClient,
	empClient employment.EmploymentClient,
	salaryReferralsClient salaryReferralsPb.ReferralsClient,
	uaClient useractions.UserActionsClient,
	riskClient riskPb.RiskClient,
	leaRiskClient leaPb.LeaClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	client nudge.NudgeServiceClient,
	accountsDevSvcClient accountsDevPb.AccountsDevServiceClient,
	payClient payPb.PayClient,
	discountClient discountsPb.DiscountServiceClient,
	userIssueInfoClient userIssueInfoPb.UserIssueInfoServiceClient,
	payFileGeneratorClient payFileGeneratorPb.FileGeneratorClient,
	iftFileProcessorEventPublisher cxTypes.IFTFileProcessorEventPublisher,
	rewardsManualGiveawayEventPublisher cxTypes.RewardsManualGiveawayEventPublisher,
	riskCaseIngestionPublisher cxTypes.RiskCasePublisher,
	genConf *cxGenConf.Config,
	redlistClient redlist.RedListClient,
	usstocksCatalogClient usstocksCatalogPb.CatalogManagerClient,
	amlClient amlPb.AmlClient,
	ussOrderMgClient usstocksOrderManagerPb.OrderManagerClient,
	celestialClient celestialPb.CelestialClient,
	fitttClient fitttPb.FitttClient,
	iftClient iftPb.InternationalFundTransferClient,
	caseManagementClient caseManagementPb.CaseManagementClient,
	temporalWorkflowServiceClient temporalWorkflowPb.WorkflowServiceClient,
	empFeClient employment.EmploymentFeClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	vgAccountsClient vgAccountsPb.AccountsClient,
	ussOperationClient ussOperationsPb.OperationsClient,
	extValidateClient extvalidate.ExternalValidateClient,
	nameCheckClient ncPb.UNNameCheckClient,
	cmsServiceClient cmsPb.CmsServiceClient,
	dynamicUIElementServiceClient dynUIElPb.DynamicUIElementServiceClient,
	cardCxClient cardCxPb.CxClient,
	actionClient commsDeveloperActionsPb.CommsDevActionClient,
	kycAgentPb agentPb.KycAgentServiceClient,
	p2pSimulationServiceClient p2investmentPb.SimulationClient,
	p2p p2investmentPb.P2PInvestmentClient,
	vgStocksClient stocksPb.StocksClient,
	questManagerClient questManagerPb.ManagerClient,
	oprStatusServiceClient operStatusPb.OperationalStatusServiceClient,
	redisClient cxTypes.CxRedisStore,
	vgEkycClient ekycPb.EKYCClient,
	vgP2pClient vgP2pPb.P2PClient,
	mfFolioMgClient folioPb.MfFolioServiceClient,
	salaryClient salaryprogram.SalaryProgramClient,
	rewardSimulatorClient rewardSimulatorPb.SimulatorClient,
	riskDisputeUploadPublisher wireTypes.RiskDisputePublisher,
	casperItcDownloadFileQueuePublisher cxTypes.CasperItcDownloadFileQueuePublisher,
	accountManagerClient accountManagerPb.AccountManagerClient,
	whiteListClient whitelistPb.WhiteListClient,
	orderUpdatePublisher cxTypes.OrderUpdateEventForTxnCategorizationPublisher,
	dataS3Client cxTypes.DataS3Client,
	epifiIconsS3Client cxTypes.EpifiIconsS3Client,
	ffClient ffPb.FireflyClient,
	issueReportingClient irPb.ServiceClient,
	journeyClient journey.JourneyServiceClient,
	vkyccallTroubleshootCl verifiPkg.VkycCallTroubleshootClientToOnboardingServer,
	vkyccallTroubleshootCl2 verifiPkg.VkycCallTroubleshootClientToSGApiGatewayServer,
	alfredClient alfredPb.AlfredClient,
	aggregatorClient invAggrPb.InvestmentAggregatorClient,
	ffAccountingClient ffAccountsPb.AccountingClient,
	aaOrderClient aaOrderPb.AccountAggregatorClient,
	aaTxnPublisher cxTypes.AATxnCategorizationPublisher,
	ccTxnPublisher cxTypes.CCTxnCategorizationPublisher,
	sgApiGwKycClient sgKycApiGatewayPb.KYCClient,
	salaryb2bClient salaryb2bPb.SalaryB2BClient,
	sgLmsClient sgLmsPb.LmsClient,
	saClosureClient saClosurePb.SavingsAccountClosureClient,
	spDynamicUIElementClient spDynamicElementPb.DynamicUIElementServiceClient,
	sgApplicationClient sgApplicationPb.ApplicationClient,
	rpFederalClient rpFederalPb.RecurringPaymentClient,
	publisher wireTypes.FederalEscalationCreateEventPublisher,
	federalEscalationAttachmentsS3Client wireTypes.FederalEscalationAttachmentsS3Client,
	epfClient epfPb.EpfClient,
	mfExternalClient mfExternalPb.MFExternalOrdersClient,
	networthClient networthPb.NetWorthClient,
	consentClient consentPb.ConsentClient,
	investmentAnalyticsClient investmentAnalyserPb.InvestmentAnalyticsClient,
) (*actions.DevActions, error) {
	wire.Build(
		actions.NewDevActions,
		actions.NewDevActionFactory,
		insightsActions.NewGetFilesFromBucket,
		devActionProcessors.NewRetryLiveness,
		devActionProcessors.NewRetryCreateAccount,
		devActionProcessors.NewRetryCreateBankCustomer,
		devActionProcessors.NewRetryEKYC,
		devActionProcessors.NewRetryCkyc,
		devActionProcessors.NewCreateCard,
		devActionProcessors.NewCreateRewardOffer,
		devActionProcessors.NewCreateRewardOfferInBulk,
		devActionProcessors.NewUpdateRewardOfferStatus,
		devActionProcessors.NewCreateLuckyDrawCampaign,
		devActionProcessors.NewCreateLuckyDraw,
		devActionProcessors.NewPullAppLogs,
		devActionProcessors.NewAddUserGroupMapping,
		devActionProcessors.NewDeleteUser,
		devActionProcessors.NewRecoverAuthFactorUpdate,
		devActionProcessors.NewUpdateRewardOfferDisplay,
		devActionProcessors.NewUpdateRewardOffer,
		devActionProcessors.NewUpdateRewardOfferDisplayRank,
		devActionProcessors.NewIndexActorPis,
		devActionProcessors.NewIndexActorGmailData,
		devActionProcessors.NewCreateOffer,
		devActionProcessors.NewCreateOffersInBulk,
		devActionProcessors.NewUpdateOfferDisplayRank,
		devActionProcessors.NewCreateOfferInventory,
		devActionProcessors.NewAddOfferToInventory,
		devActionProcessors.NewDeleteOfferInventory,
		devActionProcessors.NewCreateOfferListing,
		devActionProcessors.NewDeleteOfferListing,
		devActionProcessors.NewUpdateOfferListing,
		devActionProcessors.NewCreateOrUpdateMediaPlaylistSegmentMapping,
		devActionProcessors.NewCreatePinCodeDetails,
		devActionProcessors.NewReconProcessor,
		devActionProcessors.NewUnlinkGmailAccount,
		devActionProcessors.NewMarkUsersByAcquisitionInfo,
		devActionProcessors.NewSyncOnb,
		fittt2.NewCreateRule,
		fittt2.NewUpdateRule,
		fittt2.NewBulkUpdateSubscriptionsState,
		fittt2.NewArchiveRulesAndSubscriptions,
		fittt2.NewFITCreateSchedule,
		fittt2.NewFITStopSchedules,
		fittt2.NewUpdateRulesWeightage,
		fittt2.NewGetRulesForClient,
		fittt2.NewPublishSharkTankEvent,
		devActionProcessors.NewUpdateOnboardingStage,
		devActionProcessors.NewPassportManualReview,
		devActionProcessors.NewProcessNonResidentCrossValidationManualReview,
		devActionProcessors.NewPassOnboardingStage,
		devActionProcessors.NewUpdateOfferDisplay,
		devActionProcessors.NewInitiateMatchUpdate,
		devActionProcessors.NewInitiateCardNotifications,
		devActionProcessors.NewMarkLiveNessPassed,
		devActionProcessors.NewMarkFaceMatchPassed,
		devActionProcessors.NewUpdateShippingAddressAtVendor,
		devActionProcessors.NewCreateShippingPreference,
		fittt2.NewRetryPayOrderRMSEvent,
		devActionProcessors.NewSendRewardsCampaignComm,
		devActionProcessors.NewUnredactedUser,
		devActionProcessors.NewOrderProcessor,
		devActionProcessors.NewRetryRewardProcessing,
		devActionProcessors.NewAccountStatement,
		devActionProcessors.NewGetTxnAggrFit,
		devActionProcessors.NewUpdateUserProfileName,
		devActionProcessors.NewOnboardingSnapshot,
		devActionProcessors.NewBackFillFreshdeskTicketContacts,
		devActionProcessors.NewCreateRewardOfferGroup,
		devActionProcessors.NewResetKYCNameDobRetry,
		devActionProcessors.NewRetryOfferRedemption,
		devActionProcessors.NewManualScreeningUpdate,
		devActionProcessors.NewResetDebitCardNameRetry,
		devActionProcessors.NewUnblockUnNameCheckUser,
		devActionProcessors.NewRefreshVKYCStatus,
		devActionProcessors.NewUpdateCardPinSet,
		devActionProcessors.NewRaiseAaConsent,
		devActionProcessors.NewForceCardCreationEnquiry,
		devActionProcessors.NewTriggerRewardsManualGiveawayEvent,
		devActionProcessors.NewDeleteUserGroupMapping,
		devActionProcessors.NewStartUserAction,
		devActionProcessors.NewCreateActivityMetadata,
		devActionProcessors.NewUpdateActivityMetadata,
		devActionProcessors.NewHandleSavingsAccountClosure,
		helper4.NewDevHelper,
		wealthonboarding.NewSyncWealthOnboarding,
		devActionProcessors.NewUnlockInAppReferral,
		devActionProcessors.NewTriggerVkycCallback,
		devActionProcessors.NewUpdateGmailInsightsMerchants,
		devActionProcessors.NewUpdateGmailMerchantQueries,
		devActionProcessors.NewUpdateInAppHelpFAQ,
		devActionProcessors.NewAaAccountDeLink,
		devActionProcessors.NewAaConsentStatusUpdate,
		devActionProcessors.NewReactivateDevice,
		devActionProcessors.NewReopenClosedSavingsAccount,
		wealthonboarding.NewUpdateWOnbStatus,
		fittt2.NewFitttCreateHomeCard,
		fittt2.NewFitttUpdateHomeCard,
		devActionProcessors.NewUpdatePanReview,
		devActionProcessors.NewAddMediaPlaylist,
		devActionProcessors.NewUpdateMediaPlaylist,
		devActionProcessors.NewAddMediaContentStory,
		devActionProcessors.NewUpdateMediaContentStory,
		devActionProcessors.NewAddUIContextToMediaPlaylistMapping,
		devActionProcessors.NewDeleteUIContextToMediaPlaylistMapping,
		devActionProcessors.NewAddMediaPlaylistToMediaContentMapping,
		devActionProcessors.NewDeleteMediaPlaylistToMediaContentMapping,
		mutualfund2.NewCreateMutualFund,
		devActionProcessors.NewAddRedListEntry,
		mutualfund2.NewUpdateMutualFund,
		mutualfund2.NewAddCreditMISFileMetaData,
		mutualfund2.NewMutualFundReverseFeedFileUpload,
		devActionProcessors.NewAaReplayAccountEvent,
		devActionProcessors.NewCreateAllowedAnnotation,
		devActionProcessors.NewCreateExchangerOffer,
		devActionProcessors.NewCreateExchangerOfferInventory,
		devActionProcessors.NewIncrementExchangerOfferInventory,
		devActionProcessors.NewCreateExchangerOfferGroup,
		devActionProcessors.NewCreateExchangerOfferListing,
		devActionProcessors.NewUpdateExchangerOfferDisplay,
		devActionProcessors.NewUpdateExchangerOfferStatus,
		devActionProcessors.NewUpdateExchangerOfferListing,
		devActionProcessors.NewDeleteExchangerOfferListing,
		devActionProcessors.NewAaReplayTxnEvent,
		devActionProcessors.NewParseQueryBase,
		deposits.NewForceProcessDepositRequest,
		wealthonboarding.NewMarkWealthLivenessPassed,
		mutualfund2.NewMutualFundDeactivateEntityFromFile,
		fittt2.NewFitttCreateCollection,
		fittt2.NewFitttUpdateCollection,
		cxactionvkyccall.NewVKYCCallStateHandler,
		wealthonboarding.NewMarkWealthRedactionPassed,
		wealthonboarding.NewMarkWealthExpiryPassed,
		mutualfund2.NewUpdateMutualFundOrderStatus,
		devActionProcessors.NewTriggerRecurringPaymentExecution,
		devActionProcessors.NewUploadMarketingCampaignUsersList,
		usstocks2.NewUSSGetDocumentFromBucket,
		usstocks2.NewUSStocksCreateCollection,
		usstocks2.NewUSStocksUpdateStockInCollection,
		usstocks2.NewUSStocksAddStockToCollection,
		usstocks2.NewUSStocksUpdateCollection,
		usstocks2.NewUSStocksRemoveStockFromCollection,
		mutualfund2.NewMutualFundDownloadCreditMISReport,
		devActionProcessors.NewAddInAppTargetedCommsElement,
		devActionProcessors.NewUpdateInAppTargetedCommsElement,
		devActionProcessors.NewRemoveRedListEntry,
		mutualfund2.NewMFDownloadOpsFile,
		mutualfund2.NewMFRetriggerPrerequisites,
		devActionProcessors.NewUpdateInsightFramework,
		devActionProcessors.NewUpdateInsightSegment,
		devActionProcessors.NewUpdateInsightContentTemplate,
		mutualfund2.NewMfUploadCatalogUpdate,
		mutualfund2.NewMfUploadCreditMISNonProd,
		devActionProcessors.NewAddUserToVKYCPriority,
		p2pinvestment2.NewUpdateInvTranStatus,
		devActionProcessors.NewManualCardUnsuspend,
		p2pinvestment2.NewUpdateTotalInvestmentCount,
		devActionProcessors.NewUpdateUserPhoto,
		devActionProcessors.NewUpdateUserFatherName,
		mutualfund2.NewMFProcessReverseFeedFile,
		mutualfund2.NewMfCreateCollection,
		mutualfund2.NewMfAddFundToCollection,
		mutualfund2.NewMfRemoveFundsFromCollection,
		mutualfund2.NewUpdateFundInCollectionFundMapping,
		mutualfund2.NewUpdateCollection,
		p2pinvestment2.NewUpdateP2PVendorResponsesApprovalStatus,
		devActionProcessors.NewPhysicalCardRequest,
		devActionProcessors.NewPerformActionForTxnReview,
		devActionProcessors.NewCreateReferralsSeason,
		devActionProcessors.NewUpdateReferralsSeason,
		devActionProcessors.NewDeleteSegment,
		wealthonboarding.NewMarkStepStaleWealthOnboarding,
		devActionProcessors.NewRequestNewCard,
		devActionProcessors.NewTriggerVPACreation,
		devActionProcessors.NewAddManualCallRoutingMappings,
		devActionProcessors.NewCreateDiscount,
		devActionProcessors.NewDeleteDiscount,
		wire.NewSet(dao.NewCallRoutingManualMappingsDao, wire.Bind(new(dao.ICallRoutingManualMappingsDao), new(*dao.CallRoutingManualMappingsDao))),
		wire.NewSet(dao23.NewUserQueryLogDao, wire.Bind(new(dao23.UserQueryLogDao), new(*dao23.UserQueryLogDaoImpl))),
		devActionProcessors.NewGenerateModelOutput,
		devActionProcessors.NewTriggerSegmentExport,
		devActionProcessors.NewCreateSegment,
		devActionProcessors.NewUpdateSegment,
		devActionProcessors.NewCreateTicketDetailsTransformation,
		devActionProcessors.NewUpdateTicketDetailsTransformation,
		devActionProcessors.NewDeleteTicketDetailsTransformation,
		mutualfund2.NewMFReconciliation,
		devActionProcessors.NewDeepLinkBase64Encoder,
		devActionProcessors.NewCreditCardUpdateCardRequestStatus,
		devActionProcessors.NewPreApprovedLoanManualReview,
		devActionProcessors.NewPreApprovedLoanCreateOffer,
		devActionProcessors.NewProfileEvaluatorUpdateRules,
		devActionProcessors.NewPreApprovedLoanUpdateStatus,
		lending.NewPreApprovedLoanAbflDevAction,
		devActionProcessors.NewPreApprovedLoanLlEntityStatus,
		wire.Bind(new(helper4.IDevHelper), new(*helper4.DevHelper)),
		devActionProcessors.NewCategoriseScreenerDomains,
		devActionProcessors.NewVendorAccountPennyDrop,
		devActionProcessors.NewTriggerKycNameDobValidation,
		devActionProcessors.NewOnboardingTroubleshootingDetails,
		devActionProcessors.NewFailBankCustomer,
		devActionProcessors.NewCreateSalaryProgramReferralsSeason,
		devActionProcessors.NewSavingsRiskBankAction,
		devActionProcessors.NewRiskBankActionManualOverride,
		devActionProcessors.NewCreateNudge,
		devActionProcessors.NewEditNudge,
		devActionProcessors.NewUpdateNudgeStatus,
		devActionProcessors.NewUpdateAccountFreezeStatusInSimulator,
		devActionProcessors.NewPalMarkLoanRequestCancel,
		devActionProcessors.NewFailDebitCardRequest,
		wire.Bind(new(iftFileGen.IHttpClient), new(*http.Client)),
		internationalfundtransfer.NewInternationalFundTransferAcknowledgeSwiftTransfer,
		internationalfundtransfer.NewAcknowledgeInwardSwiftTransfer,
		iftConfigProvider,
		iftSlackAlertClientProvider,
		internationalfundtransfer.NewInternationalFundTransferUploadLrsCheckFile,
		internationalfundtransfer.NewPayDownloadLrsCheckFile,
		internationalfundtransfer.NewForeignRemittanceProcessInwardRemittance,
		devActionProcessors.NewUpdateAccountFreezeStatus,
		devActionProcessors.NewUploadUserIssueInforForAgents,
		devActionProcessors.NewUploadLeaComplaints,
		deposits.NewDepositUpdateInterestRate,
		devActionProcessors.NewUpdateUserEmployment,
		deposits.NewDepositAddInterestRate,
		deposits.NewDepositDeleteInterestRate,
		wealthonboarding.NewOnbDetailsByPan,
		devActionProcessors.NewVerifyIncomeOccupationDiscrepancy,
		devActionProcessors.NewCreateReferralsSegmentedComponent,
		devActionProcessors.NewUpdateReferralsSegmentedComponent,
		devActionProcessors.NewDeleteReferralsSegmentedComponent,
		devActionProcessors.NewUploadRiskCases,
		devActionProcessors.NewUploadUnifiedLeaComplaints,
		p2pinvestment2.NewP2PGetInvestorDashboard,
		p2pinvestment2.NewP2PGetInvestmentSummary,
		p2pinvestment2.NewP2PDownloadReconFile,
		p2pinvestment2.NewP2PGetInvestorCashLedger,
		p2pinvestment2.NewP2PRegisterBankingDetails,
		mutualfund2.NewMfFolioService,
		wealthonboarding.NewGetSignedUrlWealth,
		usstocks2.NewUSStocksRefreshStockDetails,
		devActionProcessors.NewAmlReportUpload,
		devActionProcessors.NewDeactivateDevice,
		internationalfundtransfer.NewCreateForexRate,
		internationalfundtransfer.NewUpdateForexRate,
		devActionProcessors.NewReprieveVkyc,
		usstocks2.NewUsstocksGrossSummary,
		devActionProcessors.NewAmlUpdateFileGenStatus,
		mutualfund2.NewGetDocumentFromBucket,
		internationalfundtransfer.NewForexRateReport,
		internationalfundtransfer.NewCurrentForexRate,
		wealthonboarding.NewWealthUploadDocument,
		p2pinvestment2.NewUpdateP2PVendorResponseMaturityTransactionDaysToExpire,
		devActionProcessors.NewCreateTemporalSchedule,
		internationalfundtransfer.NewGetForexRates,
		devActionProcessors.NewAddHomePromoBannerElement,
		usstocks2.NewUsstocksAccountStatement,
		mfpayDao.PaymentsWireSet,
		mutualfund2.NewMutualFundRetryOrderFromStart,
		devActionProcessors.NewCreateNudgesInBulk,
		wealthonboarding.NewUpdatePendingUserDataWealth,
		devActionProcessors.NewCelestialProcessor,
		devActionReferralProcessors.NewCreateReferralNotificationConfig,
		devActionReferralProcessors.NewUpdateReferralNotificationConfigStatus,
		devActionReferralProcessors.NewUpdateReferralNotificationConfigContent,
		devActionReferralProcessors.NewDeleteReferralNotificationConfig,
		devActionProcessors.NewAddHomePopupBannerElement,
		deposits.NewDepositListAccountVendor,
		devActionProcessors.NewCreateProduct,
		devActionProcessors.NewCreateSku,
		devActionProcessors.NewCreateCouponsInBulk,
		internationalfundtransfer.NewAggrTaxReport,
		devActionProcessors.NewUpdateDynamicUIElementEvaluatorConfig,
		devActionProcessors.NewCreateOrUpdateDynamicUIElementVariant,
		devActionProcessors.NewMarkCardDeliveryTrackingStateReceived,
		devActionProcessors.NewLoanUpdateLoanStepStatus,
		devActionProcessors.NewAddEmployers,
		internationalfundtransfer.NewRejectOutwardSwiftFileTransactions,
		devActionProcessors.NewWhiteListEmailId,
		devActionProcessors.NewCreateKycAgent,
		devActionProcessors.NewDeleteKycAgent,
		devActionProcessors.NewDataExtraction,
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		devActionProcessors.NewCreateIssueConfig,
		devActionProcessors.NewChatbotAccessTokenGenerator,
		devActionProcessors.NewUploadLEAComplaintNarrations,
		devActionProcessors.NewUpdateEmployerDetails,
		devActionProcessors.NewUploadLEAComplaintSources,
		devActionReferralProcessors.NewBulkSetupReferralSegmentedComponent,
		devActionProcessors.NewSetUserCommsPreference,
		ffDevAction.NewCreditCardUpdateCardRequestStageStatus,
		ffDevAction.NewTriggerUnsecuredCCRenewalFeeReversal,
		usstocks2.NewSimulateOutwardFundTransferNonProd,
		p2pinvestment2.NewP2PUpdateInvestmentTransaction,
		devActionProcessors.NewCreateFaqContextMapping,
		devActionProcessors.NewFetchAccountStatus,
		devActionProcessors.NewCreateHomeSimulatorLayout,
		usstocks2.NewCreateBankRelationshipWithBroker,
		usstocks2.NewGetUsStocksAccountActivitiesCsv,
		devActionProcessors.NewCacheContactUsModelResponse,
		devActionProcessors.NewCreateSuggestedActionsForRule,
		devActionProcessors.NewCreateRuleReviewTypeMapping,
		wire.NewSet(annotations.NewAnnotationsHelper, wire.Bind(new(annotations.IAnnotationsHelper), new(*annotations.AnnotationsHelper))),
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		cxTypes.CxS3ClientProvider,
		getHttpClientForSherlock,
		GormDBProvider,
		CxCacheStorageProvider,
		storageV2.DefaultTxnExecutorWireSet,
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		cxTypes.CxRedisStoreProvider,
		app_log.NewAppLogService,
		wire.NewSet(dao7.NewAppLogDao, wire.Bind(new(dao7.IAppLogDao), new(*dao7.AppLogDao))),
		wire.NewSet(dao7.NewActorLogMappingDao, wire.Bind(new(dao7.IActorLogMappingDao), new(*dao7.ActorLogMappingDao))),
		wire.NewSet(dao7.NewLogIdDao, wire.Bind(new(dao7.ILogIdMap), new(*dao7.LogIdDao))),
		getAppLogsNotificationContent,
		getDynAppLog,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		initialiseMonorailHttpClient,
		wire.Bind(new(monorailApi.IMonorailApiWrapper), new(*monorailApi.MonorailApiWrapper)),
		initialiseAirflowHttpClient,
		wire.Bind(new(pkgAirflowApiWrapper.IAirflowApiWrapper), new(*pkgAirflowApiWrapper.AirflowApiWrapper)),
		devActionProcessors.NewCreateWatsonTicketDetails,
		devActionProcessors.NewUpdateWatsonTicketDetails,
		uploadCreditMISToVendorPublisherProvider,
		wire.NewSet(dao19.NewEventConfigsDaoImpl, wire.Bind(new(dao19.EventConfigDao), new(*dao19.EventConfigDaoImpl))),
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		wire.NewSet(manager2.NewIssueCategoryManagerImpl, wire.Bind(new(manager2.IssueCategoryManager), new(*manager2.IssueCategoryManagerImpl))),
		wire.NewSet(dao20.NewActivityMetadataDao, wire.Bind(new(dao20.ActivityMetadataDao), new(*dao20.ActivityMetadataDaoImpl))),
		devActionProcessors.NewCreateEventConfig,
		devActionProcessors.NewUpdateEventConfig,
		internationalfundtransfer.NewInitiateRefund,
		devActionProcessors.NewRaiseManualSalaryVerificationRequestsInBulk,
		devActionProcessors.NewSimulateRewardGenerationService,
		devActionProcessors.NewUpdateSalaryProgramReferralsSeason,
		casper.NewProcessItcPointsHandbackFile,
		devActionProcessors.NewUploadDisputes,
		devActionProcessors.NewSegmentMetadataAction,
		devActionProcessors.NewSetSegmentMetadataApprovalStatus,
		usstocks2.NewSofLimitManualOverride,
		devActionProcessors.NewPassRiskScreenerAttempt,
		wealthonboarding.NewExtractPanFromDocket,
		usstocks2.NewResetOnboardingData,
		userrisk.NewRiskManageWhiteList,
		usstocks2.NewAddInvestorAddress,
		devActionProcessors.NewTriggerTxnCategorization,
		internationalfundtransfer.NewGenerateAdHocInwardRemittanceFiles,
		internationalfundtransfer.NewRegenerateInwardFile,
		devActionProcessors.NewMapDebitCardForexTxn,
		devActionProcessors.NewCreateJourney,
		devActionProcessors.NewUpdateJourney,
		devActionProcessors.NewDeleteServiceRequest,
		usstocks2.NewUsStocksTradingAccountSummary,
		verifiPkg.NewVkycCallTroubleshootClientWrapper,
		devActionProcessors.NewB2BOnboardingStatusTracking,
		devActionProcessors.NewB2BUserOnboardingStatusTrackingLimitedDetails,
		usstocks2.NewUSStocksUpdateDetails,
		mutualfund2.NewDownloadFilesByVendorOrderIds,
		devActionProcessors.NewStockguardianCKYCImageRedaction,
		devActionProcessors.NewLeadMgmtDownloadFile,
		deposits.NewDepositUpdateState,
		stockguardian.NewExecuteEmiMandate,
		stockguardian.NewExecuteDisbursalRetry,
		stockguardian.NewExecuteEmploymentVerification,
		devActionProcessors.NewGetSaClosureEligibility,
		devActionProcessors.NewCreateOrUpdateSPDynamicUIVariant,
		devActionProcessors.NewCreatingOrUpdatingNewSPDynamicUIEvaluatorConfig,
		devActionProcessors.NewRedListProcessor,
		devActionProcessors.NewSendEnachMandateNotificationCallback,
		lending.NewUpdateLoansOutcallTicket,
		devActionProcessors.NewFederalEscalationCreation,
		wire.NewSet(dao24.NewEscalationDao, wire.Bind(new(dao24.EscalationDAO), new(*dao24.EscalationDao))),
		devActionProcessors.NewB2BUnNameCheckFailureEmail,
		devActionProcessors.NewRefreshUserInfoFromPartnerBank,
		devActionInsights.NewDeleteUserAssets,
		devActionProcessors.NewUploadImageToEpifiIconsS3Bucket,
		devActionProcessors.NewExpireLOEC,
		openSearchClientProvider,
		NewOpenSearchLogService,
		mutualfund2.NewFetchUtrsFromLogProcessor,
		devActionProcessors.NewResetLoanRequest,
	)
	return &actions.DevActions{}, nil
}

func InitializeDevActionsCLIRegistryService(
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient,
	palDevClient preApprovedLoanDevPb.DevPreApprovedLoanClient,
) (*devActionRegistryProcessors.DevActionCLIRegistry, error) {
	wire.Build(
		devActionRegistryProcessors.NewDevActionRegistry,
		devActionProcessors.NewAddUserGroupMapping,
		devActionProcessors.NewPreApprovedLoanCreateOffer,
		devActionRegistryProcessors.NewAddUserGroupMappingDevAction,
		devActionRegistryProcessors.NewPreApprovedLoanCreateOffer,
	)
	return &devActionRegistryProcessors.DevActionCLIRegistry{}, nil
}

func InitializeDevActionConsumerService(db cmdTypes.SherlockPGDB, ticketPublisher cxTypes.FreshdeskTicketPublisher, fdClient freshdesk.FreshdeskClient) *consumer5.Service {
	wire.Build(
		consumer5.NewDevActionConsumerService,
		event_processors.NewFDTicketContactUpdate,
		consumer5.NewEventFactory,
		wire.NewSet(dao4.NewFreshdeskUpdateEventDAO, wire.Bind(new(dao4.IFreshdeskUpdateEventDAO), new(*dao4.FreshdeskUpdateEventDAO))),
	)
	return &consumer5.Service{}
}

func accessControlFuncProvider() interceptor.AccessControlFunction {
	return interceptor.EnforceAccessControl
}

func getCxDescriptorProvider() interceptor.GetCxDescriptor {
	return interceptor.GetCxMethodDescriptor
}

// config: {"sherlockS3Client": "LivenessVideoConfig().S3BucketName"}
func InitializeLivenessVideoService(ctx context.Context, lClient livenessPb.LivenessClient,
	casbinClient casbinPb.CasbinClient,
	sherlockS3Client cxTypes.LivenessVideoConfigS3Client,
	conf *config.Config, dbConn cmdTypes.SherlockPGDB) *liveness_video.Service {
	wire.Build(
		getAuthFunc,
		accessControlFuncProvider,
		getCxDescriptorProvider,
		cxTypes.LivenessVideoConfigS3ClientProvider,
		liveness_video.NewLivenessVideoService,
		audit_log.NewAuditLogService,
		getAuditLog,
		wire.NewSet(dao5.NewAuditLogDao, wire.Bind(new(dao5.IAuditLogDao), new(*dao5.AuditLogDao))),
		getCognitoIDPClient,
		getCognitoUserPoolId,
		getAuthValidationConfig,
		sherlock_auth.NewCognitoAuthService,
		sherlock_auth.NewCasbinAuthorizationService,
	)
	return &liveness_video.Service{}
}

func InitializeTicketService(ctx context.Context, fdClient freshdesk.FreshdeskClient,
	casbinClient casbinPb.CasbinClient, db cmdTypes.SherlockPGDB, paymentClient paymentPb.PaymentClient,
	orderClient orderPb.OrderServiceClient, updateTicketPublisher cxTypes.UpdateTicketPublisher, createTicketPublisher cxTypes.CreateTicketPublisher,
	gconf *cxGenConf.Config, userClient userPb.UsersClient, actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient, cxConf *config.Config, redisClient cxTypes.CxRedisStore,
	disputeExtPub cxTypes.DisputeExternalPublisher, piClient piPb.PiClient,
	userGroupClient userGroupPb.GroupClient, createDisputeTicketPub cxTypes.DisputeCreateTicketPublisher,
	savingsClient savingsPb.SavingsClient, commsClient commsPb.CommsClient, vgDisputeClient dispute2.DisputeClient,
	eventBroker events.Broker, ticketClient ticketPb.TicketClient) (*ticket.Service, error) {
	wire.Build(
		accessControlFuncProvider,
		getCxDescriptorProvider,
		getAuthFunc,
		getDynTicketConfig,
		getBulkTicketJobConfig,
		release.EvaluatorWireSet,
		getDynFeatureReleaseConfig,
		wire.NewSet(dao11.NewSherlockUserInfoDao, wire.Bind(new(dao11.ISherlockUserInfoDao), new(*dao11.SherlockUserInfoDao))),
		wire.NewSet(dao11.NewSherlockUserRoleDao, wire.Bind(new(dao11.ISherlockUserRoleDao), new(*dao11.SherlockUserRoleDao))),
		wire.NewSet(sherlockUserWrapper.NewSherlockUserDao, wire.Bind(new(sherlockUserWrapper.ISherlockUser), new(*sherlockUserWrapper.SherlockUser))),
		wire.NewSet(dao9.NewBulkTicketJobDao, wire.Bind(new(dao9.IBulkTicketJobDao), new(*dao9.BulkTicketJobDao))),
		wire.NewSet(dao9.NewTicketFailureLogDao, wire.Bind(new(dao9.ITicketFailureLogDao), new(*dao9.TicketFailureLogDao))),
		wire.NewSet(dao23.NewUserQueryLogDao, wire.Bind(new(dao23.UserQueryLogDao), new(*dao23.UserQueryLogDaoImpl))),
		token_manager.CsatTokenManagerWireSet,
		ticket.NewTicketService,
		csat.EvaluatorWireSet,
		dao9.InAppCsatResponseDaoWireSet,
		csatConfigProvider,
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		processor9.NewRewardsProcessor,
		processor9.NewTransactionsProcessor,
		processor9.NewSavingsProcessor,
		processor9.NewActorActivityProcessor,
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		wire.NewSet(processor9.NewAttachEntityFactory, wire.Bind(new(processor9.IAttachEntityFactory), new(*processor9.AttachEntityFactory))),
		wire.NewSet(helper7.NewTicketHelper, wire.Bind(new(helper7.ITicketHelper), new(*helper7.TicketHelper))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		wire.NewSet(dao9.NewTicketDetailsTransformationDao, wire.Bind(new(dao9.ITicketDetailsTransformationDao), new(*dao9.TicketDetailsTransformationDao))),
		NewRedisClient,
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		wire.NewSet(manager2.NewIssueCategoryManagerImpl, wire.Bind(new(manager2.IssueCategoryManager), new(*manager2.IssueCategoryManagerImpl))),
		helper2.NewDisputeHelper,
		getDynDispute,
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(dao6.NewDisputeConfigDao, wire.Bind(new(dao6.IDisputeConfigDao), new(*dao6.DisputeConfigDao))),
		wire.NewSet(dao6.NewFederalDmpDisputeDetailDao, wire.Bind(new(dao6.IFederalDmpDisputeDetailDao), new(*dao6.FederalDmpDisputeDetailDao))),
		wire.Bind(new(helper2.IDisputeHelper), new(*helper2.DisputeHelper)),
		audit_log.NewAuditLogService,
		getAuditLog,
		wire.NewSet(dao5.NewAuditLogDao, wire.Bind(new(dao5.IAuditLogDao), new(*dao5.AuditLogDao))),
		sherlock_auth.NewCasbinAuthorizationService,
		getCognitoIDPClient,
		getCognitoUserPoolId,
		getAuthValidationConfig,
		sherlock_auth.NewCognitoAuthService,
		getS3TranscriptionClient,
		getS3CallRecordingClient,
	)
	return &ticket.Service{}, nil
}

func InitializeTicketConsumerHelper(db cmdTypes.SherlockPGDB, paymentClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient, conf *config.Config) *helper7.TicketHelper {
	wire.Build(
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		helper7.NewTicketHelper,
	)
	return &helper7.TicketHelper{}
}

func InitializeCxDevService(db cmdTypes.SherlockPGDB, redisClient cxTypes.CxRedisStore, cxGenConf *cxGenConf.Config, riskProfileClient profilePb.ProfileClient) *developer.CXDev {
	wire.Build(
		developer.NewCXDev,
		developer.NewDevFactory,
		processor5.NewDevDisputeConfig,
		processor5.NewDevDispute,
		processor5.NewDevCustomerAuth,
		processor5.NewDevAuthFactor,
		processor5.NewDevFreshdeskConsumer,
		processor5.NewDevDisputeManualIntervention,
		processor5.NewDevSupportTicketDetails,
		processor5.NewDevCallDetails,
		processor5.NewDevTicketDetailsTransformations,
		processor5.NewDevIssueResolutionFeedbacks,
		processor5.NewDevIssueResolutionUserResponses,
		processor5.NewDevPayoutDetails,
		processor5.NewDevFreshchatUserMappings,
		processor5.NewDevSprinklrCaseDetails,
		processor5.NewDevWatsonIncident,
		processor5.NewDevWatsonIncidentCommsDetail,
		processor5.NewDevWatsonIncidentTicketDetail,
		processor5.NewDevActorAppLogMapping,
		processor5.NewDevCrmToIssueTrackerMappings,
		processor5.NewDevDisputeTicketLog,
		processor5.NewDevDisputeNotificationLog,
		processor5.NewDevDisputeCorrespondenceDetail,
		processor5.NewDevDisputeDocumentDetail,
		processor5.NewDevDisputeIdToTicketIdMapping,
		processor5.NewDevFederalDmpDisputeDetail,
		processor5.NewDevIssueConfig,
		processor5.NewDevIssueCategory,
		processor5.NewCallIVRDetails,
		processor5.NewActivityMetaData,
		processor5.NewDevNuggetResourceAPI,
		CxCacheStorageProvider,
		wire.NewSet(dao8.NewPayoutRequestDAO, wire.Bind(new(dao8.IPayoutRequestDAO), new(*dao8.PayoutRequestDAO))),
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(dao6.NewDisputeConfigDao, wire.Bind(new(dao6.IDisputeConfigDao), new(*dao6.DisputeConfigDao))),
		wire.NewSet(dao6.NewDisputeTicketLogDao, wire.Bind(new(dao6.IDisputeTicketLogDao), new(*dao6.DisputeTicketLogDao))),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCallbackResponseDAO, wire.Bind(new(dao3.ICustomerAuthenticationCallbackResponsesDAO), new(*dao3.CallbackResponseDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		wire.NewSet(dao3.NewAuthFactorRetryLogsDAO, wire.Bind(new(dao3.IAuthFactorRetryLogsDao), new(*dao3.AuthFactorRetryLogsDAO))),
		wire.NewSet(dao4.NewFreshdeskUpdateEventDAO, wire.Bind(new(dao4.IFreshdeskUpdateEventDAO), new(*dao4.FreshdeskUpdateEventDAO))),
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		wire.NewSet(dao12.NewCallDetailsDao, wire.Bind(new(dao12.ICallDetailsDao), new(*dao12.CallDetailsDao))),
		wire.NewSet(dao9.NewTicketDetailsTransformationDao, wire.Bind(new(dao9.ITicketDetailsTransformationDao), new(*dao9.TicketDetailsTransformationDao))),
		wire.NewSet(dao15.NewIssueResolutionFeedbackDao, wire.Bind(new(dao15.IIssueResolutionFeedbackDao), new(*dao15.IssueResolutionFeedbackDao))),
		wire.NewSet(dao15.NewIssueResolutionUserResponseLogDao, wire.Bind(new(dao15.IIssueResolutionUserResponseLogDao), new(*dao15.IssueResolutionUserResponseLogDao))),
		wire.NewSet(dao4.NewFreshchatUserMappingDao, wire.Bind(new(dao4.IFreshchatUserMappingDao), new(*dao4.FreshchatUserMappingDao))),
		wire.NewSet(dao17.NewSprinklrCaseDetailsDao, wire.Bind(new(dao17.ISprinklrCaseDetailsDao), new(*dao17.SprinklrCaseDetailsDao))),
		wire.NewSet(watsonDao.NewIncidentDao, wire.Bind(new(watsonDao.IIncidentDao), new(*watsonDao.IncidentDao))),
		wire.NewSet(watsonDao.NewIncidentCommsDetailDao, wire.Bind(new(watsonDao.IIncidentCommsDetailDao), new(*watsonDao.IncidentCommsDetailDao))),
		wire.NewSet(watsonDao.NewIncidentTicketDetailDao, wire.Bind(new(watsonDao.IIncidentTicketDetailDao), new(*watsonDao.IncidentTicketDetailDao))),
		wire.NewSet(appLogDao.NewActorLogMappingDao, wire.Bind(new(appLogDao.IActorLogMappingDao), new(*appLogDao.ActorLogMappingDao))),
		wire.NewSet(citDao.NewCrmToIssueTrackerMappingDao, wire.Bind(new(citDao.ICrmToIssueTrackerMappingDao), new(*citDao.CrmToIssueTrackerMappingDao))),
		wire.NewSet(dao6.NewDisputeNotificationLogDao, wire.Bind(new(dao6.IDisputeNotificationLogDao), new(*dao6.DisputeNotificationLogDao))),
		wire.NewSet(dao6.NewDisputeCorrespondenceDetailDao, wire.Bind(new(dao6.IDisputeCorrespondenceDetailDao), new(*dao6.DisputeCorrespondenceDetailDao))),
		wire.NewSet(dao6.NewDisputeDocumentDetailDao, wire.Bind(new(dao6.IDisputeDocumentDetailDao), new(*dao6.DisputeDocumentDetailDao))),
		wire.NewSet(dao6.NewDisputeIdToTicketIdMappingDao, wire.Bind(new(dao6.IDisputeIdToTicketIdMappingDao), new(*dao6.DisputeIdToTicketIdMappingDao))),
		wire.NewSet(dao6.NewFederalDmpDisputeDetailDao, wire.Bind(new(dao6.IFederalDmpDisputeDetailDao), new(*dao6.FederalDmpDisputeDetailDao))),
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		wire.NewSet(dao25.NewCallIvrDetailsDaoImpl, wire.Bind(new(dao25.CallIvrDetailsDao), new(*dao25.CallIvrDetailsDaoImpl))),
		wire.NewSet(dao20.NewActivityMetadataDao, wire.Bind(new(dao20.ActivityMetadataDao), new(*dao20.ActivityMetadataDaoImpl))),
		processor5.NewDevGetFederalEscalation,
		wire.NewSet(dao24.NewEscalationDao, wire.Bind(new(dao24.EscalationDAO), new(*dao24.EscalationDao))),
		processor5.NewDevGetFederalEscalationUpdates,
		wire.NewSet(dao24.NewEscalationUpdateDao, wire.Bind(new(dao24.EscalationUpdateDAO), new(*dao24.EscalationUpdateDao))),
		wire.NewSet(processor5.NewDevGetFederalEscalationAttachments),
		wire.NewSet(dao24.NewEscalationAttachmentDao, wire.Bind(new(dao24.EscalationAttachmentDAO), new(*dao24.EscalationAttachmentDao))),
	)
	return &developer.CXDev{}
}

func InitializeRateLimiterService(redisClient cxTypes.CxRedisStore, rlClient ratelimiter.RateLimiter, genConf *cxGenConf.Config) *rate_limit.RedisRateLimiter {
	wire.Build(
		NewRedisClient,
		rate_limit.NewRedisRateLimiterService,
	)
	return &rate_limit.RedisRateLimiter{}
}

func InitializeFederalService(fdClient freshdesk.FreshdeskClient) *federal.Service {
	wire.Build(
		federal.NewFederalService,
	)
	return &federal.Service{}
}

func InitializeFitttService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient, fitttClient fitttPb.FitttClient, rmsClient manager.RuleManagerClient, usStocksCatalogManagerClient usstocksCatalogPb.CatalogManagerClient) *fittt.Service {
	wire.Build(
		fittt.NewService,
		fittt3.USStocksWireSet,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &fittt.Service{}
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeAdminActionService(chatClient chatPb.ChatsClient, vmClient vendormapping.VendorMappingServiceClient,
	db cmdTypes.SherlockPGDB, userClient userPb.UsersClient, authClient authPb.AuthClient, actorClient actorPb.ActorClient, ticketPublisher cxTypes.FreshdeskTicketPublisher,
	obClient onboarding2.OnboardingClient, kycClient kycPb.KycClient, casbinClient casbinPb.CasbinClient,
	cardProvisioningClient cardPb.CardProvisioningClient, freshdeskClient freshdesk.FreshdeskClient, redisClient cxTypes.CxRedisStore, genConfig *cxGenConf.Config, commsClient commsPb.CommsClient,
	cxS3Client cxTypes.CxS3Client, conf *config.Config) *admin_actions.AdminActionsService {
	wire.Build(
		admin_actions.NewAdminActionsService,
		admin_actions.NewAdminActionFactory,
		processor6.NewRevokeUserAccess,
		processor6.NewResetKYCNameDobRetry,
		processor6.NewResetLivenessFmRetry,
		processor6.NewResetUser,
		processor6.NewResetDebitCardNameRetry,
		processor6.NewRefreshUserInfoFromPartnerBank,
		processor6.NewManualScreeningUpdate,
		processor6.NewCreateCard,
		processor6.NewUpdateUserParentsName,
		processor6.NewSetUserCommsPreference,
		processor6.NewPullAppLogs,
		helper5.NewAdminActionHelper,
		wire.NewSet(dao4.NewFreshdeskUpdateEventDAO, wire.Bind(new(dao4.IFreshdeskUpdateEventDAO), new(*dao4.FreshdeskUpdateEventDAO))),
		wire.NewSet(dao12.NewCallDetailsDao, wire.Bind(new(dao12.ICallDetailsDao), new(*dao12.CallDetailsDao))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		validation.NewTicketValidation,
		NewRedisClient,
		CxCacheStorageProvider,
		cxTypes.CxS3ClientProvider,
		app_log.NewAppLogService,
		getDynAppLog,
		wire.NewSet(dao7.NewAppLogDao, wire.Bind(new(dao7.IAppLogDao), new(*dao7.AppLogDao))),
		wire.NewSet(dao7.NewActorLogMappingDao, wire.Bind(new(dao7.IActorLogMappingDao), new(*dao7.ActorLogMappingDao))),
		wire.NewSet(dao7.NewLogIdDao, wire.Bind(new(dao7.ILogIdMap), new(*dao7.LogIdDao))),
		auth_engine.NewAuthEngine,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		getAppLogsNotificationContent,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wire.Bind(new(validation.ITicketValidation), new(*validation.TicketValidation)),
	)
	return &admin_actions.AdminActionsService{}
}

func InitializeReferralService(actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient,
	refClient inappReferralPb.InAppReferralClient, rewardsClient rewardsPb.RewardsGeneratorClient,
	onboardingClient onboarding2.OnboardingClient, savingsClient savingsPb.SavingsClient, orderTxnClient cx.CXClient,
	config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient) *referrals.Service {
	wire.Build(
		referrals.NewReferralService,
		getReferralConfig,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
	)
	return &referrals.Service{}
}

func InitializeFITDevConsoleService(fitDevClient devconsolepb.DeveloperConsoleServiceClient) *devconsole.Service {
	wire.Build(
		devconsole.NewService,
	)
	return &devconsole.Service{}
}

func InitializeCallRoutingService(chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient, onboardingClient onboarding2.OnboardingClient, userGroupClient userGroupPb.GroupClient,
	salaryProgramClient salaryprogram.SalaryProgramClient, externalAccountsClient extacct.ExternalAccountsClient,
	dbConn cmdTypes.SherlockPGDB, conf *cxGenConf.Config, segmentClient segmentPb.SegmentationServiceClient,
	eventsBroker events.Broker, ffClient ffPb.FireflyClient, actorClient actorPb.ActorClient, piClient piPb.PiClient, usersClient userPb.UsersClient,
	pClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient, savingsClient savingsPb.SavingsClient,
	depositClient depositPb.DepositClient, investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient,
	p2pInvestmentClient p2investmentPb.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient,
	usstocksOrderManagerClient orderManagerPb.OrderManagerClient, accountBalanceClinet accountBalancePb.BalanceClient, riskProfileClient profilePb.ProfileClient, commsClient commsPb.CommsClient, productClient product.ProductClient,
	callRoutingEventPublisher wireTypes.CallRoutingEventPublisher, redisClient cxTypes.CxRedisStore,
	tieringClient beTieringPb.TieringClient, ticketClient ticketPb.TicketClient, preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient,
	compClient compliancePb.ComplianceClient) *call_routing.Service {
	wire.Build(
		getCallRoutingConfig,
		call_routing.NewCallRoutingService,
		NewRedisClient,
		release.EvaluatorWireSet,
		getDynFeatureReleaseConfig,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wire.NewSet(dao.NewCallRoutingManualMappingsDao, wire.Bind(new(dao.ICallRoutingManualMappingsDao), new(*dao.CallRoutingManualMappingsDao))),
		wire.NewSet(helper9.NewCallRoutingHelper, wire.Bind(new(helper9.ICallRoutingHelper), new(*helper9.CallRoutingHelper))),
		wire.NewSet(dao12.NewCallDetailsDao, wire.Bind(new(dao12.ICallDetailsDao), new(*dao12.CallDetailsDao))),
		wire.NewSet(priority_helper.NewPriorityHelper, wire.Bind(new(priority_helper.IPriorityHelper), new(*priority_helper.PriorityHelper))),
		wire.NewSet(helper3.NewDataCollectorHelper, wire.Bind(new(helper3.IDataCollectorHelper), new(*helper3.DataCollectorHelper))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		wire.NewSet(blocker.NewBlocker, wire.Bind(new(blocker.Blocker), new(*blocker.BlockerImpl))),
	)
	return &call_routing.Service{}
}

// config: {"s3Client": "LivenessVideoConfig().S3BucketName", "riskS3Client": "RiskS3Config().BucketName"}
func InitializeRiskOpsService(onboardingClient onboarding2.OnboardingClient, userClient userPb.UsersClient, kycClient kycPb.KycClient,
	vkycClient vkycPb.VKYCClient, locationClient location.LocationClient, actorClient actorPb.ActorClient,
	livenessClient livenessPb.LivenessClient, caClient connectedAccountPb.ConnectedAccountClient, authClient authPb.AuthClient, riskClient riskPb.RiskClient,
	userLocationClient location2.LocationClient, employmentClient employment.EmploymentClient, emailParserClient emailParserPb.EmailParserClient,
	palCXClient preapprovedloanCxPb.CxClient, screenerClient screener.ScreenerClient, caseManagementClient caseManagementPb.CaseManagementClient,
	bcClient bankCustPb.BankCustomerServiceClient, s3Client cxTypes.LivenessVideoConfigS3Client, genConf *cxGenConf.Config,
	redListClient redlist.RedListClient, payClient payPb.PayClient,
	txnCategorizerClient categorizerPb.TxnCategorizerClient, savingsClient savingsPb.SavingsClient,
	riskProfileClient profilePb.ProfileClient, eventBroker events.Broker, preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient,
	nameCheckClient namecheckVgPb.UNNameCheckClient, fennelClient fennelPb.FennelFeatureStoreClient, scienapticClient vgScienapticPb.ScienapticClient,
	riskS3Client wireTypes.RiskS3Client, orderClient orderPb.OrderServiceClient, accountPIClient accountPIPb.AccountPIRelationClient,
	actorActivityClient aaClient.ActorActivityClient, cxTicketClient ticketPb.TicketClient, sgApiGwKycClient sgKycApiGatewayPb.KYCClient,
	leaClient leaPb.LeaClient, config2 *config.Config) *risk_ops.Service {
	wire.Build(
		wire.NewSet(actor.NewEntityDetailManagerImpl, wire.Bind(new(actor.EntityDetailManager),
			new(*actor.EntityDetailManagerImpl))),
		wire.NewSet(builder.NewRiskReviewTransactionBuilderImpl, wire.Bind(new(builder.RiskReviewTransactionBuilder),
			new(*builder.RiskReviewTransactionBuilderImpl))),
		wire.NewSet(fetcher.NewDataFetcherImpl, wire.Bind(new(fetcher.DataFetcher),
			new(*fetcher.DataFetcherImpl))),
		wire.NewSet(categorizer.NewActivityCategoryManagerImpl,
			wire.Bind(new(categorizer.ActivityCategoryManager), new(*categorizer.ActivityCategoryManagerImpl))),
		risk_ops.NewRiskOpsService,
		wire.NewSet(reviewActions.NewActionFactory, wire.Bind(new(reviewActions.IActionFactory), new(*reviewActions.ActionFactory))),
		review.NewAction,
		cxTypes.LivenessVideoConfigS3ClientProvider,
		reviewActions.NewAccountFreeze,
		reviewActions.NewAccountUnfreeze,
		reviewActions.NewMoveToReview,
		reviewActions.NewRequestUserInfo,
		reviewActions.NewPassAccount,
		reviewActions.NewFailOnboarding,
		reviewActions.NewPassOnboarding,
		reviewActions.NewRetryLiveness,
		reviewActions.NewSnooze,
		reviewActions.NewFailAFU,
		reviewActions.NewPassAFU,
		reviewActions.NewInvestigateLEAActor,
		reviewActions.NewRejectEscalation,
		reviewActions.NewAddLien,
		wire.NewSet(annotations.NewAnnotationsHelper, wire.Bind(new(annotations.IAnnotationsHelper), new(*annotations.AnnotationsHelper))),
		wire.NewSet(comments.NewCommentsHelper, wire.Bind(new(comments.ICommentsHelper), new(*comments.CommentsHelper))),
		watchlist.HandlerWireSet,
		watchlist.UIHandlerWireSet,
		products.FetcherWireSet,
		featureStoreWire.InitializeFeatureStore,
		IFeatureStoreProvider,
		transaction2.WireSet,
		transaction2.NewSelectedOrderWithTransactionManagerImpl,
		transaction2.NewOrderWithTransactionManagerImpl,
	)
	return &risk_ops.Service{}
}

func InitializeRiskOpsWealthService(wOnbClient woCxPb.WealthCxServiceClient) *riskOpsWealth.Service {
	wire.Build(
		riskOpsWealth.NewService,
	)
	return &riskOpsWealth.Service{}
}

func InitializeConnectedAccountService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient, caClient connectedAccountPb.ConnectedAccountClient) *cxConnectedAccount.Service {
	wire.Build(
		cxConnectedAccount.NewConnectedAccountService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &cxConnectedAccount.Service{}
}

// config: {"s3Client": "SalaryProgramLeadManagementConfig().SalaryProgramS3BucketName","s3ClientB2B": "SalaryProgramLeadManagementConfig().SalaryProgramB2BS3BucketName"}
func InitializeSalaryB2BService(client vgLeadsquaredPb.LeadManagementClient, s3Client wireTypes.SalaryProgramNonProdS3Client, s3ClientB2B wireTypes.SalaryProgramB2BS3Client, authClient authPb.AuthClient,
	commsClient commsPb.CommsClient, conf *config.Config) *salaryb2b.SalaryB2BService {
	wire.Build(
		salaryb2b.NewSalaryB2BService,
		getSalaryProgramLeadManagementConfig,
	)
	return &salaryb2b.SalaryB2BService{}
}

func InitializeTicketConsumerService(db cmdTypes.SherlockPGDB, conf *config.Config, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient,
	paymentClient paymentPb.PaymentClient, orderClient order.OrderServiceClient,
	updateTicketPublisher cxTypes.UpdateTicketPublisher, crmIssueTrackerIntegrationPublisher cxTypes.CrmIssueTrackerIntegrationPublisher,
	actorClient actorPb.ActorClient, userClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient, cxGenConf *cxGenConf.Config,
	watsonClient watsonPb.WatsonClient, commsClient commsPb.CommsClient, authClient authPb.AuthClient, ticketUpdateEventPublisher cxTypes.TicketUpdateEventPublisher, ticketClient ticketPb.TicketClient, eventBroker events.Broker) *consumer6.Service {
	wire.Build(
		InitializeTicketConsumerHelper,
		wire.Bind(new(helper7.ITicketHelper), new(*helper7.TicketHelper)),
		consumer6.NewTicketConsumerService,
		token_manager.CsatTokenManagerWireSet,
		csatConfigProvider,
		comms2.NewBaseMessageCreator, comms2.NewSystemTrayCreator, comms2.NewWhatsappCreator, comms2.NewEmailCreator,
		csat.EvaluatorWireSet,
		comms2.SenderWireSet,
		comms2.MessageCreatorFactoryWireSet,
		dao18.IssueConfigDaoWireSet,
		dao9.InAppCsatResponseDaoWireSet,
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		wire.NewSet(manager2.NewIssueCategoryManagerImpl, wire.Bind(new(manager2.IssueCategoryManager), new(*manager2.IssueCategoryManagerImpl))),
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		release.EvaluatorWireSet,
		getDynFeatureReleaseConfig,
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		wire.NewSet(watsonDao.NewIncidentTicketDetailDao, wire.Bind(new(watsonDao.IIncidentTicketDetailDao), new(*watsonDao.IncidentTicketDetailDao))),
		wire.NewSet(watsonDao.NewIncidentDao, wire.Bind(new(watsonDao.IIncidentDao), new(*watsonDao.IncidentDao))),
	)
	return &consumer6.Service{}
}

func rateLimiterConfigProvider(conf *config.Config) *cfg.RateLimitConfig {
	return conf.RlConfig
}

func InitializeWatsonConsumerService(db cmdTypes.SherlockPGDB, conf *config.Config, rlRedisStore cmdTypes.RateLimiterRedisStore, celestialClient celestialPb.CelestialClient,
	signalWorkflowPublisher cxTypes.CelestialSignalWorkflowPublisher, eventBroker events.Broker,
	simulatorWatsonClientClient simulatorWcPb.WatsonClientClient,
	mockCxWatsonClient mockWatsonClient.MockWatsonClientServiceClient,
	watsonSavingsClient savingsWatsonClient.WatsonClient,
	watsonOnboardingClient onboardingWatsonClient.WatsonClient,
	payIncidentManagerClient payIncidentManagerPb.PayIncidentManagerClient,
	investmentWatsonClient investmentWatsonPb.WatsonClient,
	watsonDepositsClient depositWatsonPb.WatsonClient,
	p2pWatsonClient p2pIncidentManagerPb.IncidentManagerClient,
	watsonClient watsonPb.WatsonClient,
	errorActivityWatsonClient watsonInfoProviderPb.WatsonInfoProviderClient,
	manualTicketStageWiseCommsClient stageWiseCommsPb.ManualTicketStageWiseCommsClient) (*watsonConsumer.WatsonConsumerService, error) {
	wire.Build(
		rateLimiterConfigProvider,
		cmdTypes.RateLimiterRedisStoreRedisClientProvider,
		rlwireset.WireSetV1,
		WatsonIncidentInfoCollectorFactoryProvider,
		wire.NewSet(watsonHelper.NewRateLimitHelper, wire.Bind(new(watsonHelper.IRateLimitHelper), new(*watsonHelper.RateLimitHelper))),
		wire.NewSet(internal_celestial.NewCelestialProcessor, wire.Bind(new(internal.ICelestialProcessor), new(*internal_celestial.Processor))),
		wire.NewSet(watsonDao.NewIncidentDao, wire.Bind(new(watsonDao.IIncidentDao), new(*watsonDao.IncidentDao))),
		watson2.NewWatsonHelper,
		watson2.NewWatsonHelperV2,
		wire.NewSet(watson2.NewHelperFactoryImpl, wire.Bind(new(watson2.HelperFactory), new(*watson2.HelperFactoryImpl))),
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		storageV2.DefaultTxnExecutorWireSet,
		GormDBProvider,
		helper10.NewWatsonConsumerHelper,
		helper10.NewWatsonConsumerHelperV2,
		wire.NewSet(helper10.NewConsumerHelperFactoryImpl, wire.Bind(new(helper10.ConsumerHelperFactory), new(*helper10.ConsumerHelperFactoryImpl))),
		wire.NewSet(watsonDao.NewIncidentTicketDetailDao, wire.Bind(new(watsonDao.IIncidentTicketDetailDao), new(*watsonDao.IncidentTicketDetailDao))),
		wire.NewSet(watsonTicketEventConsumer.NewTicketEventProcessorFactory, wire.Bind(new(watsonTicketEventConsumer.ITicketEventProcessorFactory), new(*watsonTicketEventConsumer.TicketEventProcessorFactory))),
		watsonTicketEventConsumer.NewTicketStatusChangeEventProcessor,
		watsonTicketEventConsumer.NewTicketManualUpdateEventProcessor,
		watsonTicketEventConsumer.NewTicketManualCreationEventProcessor,
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		wire.NewSet(manager2.NewIssueCategoryManagerImpl, wire.Bind(new(manager2.IssueCategoryManager), new(*manager2.IssueCategoryManagerImpl))),
		watsonConsumer.NewWatsonConsumerService,
	)
	return &watsonConsumer.WatsonConsumerService{}, nil
}

func InitializeSherlockUserDao(db cmdTypes.SherlockPGDB) *sherlockUserWrapper.SherlockUser {
	wire.Build(
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		wire.NewSet(dao11.NewSherlockUserInfoDao, wire.Bind(new(dao11.ISherlockUserInfoDao), new(*dao11.SherlockUserInfoDao))),
		wire.NewSet(dao11.NewSherlockUserRoleDao, wire.Bind(new(dao11.ISherlockUserRoleDao), new(*dao11.SherlockUserRoleDao))),
		wire.NewSet(sherlockUserWrapper.NewSherlockUserDao, wire.Bind(new(sherlockUserWrapper.ISherlockUser), new(*sherlockUserWrapper.SherlockUser))),
	)
	return &sherlockUserWrapper.SherlockUser{}
}

func InitializeSherlockUserService(db cmdTypes.SherlockPGDB, casbinClient casbinPb.CasbinClient) *sherlock_user.Service {
	wire.Build(
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		wire.NewSet(dao11.NewSherlockUserInfoDao, wire.Bind(new(dao11.ISherlockUserInfoDao), new(*dao11.SherlockUserInfoDao))),
		wire.NewSet(dao11.NewSherlockUserRoleDao, wire.Bind(new(dao11.ISherlockUserRoleDao), new(*dao11.SherlockUserRoleDao))),
		wire.NewSet(sherlockUserWrapper.NewSherlockUserDao, wire.Bind(new(sherlockUserWrapper.ISherlockUser), new(*sherlockUserWrapper.SherlockUser))),
		provisioner2.NewEpifiUserProvisioner,
		provisioner2.NewCasbinUserProvisioner,
		sherlock_user.NewUserProvisionerFactory,
		sherlock_user.NewSherlockUserService,
	)
	return &sherlock_user.Service{}
}

// config: {"s3Client": "CallRecording().CallRecordingBucketName"}
func InitializeCallService(db cmdTypes.SherlockPGDB,
	freshdeskClient freshdesk.FreshdeskClient, ticketClient ticketPb.TicketClient,
	conf *config.Config, s3Client cxTypes.CallRecordingS3Client) *call.Service {
	wire.Build(
		cxTypes.CallRecordingS3ClientProvider,
		wire.NewSet(dao12.NewCallDetailsDao, wire.Bind(new(dao12.ICallDetailsDao), new(*dao12.CallDetailsDao))),
		wire.NewSet(helper11.NewCallHelper, wire.Bind(new(helper11.ICallHelper), new(*helper11.CallHelper))),
		call.NewCallService,
		getCallConfig,
		getTicketConfig,
	)
	return &call.Service{}
}

func InitializeOzonetelConsumerService(db cmdTypes.SherlockPGDB, updateTicketPublisher cxTypes.UpdateTicketPublisher,
	userClient userPb.UsersClient, actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient, gconf *cxGenConf.Config,
	commsClient commsPb.CommsClient, redisClient cxTypes.CxRedisStore) *ozonetel_consumer.Service {
	wire.Build(
		NewRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wire.NewSet(dao12.NewCallDetailsDao, wire.Bind(new(dao12.ICallDetailsDao), new(*dao12.CallDetailsDao))),
		wire.NewSet(processor8.NewCallStageFactory, wire.Bind(new(processor8.ICallStageFactory), new(*processor8.CallStageFactory))),
		processor8.NewCallInitiatedProcessor,
		processor8.NewAgentAssignedProcessor,
		processor8.NewCallEndedProcessor,
		ozonetel_consumer.NewService,
		getDynamicCallConfig,
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
	)
	return &ozonetel_consumer.Service{}
}

func InitializeCallDao(db cmdTypes.SherlockPGDB) *dao12.CallDetailsDao {
	wire.Build(
		wire.NewSet(dao12.NewCallDetailsDao, wire.Bind(new(dao12.ICallDetailsDao), new(*dao12.CallDetailsDao))),
	)
	return &dao12.CallDetailsDao{}
}

func InitializeP2PInvestmentService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient, p2pInvestmentCxClient p2pCxPb.CxClient) *p2pinvestment.Service {
	wire.Build(
		p2pinvestment.NewService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &p2pinvestment.Service{}
}

func InitializePreApprovedLoanService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient, palCxClient preapprovedloanCxPb.CxClient,
	catalogManagerClient investmentCatalogPb.CatalogManagerClient, ticketClient ticketPb.TicketClient, fdClient freshdesk.FreshdeskClient) *preapprovedloan.Service {
	wire.Build(
		preapprovedloan.NewService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		factory.NewViewFactory,
		generator.NewBaseWebViewGenerator,
		impl.NewLamfWebViewGenerator,
		providers.NewBaseDataProvider,
	)
	return &preapprovedloan.Service{}
}

func InitializeFireflyService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient,
	ccCxClient ccCxPb.CxClient, ffClient ffPb.FireflyClient, ffBillingClient ffBillPb.BillingClient, ffAccountingClient ffAccountsPb.AccountingClient,
	creditLimitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient, ffLmsClient ffLmsPb.LoanManagementSystemClient, actorClient actorPb.ActorClient,
	rewardsClient rewardsPb.RewardsGeneratorClient, bcClient bankCustPb.BankCustomerServiceClient, depositClient depositPb.DepositClient,
	projectorClient rewardsProjectionPb.ProjectorServiceClient, txnAggClient ffPinotPb.TxnAggregatesClient, client segmentPb.SegmentationServiceClient) *firefly.Service {
	wire.Build(
		firefly.NewService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &firefly.Service{}
}

// config: {"salaryProgramS3Client": "SalaryOpsConfig().SalaryProgramS3BucketName()"}
func InitializeSalaryDataOpsService(gconf *cxGenConf.Config, conf *config.Config, dbconn cmdTypes.SherlockPGDB, usersClient userPb.UsersClient,
	actorClient actorPb.ActorClient, empClient employment.EmploymentClient,
	salaryClient salaryprogram.SalaryProgramClient, orderClient order.OrderServiceClient, piClient piPb.PiClient, vkycClient vkycPb.VKYCClient, userGroupClient userGroupPb.GroupClient,
	vgPaymentClient vgPaymentPb.PaymentClient, salaryCxClient salaryCxPb.CxClient, txnCatClient categorizerPb.TxnCategorizerClient,
	bcClient bankCustPb.BankCustomerServiceClient, healthInsuranceClient healthinsurancePb.HealthInsuranceClient, salaryProgramS3Client cxTypes.SalaryProgramS3Client,
	employerNameCategoriserClient employernamecategoriserPb.EmployerNameCategoriserClient, recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient) *salarydataops.Service {
	wire.Build(
		salarydataops.NewSalaryDataOpsService,
		getSalaryOpsConfig,
		getDynSalaryOpsConfig,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &salarydataops.Service{}
}

func GormDBProvider(db cmdTypes.SherlockPGDB) *gorm.DB {
	return db
}

func sherlockBannersCollectorFactoryProvider(
	getBannersHelper sbHelper.GetBannersHelper,
	palSherlockBannersClient palSherlockBannersPb.PreApprovedLoanSherlockBannersClient,
	tieringClient beTieringPb.TieringClient,
	riskProfileClient profilePb.ProfileClient,
) sbCollector.ISherlockBannersCollectorFactory {
	sherlockBannersCollectorFactory := sbCollector.NewSherlockBannersCollectorFactory()
	cxSherlockBannersCollector := sbCollector.NewCxSherlockBannersCollector(getBannersHelper)
	sherlockBannersCollectorFactory.RegisterSherlockBannersCollector(types.ServiceName_CX_SERVICE, cxSherlockBannersCollector)
	sherlockBannersCollectorFactory.RegisterSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE, palSherlockBannersClient)
	sherlockBannersCollectorFactory.RegisterSherlockBannersCollector(types.ServiceName_TIERING_SERVICE, tieringClient)
	sherlockBannersCollectorFactory.RegisterSherlockBannersCollector(types.ServiceName_RISK_SERVICE, riskProfileClient)
	return sherlockBannersCollectorFactory
}

func InitializeSherlockBannersService(db cmdTypes.SherlockPGDB, conf *cxGenConf.Config, palSherlockBannersClient palSherlockBannersPb.PreApprovedLoanSherlockBannersClient, tieringClient beTieringPb.TieringClient, riskProfileClient profilePb.ProfileClient) *sherlock_banners.Service {
	wire.Build(
		sherlock_banners.NewSherlockBannersService,
		wire.NewSet(dao13.NewSherlockBannerDao, wire.Bind(new(dao13.ISherlockBannerDao), new(*dao13.SherlockBannerDao))),
		wire.NewSet(dao13.NewBannerMappingDao, wire.Bind(new(dao13.IBannerMappingDao), new(*dao13.BannerMappingDao))),
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		sherlockBannersCollectorFactoryProvider,
		wire.NewSet(sbHelper.NewGetBannersFromDb, wire.Bind(new(sbHelper.GetBannersHelper), new(*sbHelper.GetBannersFromDb))),
	)
	return &sherlock_banners.Service{}
}

func InitializeSherlockFeedbackDetailsService(db cmdTypes.SherlockPGDB, conf *config.Config) *sherlock_feedback.Service {
	wire.Build(
		sherlock_feedback.NewSherlockFeedbackService,
		wire.NewSet(dao16.NewSherlockFeedbackDetailsDao, wire.Bind(new(dao16.ISherlockFeedbackDetailsDao), new(*dao16.SherlockFeedbackDetailsDao))),
	)
	return &sherlock_feedback.Service{}
}

func InitializeIssueResolutionFeedbackService(db cmdTypes.SherlockPGDB, conf *cxGenConf.Config, celestialClient celestialPb.CelestialClient, signalWorkflowPublisher cxTypes.CelestialSignalWorkflowPublisher, ticketClient ticketPb.TicketClient) *irfService.Service {
	wire.Build(
		irfService.NewService,
		wire.NewSet(dao14.NewIssueResolutionFeedbackDao, wire.Bind(new(dao14.IIssueResolutionFeedbackDao), new(*dao14.IssueResolutionFeedbackDao))),
		wire.NewSet(dao14.NewIssueResolutionUserResponseLogDao, wire.Bind(new(dao14.IIssueResolutionUserResponseLogDao), new(*dao14.IssueResolutionUserResponseLogDao))),
		wire.NewSet(internal_celestial.NewCelestialProcessor, wire.Bind(new(internal.ICelestialProcessor), new(*internal_celestial.Processor))),
	)
	return &irfService.Service{}
}

func InitializeActivityProcessor(db cmdTypes.SherlockPGDB, commsClient commsPb.CommsClient, workerConf *cxWorkerConfig.Config,
	actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient, updateTicketPublisher cxTypes.UpdateTicketPublisher, usersClient userPb.UsersClient, eventBroker events.Broker,
	infoCollectorFactory watsonCollector.IWatsonIncidentInfoCollectorFactory, watsonClient watsonPb.WatsonClient, ticketClient ticketPb.TicketClient) *activity.Processor {
	wire.Build(
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		activity.NewProcessor,
		internal_irf.NewDisputeProcessor,
		wire.NewSet(dao14.NewIssueResolutionFeedbackDao, wire.Bind(new(dao14.IIssueResolutionFeedbackDao), new(*dao14.IssueResolutionFeedbackDao))),
		wire.NewSet(dao14.NewIssueResolutionUserResponseLogDao, wire.Bind(new(dao14.IIssueResolutionUserResponseLogDao), new(*dao14.IssueResolutionUserResponseLogDao))),
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(internal_irf.NewIssueResolutionFeedbackProcessorFactory, wire.Bind(new(internal_irf.IIssueResolutionFeedbackProcessorFactory), new(*internal_irf.IssueResolutionFeedbackProcessorFactory))),
		watsonComms.NewEmailProcessor,
		watsonComms.NewPushNotificationProcessor,
		watsonComms.NewSmsProcessor,
		watsonComms.NewWhatsappProcessor,
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		wire.NewSet(manager2.NewIssueCategoryManagerImpl, wire.Bind(new(manager2.IssueCategoryManager), new(*manager2.IssueCategoryManagerImpl))),
		activity_helper.NewActivityHelperV2,
		activity_helper.NewWatsonActivityHelper,
		wire.NewSet(activity_helper.NewActivityHelperFactoryImpl, wire.Bind(new(activity_helper.ActivityHelperFactory), new(*activity_helper.ActivityHelperFactoryImpl))),
		watson2.NewWatsonHelper,
		watson2.NewWatsonHelperV2,
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		wire.NewSet(dao9.NewTicketDetailsTransformationDao, wire.Bind(new(dao9.ITicketDetailsTransformationDao), new(*dao9.TicketDetailsTransformationDao))),
		wire.NewSet(watson2.NewHelperFactoryImpl, wire.Bind(new(watson2.HelperFactory), new(*watson2.HelperFactoryImpl))),
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		wire.NewSet(watsonComms.NewSendCommsFactory, wire.Bind(new(watsonComms.ISendCommsFactory), new(*watsonComms.SendCommsFactory))),
		wire.NewSet(watsonDao.NewIncidentDao, wire.Bind(new(watsonDao.IIncidentDao), new(*watsonDao.IncidentDao))),
		wire.NewSet(watsonDao.NewIncidentTicketDetailDao, wire.Bind(new(watsonDao.IIncidentTicketDetailDao), new(*watsonDao.IncidentTicketDetailDao))),
		wire.NewSet(watsonDao.NewIncidentCommsDetailDao, wire.Bind(new(watsonDao.IIncidentCommsDetailDao), new(*watsonDao.IncidentCommsDetailDao))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
	)
	return &activity.Processor{}
}

func InitializeWatsonInfoProvider(db cmdTypes.SherlockPGDB, userClient userPb.UsersClient) *watsonInfoProvider.WatsonInfoProvider {
	wire.Build(
		watsonInfoProvider.NewWatsonInfoProvider,
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		storageV2.DefaultTxnExecutorWireSet,
		GormDBProvider,
	)
	return &watsonInfoProvider.WatsonInfoProvider{}
}

func InitializeLiveChatFallbackService(db cmdTypes.SherlockPGDB, gconf *cxGenConf.Config, freshChatClient vgFcPb.FreshchatClient, vmClient vendormapping.VendorMappingServiceClient,
	userClient userPb.UsersClient, actorClient actorPb.ActorClient, redisClient cxTypes.CxRedisStore, conf *config.Config, onbClient onboarding2.OnboardingClient,
	salaryProgramClient salaryprogram.SalaryProgramClient, commsClient commsPb.CommsClient) *livechatfallback.Service {
	wire.Build(
		livechatfallback.NewService,
		wire.NewSet(helper8.NewFreshchatUserMappingHelper, wire.Bind(new(helper8.IFreshchatUserMappingHelper), new(*helper8.FreshchatUserMappingHelper))),
		processor7.NewLowPriorityRuleProcessor,
		processor7.NewHighPriorityRuleProcessor,
		processor7.NewCurrentlyOnbRuleProcessor,
		processor7.NewSalaryProgramUsersRuleProcessor,
		wire.NewSet(routing_engine.NewRoutingEngine, wire.Bind(new(routing_engine.IRoutingEngine), new(*routing_engine.RoutingEngine))),
		wire.NewSet(priority_routing_helper.NewPriorityDataHelper, wire.Bind(new(priority_routing_helper.IHelper), new(*priority_routing_helper.Helper))),
		wire.NewSet(dao10.NewUserPriorityPropertiesDao, wire.Bind(new(dao10.IUserPriorityPropertiesDAO), new(*dao10.UserPriorityPropertiesDao))),
		wire.NewSet(processor7.NewRuleFactory, wire.Bind(new(processor7.IFactory), new(*processor7.Factory))),
		wire.NewSet(dao4.NewFreshchatUserMappingDao, wire.Bind(new(dao4.IFreshchatUserMappingDao), new(*dao4.FreshchatUserMappingDao))),
		getPriorityRoutingConfig,
	)
	return &livechatfallback.Service{}
}

func NewRedisClient(cxRedisStore cxTypes.CxRedisStore) *redis.Client {
	return cxRedisStore
}

func InitializeFreshchatConsumerService(conf *config.Config, senseforthLiveChatFallbackClient vgSenseforthPb.SenseforthLiveChatFallbackClient, freshchatClient vgFcPb.FreshchatClient, redisClient cxTypes.CxRedisStore) *chatConsumer.Service {
	wire.Build(
		chatConsumer.NewService,
		wire.NewSet(processor11.NewFreshchatActionFactory, wire.Bind(new(processor11.IFreshchatActionFactory), new(*processor11.FreshchatActionFactory))),
		processor11.NewMessageCreateProcessor,
		processor11.NewConversationAssignmentProcessor,
		processor11.NewConversationResolutionProcessor,
		NewRedisClient,
		wire.NewSet(helper8.NewFreshchatInfoHelper, wire.Bind(new(helper8.IFreshchatInfotHelper), new(*helper8.FreshchatInfoHelper))),
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &chatConsumer.Service{}
}

func InitializeChatbotWorkflowService(cxConf *config.Config, cxGenConf *cxGenConf.Config, faqServingClient servingPb.ServeFAQClient, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient, fdClient freshdesk.FreshdeskClient, vkycClient vkycPb.VKYCClient,
	bankCustClient bankCustPb.BankCustomerServiceClient, orderTxnClient cx.CXClient, cardProvisioningClient cardPb.CardProvisioningClient,
	ffClient ffPb.FireflyClient, ffAccountingClient ffAccountsPb.AccountingClient, payClient payPb.PayClient, payCxClient payCxPb.CXClient, employmentClient employment.EmploymentClient,
	onbClient onboarding2.OnboardingClient, livClient livenessPb.LivenessClient, rewardOffersClient rewardOfferPb.RewardOffersClient, salaryClient salaryprogram.SalaryProgramClient, inAppReferralClient inappReferralPb.InAppReferralClient,
	rewardsCreditCardTxnEventQueuePublisher wireTypes.RewardsCreditCardTxnEventQueuePublisher, rewardsOrderUpdateEventQueuePublisher wireTypes.RewardsOrderUpdateEventQueuePublisher, rewardsGeneratorClient rewardsPb.RewardsGeneratorClient,
	accountPiClient accountPIPb.AccountPIRelationClient, upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	actorClient actorPb.ActorClient, piClient piPb.PiClient, usersClient userPb.UsersClient,
	pClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient, savingsClient savingsPb.SavingsClient,
	depositClient depositPb.DepositClient, investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient,
	p2pInvestmentClient p2investmentPb.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient,
	usstocksOrderManagerClient orderManagerPb.OrderManagerClient, accountBalanceClinet accountBalancePb.BalanceClient,
	db cmdTypes.SherlockPGDB, disputeExtPub cxTypes.DisputeExternalPublisher, userGroupClient userGroupPb.GroupClient,
	createDisputeTicketPub cxTypes.DisputeCreateTicketPublisher, commsClient commsPb.CommsClient, vgDisputeClient dispute2.DisputeClient,
	riskProfileClient profilePb.ProfileClient) *botWorkflow.Service {
	wire.Build(
		botWorkflow.NewService,
		fetch_data_processor.NewFaqProcessor,
		fetch_data_processor.NewUserDetailsProcessor,
		fetch_data_processor.NewTxnListProcessor,
		fetch_data_processor.NewTxnDetailsProcessor,
		fetch_data_processor.NewDebitCardTrackingProcessor,
		fetch_data_processor.NewCreditCardStateProcessor,
		fetch_data_processor.NewCreditCardTxnListProcessor,
		fetch_data_processor.NewEmploymentDataProcessor,
		fetch_data_processor.NewLivenessDataProcessor,
		execute_action_processor.NewCreateTicketProcessor,
		fetch_data_processor.NewFetchDisputeProcessor,
		fetch_data_processor.NewFetchRewardOffersForUsersProcessor,
		fetch_data_processor.NewCheckSalaryProgramAmazonVoucherEligibilityProcessor,
		wire.NewSet(botWorkflow.NewFetchDataFactory, wire.Bind(new(botWorkflow.IFetchDataFactory), new(*botWorkflow.FetchDataFactory))),
		wire.NewSet(botWorkflow.NewExecuteActionFactory, wire.Bind(new(botWorkflow.IExecuteActionFactory), new(*botWorkflow.ExecuteActionFactory))),
		wire.NewSet(helper3.NewTransactionsDataCollectorHelper, wire.Bind(new(helper3.ITransactionsDataCollectorHelper), new(*helper3.TransactionsDataCollectorHelper))),
		fetch_data_processor.NewFetchChargesForActorProcessor,
		fetch_data_processor.NewDisplayTxnReasonProcessor,
		fetch_data_processor.NewFetchFailedTxnsProcessor,
		fetch_data_processor.NewFetchRewardEventDetailsProcessor,
		fetch_data_processor.NewFetchSalaryProgramRegistrationDetailsProcessor,
		fetch_data_processor.NewFetchRewardForEventProcessor,
		wire.NewSet(helper3.NewDataCollectorHelper, wire.Bind(new(helper3.IDataCollectorHelper), new(*helper3.DataCollectorHelper))),
		wire.NewSet(helper.NewCustomerIdentifier, wire.Bind(new(helper.ICustomerIdentifier), new(*helper.CustomerIdentifier))),
		helper2.NewDisputeHelper,
		GormDBProvider,
		storageV2.DefaultTxnExecutorWireSet,
		getDynDispute,
		wire.NewSet(dao6.NewDisputeDao, wire.Bind(new(dao6.IDisputeDao), new(*dao6.DisputeDao))),
		wire.NewSet(dao6.NewDisputeConfigDao, wire.Bind(new(dao6.IDisputeConfigDao), new(*dao6.DisputeConfigDao))),
		wire.NewSet(dao6.NewFederalDmpDisputeDetailDao, wire.Bind(new(dao6.IFederalDmpDisputeDetailDao), new(*dao6.FederalDmpDisputeDetailDao))),
		wire.Bind(new(helper2.IDisputeHelper), new(*helper2.DisputeHelper)),
		fetch_data_processor.NewBalanceRefreshProcessor,
		fetch_data_processor.NewPredefinedMessageTemplateProcessor,
		fetch_data_processor.NewFetchUserTransactionsProcessor,
	)
	return &botWorkflow.Service{}
}

func InitializeUserIssueInfoService(redisClient cxTypes.CxRedisStore) *user_issue_info.Service {
	wire.Build(
		user_issue_info.NewService,
		NewRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &user_issue_info.Service{}
}

// config: {"s3Client": "InternationalFundTransfer().DocumentsBucketName()"}
func NewInternationalFundTransferService(
	config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient,
	fgClient payFileGeneratorPb.FileGeneratorClient, s3Client cxTypes.PayIFTDocumentsS3Client,
	iftClient iftPb.InternationalFundTransferClient) *internationalFundTransferCxDataCollector.Service {
	wire.Build(
		cxTypes.PayIFTDocumentsS3ClientProvider,
		internationalFundTransferCxDataCollector.NewInternationalFundTransferService,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &internationalFundTransferCxDataCollector.Service{}
}

func InitializeSprinklrEventsService(updateTicketPub cxTypes.UpdateTicketPublisher, createTicketPub cxTypes.CreateTicketPublisher, db cmdTypes.SherlockPGDB, userClient userPb.UsersClient, actorClient actorPb.ActorClient, chatClient chatPb.ChatsClient,
	vmClient vendormapping.VendorMappingServiceClient) *sprinklr.EventsService {
	wire.Build(
		InitializeCustomerIdentifier,
		sprinklr.NewEventsService,
		wire.NewSet(dao17.NewSprinklrCaseDetailsDao, wire.Bind(new(dao17.ISprinklrCaseDetailsDao), new(*dao17.SprinklrCaseDetailsDao))),
	)
	return &sprinklr.EventsService{}
}

func InitializeAlfredService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient,
	alfredClient alfredPb.AlfredClient, savingsClient savingsPb.SavingsClient, operationalStatusClient operStatusPb.OperationalStatusServiceClient) *alfred.Service {
	wire.Build(
		alfred.NewService,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &alfred.Service{}
}

func InitializeTieringService(config *config.Config, genConfig *cxGenConf.Config, dbConn cmdTypes.SherlockPGDB, userClient userPb.UsersClient, tieringClient beTieringPb.TieringClient) *tiering.Service {
	wire.Build(
		tiering.NewService,
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
	)
	return &tiering.Service{}
}

func InitializeCrmIssueTrackerIntegrationService(ctx context.Context, db cmdTypes.SherlockPGDB, gconf *cxGenConf.Config, conf *config.Config,
	freshdeskTicketClient ticketPb.TicketClient, freshdeskClient freshdesk.FreshdeskClient, ffClient ffPb.FireflyClient) (*crm_issue_tracker_integration.Service, error) {
	wire.Build(
		wire.NewSet(citTicketTranslator.NewTicketTranslatorFactory, wire.Bind(new(citTicketTranslator.ITicketTranslatorFactory), new(*citTicketTranslator.TicketTranslatorFactory))),
		citTicketTranslator.NewFreshdeskMonorailTranslator,
		wire.NewSet(citMonorailDescriptionProcessor.NewMonorailDescriptionFactory, wire.Bind(new(citMonorailDescriptionProcessor.IMonorailDescriptionFactory), new(*citMonorailDescriptionProcessor.MonorailDescriptionFactory))),
		citMonorailDescriptionProcessor.NewLendingCreditCardProcessor,
		wire.NewSet(citHelper.NewCrmIssueTrackerIntegrationHelperFactory, wire.Bind(new(citHelper.ICrmIssueTrackerIntegrationHelperFactory), new(*citHelper.CrmIssueTrackerIntegrationHelperFactory))),
		citHelper.NewFreshdeskMonorailApiHelper,
		wire.NewSet(dao9.NewTicketDetailsTransformationDao, wire.Bind(new(dao9.ITicketDetailsTransformationDao), new(*dao9.TicketDetailsTransformationDao))),
		wire.NewSet(citDao.NewCrmToIssueTrackerMappingDao, wire.Bind(new(citDao.ICrmToIssueTrackerMappingDao), new(*citDao.CrmToIssueTrackerMappingDao))),
		crm_issue_tracker_integration.NewService,
		initialiseMonorailHttpClient,
		wire.Bind(new(monorailApi.IMonorailApiWrapper), new(*monorailApi.MonorailApiWrapper)),
	)
	return &crm_issue_tracker_integration.Service{}, nil
}

func InitializeCrmIssueTrackerIntegrationConsumerService(conf *cxGenConf.Config, crmIssueTrackerClient citPb.CrmIssueTrackerIntegrationClient) *citConsumer.Service {
	wire.Build(
		citConsumerProcessor.NewFreshdeskTicketChangeProcessor,
		citConsumerProcessor.NewFreshdeskTicketConversationProcessor,
		citConsumerProcessor.NewMonorailUpdatesCommentsProcessor,
		wire.NewSet(citConsumerProcessor.NewCrmIssueTrackerEventProcessorFactory, wire.Bind(new(citConsumerProcessor.ICrmIssueTrackerEventProcessorFactory), new(*citConsumerProcessor.CrmIssueTrackerEventProcessorFactory))),
		citConsumer.NewService,
	)
	return &citConsumer.Service{}
}

func InitializeDataCollectorHelper(actorClient actorPb.ActorClient, piClient piPb.PiClient, usersClient userPb.UsersClient,
	pClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient, savingsClient savingsPb.SavingsClient,
	depositClient depositPb.DepositClient, investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient,
	p2pInvestmentClient p2investmentPb.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient,
	usstocksOrderManagerClient orderManagerPb.OrderManagerClient, accountBalanceClinet accountBalancePb.BalanceClient, ffClient ffPb.FireflyClient) *helper3.DataCollectorHelper {

	wire.Build(
		wire.NewSet(helper3.NewDataCollectorHelper, wire.Bind(new(helper3.IDataCollectorHelper), new(*helper3.DataCollectorHelper))),
	)
	return &helper3.DataCollectorHelper{}
}

func InitializeQuestionResponseSubscriptionClient(db cmdTypes.SherlockPGDB, eventBroker events.Broker) *feedback_subscription.FeedbackSubscriptionClient {
	wire.Build(
		wire.NewSet(dao9.NewInAppCsatResponsesDao, wire.Bind(new(dao9.IInAppCsatResponseDao), new(*dao9.InAppCsatResponsesDao))),
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		feedback_subscription.NewFeedbackSubscriptionClient,
	)
	return &feedback_subscription.FeedbackSubscriptionClient{}
}

func getAuthFactoryRetryLimit(conf *config.Config) *config.AuthFactorRetryLimit {
	return conf.AuthFactorRetryLimit
}

func getEmailVerificationConf(conf *config.Config) *config.EmailVerification {
	return conf.EmailVerification
}

func getCustomerAuthConf(conf *config.Config) *config.CustomerAuth {
	return conf.CustomerAuth
}

func getTransactionConf(conf *config.Config) *config.Transaction {
	return conf.Transaction
}

func getOrderConfig(conf *config.Config) *config.OrderConfig {
	return conf.OrderConfig
}

func getMobilePromptVerificationConf(conf *config.Config) *config.MobilePromptVerification {
	return conf.MobilePromptVerification
}

func getSherlockConf(conf *config.Config) *config.Sherlock {
	return conf.Sherlock
}

func getSecrets(conf *config.Config) *cfg.Secrets {
	return conf.Secrets
}

func getHttpClientForSherlock(conf *config.Config) *http.Client {
	tr := &http.Transport{
		MaxIdleConns:    conf.Sherlock.ClientMaxIdleConns,
		IdleConnTimeout: time.Duration(conf.Sherlock.ClientIdleConnTimeout) * time.Second,
	}

	httpClient := &http.Client{Transport: tr}
	return httpClient
}

func iftSlackAlertClientProvider(conf *config.Config) cxTypes.IftSlackAlertClient {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.IFTReportsSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.IFTReportsSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func iftConfigProvider(conf *config.Config) *config.InternationalFundsTransferConfig {
	return conf.InternationalFundsTransferConfig
}

func getAuthFunc(conf *config.Config) interceptor.AuthFunction {
	env := conf.Application.Environment
	if env == cfg.TestEnv {
		return interceptor.MockVerifyAccessToken
	}
	return interceptor.VerifyAccessToken
}

func getCognitoIDPClient(ctx context.Context, conf *config.Config) *cognitoidentityprovider.Client {
	awsConf, err := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		panic(err)
	}
	return cognitoidentityprovider.NewFromConfig(awsConf)
}

func getCognitoUserPoolId(conf *config.Config) string {
	return conf.Cognito.UserPoolId
}

func getAuthValidationConfig(conf *config.Config) *config.AuthValidation {
	return conf.AuthValidation
}

func getCallRoutingConfig(gconf *cxGenConf.Config) *cxGenConf.CallRoutingConfig {
	return gconf.CallRoutingConfig()
}

func getPriorityRoutingConfig(conf *config.Config) *config.PriorityRoutingConfig {
	return conf.PriorityRoutingConfig
}

func getFeatureReleaseConfig(conf *config.Config) *releaseConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig
}

func getDynFeatureReleaseConfig(gconf *cxGenConf.Config) *releaseGenConf.FeatureReleaseConfig {
	return gconf.FeatureReleaseConfig()
}

func csatConfigProvider(conf *cxGenConf.Config) *cxGenConf.CsatConfig {
	return conf.TicketConfig().CsatConfig()
}

func getCallConfig(conf *config.Config) *config.CallConfig {
	return conf.CallConfig
}

func getDynamicCallConfig(gconf *cxGenConf.Config) *cxGenConf.CallConfig {
	return gconf.CallConfig()
}

func getFreshChatConfig(conf *config.Config) *config.FreshChatConfig {
	return conf.FreshChatConfig
}

func getDynSalaryOpsConfig(gconf *cxGenConf.Config) *cxGenConf.SalaryOpsConfig {
	return gconf.SalaryOpsConfig()
}

func getSalaryOpsConfig(conf *config.Config) *config.SalaryOpsConfig {
	return conf.SalaryOpsConfig
}

func getTicketConfig(conf *config.Config) *config.TicketConfig {
	return conf.TicketConfig
}

func getDynTicketConfig(gconf *cxGenConf.Config) *cxGenConf.TicketConfig {
	return gconf.TicketConfig()
}

func getDynPayout(gconf *cxGenConf.Config) *cxGenConf.Payout {
	return gconf.Payout()
}

func getDynDispute(gconf *cxGenConf.Config) *cxGenConf.Dispute {
	return gconf.Dispute()
}

func getDispute(conf *config.Config) *config.Dispute {
	return conf.Dispute
}

func getAuditLog(conf *config.Config) *config.AuditLog {
	return conf.AuditLog
}

func getWatsonConfig(conf *config.Config) *config.WatsonConfig {
	return conf.WatsonConfig
}

func getKYCConfig(conf *config.Config) *config.KYCConfig {
	return conf.KYCConfig
}

func getSherlockUserRequestsConfig(conf *config.Config) *config.SherlockUserRequestsConfig {
	return conf.SherlockUserRequestsConfig
}

func getOnboardingStageDetailsMapping(conf *config.Config) map[string]*config.OnboardingStageDetails {
	return conf.OnboardingStageDetailsMapping
}

func getUsStocksOpsConfig(conf *config.Config) *config.UsStocksOpsConfig {
	return conf.UsStocksOpsConfig
}

func getAppLogsNotificationContent(conf *config.Config) *config.AppLogsNotificationContent {
	return conf.AppLogsNotificationContent
}

func getCommsConf(conf *config.Config) *config.Comms {
	return conf.Comms
}

func getBulkTicketJobConfig(conf *config.Config) *config.BulkTicketJobConfig {
	return conf.BulkTicketJobConfig
}

func getReferralConfig(conf *config.Config) *config.ReferralConfig {
	return conf.ReferralConfig
}

func getSalaryProgramLeadManagementConfig(conf *config.Config) *config.SalaryProgramLeadManagementConfig {
	return conf.SalaryProgramLeadManagementConfig
}

func getDynAppLog(gconf *cxGenConf.Config) *cxGenConf.AppLog {
	return gconf.AppLog()
}

// Initialise the HTTP client to invoke Monorail APIs (deployed on GCP) with the required credentials
func initialiseMonorailHttpClient(ctx context.Context, conf *config.Config) *pkgMonorailApiWrapper.MonorailApiWrapper {
	keyJson := conf.Secrets.Ids[config.MonorailServiceAccountKey]
	key := &monorailPayload.ServiceAccountKey{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(keyJson), key)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshal monorail service account key", zap.Error(err))
		return nil
	}
	return pkgMonorailApiWrapper.NewMonorailApiWrapper(ctx, conf.MonorailConfig, key)
}

// Initialise the HTTP client to invoke Airflow APIs with the required credentials
func initialiseAirflowHttpClient(conf *config.Config) (*pkgAirflowApiWrapper.AirflowApiWrapper, error) {
	keyJson := conf.Secrets.Ids[config.AirflowUsernamePassword]
	key := &airflowPayload.AirflowCredentials{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(keyJson), key)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshal airflow credentials", zap.Error(err))
		return nil, err
	}
	httpClient := &http.Client{
		Transport: &BasicAuthTransport{
			Username: key.Username,
			Password: key.Password,
		},
	}
	return pkgAirflowApiWrapper.NewAirflowApiWrapper(conf.AirflowConfig, httpClient), nil
}

type BasicAuthTransport struct {
	Username string
	Password string
}

func (t *BasicAuthTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.SetBasicAuth(t.Username, t.Password)
	return http.DefaultTransport.RoundTrip(req)
}

func newS3Client(ctx context.Context, region, bucket string) (*awss3.Client, error) {
	awsConfig, err := awsconfpkg.NewAWSConfig(ctx, region, true)
	if err != nil {
		return nil, err
	}
	return awss3.NewClient(awsConfig, bucket), nil
}

func getS3CallRecordingClient(ctx context.Context, conf *config.Config) (ticket.S3RecordingClient, error) {
	return newS3Client(ctx, conf.Aws.Region, conf.CallRecording.CallRecordingBucketName)
}

func getS3TranscriptionClient(ctx context.Context, conf *config.Config) (ticket.S3TranscriptClient, error) {
	return newS3Client(ctx, conf.Aws.Region, conf.CallRecording.CallTranscriptionBucketName)
}

func getDBStateCollectorFactory(
	commsDbStateClient commsDeveloper.CommsDbStatesClient,
	devActorEntityClient actorDeveloper.DevActorClient,
	devDepositClient depositDeveloper.DevDepositClient,
	devKycClient kycDeveloper.DevKYCClient,
	devUserClient userDeveloper.DevUserClient,
	orderDbStateClient orderDeveloper.DevClient,
	piDbStateClient piDeveloper.DevPaymentIntrumentClient,
	devCardClient cardDeveloper.CardDbStatesClient,
	devLivenessClient livenessDeveloper.DevLivenessClient,
	devSavingsClient savingsDeveloper.SavingsDbStatesClient,
	upiDbStateClient upiDeveloper.DevClient,
	devInapphelpClient inapphelpDeveloper.DevInapphelpClient,
	devCxClient cxDeveloper.DevCXClient,
	casbinDevClient casbinDeveloper.DevCasbinClient,
	insightsDevClient insightsDeveloper.DevInsightsClient,
	categorizerDevClient categorizerDeveloper.DevCategorizerClient,
	rewardsDevClient rewardsDeveloper.RewardsDevClient,
	devAuthClient authDeveloper.DevAuthClient,
	vmDevClient vmDeveloper.DevVendorMappingClient,
	timelineDevClient timelineDeveloper.DevTimelineClient,
	casperDevClient casperDeveloper.CasperDevClient,
	rmsDevClient rmsDeveloper.RMSDbStatesClient,
	devMerchantClient merchantDeveloper.DevMerchantClient,
	fitttDevClient fitttDeveloper.FITTTDbStatesClient,
	caDevClient caDeveloper.DevConnectedAccClient,
	devInappreferralClient inappreferralDeveloper.DevInAppReferralClient,
	woOnbDevClient wonbDeveloper.DevWealthOnboardingClient,
	investmentDBStateClient investDeveloper.MutualFundDbStatesClient,
	devrecurringPaymentClient recurrDeveloper.RecurringPaymentDevClient,
	enachDevClient enachDeveloper.EnachDevClient,
	devP2PInvestmentClient p2pDeveloper.DevP2PInvestmentClient,
	devSegmentClient segmentDeveloper.SegmentDbStatesClient,
	devNudgeClient nudgeDeveloper.NudgeDbStatesClient,
	salaryProgramDevClient spDeveloper.SalaryProgramDevClient,
	analyserDevClient analyserDeveloper.DevAnalyserClient,
	preApprovedDevClient plDeveloper.DevPreApprovedLoanClient,
	ccDevClient ffDeveloper.DevFireflyClient,
	celestialDevClient celestialDeveloper.DeveloperClient,
	riskDevClient riskDeveloper.DeveloperClient,
	scrnrDevClient screenerDeveloper.DeveloperClient,
	devAuthOrchClient authOrchDeveloper.DevOrchestratorClient,
	payDevClient payDeveloper.DevClient,
	usStocksDBStatesClient usstockDeveloper.DBStateClient,
	tieringDevClient tieringDeveloper.TieringDevServiceClient,
	amlDevClient amlDeveloper.AmlDevServiceClient,
	alfredDevClient alfredDeveloper.DeveloperClient,
	panDevClient panDeveloper.DeveloperClient,
	questDevClient questDeveloper.QuestDbStatesClient,
	upcomingTxnsDevClient upcomingtxnDeveloper.DevUpcomingTransactionsClient,
	healthEngineDevClient heDeveloper.HealthEngineDevClient,
	cmsDevClient cmsDeveloper.CmsDevClient,
	devBcClient bankCustDeveloper.DevBankCustClient,
	epifiTechVkycCallCollector vkyccall.EpifiTechVkycCallCollector,
	stockguardianVkycCallCollector vkyccall.StockguardianVkycCallCollector,
	epifiTechOmegleCollector omegle.EpifiTechOmegleCollector,
	stockguardianOmegleCollector omegle.StockguardianOmegleCollector,
	devEmpClient employmentDeveloper.DevEmploymentClient,
	collectionDeveloperClient collectionDeveloperPb.DeveloperClient,
	stockguardianApiGatewayDbStateCollector sherlockDbState.StockguardianApiGatewayDbStateCollector,
	devClient developerClient.DeveloperClient,
	leadsDevClient devLeadsPb.DevLeadClient,
	crDevClient creditReportPb.DevCreditReportClient,
	accountsDevClient developer2.AccountsDbStatesClient,
	npsDevClient npsPb.NpsDbStatesClient,
	securitiesDevClient securitiesPb.SecuritiesDevClient,
	salaryEstDevClient salaryEstDevPb.DevSalaryEstimationClient,
) collector.ICollectorFactory {
	// initialize collector factory
	dbStateCollectorFactory := collector.NewCollectorFactory()
	// Register all the collector clients here
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_COMMS, commsDbStateClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_ACTOR, devActorEntityClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_DEPOSIT, devDepositClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_KYC, devKycClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_USER, devUserClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_ORDER, orderDbStateClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_PAYMENT_INSTRUMENT, piDbStateClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CARD, devCardClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_LIVENESS, devLivenessClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_SAVINGS, devSavingsClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_UPI, upiDbStateClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_INAPPHELP, devInapphelpClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CX, devCxClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CASBIN, casbinDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_INSIGHTS, insightsDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CATEGORIZER, categorizerDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_REWARDS, rewardsDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_AUTH, devAuthClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_VENDOR_MAPPING, vmDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_TIMELINE, timelineDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CASPER, casperDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_RMS, rmsDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_MERCHANT, devMerchantClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_FITTT, fitttDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CONNECTED_ACCOUNT, caDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_IN_APP_REFERRAL, devInappreferralClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_WEALTH_ONBOARDING, woOnbDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_INVESTMENT, investmentDBStateClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_RECURRING_PAYMENT, devrecurringPaymentClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_ENACH, enachDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_P2P_INVESTMENT, devP2PInvestmentClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_SEGMENT, devSegmentClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_NUDGE, devNudgeClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_SALARY_PROGRAM, salaryProgramDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_ANALYSER, analyserDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_PRE_APPROVED_LOAN, preApprovedDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_FIREFLY, ccDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CELESTIAL, celestialDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_RISK, riskDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_SCREENER, scrnrDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_AUTH_ORCHESTRATOR, devAuthOrchClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_PAY, payDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_USSTOCKS, usStocksDBStatesClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_TIERING, tieringDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_AML, amlDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_ALFRED, alfredDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_PAN, panDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_QUEST, questDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_UPCOMING_TRANSACTIONS, upcomingTxnsDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_HEALTH_ENGINE, healthEngineDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CMS, cmsDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_BANK_CUSTOMER, devBcClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_OMEGLE, epifiTechOmegleCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_OMEGLE, stockguardianOmegleCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_VKYC_CALL, epifiTechVkycCallCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_VKYC_CALL, stockguardianVkycCallCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_EMPLOYMENT, devEmpClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_COLLECTION, collectionDeveloperClient)
	// adding same collector for all stockguardian services
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_CREDIT_REPORT, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_CREDIT_RISK, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_E_SIGN, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_IN_HOUSE_BRE, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_LMS, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_APPLICATION, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_BRE, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_DOCS, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_MATRIX, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_CUSTOMER, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_APPLICANT, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_STOCKGUARDIAN_KYC_VENDOR_DATA, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_TSP_USER, devClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_LEADS, leadsDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_CREDIT_REPORT, crDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_ACCOUNTS, accountsDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_NPS, npsDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_SECURITIES, securitiesDevClient)
	dbStateCollectorFactory.RegisterCollector(dbStatePb.Service_SALARY_ESTIMATION, salaryEstDevClient)
	return dbStateCollectorFactory
}

func InitializeEventConsumer(db cmdTypes.SherlockPGDB, genConfig *cxGenConf.Config, watsonClient watsonPb.WatsonClient, ticketClient ticketPb.TicketClient) *error_activity.Consumer {
	wire.Build(
		error_activity.NewConsumer,
		wire.NewSet(dao19.NewEventConfigsDaoImpl, wire.Bind(new(dao19.EventConfigDao), new(*dao19.EventConfigDaoImpl))),
		trigger_processor.NewCreateIncidentTriggerProcessor,
		trigger_processor.NewResolveIncidentTriggerProcessor,
		wire.NewSet(watsonDao.NewIncidentDao, wire.Bind(new(watsonDao.IIncidentDao), new(*watsonDao.IncidentDao))),
		wire.NewSet(trigger_processor.NewTriggerProcessorFactoryImpl, wire.Bind(new(trigger_processor.TriggerProcessorFactory), new(*trigger_processor.TriggerProcessorFactoryImpl))),
	)
	return &error_activity.Consumer{}
}

func InitializeManualTicketStageWiseCommsService(db cmdTypes.SherlockPGDB, genConfig *cxGenConf.Config) *manual_ticket_stage_wise_comms.ManualTicketStageWiseCommsService {
	wire.Build(
		manual_ticket_stage_wise_comms.NewManualTicketStageWiseCommsService,
		wire.NewSet(dao9.NewSupportTicketDao, wire.Bind(new(dao9.ISupportTicketDao), new(*dao9.SupportTicketDao))),
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		storageV2.DefaultTxnExecutorWireSet,
		GormDBProvider,
	)
	return &manual_ticket_stage_wise_comms.ManualTicketStageWiseCommsService{}
}

func IFeatureStoreProvider(store *featurestore.FeatureStore) featurestore.IFeatureStore {
	return store
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeIssueConfigService(db cmdTypes.SherlockPGDB, genConfig *cxGenConf.Config, cxS3Client cxTypes.CxS3Client, redisClient cxTypes.CxRedisStore) *issue_config.IssueConfig {
	wire.Build(
		issue_config.NewIssueConfig,
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		wire.NewSet(manager2.NewIssueCategoryManagerImpl, wire.Bind(new(manager2.IssueCategoryManager), new(*manager2.IssueCategoryManagerImpl))),
		wire.NewSet(dao18.NewIssueConfigDaoPGDB, wire.Bind(new(dao18.IssueConfigDao), new(*dao18.IssueConfigDaoPGDB))),
		storageV2.DefaultTxnExecutorWireSet,
		GormDBProvider,
		wire.NewSet(issue_config.NewCsvHelper, wire.Bind(new(issue_config.ConfigFileHelper), new(*issue_config.CsvHelper))),
		cxTypes.CxS3ClientProvider,
		NewRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
	)
	return &issue_config.IssueConfig{}
}

func InitializeIssueCategoryService(db cmdTypes.SherlockPGDB, actorActivityClient aaClient.ActorActivityClient) *issue_category.IssueCategory {
	wire.Build(
		issue_category.NewIssueCategory,
		wire.NewSet(dao2.NewIssueCategoryDao, wire.Bind(new(dao2.IIssueCategoryDao), new(*dao2.IssueCategoryDao))),
		issue_category_id_fetcher.NewIssueCategoryId,
		issue_category_id_fetcher.NewActivityMetaData,
		wire.NewSet(issue_category_id_fetcher.NewIssueCategoryIdFactoryImpl, wire.Bind(new(issue_category_id_fetcher.IssueCategoryIdFactory), new(*issue_category_id_fetcher.IssueCategoryIdFactoryImpl))),
	)
	return &issue_category.IssueCategory{}
}

func InitializeSherlockScriptsService(searchClient searchPb.ActionBarClient, cxConfig *config.Config) *sherlockScripts.SherlockScriptsService {
	wire.Build(
		strapiApiKeyProvider,
		strapiConfigProvider,
		strapi.NewStrapiClient,
		wire.NewSet(script_helper.NewSherlockScriptHelperImpl, wire.Bind(new(script_helper.ISherlockScriptHelper), new(*script_helper.SherlockScriptHelperImpl))),
		sherlockScripts.NewSherlockScriptsService,
	)
	return &sherlockScripts.SherlockScriptsService{}
}

func strapiConfigProvider(cxConfig *config.Config) *cfg.StrapiConfig {
	return cxConfig.StrapiConfig
}

func strapiApiKeyProvider(cxConfig *config.Config) wireTypes.StrapiApiKey {
	return wireTypes.StrapiApiKey(cxConfig.Secrets.Ids[config.StrapiApiKey])
}

func InitializeSherlockSopService(db cmdTypes.SherlockPGDB, cxConfig *config.Config, updateTicketPublisher cxTypes.UpdateTicketPublisher, searchClient searchPb.ActionBarClient, ticketServiceClient ticketPb.TicketClient) *sherlock_sop.SherlockSopService {
	wire.Build(
		strapiConfigProvider,
		strapiApiKeyProvider,
		strapi.NewStrapiClient,
		wire.NewSet(dao21.NewSherlockStepUserResponseDaoImpl, wire.Bind(new(dao21.SOPStepUserResponseDao), new(*dao21.SherlockStepUserResponseDaoImpl))),
		wire.NewSet(sop_helper.NewSherlockSopHelperImpl, wire.Bind(new(sop_helper.SherlockSopHelper), new(*sop_helper.SherlockSopHelperImpl))),
		sherlock_sop.NewSherlockSopService,
	)
	return &sherlock_sop.SherlockSopService{}
}

func InitializeRiskChartsService(txnAggregateClient txnAggregatesPb.TxnAggregatesClient, employmentClient employment.EmploymentClient, authClient authPb.AuthClient,
	caseManagementClient caseManagementPb.CaseManagementClient, savingsClient savingsPb.SavingsClient, conf *cxGenConf.Config, client tieringPinotPb.EODBalanceClient) *riskChart.Service {
	wire.Build(
		riskChart.NewService,
	)
	return &riskChart.Service{}
}

func InitializeCallIvrService(db cmdTypes.SherlockPGDB, genConfig *cxGenConf.Config, obClient onboarding2.OnboardingClient,
	userClient userPb.UsersClient, savingsClient savingsPb.SavingsClient, commsClient commsPb.CommsClient,
	redisClient cxTypes.CxRedisStore, tieringClient beTieringPb.TieringClient, riskProfileClient profilePb.ProfileClient,
	ticketClient ticketPb.TicketClient, eventBroker events.Broker, actorClient actorPb.ActorClient,
	userGroupClient userGroupPb.GroupClient, preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient,
	compClient compliancePb.ComplianceClient, ffClient ffPb.FireflyClient, cpClient cardPb.CardProvisioningClient, ccClient ccPb.CardControlClient) *call_ivr.Service {
	wire.Build(
		NewRedisClient,
		getDynFeatureReleaseConfig,
		release.EvaluatorWireSet,
		wire.NewSet(dao22.NewCallIvrDetailsDaoImpl, wire.Bind(new(dao22.CallIvrDetailsDao), new(*dao22.CallIvrDetailsDaoImpl))),
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		wire.NewSet(blocker.NewBlocker, wire.Bind(new(blocker.Blocker), new(*blocker.BlockerImpl))),
		call_ivr.NewCallIvrService,
	)
	return &call_ivr.Service{}
}

func InitializeVKYCCallService(vkycCallClientToOnboardingServer verifiPkg.VkycCallClientToOnboardingServer, vkycCallClientToSGApiGatewayServer verifiPkg.VkycCallClientToSGApiGatewayServer, config *config.Config, genConfig *cxGenConf.Config, userClient userPb.UsersClient, dbConn cmdTypes.SherlockPGDB,
	vgNameCheckClient vgNcPb.UNNameCheckClient) *cxvkyccall.Service {
	wire.Build(
		wire.Bind(new(auth_engine.IAuthEngine), new(*auth_engine.AuthEngine)),
		auth_engine.NewAuthEngine,
		getAuthFactoryRetryLimit,
		wire.NewSet(dao3.NewAuthFactorStatesDAO, wire.Bind(new(dao3.ICustomerAuthenticationFactorsStateDAO), new(*dao3.AuthFactorStatesDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationDAO, wire.Bind(new(dao3.ICustomerAuthenticationDAO), new(*dao3.CustomerAuthenticationDAO))),
		wire.NewSet(dao3.NewCustomerAuthenticationV2DAO, wire.Bind(new(dao3.ICustomerAuthenticationV2DAO), new(*dao3.CustomerAuthenticationV2DAO))),
		cxvkyccall.NewService,
		verifiPkg.NewVkycCallClientWrapper,
		serviceprovider.NewFactory,
		serviceprovider.NewFederalNRIServiceProvider,
		serviceprovider.NewStockGuardianServiceProvider,
		serviceprovider.NewFederalNRIQatarServiceProvider,
	)
	return &cxvkyccall.Service{}
}

func InitialiseSGKycService(sgApiGwKycClient sgKycApiGatewayPb.KYCClient) *cxSGPb.Service {
	wire.Build(
		cxSGPb.NewService,
	)
	return &cxSGPb.Service{}
}

func InitialiseEscalationsService(db cmdTypes.SherlockPGDB, genConf *cxGenConf.Config) *escalations.Service {
	wire.Build(
		escalationsConfigProvider,
		escalations.NewService,
		wire.NewSet(dao24.NewEscalationDao, wire.Bind(new(dao24.EscalationDAO), new(*dao24.EscalationDao))),
	)
	return &escalations.Service{}
}

func escalationsConfigProvider(conf *cxGenConf.Config) *cxGenConf.EscalationConfig {
	return conf.EscalationConfig()
}

func IsNewOperationalStatusAPIEnabled(conf *config.Config) accountstatus.UseNewOperationalStatusAPIFlag {
	return accountstatus.UseNewOperationalStatusAPIFlag(conf.IsNewOperationalStatusAPIEnabled)
}

// Provider for *opensearch.Client
func openSearchClientProvider(conf *cxGenConf.Config) *opensearch.Client {
	awsConfig, cfgErr := awsConfigOP.LoadDefaultConfig(context.Background(), awsConfigOP.WithRegion(conf.Aws().Region))
	if cfgErr != nil {
		logger.Panic("error in loading aws config", zap.Error(cfgErr))
	}
	client := stsv2.NewFromConfig(awsConfig)
	creds := stscreds.NewAssumeRoleProvider(client, conf.EsConfig().RoleArn)
	awsConfig.Credentials = creds

	signer, errSigner := osPkg.NewAWSSigner(awsConfig)
	if errSigner != nil {
		logger.Panic("err in getting AWS signer", zap.Error(errSigner))
	}

	esClient, errESClient := osPkg.NewESClientWithRoleAuth(conf.EsConfig().ESEndpoint, signer)
	if errESClient != nil {
		logger.Panic("error in creating es client", zap.Error(errESClient))
	}

	return esClient
}

// Provider for *logsource.OpenSearchLogService
func NewOpenSearchLogService(client *opensearch.Client) *logsource.OpenSearchLogService {
	return logsource.NewOpenSearchLogService(client)
}
