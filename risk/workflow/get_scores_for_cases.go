package workflow

import (
	"fmt"
	"strconv"

	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/celestial/activity"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"

	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	workflowPb "github.com/epifi/gamma/api/risk/workflow"
	riskTicketPb "github.com/epifi/gamma/api/vendorgateway/crm/risk"
)

// GetScoresForCases processes tickets to get ML confidence scores for each case
// Configuration: See gamma/cmd/worker/risk/config/risk-prod.yml GetScoresForCases section
func GetScoresForCases(ctx workflow.Context, req *workflowPb.GetScoresForCasesRequest) (*workflowPb.GetScoresForCasesResponse, error) {
	logger := workflow.GetLogger(ctx)

	if req.GetS3Path() == "" {
		return nil, fmt.Errorf("s3_path is required")
	}

	logger.Info("Starting GetScoresForCases workflow",
		zap.String("s3_path", req.GetS3Path()),
		zap.String("batch_id", req.GetBatchId()),
		zap.Int32("max_concurrent_ds_calls", req.GetMaxConcurrentDsCalls()))

	response := &workflowPb.GetScoresForCasesResponse{
		TicketsWithScores: []*workflowPb.TicketWithScore{},
		ErroredTicketIds:  []string{},
	}

	// Step 1: Read case data from S3
	tickets, err := readCaseDataFromS3(ctx, req.GetS3Path())
	if err != nil {
		logger.Error("Failed to read case data from S3", zap.Error(err))
		return response, err
	}

	logger.Debug("Successfully read case data from S3", zap.Int("ticket_count", len(tickets)))

	// Step 2: Process tickets in batches to get confidence scores
	ticketsWithScores, erroredTicketIds := processTicketsForScoring(ctx, tickets, req.GetMaxConcurrentDsCalls())

	response.TicketsWithScores = ticketsWithScores
	response.ErroredTicketIds = erroredTicketIds

	logger.Info("GetScoresForCases workflow completed",
		zap.Int("successful_tickets", len(ticketsWithScores)),
		zap.Int("errored_tickets", len(erroredTicketIds)))

	return response, nil
}

// readCaseDataFromS3 reads case data from S3 using ReadCaseDataFromS3 activity
// Activity timeout and retry configuration: See gamma/cmd/worker/risk/config/risk-prod.yml
func readCaseDataFromS3(ctx workflow.Context, s3Path string) ([]*riskTicketPb.RiskTicket, error) {
	logger := workflow.GetLogger(ctx)
	logger.Debug("Reading case data from S3", zap.String("s3_path", s3Path))

	// Activity options configured through config file
	var response activityPb.ReadCaseDataFromS3Response
	err := activityPkg.Execute(ctx, riskNs.ReadCaseDataFromS3, &response, &activityPb.ReadCaseDataFromS3Request{
		RequestHeader: &activity.RequestHeader{},
		S3Path:        s3Path,
	})

	if err != nil {
		return nil, fmt.Errorf("ReadCaseDataFromS3 activity failed: %w", err)
	}

	return response.GetTickets(), nil
}

// processTicketsForScoring processes tickets to get confidence scores
// This function handles batching and parallel processing of tickets
func processTicketsForScoring(ctx workflow.Context, tickets []*riskTicketPb.RiskTicket, maxConcurrentDsCalls int32) ([]*workflowPb.TicketWithScore, []string) {
	logger := workflow.GetLogger(ctx)
	logger.Debug("Processing tickets for scoring", zap.Int("ticket_count", len(tickets)))

	var ticketsWithScores []*workflowPb.TicketWithScore
	var erroredTicketIds []string

	// Process tickets in batches to avoid overwhelming the ML service
	batchSize := 50 // Configure through config file if needed
	for i := 0; i < len(tickets); i += batchSize {
		end := min(i+batchSize, len(tickets))

		batch := tickets[i:end]
		batchTicketsWithScores, batchErroredIds := processTicketBatch(ctx, batch, maxConcurrentDsCalls)

		ticketsWithScores = append(ticketsWithScores, batchTicketsWithScores...)
		erroredTicketIds = append(erroredTicketIds, batchErroredIds...)
	}

	return ticketsWithScores, erroredTicketIds
}

// processTicketBatch processes a batch of tickets to get confidence scores
// Activity timeout and retry configuration: See gamma/cmd/worker/risk/config/risk-prod.yml
// Uses workflow channels for proper concurrency control to respect MaxConcurrentDsCalls
func processTicketBatch(ctx workflow.Context, tickets []*riskTicketPb.RiskTicket, maxConcurrentDsCalls int32) ([]*workflowPb.TicketWithScore, []string) {
	logger := workflow.GetLogger(ctx)
	logger.Debug("Processing ticket batch", zap.Int("batch_size", len(tickets)), zap.Int32("max_concurrent_ds_calls", maxConcurrentDsCalls))

	var ticketsWithScores []*workflowPb.TicketWithScore
	var erroredTicketIds []string

	resultCh := workflow.NewBufferedChannel(ctx, len(tickets))

	// Set up concurrency control using workflow channels
	concurrency := int(maxConcurrentDsCalls)
	if concurrency <= 0 {
		concurrency = 10 // Default concurrency limit
	}

	// Create semaphore channel and initialize with tokens
	semaphoreCh := workflow.NewBufferedChannel(ctx, concurrency)
	for i := 0; i < concurrency; i++ {
		semaphoreCh.Send(ctx, struct{}{})
	}

	ticketCount := 0
	for _, ticket := range tickets {
		if ticket == nil {
			continue
		}
		ticketCount++

		t := ticket // capture loop variable
		workflow.Go(ctx, func(ctx workflow.Context) {
			// Acquire semaphore token
			var token struct{}
			semaphoreCh.Receive(ctx, &token)
			defer semaphoreCh.Send(ctx, token) // Release token when done

			// Add panic recovery to ensure result is always sent
			defer func() {
				if r := recover(); r != nil {
					logger.Error("Panic in processTicketBatch worker",
						zap.String("ticket_id", strconv.FormatUint(t.GetId(), 10)),
						zap.Any("panic", r))
					resultCh.Send(ctx, struct {
						ticketId string
						score    *workflowPb.TicketWithScore
						err      error
					}{
						ticketId: strconv.FormatUint(t.GetId(), 10),
						score:    nil,
						err:      fmt.Errorf("worker panic: %v", r),
					})
				}
			}()

			ticketWithScore, err := getConfidenceScoreForTicket(ctx, t)
			resultCh.Send(ctx, struct {
				ticketId string
				score    *workflowPb.TicketWithScore
				err      error
			}{
				ticketId: strconv.FormatUint(t.GetId(), 10),
				score:    ticketWithScore,
				err:      err,
			})
		})
	}

	// Wait for all results
	for i := 0; i < ticketCount; i++ {
		var res struct {
			ticketId string
			score    *workflowPb.TicketWithScore
			err      error
		}
		resultCh.Receive(ctx, &res)
		if res.err != nil {
			logger.Error("Failed to get confidence score for ticket",
				zap.String("ticket_id", res.ticketId),
				zap.Error(res.err))
			erroredTicketIds = append(erroredTicketIds, res.ticketId)
		} else {
			ticketsWithScores = append(ticketsWithScores, res.score)
		}
	}

	return ticketsWithScores, erroredTicketIds
}

// getConfidenceScoreForTicket gets confidence score for a single ticket
// Activity timeout and retry configuration: See gamma/cmd/worker/risk/config/risk-prod.yml
func getConfidenceScoreForTicket(ctx workflow.Context, ticket *riskTicketPb.RiskTicket) (*workflowPb.TicketWithScore, error) {
	logger := workflow.GetLogger(ctx)
	logger.Debug("Getting confidence score for ticket", zap.String("ticket_id", strconv.FormatUint(ticket.GetId(), 10)))

	// Activity options configured through config file
	var response activityPb.GetConfidenceScoreResponse
	err := activityPkg.Execute(ctx, riskNs.GetConfidenceScore, &response, &activityPb.GetConfidenceScoreRequest{
		RequestHeader: &activity.RequestHeader{},
		CaseId:        strconv.FormatUint(ticket.GetId(), 10),
	})

	if err != nil {
		return nil, fmt.Errorf("GetConfidenceScore activity failed for ticket %s: %w", strconv.FormatUint(ticket.GetId(), 10), err)
	}

	// Convert activity response to workflow response format
	ticketWithScore := &workflowPb.TicketWithScore{
		TicketId:    strconv.FormatUint(ticket.GetId(), 10),
		Model1Score: response.GetModel1Score(),
		Model1Name:  response.GetModel1Name(),
		Model2Score: response.GetModel2Score(),
		Model2Name:  response.GetModel2Name(),
	}

	// Log the scores to ensure zero values are captured
	logger.Debug("Ticket score details",
		zap.String("ticket_id", strconv.FormatUint(ticket.GetId(), 10)),
		zap.Float32("model1_score", response.GetModel1Score()),
		zap.String("model1_name", response.GetModel1Name()),
		zap.Float32("model2_score", response.GetModel2Score()),
		zap.String("model2_name", response.GetModel2Name()))

	return ticketWithScore, nil
}
