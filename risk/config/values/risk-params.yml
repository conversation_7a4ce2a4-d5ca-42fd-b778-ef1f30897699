AWS:
  S3:
    BucketNames:
      DataBucket: "epifi-dp-dev"
      OnboardingBucket: "epifi-onboarding-dev"

OnboardingVelocityConfig:
  QueryRangeDuration: 24h
  Threshold: 3
  D2HThreshold: 20
  BucketExpiry: 24h
  BucketPrecision: 3

RedListLatLongPrecisions: [1,2,3,4]

TxnMonitoringConfig:
  ContactSignalBatchSize: 1000
  ContactSignalsMaxIteration: 200

RedList:
  OnboardingStateThreshold: 90
  AtmHighRiskThreshold: 65
  AtmMediumRiskThreshold: 45

Tracing:
  Enable: false

HunterRollOutPercentage: 0

BlockingFlowErrorReportConfig:
  ReportName: "Blocking flow error report"
  ToEmailId: "<EMAIL>"
  FromEmailId: "<EMAIL>"

CaseStore:
  ListTopCases:
    CreatedAtBuckets: ["720h","360h","168h","92h","80h","68h","58h","50h","44h","38h","34h","30h","28h","26h","24h","22h","20h","18h","16h","14h","12h","10h","8h","6h","4h","2h","0"]
    UpdatedAtBuckets: ["720h","360h","168h","92h","80h","68h","58h","50h","44h","38h","34h","30h","28h","26h","24h","22h","20h","18h","16h","14h","12h","10h","8h","6h","4h","2h","0"]
    ConfidenceScoreBuckets: [-1,10,20,30,35,40,45,50,55,60,65,70,75,80,85,90,95,100]
    MaxCallsToVendor: 10

CasePriorityQueue:
  LiveQueueTags: ["ADHOC_HUNTER"]
  CachedCaseStaleThreshold: "1h"
  QueueCapacityMap:
    transaction_review_queue: 50
    post_onboarding_queue: 200
    live_queue: 50
    escalation_review_queue: 50
  QueueTTLMap:
    transaction_review_queue: "1m"
    post_onboarding_queue: "1m"
    live_queue: "1m"
    escalation_review_queue: "1m"
  # the values of rates are in percentages, they should satisfy model1rate + model2rate + cgrate = 100
  modelSelectionRates:
    model1Rate: 60
    model2Rate: 30
    cgRate: 10
    UseModelRateForSelection: false

UpiVPANameMatchingCheckConfig:
  IsEnabled: true
  CompleteMismatchScore: 0
  FailureScore: 0.7

InstalledAppsCheckConfig:
  IsEnabled: true
  MinRequiredSocialAppCount: 1
  MinRequiredNonSystemAppCount: 1
  SocialApps: [ "com.whatsapp", "com.facebook.katana", "com.facebook.orca", "com.twitter.android", "com.linkedin.android", "org.telegram.messenger", "com.snapchat.android" ]
  DurationToCheckForPreviousFailures: "1h"
  CheckToScoreMap:
    INSTALLED_APPS_CHECK_NON_SYSTEM_APPS: 10
    INSTALLED_APPS_CHECK_SOCIAL_APPS: 20
    INSTALLED_APPS_CHECK_MALICIOUS_APPS_ONLY: 11
    HIGH_LIFT_BAD_APPS_LOW_LEA_GOOD_APPS: 12
    LOW_LIFT_BAD_APPS_HIGH_LEA_GOOD_APPS: 13
    INSTALLED_APPS_CHECK_PREVIOUS_FAILURES: 51

CreditReportAffluenceClassConfig:
  IsEnabled: true
  FailCreditScoreThreshold: 600
  InvalidCreditScoreThreshold: 10
  PassCreditScoreThreshold: 650
  FailureScore: 9
  BadAffluenceIncomeThreshold: 20000
  SuitFiledWilfulDefault: 0
  SuitFiledWillfulDefaultWrittenOff: 0

LEAComplaint:
  ReviewExternalRuleId: "LEA_COMPLAINTS_1"
  AuditDateCutOffForCaseCreation: "720h"

ContactAssociationsConfig:
  ContactSignalBatchSize: 1000
  ContactSignalsMaxIteration: 200

ContactAssociationsCheckConfig:
  IsEnabled: true
  IngressMaxAssociationsWithLEAThreshold: 2
  FailedCheckScore: 0.71
  ManualReviewFailCheckScore: 0.70
  IngressManualReviewForLEAThreshold: 1
  IngressManualReviewForBlockedThreshold: 2
  EgressMaxAssociationsWithLEAThreshold: 2

IPAddressRedListCheckConfig:
  IsEnabled: true
  MinRedListScoreToSendForManualReview: 50
  ManualReviewScore: 0.50

ContactAssociationAndRiskyProfileCheckConfig:
  IsEnabled: true
  ManualReviewScore: 0.50

ValidateSavingsAccountStatusConfig:
  IsEnabled: true
  FailedCheckScore: 0.91

InvestigationEmail:
  FromEmail: "<EMAIL>"

RiskBankAction:
  ActionWorkflowMaxRuntime: "240h"

RiskBankActionComms:
  FromEmail: "<EMAIL>"
  CreditFreezePushNotification:
    Title: "Unusual Activity Detected🔒"
    Body: "Your Federal Bank a/c is now temporarily frozen. Tap here for steps to unfreeze."

ProfileBannerForSherlock:
  IsEnabled: true
  MaxBannersForType: 1

CrossVideoFacematchCheckConfig:
  IsEnabled: true
  LivenessScoreCutoff: 1
  FaceMatchScoreCutoff: 1
  FailureScore: 0.7

LowContactCountCheckConfig:
  LowContactCountManualReviewThreshold: 5
  LowContactCountFlagThreshold: 10

DynamicElementsConfig:
  IsEnabled: true
  DurationToCheckPreviousLEAComplaints: "2160h" # 90 days
  FreezeBannerReleaseConf:
    FeatureName: "freeze_home_banner"
    RolloutPercentage: 70
  FreezeStatusToBannerConfigMap:
    FREEZE_STATUS_CREDIT_FREEZE:
      FontColor: "#FFFFFF"
      Title: "Credit Freeze enabled on your account by partner bank"
      BodyColor: "#A73F4B"
      ImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
      IndicatorSelectedColor: "#D65779"
      IndicatorDefaultColor: "#FFFFFF"
      PopupCTAConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
        Title: "Your account is temporarily restricted"
        Body: "For now, you can withdraw money from this a/c, but can’t add money into it.\n\nTo unfreeze: Check for an <NAME_EMAIL>"
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#A73F4B"
        DismissCTAText: "Ok, got it"
        FormCTAText: "Fill details"
      PopupCTAWithFormsConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
        Title: "Your account is temporarily restricted"
        Body: "You can withdraw money from your savings account but can’t add money into it. To unfreeze, please fill in a few details so our team can review the issue"
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#fce6a9"
        NotifyLaterCTA:
          Title: "Later"
          FontColor: "#00B899"
          BodyColor: "#E6E9ED"
        RedirectCTA:
          Title: "Fill Details"
          FontColor: "#E6E9ED"
          BodyColor: "#00B899"
    ACCOUNT_FREEZE_BANNER:
      FontColor: "#FFFFFF"
      Title: "LEA Enquiry received: %s applied"
      BodyColor: "#A73F4B"
      ImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
      PopupCTAConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/chevron-right-violet.png"
        Title: "Your account's temporarily restricted"
        Body: "%s \n\nPlease check your email for details and next steps."
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#A73F4B"
        DismissCTAText: "Ok, got it"
    ACCOUNT_LIEN_BANNER:
      FontColor: "#FFFFFF"
      Title: "LEA Enquiry received: ₹%s frozen"
      BodyColor: "#A73F4B"
      ImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
      PopupCTAConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/chevron-right-violet.png"
        Title: "Your account's temporarily restricted"
        Body: "%s \n\nPlease check your email for details and next steps."
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#A73F4B"
        DismissCTAText: "Ok, got it"
    ACCOUNT_FREEZE_AND_LIEN_BANNER:
      FontColor: "#FFFFFF"
      Title: "LEA Enquiry received: %s applied and ₹%s frozen"
      BodyColor: "#A73F4B"
      ImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
      PopupCTAConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/chevron-right-violet.png"
        Title: "Your account's temporarily restricted"
        Body: "%s \n\nPlease check your email for details and next steps. "
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#A73F4B"
        DismissCTAText: "Ok, got it"
  OutcallBannerReleaseConf:
    FeatureName: "outcall_home_banner"
    RolloutPercentage: 100
  OutcallBanner:
    FontColor: "#FFFFFF"
    Title: "Unusual activity detected: Fill in some details"
    BodyColor: "#F0BECE"
    ImageURL: "https://epifi-icons.pointz.in/risk/alert.png"
    IndicatorSelectedColor: "#D65779"
    IndicatorDefaultColor: "#FFFFFF"
    PopupCTAConfig:
      CTAImageURL: "https://epifi-icons.pointz.in/risk/alert.png"
      Title: "Unusual activity detected"
      Body: "Fill in a questionnaire with correct information before %s to avoid restrictions on your Federal Bank Savings a/c through Fi."
      IconUrl: "https://epifi-icons.pointz.in/risk/alert.png"
      BackgroundColor: "#E6E9ED"
      NotifyLaterCTA:
        Title: "Later"
        FontColor: "#00B899"
        BodyColor: "#E6E9ED"
      RedirectCTA:
        Title: "Fill Details"
        FontColor: "#E6E9ED"
        BodyColor: "#00B899"

UnifiedLEAComplaintConfig:
  MaxLayerNumberToSendComms: 3
  IsWorkflowEnabled: true
  LayerNumberToSendInvestigationEmail: 3
  LayerNumberEqualAndAboveNotFraud: 5

PrioritiserConfig:
  DSModelPrioritisationRolloutPercentage: 0
  ReprioritisationS3Bucket: "epifi-dev-risk-case-prioritisation"

ProcessRiskSignalEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-data-risk-bq-signal-ingestion-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 3
        Period: 1m
    Namespace: "risk"
