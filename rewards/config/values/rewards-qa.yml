Application:
  Environment: "qa"
  Name: "rewards"

Server:
  Ports:
    GrpcPort: 9091
    GrpcSecurePort: 9515
    HttpPort: 9999
    HttpPProfPort: 9990

RewardsDb:
  AppName: "rewards"
  StatementTimeout: 5s
  Name: "rewards"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 30
  MaxIdleConn: 10
  MaxConnTtl: "30m"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: rewards-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "qa/rds/postgres/rewards"
    RudderWriteKey: "qa/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"
    SlackOauthToken: "qa/rewards/slack-oauth-token"
    KafkaCredentials: "qa/kafka/rewards"
    KafkaCaCertificate: "qa/kafka/cert"
    StartreePinotAuthToken: "qa/pinot/startree"

OrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

InvestmentEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

UserSearchSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-user-search-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

UserSearchV1SqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-user-search-v1-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ManualGiveawayEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-manual-giveaway-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

FitttExecutionUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-fittt-execution-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

ExtraInterestSdBonusPayoutSqsPublisher:
  QueueName: "qa-rewards-extra-interest-sd-bonus-payout-event"

ExtraInterestSdBonusPayoutSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-extra-interest-sd-bonus-payout-event"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 10
      TimeUnit: "Minute"

MinBalanceSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-min-balance-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

CAAccountUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-ca-account-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

KYCSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-kyc-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

FitttSportsEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-fittt-sports-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SavingsAccountStateUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-savings-account-state-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SalaryDetectionSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-salaryprogram-salary-txn-detection-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

DebitCardSwitchNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-dc-card-switch-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SalaryProgramStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-salaryprogram-salary-status-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

MerchantPiUpdateAffectedEntitiesEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-rewards-merchant-pi-update-affected-entities-event-queue"
  # to access cross account queue in data-dev
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

OnboardingStageUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-onboarding-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

DataCollectorEventSqsPublisher:
  QueueName: "qa-rewards-data-collector-queue"

DataCollectorEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-data-collector-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Minute"

RewardClawbackEventCollectedDataPublisher:
  QueueName: "qa-rewards-reward-clawback-event-collector-queue"

RewardClawbackEventCollectorSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-reward-clawback-event-collector-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 6
      TimeUnit: "Minute"

RewardsClawbackEventCollectedDataCustomDelayPublisher:
  DestQueueName: "qa-rewards-reward-clawback-event-collector-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

ClaimRewardEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-claim-reward-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

RewardsManualGiveawayEventSqsPublisher:
  QueueName: "qa-rewards-manual-giveaway-event-queue"

RewardsClaimRewardEventSqsCustomDelayPublisher:
  DestQueueName: "qa-rewards-claim-reward-event-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

DataCollectorEventSqsCustomDelayPublisher:
  DestQueueName: "qa-rewards-data-collector-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

Sentry:
  DSN: "http://c6171cb4513f4c15a98cadea428bc863@13.233.212.81:9000/4"
  FlushTimeoutInSecs: 5
  Tags:
    app-name: "rewards"

RewardsSqsPublisher:
  QueueName: "qa-rewards-data-queue"

RewardsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-data-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 3
      MaxAttempts: 15
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 2s
    Namespace: "rewards"

RewardsProcessingSqsCustomDelayPublisher:
  DestQueueName: "qa-rewards-processing-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

RewardsProcessingDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-processing-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 2s
    Namespace: "rewards"

LuckyDrawWinningProcessingSqsPublisher:
  QueueName: "qa-lucky-draw-winning-processing-queue"

LuckyDrawWinningProcessingSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-lucky-draw-winning-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"

RewardClawbackProcessingSqsPublisher:
  QueueName: "qa-rewards-reward-clawback-processing-queue"

RewardClawbackProcessingSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-reward-clawback-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"

CreditCardTransactionsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-credit-card-txn-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CreditCardRequestStageUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-credit-card-request-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CreditCardBillGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-cc-bill-generation-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsOfferRedemptionStatusUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-offer-redemption-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-reward-status-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"

RewardGenerationEventSnsPublisher:
  TopicName: "qa-reward-generation-event-topic"

RewardStatusUpdateEventSnsPublisher:
  TopicName: "qa-rewards-reward-status-update-event-topic"

RewardsNotificationEventSqsCustomDelayPublisher:
  DestQueueName: "qa-rewards-notification-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

RewardsNotificationEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

CreditReportDownloadEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-credit-report-download-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

VendorRewardFulfillmentEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-vendor-reward-fulfillment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

Jobs:
  ClaimedGiftHampersReportJob :
    S3BucketName : "epifi-qa-rewards"
    DirPath      : "/jobs/claimed-gift-hampers-report"

RewardsBucketName: "epifi-qa-rewards"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  DisableRewardsProcessedNotification: false
  TrimDebugMessageFromStatus: false
  EnableGenerationOfRewardsInLockedState: true
  EnableTPAPOrdersForRewardsProcessing: true


RewardsCampaignCommConfig:
  SalaryDepositV1RewardOfferId: ""

RetryDelayForProcessingInProcessRewards : 5s
RetryDelayForProcessingInProgressRewardClawbacks: 5s

ReferralRewardsCappingAndPeriod:
  RewardsCap: 4
  CappingPeriod: 48h # 2 days
  Active: true

RewardsPeriodicCappingsPerOfferType:
  - 1:
      OfferType: "REFERRAL_REFERRER_OFFER"
      RewardsCap: 10
      CappingPeriod: 1
      CappingDuration: "CALENDAR_MONTH"
      Active: false

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CreditCardRewardsConfig:
  MinReqDelayForRewardEvalRelativeToBillWindowEndDate: 5m # 5 minutes
  ShouldProcessBillingEventImmediatelyForRewardEvaluation: true
  CuratedMerchantNameToKnownMerchantMap:
    "swiggy":
      - 3 #SWIGGY
    "dominos":
      - 27 #DOMINOS
    "zomato":
      - 1 #ZOMATO
      - 593 #ZOMATO_HYPERPURE
    "bookmyshow":
      - 105 #BOOKMYSHOW
    "big_basket":
      - 6 #BIG_BASKET
    "decathlon":
      - 145 #DECATHLON
    "myntra":
      - 7 #MYNTRA
    "ola":
      - 9 #OLA
      - 26 #OLA_MONEY
      - 384 #OLA_CARS
      - 385 #OLA_ELECTRIC
      - 386 #OLA_FOODS
    "uber":
      - 10 #UBER
      - 541 #UBER_EATS
    "dmart":
      - 155 #DMART
    "apollo_pharmacy":
      - 67 #APOLLO_PHARMACY
    "yatra":
      - 878 #YATRA
    "croma":
      - 139 #CROMA
    "amazon":
      - 2   #AMAZON
      - 12 #AMAZON_PAY
      - 13 #AMAZON_INTL
      - 51  #AMAZON_PRIME
      - 52 #AMAZON_SELLER_SERVICES
    "flipkart":
      - 4 #FLIPKART
      - 168 #EKART
      - 193 #FLIPKART_WHOLESALE
    "lifestyle":
      - 315 #LIFESTYLE_INTERNATIONAL
    "urban_company":
      - 549 #URBAN_COMPANY
    "zepto":
      - 595 #ZEPTO
    "shoppers_stop":
      - 596 #SHOPPERS_STOP
    "cleartrip":
      - 597 #CLEARTRIP
    "vistara":
      - 886 #VISTARA
  CreditCardSpends1xRewardCalculationConfig:
    TxnAmountToRewardUnitsFactor: 5
    TxnLevelMaxRewardUnitsCap: 500
  CreditCardTopMerchantSpendsRewardConfig:
    MinReqCCSpentAmountInRsForRewardEligibility: 3000
    CountOfMerchantsToBeRewarded : 3
    MultiplierValueOn1xRewardAmount: 3
    MaxRewardUnitsCap: 3000
    OntologyIdsToExcludeWhileCalculatingAggregateSpends:
      - "9c4d98b7-c7b2-452e-a38f-1de36bf64fe9" # Fees and Service taxes
      - "36e073c8-e43f-4ba4-b766-4c7624cfeda4" # Interest
      - "62ec4b2a-fe6b-4415-8ffd-a128e5591dd5" # Wallet

RewardsRewardExpirySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-reward-expiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsRewardExpirySqsPublisher:
  QueueName: "qa-rewards-reward-expiry-queue"

RewardsRewardExpirySqsCustomDelayPublisher:
  DestQueueName: "qa-rewards-reward-expiry-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

CaAccountDataSyncEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-reward-ca-account-data-sync-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

EpfPassbookImportEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-reward-epf-passbook-import-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

RewardsActorNudgeStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-actor-nudge-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

RewardsRewardUnlockerSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-reward-unlocker-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsRewardUnlockerSqsPublisher:
  QueueName: "qa-rewards-reward-unlocker-queue"

RewardsRewardUnlockerSqsCustomDelayPublisher:
  DestQueueName: "qa-rewards-reward-unlocker-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

RewardsProjectionUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-projection-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsProjectionsGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-projections-generation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TieringPeriodicRewardEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  # to access cross account queue in data-dev
  QueueName: "dev-tiering-periodic-reward-event-queue"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TieringTierUpdateEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-tiering-tier-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsProjectionUpdateEventSqsPublisher:
  QueueName: "qa-rewards-projection-update-event-queue"

RewardsProjectionsGenerationEventSqsPublisher:
  QueueName: "qa-rewards-projections-generation-event-queue"

RewardsOrderUpdateEventQueueSqsPublisher:
  QueueName: "qa-rewards-order-update-queue"

RewardsCreditCardTxnEventQueueSqsPublisher:
  QueueName : "qa-rewards-credit-card-txn-event-queue"

RewardsCreditCardBillingEventQueueSqsPublisher:
  QueueName : "qa-rewards-cc-bill-generation-event-consumer-queue"

BulkClaimRewardsEventSqsPublisher:
  QueueName: "qa-bulk-claim-rewards-event-queue"

BulkClaimRewardsEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-bulk-claim-rewards-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsAccountStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-account-operational-status-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TerminalPinotRewardProducer:
  TopicName: "qa.service.rewards.terminal_state_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

RewardsTerminalStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-reward-terminal-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

PinotProjectionProducer:
  TopicName: "qa.service.rewards.reward_projections_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

ProjectionEventSnsPublisher:
  TopicName: "qa-rewards-projection-event-topic"

ProjectionEventForPinotSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-rewards-projection-event-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

NonTerminalPinotRewardProducer:
  TopicName: "qa.service.rewards.non_terminal_state_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

WatsonMeta:
  IsEnabled: true
  IsErrorActivityEnabled: true
  IsUserActivityEnabled: true
  DefaultIssueCategoryIdForRewards: "652a72a3-61cf-5ae4-a156-5e4dec5b951a"
  DefaultIssueCategoryIdForProjections: "652a72a3-61cf-5ae4-a156-5e4dec5b951a"
