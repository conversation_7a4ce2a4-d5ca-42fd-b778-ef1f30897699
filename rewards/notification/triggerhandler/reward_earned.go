package triggerhandler

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"

	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	nudgePb "github.com/epifi/gamma/api/nudge"
	rewardPb "github.com/epifi/gamma/api/rewards"
	rewardsNotificationPb "github.com/epifi/gamma/api/rewards/notification"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/rewards/constants"

	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/generator/dao"
	"github.com/epifi/gamma/rewards/notification/helper"
	"github.com/epifi/gamma/rewards/notification/internalerrors"
)

type RewardEarnedHandler struct {
	notificationParamsMap *config.RewardsNotificationParams
	rewardsDao            dao.RewardsDao
	helper                *helper.HelperService
}

func NewRewardEarnedHandler(
	notificationParamsMap *config.RewardsNotificationParams,
	rewardsDao dao.RewardsDao,
	helper *helper.HelperService,
) *RewardEarnedHandler {
	return &RewardEarnedHandler{
		notificationParamsMap: notificationParamsMap,
		rewardsDao:            rewardsDao,
		helper:                helper,
	}
}

var _ ITriggerHandler = &RewardEarnedHandler{}

func (r *RewardEarnedHandler) IsTriggerValid(ctx context.Context, actorId string, triggerMetadata *rewardsNotificationPb.TriggerMetadata, notificationTypeMetadata *rewardsNotificationPb.NotificationTypeMetadata) (bool, error) {
	rewardEarnedTriggerMetadata := triggerMetadata.GetRewardEarnedTriggerMetadata()
	if rewardEarnedTriggerMetadata == nil {
		return false, fmt.Errorf("rewardEarnedTriggerMetadata is nil")
	}

	rewardModel, err := r.rewardsDao.FetchRewardById(ctx, rewardEarnedTriggerMetadata.GetRewardId())
	if err != nil {
		return false, fmt.Errorf("error in FetchByActorAndOfferId call inside IsTriggerValid for actorId %s and rewardId %s: %w", actorId, rewardEarnedTriggerMetadata.GetRewardId(), err)
	}
	rewardProto, err := rewardModel.GetProtoReward()
	if err != nil {
		return false, fmt.Errorf("error in GetProtoReward() call inside IsTriggerValid for actorId %s and rewardId %s: %w", actorId, rewardEarnedTriggerMetadata.GetRewardId(), err)
	}

	// If reward is expired, return false
	if rewardProto.GetExpiresAt() != nil && !rewardProto.GetExpiresAt().AsTime().IsZero() && rewardProto.GetExpiresAt().AsTime().Before(time.Now()) {
		return false, nil
	}

	if !rewardEarnedTriggerMetadata.GetShouldSkipStatusValidation() {
		// checking if reward is already claimed by user, if yes then don't send notification
		if rewardProto.GetStatus() != rewardPb.RewardStatus_CREATED {
			return false, nil
		}
	}

	return true, nil
}

// todo (sresth) add enum for campaign
// nolint: funlen
func (r *RewardEarnedHandler) GetNotificationMessage(ctx context.Context, actorId string, triggerMetadata *rewardsNotificationPb.TriggerMetadata, rewardNotificationType rewardOffersPb.RewardNotificationType, content *rewardsNotificationPb.Content, notificationTypeMetadata *rewardsNotificationPb.NotificationTypeMetadata) (*NotificationTypeMessage, error) {
	var (
		message NotificationTypeMessage
	)
	notificationParamsMap := r.notificationParamsMap.RewardEarnedNotificationParamsMap

	rewardEarnedTriggerMetadata := triggerMetadata.GetRewardEarnedTriggerMetadata()
	if rewardEarnedTriggerMetadata == nil {
		return nil, fmt.Errorf("rewardEarnedTriggerMetadata is nil")
	}

	rewardModel, err := r.rewardsDao.FetchRewardById(ctx, rewardEarnedTriggerMetadata.GetRewardId())
	if err != nil {
		return nil, fmt.Errorf("error in FetchByActorAndOfferId call inside GetNotificationMessage for actorId %s and rewardId %s: %w", actorId, rewardEarnedTriggerMetadata.GetRewardId(), err)
	}
	rewardProto, err := rewardModel.GetProtoReward()
	if err != nil {
		return nil, fmt.Errorf("error in GetProtoReward() call inside GetNotificationMessage for actorId %s and rewardId %s: %w", actorId, rewardEarnedTriggerMetadata.GetRewardId(), err)
	}

	notificationParams, isPresent := notificationParamsMap[rewardNotificationType.String()]
	if !isPresent {
		logger.Error(ctx, "notification params not present for notification type, not sending notification", zap.String(logger.NOTIFICATION_TYPE, rewardNotificationType.String()))
		return nil, fmt.Errorf("notification params not present for notification type in RewardEarnedNotificationParamsMap, err : %w", internalerrors.ErrMandatoryParamsMissing)
	}

	var (
		notificationTitle    = content.GetTitle()
		notificationBody     = content.GetBody()
		imageUrl             = content.GetImageUrl()
		notificationDeeplink = content.GetDeeplink()
		expiresAfterDuration time.Duration
	)

	if notificationParams != nil {
		if notificationTitle == "" {
			notificationTitle = notificationParams.Title
		}
		if notificationBody == "" {
			notificationBody = fmt.Sprintf(notificationParams.Body, rewardProto.GetRewardOptions().GetActionDetails())
		}
		if imageUrl == "" {
			imageUrl = accrualPkg.ReturnApplicableValue(notificationParams.ImageUrl, constants.FiPoints3dWithBg, rewardProto.GetActionTime(), true).(string)
		}
		if notificationDeeplink == nil {
			notificationDeeplink = &deeplink.Deeplink{
				Screen: deeplink.Screen_MY_REWARDS_SCREEN,
			}
		}
		expiresAfterDuration = notificationParams.ExpiresAfterDuration
	}

	message.CampaignName = commsPb.CampaignName_CAMPAIGN_NAME_REWARD_EARNED

	// setting message.NotificationMessage
	switch rewardNotificationType {
	case rewardOffersPb.RewardNotificationType_SYSTEM_TRAY:
		commonTemplateFields := &fcm.CommonTemplateFields{
			Title:           notificationTitle,
			Body:            notificationBody,
			ExpireAt:        timestampPb.New(time.Now().Add(expiresAfterDuration)),
			Deeplink:        notificationDeeplink,
			ExpiryTimerType: fcm.ExpiryTimerType_EXPIRY_TIMER_TYPE_HIDDEN,
		}
		if imageUrl != "" {
			commonTemplateFields.IconAttributes = &fcm.IconAttributes{IconUrl: imageUrl}
		}

		message.NotificationMessage = &fcm.Notification{
			NotificationType: fcm.NotificationType_SYSTEM_TRAY,
			NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
				SystemTrayTemplate: &fcm.SystemTrayTemplate{
					CommonTemplateFields: commonTemplateFields,
				},
			},
		}
	case rewardOffersPb.RewardNotificationType_IN_APP:
		commonTemplateFields := &fcm.CommonTemplateFields{
			Title:           notificationTitle,
			Body:            notificationBody,
			ExpireAt:        timestampPb.New(time.Now().Add(expiresAfterDuration)),
			Deeplink:        notificationDeeplink,
			ExpiryTimerType: fcm.ExpiryTimerType_EXPIRY_TIMER_TYPE_HIDDEN,
		}
		if imageUrl != "" {
			commonTemplateFields.IconAttributes = &fcm.IconAttributes{IconUrl: imageUrl}
		}

		message.NotificationMessage = &fcm.Notification{
			NotificationType: fcm.NotificationType_IN_APP,
			NotificationTemplates: &fcm.Notification_InAppTemplate{
				InAppTemplate: &fcm.InAppTemplate{
					CommonTemplateFields: commonTemplateFields,
					AfterClickAction:     fcm.AfterClickAction_DISMISS,
				},
			},
		}

	case rewardOffersPb.RewardNotificationType_IN_APP_CRITICAL:
		commonTemplateFields := &fcm.CommonTemplateFields{
			Title:           notificationTitle,
			Body:            notificationBody,
			ExpireAt:        timestampPb.New(time.Now().Add(expiresAfterDuration)),
			Deeplink:        notificationDeeplink,
			ExpiryTimerType: fcm.ExpiryTimerType_EXPIRY_TIMER_TYPE_HIDDEN,
		}
		if imageUrl != "" {
			commonTemplateFields.IconAttributes = &fcm.IconAttributes{IconUrl: imageUrl}
		}

		message.NotificationMessage = &fcm.Notification{
			NotificationType: fcm.NotificationType_IN_APP,
			NotificationTemplates: &fcm.Notification_InAppTemplate{
				InAppTemplate: &fcm.InAppTemplate{
					CommonTemplateFields: commonTemplateFields,
					AfterClickAction:     fcm.AfterClickAction_DISMISS,
					NotificationPriority: fcm.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL,
				},
			},
		}
	case rewardOffersPb.RewardNotificationType_NUDGE:
		message.NudgeMessage = &nudgePb.NudgeMetadata{
			TemplateData: map[string]string{
				"ACTION_DETAILS": rewardProto.GetRewardOptions().GetActionDetails(),
			},
		}
	case rewardOffersPb.RewardNotificationType_EMAIL:
		emailMessage, err := r.helper.GetEmailMessage(ctx, notificationTypeMetadata.GetEmail(), rewardProto)
		if err != nil {
			return nil, fmt.Errorf("error while getting email message: %w", err)
		}
		message.EmailMessage = emailMessage
	default:
		return nil, fmt.Errorf("unsupported rewardNotificationType : %s while getting notification message, err : %w", rewardNotificationType, internalerrors.ErrPermanentFailure)
	}

	return &message, nil
}
