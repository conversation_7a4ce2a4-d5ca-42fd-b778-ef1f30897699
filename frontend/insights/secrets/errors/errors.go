//nolint:staticcheck
package errors

import "errors"

var NoDataToBuildSecret = errors.New("no data to build secret")
var NoDataToBuildSecretPostFilters = errors.New("no data to build secret after applying data filters")
var NoRefreshRequired = errors.New("no refresh required for secret")
var UiComponentBuildingSkippedErr = errors.New("ui component building skipped")
var SecretNotReleasedErr = errors.New("secret not released yet")
var NoDataToBuildPortfolioTracker = errors.New("no data to build portfolio tracker")

// NoDataFoundToBuildSecret explicitly indicates that no data was found from experian to build the secret, which is different from NoDataToBuildSecret as it can be used in a more generic context.
var NoDataFoundToBuildSecret = errors.New("no data found to build secret")
