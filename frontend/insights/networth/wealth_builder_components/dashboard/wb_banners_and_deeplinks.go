//nolint:dupl,gocritic
package dashboard

import (
	"fmt"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	beNetworthPb "github.com/epifi/gamma/api/insights/networth"
	netWorthScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/frontend/insights/networth/data_fetcher"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	magicImportScreenOpts "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/magicimport"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/frontend/insights/networth/common"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"

	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"

	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
)

// TODO(Shweta/Nigam): refactor this file to have common code for Android & IOS
func getExportToAiBanner(deeplink *deeplinkPb.Deeplink, platform commontypes.Platform) (*sections.Section, error) {
	interactionBehaviors := &behaviors.InteractionBehavior{
		Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
			OnClickBehavior: &behaviors.OnClickBehavior{
				Action: ui.GetAnyWithoutError(deeplink),
			},
		},
		AnalyticsEvent: &analytics.AnalyticsEvent{
			EventName: "D2HDashboardIntroBannerActioned",
			Properties: map[string]string{
				"banner_type": "talk_to_ai",
			},
		},
	}
	switch platform {
	case commontypes.Platform_IOS:
		return getExportToAiBannerForiOS(interactionBehaviors), nil
	case commontypes.Platform_ANDROID:
		return getExportToAiBannerAndroid(interactionBehaviors), nil
	default:
		return nil, fmt.Errorf("unsupported platform")
	}
}

func getExportToAiBannerAndroid(interactionBehavior *behaviors.InteractionBehavior) *sections.Section {
	exportToAiMsg := "Fi MCP is now live! Talk about your money with your preferred AI assistant"
	IconAiApp := "https://epifi-icons.pointz.in/wb-landing/ai_apps_horizontal.png"
	return &sections.Section{
		Content: &sections.Section_HorizontalListSection{
			HorizontalListSection: &sections.HorizontalListSection{
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
								BgColor: widget.GetLinearGradientBackgroundColour(
									24,
									[]*widget.ColorStop{
										{
											Color:          "#D9CF71",
											StopPercentage: 0,
										},
										{
											Color:          "#D3FFF7",
											StopPercentage: 100,
										},
									},
								),
								Padding: &properties.PaddingProperty{
									Left:   16,
									Right:  16,
									Top:    12,
									Bottom: 12,
								},
							},
						},
					},
				},
				VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
				Components: []*components.Component{
					{
						Content: ui.GetAnyWithoutError(
							&sections.VerticalListSection{
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Size: &properties.Size{
													Width: &properties.Size_Dimension{
														Type: properties.Size_Dimension_DIMENSION_TYPE_WEIGHT,
														Weight: &properties.Weight{
															Value: float32(1),
														},
													},
													Height: &properties.Size_Dimension{
														Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
													},
												},
											},
										},
									},
								},
								Components: []*components.Component{
									{
										Content: ui.GetAnyWithoutError(
											commontypes.GetTextFromStringFontColourFontStyleFontAlignment(
												exportToAiMsg,
												colors.ColorSupportingMoss900,
												commontypes.FontStyle_SUBTITLE_S,
												commontypes.Text_ALIGNMENT_LEFT,
											),
										),
									},
								},
								HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
							},
						),
					},
					{
						Content: ui.GetAnyWithoutError(
							&components.Spacer{
								SpacingValue: components.Spacing_SPACING_XS,
							},
						),
					},
					{
						Content: ui.GetAnyWithoutError(
							commontypes.GetVisualElementFromUrlHeightAndWidth(IconAiApp, 36, 108),
						),
					},
				},
				InteractionBehaviors: []*behaviors.InteractionBehavior{interactionBehavior},
			},
		},
	}
}

func getExportToAiBannerForiOS(interactionBehavior *behaviors.InteractionBehavior) *sections.Section {
	exportToAiMsg := "Fi MCP is now live! Talk about your money with your preferred AI assistant"
	IconAiApp := "https://epifi-icons.pointz.in/wb-landing/ai_apps_horizontal.png"
	return &sections.Section{
		Content: &sections.Section_HorizontalListSection{
			HorizontalListSection: &sections.HorizontalListSection{
				VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
								BgColor: widget.GetLinearGradientBackgroundColour(
									24,
									[]*widget.ColorStop{
										{
											Color:          "#D9CF71",
											StopPercentage: 0,
										},
										{
											Color:          "#D3FFF7",
											StopPercentage: 100,
										},
									},
								),
								Padding: &properties.PaddingProperty{
									Left:   16,
									Right:  16,
									Top:    12,
									Bottom: 12,
								},
							},
						},
					},
				},
				Components: []*components.Component{
					{
						Content: ui.GetAnyWithoutError(
							commontypes.GetTextFromStringFontColourFontStyleFontAlignment(
								exportToAiMsg,
								colors.ColorSupportingMoss900,
								commontypes.FontStyle_SUBTITLE_S,
								commontypes.Text_ALIGNMENT_LEFT,
							),
						),
					},
					{
						Content: ui.GetAnyWithoutError(
							commontypes.GetVisualElementFromUrlHeightAndWidth(IconAiApp, 36, 108),
						),
					},
				},
				InteractionBehaviors: []*behaviors.InteractionBehavior{interactionBehavior},
			},
		},
	}
}

func getMagicImportScreen() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_NETWORTH_MAGIC_IMPORT_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&magicImportScreenOpts.NetworthMagicImportScreenOptions{
			HeaderBar: &ui.HeaderBar{
				LeftItc:   ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportBackIcon, 28, 28)),
				CenterItc: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportHeaderBarIcon, 28, 106)),
			},
			ScanScreen: &magicImportScreenOpts.ScanStateScreen{
				ScreenDisabledDetails: &magicImportScreenOpts.ScanStateScreen_ScreenDisabledDetails{
					IsDisabled: false,
				},
				Title:    commontypes.GetPlainStringText("Snap & add anything to \nyour Net Worth"),
				SubTitle: commontypes.GetPlainStringText("Scan to know the value of any asset"),
				IdeasBanner: &magicImportScreenOpts.IdeasBanner{
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_NETWORTH_MAGIC_IMPORT_IDEAS_SCREEN,
					},
					LeftVisualElement: commontypes.GetVisualElementLottieFromUrlHeightAndWidth(common.MagicImportTryGadgetsAnimation, 58, 183),
					RightIconTextComponent: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("SEE IDEAS", "#00B899", commontypes.FontStyle_OVERLINE_XS_CAPS)).
						WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportUpIcon, 24, 24)),
					Background: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: "#18191B",
						},
					},
					Border: &properties.BorderProperty{
						CornerRadius:    16,
						BorderThickness: 1,
						BorderBgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_RadialGradient{
								RadialGradient: &widget.RadialGradient{
									Center: &widget.CenterCoordinates{
										CenterX: 50,
										CenterY: 0,
									},
									OuterRadius: 100,
									Colours: []string{
										"#4DDCF3EE", "#0DDCF3EE",
									},
								},
							},
						},
					},
				},
				PickerComponent: &magicImportScreenOpts.PickerComponent{
					GalleryItc: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Gallery", colors.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_M)).
						WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(common.MagicImportUploadIcon).
							WithProperties(&commontypes.VisualElementProperties{Width: 16, Height: 16})).
						WithLeftImagePadding(4),
					DocumentItc: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Document", colors.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_M)).
						WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(common.MagicImportFilePlusIcon).
							WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24})).
						WithLeftImagePadding(4),
					Background: widget.GetLinearGradientBackgroundColour(180, []*widget.ColorStop{
						{
							Color:          "#4C3F4146",
							StopPercentage: 0,
						},
						{
							Color:          "#26313234",
							StopPercentage: 100,
						},
					}),
					Border: widget.GetRadialGradientBackgroundColor(
						&widget.CenterCoordinates{
							CenterX: 50,
							CenterY: 0,
						},
						101,
						[]string{"#4CDCF3EE", "#0CDCF3EE"},
					),
					CornerRadius: 16,
				},
				NoOfAssetsSupported: 3,
				// List of supported files by vertex
				// https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/inference
				SupportedFiles: []*magicImportScreenOpts.SupportedFile{
					{
						FileExtension: "pdf",
						IconUrl:       commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportPdfIcon, 32, 32),
						PreviewImg:    commontypes.GetVisualElementImageFromUrl(common.MagicImportPdfPreview),
					},
					{
						FileExtension: "jpg",
						IconUrl:       commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportJPGIcon, 32, 32),
						PreviewImg:    commontypes.GetVisualElementImageFromUrl(common.MagicImportJPGPreview),
					},
					{
						FileExtension: "jpeg",
						IconUrl:       commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportJPEGIcon, 32, 32),
						PreviewImg:    commontypes.GetVisualElementImageFromUrl(common.MagicImportJPEGPreview),
					},
					{
						FileExtension: "png",
						IconUrl:       commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportPNGIcon, 32, 32),
						PreviewImg:    commontypes.GetVisualElementImageFromUrl(common.MagicImportPNGPreview),
					},
					{
						FileExtension: "txt",
						IconUrl:       commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportTextIcon, 32, 32),
						PreviewImg:    commontypes.GetVisualElementImageFromUrl(common.MagicImportTextPreview),
					},
				},
				MaxFileSizeKb: 3072, // 3 MB
			},
			PreviewScreen: &magicImportScreenOpts.PreviewStateScreen{
				Title: commontypes.GetPlainStringText("Ready to analyse its value?"),
				ScanMore: &magicImportScreenOpts.ScanMoreComponent{
					Border: &properties.BorderProperty{
						IsDash:          true,
						BorderColor:     colors.ColorLightPrimaryAction,
						DashLength:      5,
						DashGapLength:   5,
						CornerRadius:    13,
						BorderThickness: 1,
					},
					Icon: commontypes.GetVisualElementImageFromUrl(common.MagicImportScanMoreIcon),
					Text: commontypes.GetPlainStringText("SCAN"),
				},
				AnalyseCta: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Analyse", colors.ColorSnow, commontypes.FontStyle_BUTTON_M)).
					WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(common.MagicImportStrikeIcon).
						WithProperties(
							&commontypes.VisualElementProperties{
								Width:  24,
								Height: 24,
							})).
					WithContainerProperties(&ui.IconTextComponent_ContainerProperties{
						CornerRadius: 40,
						BackgroundColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{BlockColour: colors.ColorLightPrimaryAction},
						},
						TopPadding:    14,
						BottomPadding: 14,
					}),
			},
			LoadingScreen: &magicImportScreenOpts.LoadingStateScreen{
				Title:              commontypes.GetPlainStringText("Analysing..."),
				LoaderAnimationUrl: common.MagicImportAnalysingAnimation,
				ConfidentialMessage: ui.NewITC().WithTexts(commontypes.
					GetTextFromStringFontColourFontStyleFontAlignment("Your data is safe & confidential", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_DISPLAY_M, commontypes.Text_ALIGNMENT_CENTER)).
					WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(common.MagicImportPrivacyIcon).
						WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24})).
					WithRightVisualElement(commontypes.GetVisualElementImageFromUrl(common.MagicImportPrivacyIcon).
						WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24})),
			},
			NetworkErrorScreen: &magicImportScreenOpts.ErrorStateScreen{
				Title:   commontypes.GetTextFromStringFontColourFontStyle("Seems like a network issue", colors.ColorSupportingCherry100, commontypes.FontStyle_HEADLINE_L),
				Message: commontypes.GetTextFromStringFontColourFontStyle("Check your internet connection and scan again.", colors.ColorOnlightLowEmphasis, commontypes.FontStyle_SUBTITLE_M),
				Icon:    commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportErrorIcon, 49, 49),
				ActionCtas: []*deeplinkPb.Cta{
					{
						Text:         "Try again",
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
						Type:         deeplinkPb.Cta_RETRY,
						CtaTrailingImage: commontypes.GetVisualElementImageFromUrl(common.MagicImportTryAgain).
							WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24}),
					},
				},
			},
			LimitExhaustedErrorScreen: &magicImportScreenOpts.ErrorStateScreen{
				Title:   commontypes.GetTextFromStringFontColourFontStyle("Usage limit exhausted ⏳", colors.ColorSupportingCherry100, commontypes.FontStyle_HEADLINE_L),
				Message: commontypes.GetTextFromStringFontColourFontStyle("Please try again after some time.", colors.ColorOnlightLowEmphasis, commontypes.FontStyle_SUBTITLE_M),
				Icon:    commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportErrorIcon, 49, 49),
				ActionCtas: []*deeplinkPb.Cta{
					{
						Text:         "Go back to dashboard",
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
						Type:         deeplinkPb.Cta_CANCEL,
					},
				},
				ErrorImage: commontypes.GetVisualElementImageFromUrl(common.MagicImportSleepingWatch),
			},
			IntroPageScreen: &magicImportScreenOpts.IntroPageScreen{
				HeaderBar: &ui.HeaderBar{
					LeftItc:   ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportBackIcon, 28, 28)),
					CenterItc: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportHeaderBarIcon, 28, 106)),
					RightItc:  ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Skip", "#00B899", commontypes.FontStyle_BUTTON_M)),
				},
				Version:           "1",
				IntroAnimationUrl: common.MagicImportIntroAnimationUrl,
			},
		}),
	}
}

func (w *WealthBuilderLanding) getConnectMoreDeeplink(netWorthDashboardConfig *networthFePb.NetWorthDashboardConfig, categoriesDataMap map[networthFePb.NetworthCategory]*data_fetcher.CategoryData) *deeplinkPb.Deeplink {
	var notConnectedAssets, notConnectedLiabilities []string
	for _, sectionConfig := range netWorthDashboardConfig.GetSections() {
		for _, widgetConfig := range sectionConfig.GetWidgets() {
			categoryData, found := categoriesDataMap[widgetConfig.GetCategory()]
			if !found {
				continue
			}
			if assetType, exists := common.CategoryToAssetTypeMap[widgetConfig.GetCategory()]; exists {
				if categoryData.ComputationStatus == beNetworthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND {
					notConnectedAssets = append(notConnectedAssets, assetType.String())
				}
				continue
			}
			if liabilityType, exists := common.CategoryToLiabilityTypeMap[widgetConfig.GetCategory()]; exists {
				if categoryData.ComputationStatus == beNetworthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND {
					notConnectedLiabilities = append(notConnectedLiabilities, liabilityType.String())
				}
				continue
			}
		}
	}
	var connectMoreDeeplink *deeplinkPb.Deeplink
	if len(notConnectedAssets) != 0 || len(notConnectedLiabilities) != 0 {
		connectMoreDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&netWorthScreenOptions.ConnectMoreAssetsScreenOptions{
				ConnectMoreAssetsScreenRequestParams: &networthFePb.ConnectMoreAssetsScreenRequestParams{
					AssetTypes:     notConnectedAssets,
					LiabilityTypes: notConnectedLiabilities,
				},
			}),
		}
	}
	return connectMoreDeeplink
}
