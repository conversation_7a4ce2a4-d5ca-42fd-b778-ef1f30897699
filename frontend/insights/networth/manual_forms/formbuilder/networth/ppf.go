package networth

import (
	"context"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/money"

	goUtils "github.com/epifi/be-common/pkg/go_utils"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	networthBeModelPb "github.com/epifi/gamma/api/insights/networth/model"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputbuilder"
)

type PublicProvidentFund struct {
}

func NewPublicProvidentFund() *PublicProvidentFund {
	return &PublicProvidentFund{}
}

// nolint: dupl
func (a *PublicProvidentFund) BuildForm(ctx context.Context, req *networthBeFePb.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	decl := req.GetInvestmentDeclaration().GetDeclarationDetails().GetPublicProvidentFund()

	ppfName := inputbuilder.NewStringBuilder("INVESTMENT NAME", "Investment Name", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME.String())
	currentValue := inputbuilder.NewInt64Builder("CURRENT VALUE (₹)", "Current value (₹)", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE.String())
	dateOfInvestment := inputbuilder.NewDateBuilder("DATE OF INVESTMENT", "Date of investment", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE.String())
	if decl != nil {
		ppfName.WithValue(decl.GetInvestmentName())
		currentValue.WithValue(decl.GetCurrentValue().GetUnits())
		dateOfInvestment.WithValue(decl.GetInvestmentDate())
	}

	inputSection := networthFePb.NewNetWorthManualFormComponentsSection("Investment Details").
		WithInputComponent(ppfName.Build()).
		WithInputComponent(currentValue.Build()).
		WithInputComponent(dateOfInvestment.Build())

	form := networthFePb.NewNetWorthManualForm("Public Provident Fund", "Add Investment").
		WithComponentsSection(inputSection)
	// If the data has already been entered by the user, display the option to delete it.
	if decl != nil {
		form = form.WithActionCta(networthFePb.NewAssetActionButton().
			WithActionType(networthFePb.AssetActionType_ASSET_ACTION_TYPE_DELETE).
			WithDisplayText(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Remove", "#AA301F", commontypes.FontStyle_BUTTON_S)).
				WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/networth/delete_icon_manual_asset.png", 13, 13)).
				WithLeftImagePadding(8)))
	}
	return form, nil
}

func (a *PublicProvidentFund) ConvertFormInputToInvestmentDeclaration(ctx context.Context, inputComponents []*networthFePb.NetWorthManualFormInputComponent) (*networthBeModelPb.InvestmentDeclaration, error) {
	details := &networthBeModelPb.PublicProvidentFund{}
	decl := &networthBeModelPb.InvestmentDeclaration{
		InstrumentType: typesPb.InvestmentInstrumentType_PUBLIC_PROVIDENT_FUND,
		DeclarationDetails: &networthBeModelPb.OtherDeclarationDetails{
			Details: &networthBeModelPb.OtherDeclarationDetails_PublicProvidentFund{
				PublicProvidentFund: details,
			},
		},
	}

	for _, component := range inputComponents {
		data := component.GetInputData()
		inputValue := data.GetInputValueFromSingleOption()
		fieldName := goUtils.Enum(data.GetFieldName(), networthFePb.NetworthManualFormFieldName_value, networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED)
		switch fieldName {
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE:
			investmentDate := inputValue.GetDateData().GetData()
			details.InvestmentDate = investmentDate
			decl.InvestedAt = timestampPb.New(time.Date(int(investmentDate.GetYear()), time.Month(investmentDate.GetMonth()), int(investmentDate.GetDay()), 0, 0, 0, 0, datetime.IST))
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME:
			details.InvestmentName = inputValue.GetStringData().GetData().GetValue()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE:
			details.CurrentValue = money.AmountINR(inputValue.GetInt64Data().GetData().GetValue()).GetPb()
		}
	}

	return decl, nil
}
