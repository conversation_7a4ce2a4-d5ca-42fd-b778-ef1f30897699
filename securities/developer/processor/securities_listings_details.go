package processor

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	devPb "github.com/epifi/gamma/api/securities/developer"
	"github.com/epifi/gamma/securities/catalog/dao"
)

type SecuritiesListingsDetails struct {
	securityListingsDao dao.SecurityListingsDao
}

func NewSecuritiesListingsDetails(securityListingsDao dao.SecurityListingsDao) *SecuritiesListingsDetails {
	return &SecuritiesListingsDetails{
		securityListingsDao: securityListingsDao,
	}
}

func (d *SecuritiesListingsDetails) FetchParamList(ctx context.Context, entity devPb.SecuritiesEntitiy) ([]*db_state.ParameterMeta, error) {
	var exchangeOptions []string
	for value, name := range catalogPb.Exchange_name {
		if value != int32(catalogPb.Exchange_EXCHANGE_UNSPECIFIED) {
			exchangeOptions = append(exchangeOptions, name)
		}
	}

	return []*db_state.ParameterMeta{
		{
			Name:            isinKey,
			Label:           "ISIN (use with Exchange)",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            exchangeKey,
			Label:           "Exchange (use with ISIN)",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         exchangeOptions,
		},
		{
			Name:            externalIdKey,
			Label:           "External ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}, nil
}

func (d *SecuritiesListingsDetails) FetchData(ctx context.Context, entity devPb.SecuritiesEntitiy, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", epifierrors.ErrInvalidArgument
	}

	var isin, externalId string
	var exchanges []catalogPb.Exchange
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case isinKey:
			isin = filter.GetStringValue()
		case exchangeKey:
			for _, exchangeName := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				if exchangeValue, exists := catalogPb.Exchange_value[exchangeName]; exists {
					exchanges = append(exchanges, catalogPb.Exchange(exchangeValue))
				}
			}
		case externalIdKey:
			externalId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("invalid filter: %s", filter.GetParameterName())
		}
	}

	// Get all fields for the security listing
	fieldMasks := []catalogPb.SecurityListingFieldMask{
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SECURITY_ID,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_IS_PRIMARY_LISTING,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_STATUS,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_CREATED_AT,
		catalogPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_UPDATED_AT,
	}

	var listings []*catalogPb.SecurityListing
	var err error

	switch {
	case externalId != "":
		// If external ID is provided, use GetByExternalId
		listing, err := d.securityListingsDao.GetByExternalId(ctx, externalId, fieldMasks)
		if err != nil {
			logger.Error(ctx, "failed to get security listing by external ID", zap.Error(err), zap.String("external_id", externalId))
			return "", fmt.Errorf("failed to get security listing: %w", err)
		}
		listings = []*catalogPb.SecurityListing{listing}
	case isin != "":
		// If ISIN is provided, use GetByISINAndExchange
		if len(exchanges) == 0 {
			logger.Error(ctx, "exchange cannot be unspecified", zap.Error(err), zap.String("isin", isin), zap.Any("exchanges", exchanges))
			return "", fmt.Errorf("exchange cannot be unspecified for isin: %w", err)
		}

		pairs := make([]*catalogPb.ISINExchangePair, 0, len(exchanges))
		for _, exchange := range exchanges {
			pairs = append(pairs, &catalogPb.ISINExchangePair{
				Isin:     isin,
				Exchange: exchange,
			})
		}

		listings, err = d.securityListingsDao.GetByISINAndExchange(ctx, pairs, fieldMasks)
		if err != nil {
			logger.Error(ctx, "failed to get security listings by ISIN and exchange", zap.Error(err), zap.String("isin", isin), zap.Any("exchanges", exchanges))
			return "", fmt.Errorf("failed to get security listings: %w", err)
		}
	default:
		return "", fmt.Errorf("either external_id or isin must be provided")
	}

	if len(listings) == 0 {
		return "", fmt.Errorf("no security listings found")
	}

	// Marshal to JSON
	marshalOptions := protojson.MarshalOptions{
		UseEnumNumbers: false,
		Multiline:      true,
		Indent:         "  ",
	}

	// Convert listings to SecurityAndSecurityListing format
	securityAndListings := make([]*catalogPb.SecurityAndSecurityListing, len(listings))
	for i, listing := range listings {
		securityAndListings[i] = &catalogPb.SecurityAndSecurityListing{
			SecurityListing: listing,
		}
	}

	jsonBytes, err := marshalOptions.Marshal(&catalogPb.GetSecurityListingsResponse{
		SecurityAndSecurityListings: securityAndListings,
	})
	if err != nil {
		logger.Error(ctx, "failed to marshal security listings", zap.Error(err))
		return "", fmt.Errorf("failed to marshal security listings: %w", err)
	}

	return string(jsonBytes), nil
}
