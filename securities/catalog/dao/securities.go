package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/securities/catalog/dao/model"
)

// SecuritiesDaoPGDB implements the SecuritiesDao interface
type SecuritiesDaoPGDB struct {
	db *gorm.DB
}

var (
	securityFieldMaskToColumnMap = map[catalogPb.SecurityFieldMask]string{
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID:                 "id",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_SECURITY_TYPE:      "security_type",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME:               "security_name",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR:             "vendor",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID: "vendor_security_id",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL:           "logo_url",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_DETAILS:            "security_details",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_CREATED_AT:         "created_at",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_UPDATED_AT:         "updated_at",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_DELETED_AT:         "deleted_at_unix",
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_FINANCIAL_INFO:     "financial_info",
	}
)

// NewSecuritiesDaoPGDB creates a new SecuritiesDaoPGDB with the given database connection and ID generator
func NewSecuritiesDaoPGDB(db *gorm.DB) *SecuritiesDaoPGDB {
	return &SecuritiesDaoPGDB{db: db}
}

// Create creates a new Security in the database
func (s *SecuritiesDaoPGDB) Create(ctx context.Context, security *catalogPb.Security) (*catalogPb.Security, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecuritiesDaoPGDB", "Create", time.Now())

	if err := s.validateCreateRequest(security); err != nil {
		return nil, errors.Wrap(err, "failed to validate security")
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	security.Id = "SEC" + idgen.RandAlphaNumericString(4)
	securityModel, err := model.NewSecuritiesModel(security)
	if err != nil {
		return nil, fmt.Errorf("failed to create security model: %w", err)
	}

	if err = db.Create(securityModel).Error; err != nil {
		if storagev2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, fmt.Errorf("failed to create security: %w", err)
	}

	return securityModel.ToProto(), nil
}

// Update updates an existing Security in the database based on field masks
func (s *SecuritiesDaoPGDB) Update(ctx context.Context, security *catalogPb.Security,
	updateMask []catalogPb.SecurityFieldMask) (*catalogPb.Security, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecuritiesDaoPGDB", "Update", time.Now())

	if security.GetId() == "" {
		return nil, fmt.Errorf("id cannot be empty for security update operation")
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	cols, err := convertSecurityFieldMaskToColumns(updateMask, true)
	if err != nil || len(cols) == 0 {
		return nil, fmt.Errorf("failed to get column names from field mask: %w", err)
	}
	securityModel, err := model.NewSecuritiesModel(security)
	if err != nil {
		return nil, fmt.Errorf("failed to create security model: %w", err)
	}

	result := db.Model(&model.Securities{}).Clauses(clause.Returning{}).
		Where("id = ?", security.GetId()).
		Select(cols).
		Updates(securityModel)

	if err = result.Error; err != nil {
		return nil, fmt.Errorf("unable to update security model for id: %s, %w", security.GetId(), err)
	}
	if result.RowsAffected == 0 {
		return nil, fmt.Errorf("no security found with id: %s, %w", security.GetId(), epifierrors.ErrNoRowsAffected)
	}

	return securityModel.ToProto(), nil
}

// GetById retrieves a Security by its ID
func (s *SecuritiesDaoPGDB) GetById(ctx context.Context, id string, fieldMasks []catalogPb.SecurityFieldMask) (*catalogPb.Security, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecuritiesDaoPGDB", "GetById", time.Now())

	if id == "" {
		return nil, fmt.Errorf("id cannot be empty for get operation")
	}

	cols, err := convertSecurityFieldMaskToColumns(fieldMasks, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	securityModel := &model.Securities{}

	query := s.db.WithContext(ctx).Where("id = ?", id)
	if len(cols) > 0 {
		query = query.Select(cols)
	}
	dbErr := query.Take(&securityModel).Error
	if errors.Is(dbErr, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("record not found for id: %s, %w", id, epifierrors.ErrRecordNotFound)
	}
	if dbErr != nil {
		return nil, fmt.Errorf("failed to get Security: %w", dbErr)
	}

	return securityModel.ToProto(), nil
}

// GetByVendorSecurityId retrieves a Security by its vendor and vendor security id
func (s *SecuritiesDaoPGDB) GetByVendorSecurityId(ctx context.Context, vendor vendorgateway.Vendor, vendorSecurityId string, fieldMasks []catalogPb.SecurityFieldMask) (*catalogPb.Security, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecuritiesDaoPGDB", "GetByVendorSecurityId", time.Now())

	if vendorSecurityId == "" {
		return nil, fmt.Errorf("vendorSecurityId cannot be empty for get operation")
	}

	if vendor == vendorgateway.Vendor_VENDOR_UNSPECIFIED {
		return nil, fmt.Errorf("vendor cannot be unspecified for get operation")
	}

	cols, err := convertSecurityFieldMaskToColumns(fieldMasks, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	securityModel := &model.Securities{}

	query := s.db.WithContext(ctx).Where("vendor = ? and vendor_security_id = ?", vendor, vendorSecurityId)
	if len(cols) > 0 {
		query = query.Select(cols)
	}
	dbErr := query.Take(&securityModel).Error
	if errors.Is(dbErr, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("record not found for vendor: %s and vendor_security_id: %s, %w", vendor.String(), vendorSecurityId, epifierrors.ErrRecordNotFound)
	}
	if dbErr != nil {
		return nil, fmt.Errorf("failed to get Security: %w", dbErr)
	}

	return securityModel.ToProto(), nil
}

func (s *SecuritiesDaoPGDB) BulkGet(ctx context.Context, ids []string, fieldMask []catalogPb.SecurityFieldMask) (map[string]*catalogPb.Security, error) {
	defer metric_util.TrackDuration("securities/catalog/dao", "SecuritiesDaoPGDB", "BulkGet", time.Now())

	securitesMap := make(map[string]*catalogPb.Security)
	if len(ids) == 0 {
		return securitesMap, nil
	}

	// select id since it's primary key and query will happen on that
	fieldMask = append(fieldMask, catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID)
	cols, err := convertSecurityFieldMaskToColumns(fieldMask, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}

	var securityModels []*model.Securities
	query := s.db.WithContext(ctx).Where("id IN ?", ids)

	if len(cols) > 0 {
		query = query.Select(cols)
	}

	dbErr := query.Find(&securityModels).Error
	if dbErr != nil {
		return nil, fmt.Errorf("failed to bulk get Securities: %w", dbErr)
	}

	result := make(map[string]*catalogPb.Security, len(securityModels))
	for _, sm := range securityModels {
		result[sm.Id.String] = sm.ToProto()
	}

	return result, nil
}

// validateCreateRequest validates a Security create request
func (s *SecuritiesDaoPGDB) validateCreateRequest(security *catalogPb.Security) error {
	if security.GetSecurityName() == "" {
		return fmt.Errorf("security name cannot be empty")
	}

	if security.GetVendor() == 0 {
		return fmt.Errorf("vendor cannot be unspecified")
	}

	if security.GetVendorSecurityId() == "" {
		return fmt.Errorf("vendor security id cannot be empty")
	}

	return nil
}

func convertSecurityFieldMaskToColumns(fieldMasks []catalogPb.SecurityFieldMask, skipId bool) ([]string, error) {
	if len(fieldMasks) == 0 {
		return nil, nil
	}

	var cols []string
	for _, field := range lo.Uniq(fieldMasks) {
		if field == catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID && skipId {
			continue
		}
		col, found := securityFieldMaskToColumnMap[field]
		if !found {
			return nil, fmt.Errorf("%s mapping not found in fieldMask to col name map", field.String())
		}
		cols = append(cols, col)
	}

	return lo.Uniq(cols), nil
}
