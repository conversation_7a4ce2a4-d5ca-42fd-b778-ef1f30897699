package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/nulltypes"

	catalogPb "github.com/epifi/gamma/api/securities/catalog"
)

type Securities struct {
	Id               nulltypes.NullString   `gorm:"primary_key;not null"`
	SecurityType     catalogPb.SecurityType `gorm:"not null"`
	SecurityName     nulltypes.NullString
	Vendor           vendorgateway.Vendor `gorm:"not null"`
	VendorSecurityId nulltypes.NullString `gorm:"not null"`
	LogoUrl          nulltypes.NullString
	SecurityDetails  *catalogPb.SecurityDetails `gorm:"type:jsonb"`
	CreatedAt        nulltypes.NullTime         `gorm:"not null"`
	UpdatedAt        nulltypes.NullTime         `gorm:"not null"`
	DeletedAtUnix    int64                      `gorm:"not null;default:0"`
	FinancialInfo    *catalogPb.FinancialInfo   `gorm:"type:jsonb"`
}

func NewSecuritiesModel(proto *catalogPb.Security) (*Securities, error) {
	model := &Securities{
		Id:               nulltypes.NewNullString(proto.GetId()),
		SecurityType:     proto.GetSecurityType(),
		SecurityName:     nulltypes.NewNullString(proto.GetSecurityName()),
		Vendor:           proto.GetVendor(),
		VendorSecurityId: nulltypes.NewNullString(proto.GetVendorSecurityId()),
		LogoUrl:          nulltypes.NewNullString(proto.GetLogoUrl()),
		SecurityDetails:  proto.GetSecurityDetails(),
		FinancialInfo:    proto.GetFinancialInfo(),
	}

	if proto.GetCreatedAt() != nil && proto.GetCreatedAt().IsValid() {
		model.CreatedAt = nulltypes.NewNullTime(proto.GetCreatedAt().AsTime())
	} else {
		model.CreatedAt = nulltypes.NewNullTime(time.Now())
	}

	if proto.GetUpdatedAt() != nil && proto.GetUpdatedAt().IsValid() {
		model.UpdatedAt = nulltypes.NewNullTime(proto.GetUpdatedAt().AsTime())
	} else {
		model.UpdatedAt = nulltypes.NewNullTime(time.Now())
	}

	if proto.GetDeletedAt() != nil && proto.GetDeletedAt().IsValid() {
		model.DeletedAtUnix = proto.GetDeletedAt().AsTime().Unix()
	}

	return model, nil
}

func (s *Securities) ToProto() *catalogPb.Security {
	proto := &catalogPb.Security{
		Id:               s.Id.String,
		SecurityType:     s.SecurityType,
		SecurityName:     s.SecurityName.String,
		Vendor:           s.Vendor,
		VendorSecurityId: s.VendorSecurityId.String,
		LogoUrl:          s.LogoUrl.String,
		SecurityDetails:  s.SecurityDetails,
		CreatedAt:        s.CreatedAt.GetProto(),
		UpdatedAt:        s.UpdatedAt.GetProto(),
		FinancialInfo:    s.FinancialInfo,
	}

	if s.DeletedAtUnix > 0 {
		proto.DeletedAt = timestampPb.New(time.Unix(s.DeletedAtUnix, 0))
	}

	return proto
}

// TableName gives the actual table name in the DB
func (c *Securities) TableName() string {
	return "securities"
}
