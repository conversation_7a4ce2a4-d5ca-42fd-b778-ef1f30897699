package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/nulltypes"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
)

// SecurityListing represents a listing of a security (such as a stock) on a specific exchange.
type SecurityListing struct {
	// Unique internal identifier for the security listing
	InternalId nulltypes.NullString `gorm:"primaryKey;not null"`
	// External identifier (unique)
	ExternalId nulltypes.NullString `gorm:"not null"`
	// Security ID this listing refers to
	SecurityId nulltypes.NullString `gorm:"not null"`
	// Exchange where the security is listed
	Exchange catalogPb.Exchange `gorm:"not null"`
	// Trading symbol for the security
	Symbol nulltypes.NullString `gorm:"not null"`
	// Whether this is the primary listing
	IsPrimaryListing bool `gorm:"not null"`
	// Listing status
	Status catalogPb.ListingStatus `gorm:"not null"`
	// ISIN code for the security
	ISIN nulltypes.NullString
	// Vendor information
	Vendor vendorgateway.Vendor `gorm:"not null;"`
	// Vendor-specific listing ID
	VendorListingId nulltypes.NullString `gorm:"not null"`
	// Record creation timestamp
	CreatedAt time.Time `gorm:"not null;default:now()"`
	// Record last update timestamp
	UpdatedAt time.Time `gorm:"not null;default:now()"`
	// Soft delete field (Unix timestamp); 0 means not deleted
	DeletedAtUnix int64 `gorm:"not null;default:0"`
}

func (s *SecurityListing) TableName() string {
	return "security_listings"
}

// NewSecurityListing creates a SecurityListing model from a proto message
func NewSecurityListing(proto *catalogPb.SecurityListing) *SecurityListing {
	model := &SecurityListing{
		InternalId:       nulltypes.NewNullString(proto.GetInternalId()),
		ExternalId:       nulltypes.NewNullString(proto.GetExternalId()),
		SecurityId:       nulltypes.NewNullString(proto.GetSecurityId()),
		Exchange:         proto.GetExchange(),
		Symbol:           nulltypes.NewNullString(proto.GetSymbol()),
		IsPrimaryListing: proto.GetIsPrimaryListing(),
		Status:           proto.GetStatus(),
		ISIN:             nulltypes.NewNullString(proto.GetIsin()),
		Vendor:           proto.GetVendor(),
		VendorListingId:  nulltypes.NewNullString(proto.GetVendorListingId()),
	}
	if proto.GetDeletedAt() != nil {
		model.DeletedAtUnix = proto.GetDeletedAt().AsTime().Unix()
	}
	return model
}

// ToProto converts a SecurityListing model to its proto representation
func (s *SecurityListing) ToProto() *catalogPb.SecurityListing {
	proto := &catalogPb.SecurityListing{
		InternalId:       s.InternalId.GetValue(),
		ExternalId:       s.ExternalId.GetValue(),
		SecurityId:       s.SecurityId.GetValue(),
		Exchange:         s.Exchange,
		Symbol:           s.Symbol.GetValue(),
		IsPrimaryListing: s.IsPrimaryListing,
		Status:           s.Status,
		Isin:             s.ISIN.GetValue(),
		Vendor:           s.Vendor,
		VendorListingId:  s.VendorListingId.GetValue(),
		CreatedAt:        timestampPb.New(s.CreatedAt),
		UpdatedAt:        timestampPb.New(s.UpdatedAt),
	}

	if !s.CreatedAt.IsZero() {
		proto.CreatedAt = timestampPb.New(s.CreatedAt)
	}
	if !s.UpdatedAt.IsZero() {
		proto.UpdatedAt = timestampPb.New(s.UpdatedAt)
	}
	if s.DeletedAtUnix != 0 {
		proto.DeletedAt = timestampPb.New(time.Unix(int64(s.DeletedAtUnix), 0))
	}
	return proto
}
