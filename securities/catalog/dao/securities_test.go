package dao

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/vendorgateway"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
)

var (
	securityModel = &catalogPb.Security{
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     "Security 4",
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorSecurityId: "Bridgewise345",
		LogoUrl:          "logo url",
		FinancialInfo: &catalogPb.FinancialInfo{
			MarketCap: &money.Money{
				CurrencyCode: "USD",
				Units:        3000000000,
				Nanos:        0,
			},
			TtmFundamentalParameters: &catalogPb.FundamentalParameters{
				PeRatio: 30.5,
				PbRatio: 10.2,
			},
		},
	}
	securityModel2 = &catalogPb.Security{
		Id:               "SECA1C3",
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     "Security 2",
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorSecurityId: "Bridgewise234",
		LogoUrl:          "logo url",
		FinancialInfo: &catalogPb.FinancialInfo{
			MarketCap: &money.Money{
				CurrencyCode: "USD",
				Units:        2500000000,
				Nanos:        0,
			},
			TtmFundamentalParameters: &catalogPb.FundamentalParameters{
				PeRatio: 25.1,
				PbRatio: 8.7,
			},
		},
	}
	securityModelUpdateSecurityName = &catalogPb.Security{
		Id:               "SECA1B2",
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     "Security 5",
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorSecurityId: "Bridgewise123",
		LogoUrl:          "logo url",
	}
	securityModelUpdateLogoUrl = &catalogPb.Security{
		Id:               "SECA1C3",
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     "Security 2",
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorSecurityId: "Bridgewise234",
		LogoUrl:          "logo url 2",
	}
	securityModelUpdateSecurityNameAndLogoUrl = &catalogPb.Security{
		Id:               "SECA1D4",
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     "Security 6",
		Vendor:           vendorgateway.Vendor_MORNINGSTAR,
		VendorSecurityId: "MorningStar123",
		LogoUrl:          "logo url 2",
	}
	securityModelUpdateEmptyId = &catalogPb.Security{
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     "Security 6",
		Vendor:           vendorgateway.Vendor_MORNINGSTAR,
		VendorSecurityId: "MorningStar123",
		LogoUrl:          "logo url 2",
	}
	securityModelUpdateNonExistentId = &catalogPb.Security{
		Id:               "SECA1E5",
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     "Security 6",
		Vendor:           vendorgateway.Vendor_MORNINGSTAR,
		VendorSecurityId: "MorningStar123",
		LogoUrl:          "logo url 2",
	}
	securityModel2WithFieldMask = &catalogPb.Security{
		Id:           "SECA1C3",
		SecurityName: "Security 2",
		LogoUrl:      "logo url",
	}
)

func TestSecuritiesDaoPGDB_Create(t *testing.T) {
	type args struct {
		ctx      context.Context
		security *catalogPb.Security
	}

	tests := []struct {
		name    string
		args    args
		want    *catalogPb.Security
		wantErr bool
	}{
		{
			name: "Successful creation",
			args: args{
				ctx:      context.Background(),
				security: securityModel,
			},
			want:    securityModel,
			wantErr: false,
		},
		{
			name: "Empty security name",
			args: args{
				ctx: context.Background(),
				security: &catalogPb.Security{
					SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
					SecurityName:     "",
					Vendor:           vendorgateway.Vendor_FEDERAL_BANK,
					VendorSecurityId: "ZERODHA123",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Empty vendor security ID",
			args: args{
				ctx: context.Background(),
				security: &catalogPb.Security{
					SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
					SecurityName:     "Test Security",
					Vendor:           vendorgateway.Vendor_FEDERAL_BANK,
					VendorSecurityId: "",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			securitiesDao, releaseDao := getSecuritiesDao(t)
			defer releaseDao()

			got, err := securitiesDao.Create(tt.args.ctx, tt.args.security)

			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.Security{}, "id", "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestSecuritiesDaoPGDB_GetById(t *testing.T) {
	type args struct {
		ctx        context.Context
		id         string
		fieldMasks []catalogPb.SecurityFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    *catalogPb.Security
		wantErr bool
	}{
		{
			name: "Valid ID",
			args: args{
				ctx: context.Background(),
				id:  "SECA1C3",
			},
			want:    securityModel2,
			wantErr: false,
		},
		{
			name: "Invalid ID",
			args: args{
				ctx: context.Background(),
				id:  "SECB1A1",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Empty ID",
			args: args{
				ctx: context.Background(),
				id:  "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Valid ID with field mask",
			args: args{
				ctx: context.Background(),
				id:  "SECA1C3",
				fieldMasks: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL,
				},
			},
			want:    securityModel2WithFieldMask,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			securitiesDao, cleanup := getSecuritiesDao(t)
			defer cleanup()

			got, err := securitiesDao.GetById(tt.args.ctx, tt.args.id, tt.args.fieldMasks)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.Security{}, "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetById() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestSecuritiesDaoPGDB_Update(t *testing.T) {
	type args struct {
		ctx        context.Context
		security   *catalogPb.Security
		updateMask []catalogPb.SecurityFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    *catalogPb.Security
		wantErr bool
	}{
		{
			name: "Update security name",
			args: args{
				ctx:      context.Background(),
				security: securityModelUpdateSecurityName,
				updateMask: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
				},
			},
			want:    securityModelUpdateSecurityName,
			wantErr: false,
		},
		{
			name: "Update logo URL",
			args: args{
				ctx:      context.Background(),
				security: securityModelUpdateLogoUrl,
				updateMask: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL,
				},
			},
			want:    securityModelUpdateLogoUrl,
			wantErr: false,
		},
		{
			name: "Update multiple fields",
			args: args{
				ctx:      context.Background(),
				security: securityModelUpdateSecurityNameAndLogoUrl,
				updateMask: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL,
				},
			},
			want:    securityModelUpdateSecurityNameAndLogoUrl,
			wantErr: false,
		},
		{
			name: "Empty ID",
			args: args{
				ctx:      context.Background(),
				security: securityModelUpdateEmptyId,
				updateMask: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Non-existent ID",
			args: args{
				ctx:      context.Background(),
				security: securityModelUpdateNonExistentId,
				updateMask: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Empty field mask",
			args: args{
				ctx:        context.Background(),
				security:   securityModel,
				updateMask: []catalogPb.SecurityFieldMask{},
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			securitiesDao, cleanup := getSecuritiesDao(t)
			defer cleanup()

			got, err := securitiesDao.Update(tt.args.ctx, tt.args.security, tt.args.updateMask)

			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.Security{}, "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Update() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}

func TestSecuritiesDaoPGDB_GetByVendorSecurityId(t *testing.T) {
	type args struct {
		ctx              context.Context
		vendor           vendorgateway.Vendor
		vendorSecurityId string
		fieldMasks       []catalogPb.SecurityFieldMask
	}

	tests := []struct {
		name    string
		args    args
		want    *catalogPb.Security
		wantErr bool
	}{
		{
			name: "Valid ID",
			args: args{
				ctx:              context.Background(),
				vendor:           vendorgateway.Vendor_BRIDGEWISE,
				vendorSecurityId: "Bridgewise234",
			},
			want:    securityModel2,
			wantErr: false,
		},
		{
			name: "Valid ID with field masks",
			args: args{
				ctx:              context.Background(),
				vendor:           vendorgateway.Vendor_BRIDGEWISE,
				vendorSecurityId: "Bridgewise234",
				fieldMasks: []catalogPb.SecurityFieldMask{
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
					catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL,
				},
			},
			want:    securityModel2WithFieldMask,
			wantErr: false,
		},
		{
			name: "Invalid ID",
			args: args{
				ctx:              context.Background(),
				vendor:           vendorgateway.Vendor_BRIDGEWISE,
				vendorSecurityId: "InvalidId",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Empty ID",
			args: args{
				ctx:              context.Background(),
				vendor:           vendorgateway.Vendor_MORNINGSTAR,
				vendorSecurityId: "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Invalid Vendor",
			args: args{
				ctx:              context.Background(),
				vendor:           vendorgateway.Vendor_VENDOR_UNSPECIFIED,
				vendorSecurityId: "validId",
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			securitiesDao, cleanup := getSecuritiesDao(t)
			defer cleanup()

			got, err := securitiesDao.GetByVendorSecurityId(tt.args.ctx, tt.args.vendor, tt.args.vendorSecurityId, tt.args.fieldMasks)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetByVendorSecurityId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&catalogPb.Security{}, "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByVendorSecurityId() got = %v,\n want %v\n diff %s", got, tt.want, diff)
			}
		})
	}
}
