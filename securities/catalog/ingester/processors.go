package ingester

import (
	"fmt"
	"sort"
	"strings"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/type/money"

	moneyPb "github.com/epifi/be-common/pkg/money"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
)

// This file is for processing Bridgewise specific data to BE security and security listing pb

func processCompanyDescriptionFromCompanyDetails(companyDetails *vendorPb.CompanyDetails, companyFundamentalParagraphs []*vgCatalogPb.CompanyFundamentalParagraph) (*catalogPb.StockDetails, error) {
	var stockDescriptionStr string
	gicsSectorType, ok := MapGICSSectorTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsSectorName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS sector type to enum: %s", companyDetails.GetGicsSectorName())
	}
	gicsIndustryGroupType, ok := MapGICSIndustryGroupTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsIndustryGroupName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS industry group type to enum: %s", companyDetails.GetGicsIndustryGroupName())
	}
	gicsIndustryType, ok := MapGICSIndustryTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsIndustryName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS industry type to enum: %s", companyDetails.GetGicsIndustryName())
	}
	for _, paragraph := range companyFundamentalParagraphs {
		if paragraph.GetParagraphType() == vgCatalogPb.SecurityParagraphType_SECURITY_PARAGRAPH_TYPE_DESCRIPTION {
			if paragraph.GetParagraph() == "" {
				return nil, fmt.Errorf("company description is empty")
			}
			stockDescriptionStr = paragraph.GetParagraph()
		}
	}
	return &catalogPb.StockDetails{
		StockName:                companyDetails.GetCompanyName(),
		StockShortName:           companyDetails.GetCompanyNameShort(),
		WebsiteUrl:               companyDetails.GetWebsite(),
		RegionName:               companyDetails.GetRegionName(),
		IncorporationCountryName: companyDetails.GetIncorporationCountryName(),
		GicsSectorType:           gicsSectorType,
		GicsIndustryGroupType:    gicsIndustryGroupType,
		GicsIndustryType:         gicsIndustryType,
		StockDescription:         stockDescriptionStr,
	}, nil
}

func processFinancialInfoFromFundamentalParameters(companyFundamentalParameters []*vgCatalogPb.CompanyFundamentalParameters,
	companyMarketData []*vgCatalogPb.MarketData) *catalogPb.FinancialInfo {
	var bookValuePerShare *money.Money
	var dividendYield, peRatio, pbRatio, returnOnEquity, sharesOutstanding float64
	for _, parameter := range companyFundamentalParameters {
		// Bridgewise Ref: https://docs.google.com/document/d/1awDL9m1xfR0azidSC1kvJBOydwEFQYgOc7rVIHeGMAA/edit?usp=sharing
		switch parameter.GetSecurityParameterType() {
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_BOOK_VALUE_PER_SHARE:
			bookValuePerShare = moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3())
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD:
			dividendYield = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_PE_RATIO:
			peRatio = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_PB_RATIO:
			pbRatio = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_RETURN_ON_EQUITY:
			returnOnEquity = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_SHARES_OUTSTANDING:
			sharesOutstanding = parameter.GetParameterValue()
		}
	}

	return &catalogPb.FinancialInfo{
		MarketCap: processMarketCapFromMarketData(companyMarketData),
		TtmFundamentalParameters: &catalogPb.FundamentalParameters{
			BookValuePerShare: bookValuePerShare,
			DividendYield:     dividendYield,
			PeRatio:           peRatio,
			PbRatio:           pbRatio,
			ReturnOnEquity:    returnOnEquity,
			SharesOutstanding: sharesOutstanding,
		},
	}
}

func processBEFundDetails(fundDetails *vendorPb.FundDetails, fundParagraphs []*vgCatalogPb.FundParagraphs,
	fundSegments []*vendorPb.SegmentDetails, fundHoldings []*vendorPb.HoldingDetails) (*catalogPb.FundDetails, error) {
	var (
		fundDescriptionStr string
		holdingsMap        map[string]float64
		totalWeightage     float64
	)
	holdingsMap = make(map[string]float64)
	for _, paragraph := range fundParagraphs {
		if paragraph.GetParagraphType() == vgCatalogPb.SecurityParagraphType_SECURITY_PARAGRAPH_TYPE_DESCRIPTION {
			if paragraph.GetParagraph() == "" {
				return nil, fmt.Errorf("fund description is empty")
			}
			fundDescriptionStr = paragraph.GetParagraph()
		}
	}

	sectorHoldingsMap := lo.SliceToMap(fundSegments, func(segment *vendorPb.SegmentDetails) (string, float64) {
		return segment.GetSegmentTypeName(), segment.GetSegmentWeight()
	})

	sort.Slice(fundHoldings, func(i, j int) bool {
		return fundHoldings[i].GetWeight() > fundHoldings[j].GetWeight()
	})
	for _, holding := range fundHoldings {
		// We only show 10 stocks with the highest weightage
		if len(holdingsMap) >= 10 {
			holdingsMap["Others"] = 100 - totalWeightage
			break
		}
		holdingWeight := holding.GetWeight() * 100
		holdingsMap[holding.GetCompanyName()] = holdingWeight
		totalWeightage += holdingWeight
	}

	return &catalogPb.FundDetails{
		FundName:           fundDetails.GetFundName(),
		FundNameShort:      fundDetails.GetFundNameShort(),
		CountryName:        fundDetails.GetDomicileCountryName(),
		BenchmarkName:      fundDetails.GetBenchmarkName(),
		BenchmarkNameShort: fundDetails.GetBenchmarkNameShort(),
		FundDescription:    fundDescriptionStr,
		EtfHoldings: &catalogPb.Holdings{
			Holdings: holdingsMap,
		},
		EquitySectorHoldings: &catalogPb.EquitySectorHoldings{
			EquitySectors: sectorHoldingsMap,
		},
	}, nil
}

func processBEEtfFinancialDetails(fundParameters []*vgCatalogPb.FundParameters, fundMarketData []*vgCatalogPb.MarketData) *catalogPb.FinancialInfo {
	var dividendYield, sharpeRatio, trackingErrorPercentage, expenseRatioPercentage float64
	for _, parameter := range fundParameters {
		// Bridgewise Ref: https://docs.google.com/document/d/1awDL9m1xfR0azidSC1kvJBOydwEFQYgOc7rVIHeGMAA/edit?usp=sharing
		switch parameter.GetSecurityParameterType() {
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD:
			dividendYield = parameter.GetParameterValue() * 100
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_SHARPE_RATIO:
			sharpeRatio = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TRACKING_ERROR_PERCENTAGE:
			trackingErrorPercentage = parameter.GetParameterValue() * 100
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EXPENSE_RATIO_PERCENTAGE:
			expenseRatioPercentage = parameter.GetParameterValue() * 100
		}
	}

	return &catalogPb.FinancialInfo{
		MarketCap: processMarketCapFromMarketData(fundMarketData),
		TtmFundamentalParameters: &catalogPb.FundamentalParameters{
			DividendYield:           dividendYield,
			SharpeRatio:             sharpeRatio,
			TrackingErrorPercentage: trackingErrorPercentage,
			ExpenseRatioPercentage:  expenseRatioPercentage,
		},
	}
}

// market data is a slice which contains data for each day, we require the latest day's market cap
func processMarketCapFromMarketData(fundMarketData []*vgCatalogPb.MarketData) *money.Money {
	if len(fundMarketData) == 0 {
		return nil
	}
	lastReportedMarketData := fundMarketData[len(fundMarketData)-1]
	return moneyPb.ParseFloat(lastReportedMarketData.GetMarketCap(), lastReportedMarketData.GetPriceCurrencyIso3())
}
