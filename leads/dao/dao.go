//go:generate mockgen -source=dao.go -destination=mocks/mock_dao.go
//go:generate dao_metrics_gen .

package dao

import (
	"context"

	leadsPb "github.com/epifi/gamma/api/leads"
	"github.com/epifi/gamma/leads/dao/model"
)

type UserLeadDao interface {
	Create(ctx context.Context, userLead *leadsPb.UserLead) (*leadsPb.UserLead, error)
	Update(ctx context.Context, userLead *leadsPb.UserLead, updateMasks []leadsPb.UserLeadFieldMask) error
	GetById(ctx context.Context, id string) (*leadsPb.UserLead, error)
	GetByClientReqIdAndClientId(ctx context.Context, clientReqId string, clientId string) (*leadsPb.UserLead, error)
	GetUserLeadsByFilter(ctx context.Context, filter *model.UserLeadFilter, productTypes []leadsPb.ProductType, active bool) ([]*leadsPb.UserLead, error)
	// RemoveActorId will mark actorId as NULL and set the leadStatus to USER_LEAD_STATUS_LEAD_CREATED in the user lead table if user is already deleted from users table.
	RemoveActorId(ctx context.Context, actorId string) error
}
