// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"

	leads "github.com/epifi/gamma/api/leads"
	model "github.com/epifi/gamma/leads/dao/model"
	gomock "github.com/golang/mock/gomock"
)

// MockUserLeadDao is a mock of UserLeadDao interface.
type MockUserLeadDao struct {
	ctrl     *gomock.Controller
	recorder *MockUserLeadDaoMockRecorder
}

// MockUserLeadDaoMockRecorder is the mock recorder for MockUserLeadDao.
type MockUserLeadDaoMockRecorder struct {
	mock *MockUserLeadDao
}

// NewMockUserLeadDao creates a new mock instance.
func NewMockUserLeadDao(ctrl *gomock.Controller) *MockUserLeadDao {
	mock := &MockUserLeadDao{ctrl: ctrl}
	mock.recorder = &MockUserLeadDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserLeadDao) EXPECT() *MockUserLeadDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockUserLeadDao) Create(ctx context.Context, userLead *leads.UserLead) (*leads.UserLead, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, userLead)
	ret0, _ := ret[0].(*leads.UserLead)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockUserLeadDaoMockRecorder) Create(ctx, userLead interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockUserLeadDao)(nil).Create), ctx, userLead)
}

// GetByClientReqIdAndClientId mocks base method.
func (m *MockUserLeadDao) GetByClientReqIdAndClientId(ctx context.Context, clientReqId, clientId string) (*leads.UserLead, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientReqIdAndClientId", ctx, clientReqId, clientId)
	ret0, _ := ret[0].(*leads.UserLead)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientReqIdAndClientId indicates an expected call of GetByClientReqIdAndClientId.
func (mr *MockUserLeadDaoMockRecorder) GetByClientReqIdAndClientId(ctx, clientReqId, clientId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientReqIdAndClientId", reflect.TypeOf((*MockUserLeadDao)(nil).GetByClientReqIdAndClientId), ctx, clientReqId, clientId)
}

// GetById mocks base method.
func (m *MockUserLeadDao) GetById(ctx context.Context, id string) (*leads.UserLead, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*leads.UserLead)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockUserLeadDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockUserLeadDao)(nil).GetById), ctx, id)
}

// GetUserLeadsByFilter mocks base method.
func (m *MockUserLeadDao) GetUserLeadsByFilter(ctx context.Context, filter *model.UserLeadFilter, productTypes []leads.ProductType, active bool) ([]*leads.UserLead, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLeadsByFilter", ctx, filter, productTypes, active)
	ret0, _ := ret[0].([]*leads.UserLead)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLeadsByFilter indicates an expected call of GetUserLeadsByFilter.
func (mr *MockUserLeadDaoMockRecorder) GetUserLeadsByFilter(ctx, filter, productTypes, active interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLeadsByFilter", reflect.TypeOf((*MockUserLeadDao)(nil).GetUserLeadsByFilter), ctx, filter, productTypes, active)
}

// RemoveActorId mocks base method.
func (m *MockUserLeadDao) RemoveActorId(ctx context.Context, actorId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveActorId", ctx, actorId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveActorId indicates an expected call of RemoveActorId.
func (mr *MockUserLeadDaoMockRecorder) RemoveActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveActorId", reflect.TypeOf((*MockUserLeadDao)(nil).RemoveActorId), ctx, actorId)
}

// Update mocks base method.
func (m *MockUserLeadDao) Update(ctx context.Context, userLead *leads.UserLead, updateMasks []leads.UserLeadFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, userLead, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockUserLeadDaoMockRecorder) Update(ctx, userLead, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockUserLeadDao)(nil).Update), ctx, userLead, updateMasks)
}
