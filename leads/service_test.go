package leads_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"

	brePb "github.com/epifi/gamma/api/bre"
	leadsPb "github.com/epifi/gamma/api/leads"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	typesV2 "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
)

func TestService_CreateLead(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *leadsPb.CreateLeadRequest
	}

	type testCase struct {
		name    string
		args    args
		want    *leadsPb.CreateLeadResponse
		wantErr bool
		// Setup function that configures mocks for this test case
		setupMocks func(mocks *mockedDependencies)
	}

	tests := []testCase{
		{
			name: "should return 101 status when lead exists with given client request id",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(leadsPb.CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_CLIENT_REQUEST_ID), ""),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").
					Return(&leadsPb.UserLead{
						Id:              "lead-123",
						ClientRequestId: "req-123",
						ClientId:        "client-123",
					}, nil)
			},
		},
		{
			name: "should return internal error when dao.GetByClientReqIdAndClientId fails",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").
					Return(nil, errors.New("some error"))
			},
		},
		{
			name: "should return 103 status when lead creation is not allowed by product",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
					ProductType:     leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(leadsPb.CreateLeadResponse_STATUS_USER_CANNOT_START_PRODUCT_JOURNEY), ""),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Mock lead DAO to return not found
				mocks.leadDao.EXPECT().
					GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock user client to return existing actor
				mocks.userClient.EXPECT().
					GetUsers(gomock.Any(), gomock.Any()).
					Return(&userPb.GetUsersResponse{
						Status: rpc.StatusOk(),
						Users: []*userPb.User{
							{
								ActorId: "actor-123",
							},
						},
					}, nil).AnyTimes()

				// Mock loans client to return user status that doesn't allow creation
				mocks.loansClient.EXPECT().
					GetLoanUserStatusV2(gomock.Any(), gomock.Any()).
					Return(&palPb.GetLoanUserStatusV2Response{
						Status:     rpc.StatusOk(),
						UserStatus: palPb.UserStatus_USER_STATUS_REJECTED,
					}, nil)
			},
		},
		{
			name: "should return 102 status when dao.Create returns duplicate entry error because of active lead with different client",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(leadsPb.CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS), ""),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Mock lead DAO to return not found
				mocks.leadDao.EXPECT().
					GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock user client to return no actors
				mocks.userClient.EXPECT().
					GetUsers(gomock.Any(), gomock.Any()).
					Return(&userPb.GetUsersResponse{
						Status: rpc.StatusOk(),
						Users:  []*userPb.User{},
					}, nil).AnyTimes()

				// Mock lead DAO create to return duplicate error
				mocks.leadDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, userLead *leadsPb.UserLead) (*leadsPb.UserLead, error) {
						// Verify that the lead properties are set correctly
						assert.Equal(t, "req-123", userLead.GetClientRequestId())
						assert.Equal(t, "client-123", userLead.GetClientId())
						assert.Equal(t, "9876543210", userLead.GetMobileNumber())

						return nil, epifierrors.ErrDuplicateEntry
					})
				mocks.leadDao.EXPECT().GetUserLeadsByFilter(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]*leadsPb.UserLead{
					{
						ClientId: "client-124",
					},
				}, nil).Times(1)
			},
		},
		{
			name: "should return 105 status when dao.Create returns duplicate entry error because of active lead with same client",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(leadsPb.CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS_FOR_SAME_CLIENT), ""),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Mock lead DAO to return not found
				mocks.leadDao.EXPECT().
					GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock user client to return no actors
				mocks.userClient.EXPECT().
					GetUsers(gomock.Any(), gomock.Any()).
					Return(&userPb.GetUsersResponse{
						Status: rpc.StatusOk(),
						Users:  []*userPb.User{},
					}, nil).AnyTimes()

				// Mock lead DAO create to return duplicate error
				mocks.leadDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, userLead *leadsPb.UserLead) (*leadsPb.UserLead, error) {
						// Verify that the lead properties are set correctly
						assert.Equal(t, "req-123", userLead.GetClientRequestId())
						assert.Equal(t, "client-123", userLead.GetClientId())
						assert.Equal(t, "9876543210", userLead.GetMobileNumber())

						return nil, epifierrors.ErrDuplicateEntry
					})
				mocks.leadDao.EXPECT().GetUserLeadsByFilter(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]*leadsPb.UserLead{
					{
						ClientId: "client-123",
					},
				}, nil).Times(1)
			},
		},
		{
			name: "should return success when lead is created successfully with no actor id",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Mock lead DAO to return not found
				mocks.leadDao.EXPECT().
					GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock user client to return no actors
				mocks.userClient.EXPECT().
					GetUsers(gomock.Any(), gomock.Any()).
					Return(&userPb.GetUsersResponse{
						Status: rpc.StatusOk(),
						Users:  []*userPb.User{},
					}, nil).AnyTimes()

				// Mock lead DAO create to return success
				mocks.leadDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, userLead *leadsPb.UserLead) (*leadsPb.UserLead, error) {
						// Verify that the lead properties are set correctly
						assert.Equal(t, "req-123", userLead.GetClientRequestId())
						assert.Equal(t, "client-123", userLead.GetClientId())
						assert.Equal(t, "9876543210", userLead.GetMobileNumber())

						return &leadsPb.UserLead{
							Id:              "lead-123",
							ClientRequestId: "req-123",
							ClientId:        "client-123",
							MobileNumber:    "9876543210",
							LeadStatus:      leadsPb.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
						}, nil
					})
			},
		},
		{
			name: "should return success when lead is created successfully with actor id",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
					ProductType:     leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Mock lead DAO to return not found
				mocks.leadDao.EXPECT().
					GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock user client to return existing actor
				mocks.userClient.EXPECT().
					GetUsers(gomock.Any(), gomock.Any()).
					Return(&userPb.GetUsersResponse{
						Status: rpc.StatusOk(),
						Users: []*userPb.User{
							{
								ActorId: "actor-123",
							},
						},
					}, nil).AnyTimes()

				// Mock loans client to return user status that allows creation
				mocks.loansClient.EXPECT().
					GetLoanUserStatusV2(gomock.Any(), gomock.Any()).
					Return(&palPb.GetLoanUserStatusV2Response{
						Status:     rpc.StatusOk(),
						UserStatus: palPb.UserStatus_USER_STATUS_OFFER_AVAILABLE,
					}, nil)

				// Mock lead DAO create to return success
				mocks.leadDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, userLead *leadsPb.UserLead) (*leadsPb.UserLead, error) {
						// Verify that the lead properties are set correctly
						assert.Equal(t, "req-123", userLead.GetClientRequestId())
						assert.Equal(t, "client-123", userLead.GetClientId())
						assert.Equal(t, "9876543210", userLead.GetMobileNumber())
						assert.Equal(t, "actor-123", userLead.GetActorId())

						return &leadsPb.UserLead{
							Id:              "lead-123",
							ActorId:         "actor-123",
							ClientRequestId: "req-123",
							ClientId:        "client-123",
							MobileNumber:    "9876543210",
							LeadStatus:      leadsPb.UserLeadStatus_USER_LEAD_STATUS_USER_CREATED,
						}, nil
					})
			},
		},
		{
			name: "should return success and valid lenders when pre-bre check is successful",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
					ProductType:     leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
					Pan:             "**********",
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					PersonalDetails: &leadsPb.PersonalDetails{
						Name: &common.Name{
							FirstName: "John",
							LastName:  "Doe",
						},
						Dob: &date.Date{
							Year:  1990,
							Month: 5,
							Day:   15,
						},
						EmploymentDetails: &leadsPb.EmploymentDetails{
							EmploymentType: typesV2.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
							MonthlyIncome: &money.Money{
								CurrencyCode: "INR",
								Units:        50000,
								Nanos:        0,
							},
						},
						CurrentAddress: &typesV2.PostalAddress{
							PostalCode: "400001",
						},
					},
					AdditionalDetails: &leadsPb.AdditionalDetails{
						Details: &leadsPb.AdditionalDetails_FiPersonalLoanDetails{
							FiPersonalLoanDetails: &leadsPb.FiPersonalLoanDetails{
								EvaluationType: leadsPb.FiLoansEvaluationType_FI_LOANS_EVALUATION_TYPE_BASIC,
							},
						},
					},
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.StatusOk(),
				Data: &leadsPb.CreateLeadResponse_FiLoansLeadResponse{
					FiLoansLeadResponse: &leadsPb.FiLoansLeadResponse{
						AvailableLenderTypes: []leadsPb.LenderType{leadsPb.LenderType_LENDER_TYPE_LARGE_NBFC},
					},
				},
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").Return(nil, epifierrors.ErrRecordNotFound)
				mocks.userClient.EXPECT().GetUsers(gomock.Any(), gomock.Any()).Return(&userPb.GetUsersResponse{Status: rpc.StatusOk(), Users: []*userPb.User{{ActorId: "actor-123"}}}, nil).AnyTimes()
				mocks.loansClient.EXPECT().GetLoanUserStatusV2(gomock.Any(), gomock.Any()).Return(&palPb.GetLoanUserStatusV2Response{Status: rpc.StatusOk(), UserStatus: palPb.UserStatus_USER_STATUS_ELIGIBLE_TO_APPLY}, nil)
				mocks.breClient.EXPECT().GetPreBreEligibilityDetails(gomock.Any(), gomock.Any()).Return(&brePb.GetPreBreEligibilityDetailsResponse{
					Status: rpc.StatusOk(),
					Decision: &brePb.PreBreDecision{
						ValidLenders: []palPb.Vendor{palPb.Vendor_MONEYVIEW},
					},
				}, nil)
				mocks.leadDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&leadsPb.UserLead{}, nil)
			},
		},
		{
			name: "should return rejected status when pre-bre check has no valid lenders",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
					ProductType:     leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
					Pan:             "**********",
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					PersonalDetails: &leadsPb.PersonalDetails{
						Name: &common.Name{
							FirstName: "John",
							LastName:  "Doe",
						},
						Dob: &date.Date{
							Year:  1990,
							Month: 5,
							Day:   15,
						},
						EmploymentDetails: &leadsPb.EmploymentDetails{
							EmploymentType: typesV2.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
							MonthlyIncome: &money.Money{
								CurrencyCode: "INR",
								Units:        50000,
								Nanos:        0,
							},
						},
						CurrentAddress: &typesV2.PostalAddress{
							PostalCode: "400001",
						},
					},
					AdditionalDetails: &leadsPb.AdditionalDetails{
						Details: &leadsPb.AdditionalDetails_FiPersonalLoanDetails{
							FiPersonalLoanDetails: &leadsPb.FiPersonalLoanDetails{
								EvaluationType: leadsPb.FiLoansEvaluationType_FI_LOANS_EVALUATION_TYPE_BASIC,
							},
						},
					},
				},
			},
			want: &leadsPb.CreateLeadResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(leadsPb.CreateLeadResponse_STATUS_LEAD_REJECTED), ""),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").Return(nil, epifierrors.ErrRecordNotFound)
				mocks.userClient.EXPECT().GetUsers(gomock.Any(), gomock.Any()).Return(&userPb.GetUsersResponse{Status: rpc.StatusOk(), Users: []*userPb.User{{ActorId: "actor-123"}}}, nil).AnyTimes()
				mocks.loansClient.EXPECT().GetLoanUserStatusV2(gomock.Any(), gomock.Any()).Return(&palPb.GetLoanUserStatusV2Response{Status: rpc.StatusOk(), UserStatus: palPb.UserStatus_USER_STATUS_ELIGIBLE_TO_APPLY}, nil)
				mocks.breClient.EXPECT().GetPreBreEligibilityDetails(gomock.Any(), gomock.Any()).Return(&brePb.GetPreBreEligibilityDetailsResponse{
					Status: rpc.StatusOk(),
					Decision: &brePb.PreBreDecision{
						ValidLenders: []palPb.Vendor{},
					},
				}, nil)
			},
		},
		{
			name: "should return internal error when pre-bre check fails",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.CreateLeadRequest{
					ClientRequestId: "req-123",
					ClientId:        "client-123",
					ProductType:     leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
					Pan:             "**********",
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					PersonalDetails: &leadsPb.PersonalDetails{
						Name: &common.Name{
							FirstName: "John",
							LastName:  "Doe",
						},
						Dob: &date.Date{
							Year:  1990,
							Month: 5,
							Day:   15,
						},
						EmploymentDetails: &leadsPb.EmploymentDetails{
							EmploymentType: typesV2.EmploymentType_EMPLOYMENT_TYPE_SALARIED,
							MonthlyIncome: &money.Money{
								CurrencyCode: "INR",
								Units:        50000,
								Nanos:        0,
							},
						},
						CurrentAddress: &typesV2.PostalAddress{
							PostalCode: "400001",
						},
					},
					AdditionalDetails: &leadsPb.AdditionalDetails{
						Details: &leadsPb.AdditionalDetails_FiPersonalLoanDetails{
							FiPersonalLoanDetails: &leadsPb.FiPersonalLoanDetails{
								EvaluationType: leadsPb.FiLoansEvaluationType_FI_LOANS_EVALUATION_TYPE_BASIC,
							},
						},
					},
				},
			},
			want:    &leadsPb.CreateLeadResponse{Status: rpc.StatusInternal()},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().GetByClientReqIdAndClientId(gomock.Any(), "req-123", "client-123").Return(nil, epifierrors.ErrRecordNotFound)
				mocks.userClient.EXPECT().GetUsers(gomock.Any(), gomock.Any()).Return(&userPb.GetUsersResponse{Status: rpc.StatusOk(), Users: []*userPb.User{{ActorId: "actor-123"}}}, nil).AnyTimes()
				mocks.loansClient.EXPECT().GetLoanUserStatusV2(gomock.Any(), gomock.Any()).Return(&palPb.GetLoanUserStatusV2Response{Status: rpc.StatusOk(), UserStatus: palPb.UserStatus_USER_STATUS_ELIGIBLE_TO_APPLY}, nil)
				mocks.breClient.EXPECT().GetPreBreEligibilityDetails(gomock.Any(), gomock.Any()).Return(nil, errors.New("some internal error"))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, mocks := newLeadServiceWithMocks(t)

			// Setup mocks for this specific test case
			tt.setupMocks(mocks)

			got, err := service.CreateLead(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Service.CreateLead() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			assert.Equal(t, tt.want.GetStatus().GetCode(), got.GetStatus().GetCode())
		})
	}
}

func TestService_GetActiveLeads(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *leadsPb.GetActiveLeadsRequest
	}

	type testCase struct {
		name       string
		args       args
		want       *leadsPb.GetActiveLeadsResponse
		wantErr    bool
		setupMocks func(mocks *mockedDependencies)
	}

	tests := []testCase{
		{
			name: "should return invalid argument when no identifiers are provided",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.GetActiveLeadsRequest{
					Pan:         "",
					Email:       "",
					PhoneNumber: &common.PhoneNumber{},
				},
			},
			want: &leadsPb.GetActiveLeadsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("pan or email or phone number is mandatory"),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// No mocks needed as validation happens first
			},
		},
		{
			name: "should return internal error when dao.GetUserLeadsByFilter fails",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.GetActiveLeadsRequest{
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				},
			},
			want: &leadsPb.GetActiveLeadsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching active leads data"),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetUserLeadsByFilter(gomock.Any(), gomock.Any(), gomock.Any(), true).
					Return(nil, errors.New("database error"))
			},
		},
		{
			name: "should return record not found when no active leads found",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.GetActiveLeadsRequest{
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				},
			},
			want: &leadsPb.GetActiveLeadsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetUserLeadsByFilter(gomock.Any(), gomock.Any(), gomock.Any(), true).
					Return([]*leadsPb.UserLead{}, nil)
			},
		},
		{
			name: "should return success with active leads organized by product type",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.GetActiveLeadsRequest{
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
					ProductTypes: []leadsPb.ProductType{leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN},
				},
			},
			want: &leadsPb.GetActiveLeadsResponse{
				Status: rpc.StatusOk(),
				ProductTypeToActiveLeadMap: map[int32]*leadsPb.UserLead{
					int32(leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN): {
						Id:           "lead-123",
						MobileNumber: "9876543210",
						ProductType:  leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
						LeadStatus:   leadsPb.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
					},
				},
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetUserLeadsByFilter(gomock.Any(), gomock.Any(), gomock.Any(), true).
					Return([]*leadsPb.UserLead{
						{
							Id:           "lead-123",
							MobileNumber: "9876543210",
							ProductType:  leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
							LeadStatus:   leadsPb.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
						},
					}, nil)
			},
		},
		{
			name: "should mark duplicates as duplicate and keep only the earliest lead per product type",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.GetActiveLeadsRequest{
					PhoneNumber: &common.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 9876543210,
					},
				},
			},
			want: &leadsPb.GetActiveLeadsResponse{
				Status: rpc.StatusOk(),
				ProductTypeToActiveLeadMap: map[int32]*leadsPb.UserLead{
					int32(leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN): {
						Id:           "lead-123",
						MobileNumber: "9876543210",
						ProductType:  leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
						LeadStatus:   leadsPb.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
					},
				},
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Create two leads of the same product type with different timestamps
				firstLead := &leadsPb.UserLead{
					Id:           "lead-123",
					MobileNumber: "9876543210",
					ProductType:  leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
					LeadStatus:   leadsPb.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
					CreatedAt:    timestampPb.New(time.Now().Add(-24 * time.Hour)), // Older lead
				}
				secondLead := &leadsPb.UserLead{
					Id:          "lead-456",
					Email:       "<EMAIL>",
					ProductType: leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
					LeadStatus:  leadsPb.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
					CreatedAt:   timestampPb.New(time.Now()), // Newer lead
				}

				// Return both leads from the DAO
				mocks.leadDao.EXPECT().
					GetUserLeadsByFilter(gomock.Any(), gomock.Any(), gomock.Any(), true).
					Return([]*leadsPb.UserLead{firstLead, secondLead}, nil)

				// Expect update call for the duplicate (newer) lead
				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-456", lead.GetId())
						assert.Equal(t, leadsPb.UserLeadStatus_USER_LEAD_STATUS_DUPLICATE, lead.GetLeadStatus())
						assert.NotNil(t, lead.GetCompletedAt())
						return nil
					})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, mocks := newLeadServiceWithMocks(t)

			// Setup mocks for this specific test case
			tt.setupMocks(mocks)

			got, err := service.GetActiveLeads(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Service.GetActiveLeads() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			assert.Equal(t, tt.want.GetStatus().GetCode(), got.GetStatus().GetCode())

			// For successful responses, check the leads map
			if tt.want.GetStatus().IsSuccess() {
				assert.Len(t, tt.want.GetProductTypeToActiveLeadMap(), len(got.GetProductTypeToActiveLeadMap()))
				for productType, expectedLead := range tt.want.GetProductTypeToActiveLeadMap() {
					actualLead, ok := got.GetProductTypeToActiveLeadMap()[productType]
					assert.True(t, ok)
					assert.Equal(t, expectedLead.GetId(), actualLead.GetId())
					assert.Equal(t, expectedLead.GetProductType(), actualLead.GetProductType())
				}
			}
		})
	}
}

func TestService_SetActorId(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *leadsPb.SetActorIdRequest
	}

	type testCase struct {
		name       string
		args       args
		want       *leadsPb.SetActorIdResponse
		wantErr    bool
		setupMocks func(mocks *mockedDependencies)
	}

	tests := []testCase{
		{
			name: "should return invalid argument when both actor id and email are empty",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123", "lead-456"},
					ActorId: "",
					Email:   "",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("either actor id or email must be provided"),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// No mocks needed as validation happens first
			},
		},
		{
			name: "should return internal error when dao.GetById fails",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123"},
					ActorId: "actor-123",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching lead data"),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(nil, errors.New("database error"))
			},
		},
		{
			name: "should return permission denied when a different actor id is already set",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123"},
					ActorId: "actor-123",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusPermissionDeniedWithDebugMsg("actor id already set for the lead"),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(&leadsPb.UserLead{
						Id:      "lead-123",
						ActorId: "different-actor-456",
					}, nil)
			},
		},
		{
			name: "should return internal error when dao.Update fails during actor ID update",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123"},
					ActorId: "actor-123",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while updating lead data"),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Lead retrieval succeeds
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(&leadsPb.UserLead{
						Id:      "lead-123",
						ActorId: "",
					}, nil)

				// Update fails
				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-123", lead.GetId())
						assert.Equal(t, "actor-123", lead.GetActorId())
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID)
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS)
						return errors.New("database error")
					})
			},
		},
		{
			name: "should return success when actor id is set successfully for a single lead",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123"},
					ActorId: "actor-123",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Lead retrieval succeeds
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(&leadsPb.UserLead{
						Id:      "lead-123",
						ActorId: "",
					}, nil)

				// Update succeeds
				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-123", lead.GetId())
						assert.Equal(t, "actor-123", lead.GetActorId())
						assert.Equal(t, leadsPb.UserLeadStatus_USER_LEAD_STATUS_USER_CREATED, lead.GetLeadStatus())
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID)
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS)
						return nil
					})

			},
		},
		{
			name: "should return success when actor id is set successfully for multiple leads",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123", "lead-456"},
					ActorId: "actor-123",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Lead retrieval succeeds for first lead
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(&leadsPb.UserLead{
						Id:      "lead-123",
						ActorId: "",
					}, nil)

				// Lead retrieval succeeds for second lead
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-456").
					Return(&leadsPb.UserLead{
						Id:      "lead-456",
						ActorId: "",
					}, nil)

				// Update succeeds for first lead
				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-123", lead.GetId())
						assert.Equal(t, "actor-123", lead.GetActorId())
						assert.Equal(t, leadsPb.UserLeadStatus_USER_LEAD_STATUS_USER_CREATED, lead.GetLeadStatus())
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID)
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS)
						return nil
					})

				// Update succeeds for second lead
				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-456", lead.GetId())
						assert.Equal(t, "actor-123", lead.GetActorId())
						assert.Equal(t, leadsPb.UserLeadStatus_USER_LEAD_STATUS_USER_CREATED, lead.GetLeadStatus())
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID)
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS)
						return nil
					})
			},
		},
		{
			name: "should not update actor id when it is already set to the same value, but update email if provided",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123"},
					ActorId: "actor-123",
					Email:   "<EMAIL>",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				// Lead already has the same actor ID
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(&leadsPb.UserLead{
						Id:      "lead-123",
						ActorId: "actor-123",
						Email:   "<EMAIL>",
					}, nil)

				// Update is called for email
				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-123", lead.GetId())
						assert.Equal(t, "actor-123", lead.GetActorId())
						assert.Equal(t, "<EMAIL>", lead.GetEmail())
						assert.NotContains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID)    // ActorId not in mask
						assert.NotContains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS) // LeadStatus not in mask
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EMAIL)
						return nil
					})
			},
		},
		{
			name: "should return internal error when dao.Update fails during email update",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123"},
					Email:   "<EMAIL>",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while updating lead data"),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(&leadsPb.UserLead{
						Id:      "lead-123",
						ActorId: "actor-old",
						Email:   "<EMAIL>",
					}, nil)

				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-123", lead.GetId())
						assert.Equal(t, "<EMAIL>", lead.GetEmail())
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EMAIL)
						return errors.New("update error")
					})
			},
		},
		{
			name: "should set only email when actor id is empty but email is provided",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123"},
					ActorId: "",
					Email:   "<EMAIL>",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(&leadsPb.UserLead{
						Id:      "lead-123",
						ActorId: "existing-actor",
						Email:   "<EMAIL>",
					}, nil)

				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-123", lead.GetId())
						assert.Equal(t, "existing-actor", lead.GetActorId()) // ActorId should remain unchanged
						assert.Equal(t, "<EMAIL>", lead.GetEmail())
						assert.NotContains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID)
						assert.NotContains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS)
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EMAIL)
						return nil
					})
			},
		},
		{
			name: "should set both actor id and email successfully",
			args: args{
				ctx: context.Background(),
				req: &leadsPb.SetActorIdRequest{
					LeadIds: []string{"lead-123"},
					ActorId: "actor-123",
					Email:   "<EMAIL>",
				},
			},
			want: &leadsPb.SetActorIdResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			setupMocks: func(mocks *mockedDependencies) {
				mocks.leadDao.EXPECT().
					GetById(gomock.Any(), "lead-123").
					Return(&leadsPb.UserLead{
						Id:      "lead-123",
						ActorId: "",
						Email:   "<EMAIL>",
					}, nil)

				mocks.leadDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, lead *leadsPb.UserLead, masks []leadsPb.UserLeadFieldMask) error {
						assert.Equal(t, "lead-123", lead.GetId())
						assert.Equal(t, "actor-123", lead.GetActorId())
						assert.Equal(t, "<EMAIL>", lead.GetEmail())
						assert.Equal(t, leadsPb.UserLeadStatus_USER_LEAD_STATUS_USER_CREATED, lead.GetLeadStatus())
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID)
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS)
						assert.Contains(t, masks, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EMAIL)
						return nil
					})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, mocks := newLeadServiceWithMocks(t)

			// Setup mocks for this specific test case
			tt.setupMocks(mocks)

			got, err := service.SetActorId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Service.SetActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			assert.Equal(t, tt.want.GetStatus().GetCode(), got.GetStatus().GetCode())
		})
	}
}
