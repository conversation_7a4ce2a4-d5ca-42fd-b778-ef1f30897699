package leads

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	brePb "github.com/epifi/gamma/api/bre"
	leadsPb "github.com/epifi/gamma/api/leads"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/leads/config"
	"github.com/epifi/gamma/leads/dao"
	"github.com/epifi/gamma/leads/dao/model"
	leadEvents "github.com/epifi/gamma/leads/events"
)

type Service struct {
	leadsPb.UnimplementedUserLeadSvcServer
	userClient  userPb.UsersClient
	loansClient palPb.PreApprovedLoanClient
	leadDao     dao.UserLeadDao
	eventBroker events.Broker
	breClient   brePb.BreClient
}

func NewService(userClient userPb.UsersClient, loansClient palPb.PreApprovedLoanClient, leadDao dao.UserLeadDao, eventBroker events.Broker, breClient brePb.BreClient) *Service {
	return &Service{
		userClient:  userClient,
		loansClient: loansClient,
		leadDao:     leadDao,
		eventBroker: eventBroker,
		breClient:   breClient,
	}
}

func (s *Service) CreateLead(ctx context.Context, req *leadsPb.CreateLeadRequest) (*leadsPb.CreateLeadResponse, error) {
	// check if lead exists with given client req id, if yes return 101 status
	lead, err := s.leadDao.GetByClientReqIdAndClientId(ctx, req.GetClientRequestId(), req.GetClientId())
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error checking if lead exists", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
			return &leadsPb.CreateLeadResponse{Status: rpc.StatusInternal()}, nil
		}
		// Record not found is expected, proceed with lead creation
	} else if lead != nil {
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(ctx, leadEvents.NewLeadIngestionEvent("", false, req.GetClientId(), req.GetClientRequestId(), leadsPb.CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_CLIENT_REQUEST_ID.String()))
		})
		return &leadsPb.CreateLeadResponse{Status: rpc.NewStatusWithoutDebug(uint32(leadsPb.CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_CLIENT_REQUEST_ID), "")}, nil
	}

	// create the lead only if actor id doesn't exist or if exists, user can start product journey
	actorId, err := s.getActorIdIfExists(ctx, req.GetPhoneNumber(), req.GetPan(), req.GetEmail())
	if err != nil {
		logger.Error(ctx, "error getting actor id", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &leadsPb.CreateLeadResponse{Status: rpc.StatusInternal()}, nil
	}

	// if actor exists, check if user can start product journey and return duplicate lead status if not allowed
	if actorId != "" {
		isAllowed, allowErr := s.canStartProductJourney(ctx, req, actorId)
		if allowErr != nil {
			logger.Error(ctx, "error in checking canStartProductJourney", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(allowErr))
			return &leadsPb.CreateLeadResponse{Status: rpc.StatusInternal()}, nil
		}
		if !isAllowed {
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				s.eventBroker.AddToBatch(ctx, leadEvents.NewLeadIngestionEvent(actorId, false, req.GetClientId(), req.GetClientRequestId(), leadsPb.CreateLeadResponse_STATUS_USER_CANNOT_START_PRODUCT_JOURNEY.String()))
			})
			return &leadsPb.CreateLeadResponse{Status: rpc.NewStatusWithoutDebug(uint32(leadsPb.CreateLeadResponse_STATUS_USER_CANNOT_START_PRODUCT_JOURNEY), "")}, nil
		}

	}
	validLenders := make([]palPb.Vendor, 0)

	if req.GetAdditionalDetails().GetFiPersonalLoanDetails().GetEvaluationType() == leadsPb.FiLoansEvaluationType_FI_LOANS_EVALUATION_TYPE_BASIC {
		preBreResp, err := s.breClient.GetPreBreEligibilityDetails(ctx, &brePb.GetPreBreEligibilityDetailsRequest{
			ActorId:   actorId,
			RequestId: req.GetClientRequestId(),
			CustomerDetails: &brePb.CustomerDetails{
				PersonalDetails: &brePb.PersonalDetails{
					Name: req.GetPersonalDetails().GetName(),
					Pan:  req.GetPan(),
					Dob:  req.GetPersonalDetails().GetDob(),
				},
				EmploymentDetails: &brePb.EmploymentDetails{
					EmploymentType: req.GetPersonalDetails().GetEmploymentDetails().GetEmploymentType(),
					MonthlyIncome:  req.GetPersonalDetails().GetEmploymentDetails().GetMonthlyIncome(),
				},
				ResidentialAddress: req.GetPersonalDetails().GetCurrentAddress(),
			},
			IsEtbUser: false,
		})
		if err := epifigrpc.RPCError(preBreResp, err); err != nil {
			logger.Error(ctx, "error in GetPreBreEligibilityDetails", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId), zap.String("client_id", req.GetClientId()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
			return &leadsPb.CreateLeadResponse{Status: rpc.StatusInternal()}, nil
		}

		validLenders = preBreResp.GetDecision().GetValidLenders()
		if len(validLenders) == 0 {
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				s.eventBroker.AddToBatch(ctx, leadEvents.NewLeadIngestionEvent(actorId, false, req.GetClientId(), req.GetClientRequestId(), leadsPb.CreateLeadResponse_STATUS_LEAD_REJECTED.String()))
			})
			return &leadsPb.CreateLeadResponse{Status: rpc.NewStatusWithoutDebug(uint32(leadsPb.CreateLeadResponse_STATUS_LEAD_REJECTED), "")}, nil
		}
	}

	// try to create lead using leadDao, if it returns duplicateEntry error, return 102 status
	leadToCreate := &leadsPb.UserLead{
		ActorId:           actorId,
		ClientRequestId:   req.GetClientRequestId(),
		ProductType:       req.GetProductType(),
		ClientId:          req.GetClientId(),
		Pan:               req.GetPan(),
		Email:             req.GetEmail(),
		PersonalDetails:   req.GetPersonalDetails(),
		AdditionalDetails: req.GetAdditionalDetails(),
		LeadStatus:        leadsPb.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
		ExpiredAt:         timestampPb.New(time.Now().AddDate(0, 0, 7)),
	}
	if actorId != "" {
		leadToCreate.LeadStatus = leadsPb.UserLeadStatus_USER_LEAD_STATUS_USER_CREATED
	}

	// Set mobile number if phone number is provided
	if req.GetPhoneNumber() != nil {
		leadToCreate.MobileNumber = strconv.FormatUint(req.GetPhoneNumber().GetNationalNumber(), 10)
	}

	_, err = s.leadDao.Create(ctx, leadToCreate)
	if err != nil {
		logger.Error(ctx, "error creating lead", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		if errors.Is(err, epifierrors.ErrDuplicateEntry) {
			status := leadsPb.CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS
			activeLeadsRes, activeLeadErr := s.GetActiveLeads(ctx, &leadsPb.GetActiveLeadsRequest{
				Pan:          req.GetPan(),
				PhoneNumber:  req.GetPhoneNumber(),
				Email:        req.GetEmail(),
				ProductTypes: []leadsPb.ProductType{req.GetProductType()},
			})
			if activeLeadErr = epifigrpc.RPCError(activeLeadsRes, activeLeadErr); activeLeadErr != nil {
				logger.Error(ctx, "error in getting active leads", zap.Error(activeLeadErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String("client_id", req.GetClientId()))
				return &leadsPb.CreateLeadResponse{Status: rpc.StatusInternal()}, nil
			}
			if activeLead, ok := activeLeadsRes.GetProductTypeToActiveLeadMap()[int32(req.GetProductType())]; !ok {
				logger.Error(ctx, "duplicate entry error without active leads", zap.Error(activeLeadErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String("client_id", req.GetClientId()))
				return &leadsPb.CreateLeadResponse{Status: rpc.StatusInternal()}, nil
			} else if activeLead.GetClientId() == req.GetClientId() {
				status = leadsPb.CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS_FOR_SAME_CLIENT
			}
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				s.eventBroker.AddToBatch(ctx, leadEvents.NewLeadIngestionEvent(actorId, false, req.GetClientId(), req.GetClientRequestId(), status.String()))
			})
			//nolint:gosec
			return &leadsPb.CreateLeadResponse{Status: rpc.NewStatusWithoutDebug(uint32(status), "")}, nil
		}
		return &leadsPb.CreateLeadResponse{Status: rpc.StatusInternal()}, nil
	}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(ctx, leadEvents.NewLeadIngestionEvent(actorId, true, req.GetClientId(), req.GetClientRequestId(), ""))
	})
	// if the lead is created successfully, and evaluation type in request is BASIC
	if len(validLenders) > 0 {
		uniqueLenderTypes := make(map[leadsPb.LenderType]struct{})
		for _, vendor := range validLenders {
			if lenderType, ok := config.VendorToLenderType[vendor]; ok {
				uniqueLenderTypes[lenderType] = struct{}{}
			}
		}
		lenderTypes := make([]leadsPb.LenderType, 0, len(uniqueLenderTypes))
		for lenderType := range uniqueLenderTypes {
			lenderTypes = append(lenderTypes, lenderType)
		}

		response := &leadsPb.CreateLeadResponse{
			Status: rpc.StatusOk(),
			Data: &leadsPb.CreateLeadResponse_FiLoansLeadResponse{
				FiLoansLeadResponse: &leadsPb.FiLoansLeadResponse{
					AvailableLenderTypes: lenderTypes,
				},
			},
		}
		return response, nil
	}
	// if the lead is created successfully return success response
	return &leadsPb.CreateLeadResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) canStartProductJourney(ctx context.Context, req *leadsPb.CreateLeadRequest, actorId string) (bool, error) {
	if actorId == "" {
		return false, errors.New("actorId is empty")
	}
	if req.GetProductType() != leadsPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN {
		return false, fmt.Errorf("invalid product type: %s", req.GetProductType().String())
	}
	// Actor exists, check loan status
	loanStatusResp, err := s.loansClient.GetLoanUserStatusV2(ctx, &palPb.GetLoanUserStatusV2Request{
		ActorId:        actorId,
		UpdatedAtAfter: timestampPb.New(time.Now().Add(-90 * 24 * time.Hour)),
	})
	if err = epifigrpc.RPCError(loanStatusResp, err); err != nil {
		return false, fmt.Errorf("error getting loan user status: %w", err)
	}

	userStatus := loanStatusResp.GetUserStatus()
	logger.Info(ctx, "loans user status before creating lead", zap.String(logger.STATUS, userStatus.String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.String(logger.ACTOR_ID_V2, actorId))
	// If we get Status others than OFFER_AVAILABLE, ELIGIBLE_TO_APPLY, or ACTIVE_ELIGIBILITY that means user is already in loan journey or rejected in loan journey so we don't allow them to create a lead.
	if userStatus != palPb.UserStatus_USER_STATUS_OFFER_AVAILABLE && userStatus != palPb.UserStatus_USER_STATUS_ELIGIBLE_TO_APPLY && userStatus != palPb.UserStatus_USER_STATUS_ACTIVE_ELIGIBILITY {
		// if none of the above conditions are true, then return 102 status
		return false, nil
	}
	return true, nil
}

func (s *Service) getActorIdIfExists(ctx context.Context, phoneNumber *common.PhoneNumber, pan, email string) (string, error) {
	// Define a helper function to create different types of identifiers and their descriptions
	type identifierInfo struct {
		description string
		create      func() *userPb.GetUsersRequest_GetUsersIdentifier
	}

	// Create a slice of identifiers to try in order
	identifiers := make([]identifierInfo, 0)

	// Only add valid identifiers
	if phoneNumber != nil {
		identifiers = append(identifiers, identifierInfo{
			description: "phone number",
			create: func() *userPb.GetUsersRequest_GetUsersIdentifier {
				return &userPb.GetUsersRequest_GetUsersIdentifier{
					Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_PhoneNumber{
						PhoneNumber: phoneNumber,
					},
				}
			},
		})
	}

	if pan != "" {
		identifiers = append(identifiers, identifierInfo{
			description: "PAN",
			create: func() *userPb.GetUsersRequest_GetUsersIdentifier {
				return &userPb.GetUsersRequest_GetUsersIdentifier{
					Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
						Pan: pan,
					},
				}
			},
		})
	}

	if email != "" {
		identifiers = append(identifiers, identifierInfo{
			description: "email",
			create: func() *userPb.GetUsersRequest_GetUsersIdentifier {
				return &userPb.GetUsersRequest_GetUsersIdentifier{
					Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_EmailId{
						EmailId: email,
					},
				}
			},
		})
	}

	// Try each identifier in sequence
	for _, idInfo := range identifiers {
		identifier := idInfo.create()

		getUsersResp, err := s.userClient.GetUsers(ctx, &userPb.GetUsersRequest{
			Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{identifier},
		})

		if err = epifigrpc.RPCError(getUsersResp, err); err != nil {
			return "", fmt.Errorf("error in getting user by %s: %w", idInfo.description, err)
		}

		if len(getUsersResp.GetUsers()) > 0 {
			if user := getUsersResp.GetUsers()[0]; user != nil && user.GetActorId() != "" {
				return user.GetActorId(), nil
			}
		}
	}

	// No actor ID found with any of the identifiers
	return "", nil
}

func (s *Service) GetActiveLeads(ctx context.Context, req *leadsPb.GetActiveLeadsRequest) (*leadsPb.GetActiveLeadsResponse, error) {
	// --- Input Validation ---
	if req.GetPan() == "" && req.GetEmail() == "" && len(req.GetPhoneNumber().ToStringNationalNumber()) != 10 {
		logger.Error(ctx, "pan or email or phone number is mandatory")
		return &leadsPb.GetActiveLeadsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("pan or email or phone number is mandatory"),
		}, nil
	}

	// --- Fetch Active Leads ---
	activeLeads, err := s.leadDao.GetUserLeadsByFilter(ctx, &model.UserLeadFilter{
		PAN:          req.GetPan(),
		Email:        req.GetEmail(),
		MobileNumber: req.GetPhoneNumber().ToStringNationalNumber(),
	}, req.GetProductTypes(), true)
	if err != nil {
		logger.Error(ctx, "error while fetching active leads from dao", zap.Error(err))
		return &leadsPb.GetActiveLeadsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching active leads data"),
		}, nil
	}

	if len(activeLeads) == 0 {
		return &leadsPb.GetActiveLeadsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	productTypeToLeadsMap := make(map[int32][]*leadsPb.UserLead)
	for _, lead := range activeLeads {
		productTypeValue := int32(lead.GetProductType())
		productTypeToLeadsMap[productTypeValue] = append(productTypeToLeadsMap[productTypeValue], lead)
	}

	// Identify the earliest active lead for each product type and mark others as expired
	// This is being done because we want to maintain only one active lead per product type per actor,
	// and we cannot set the same actor id to multiple active leads per product type
	productTypeToLeadMap := make(map[int32]*leadsPb.UserLead)
	for productType, leads := range productTypeToLeadsMap {
		// Sort leads by CreatedAt timestamp in ascending order
		sort.Slice(leads, func(i, j int) bool {
			return leads[i].GetCreatedAt().AsTime().Before(leads[j].GetCreatedAt().AsTime())
		})

		// The first lead is the earliest, mark the rest as expired
		earliestLead := leads[0]
		for i := 1; i < len(leads); i++ {
			leadToUpdate := leads[i]
			if leadToUpdate.Id == earliestLead.Id {
				continue
			}
			leadToUpdate.LeadStatus = leadsPb.UserLeadStatus_USER_LEAD_STATUS_DUPLICATE
			leadToUpdate.CompletedAt = timestampPb.Now()
			// Update the lead in the database
			err = s.leadDao.Update(ctx, leadToUpdate, []leadsPb.UserLeadFieldMask{
				leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS,
				leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_COMPLETED_AT,
			})
			if err != nil {
				logger.Error(ctx, "error while updating lead status", zap.Error(err))
				return &leadsPb.GetActiveLeadsResponse{
					Status: rpc.StatusInternalWithDebugMsg("error in update duplicate state in DB"),
				}, nil
			}
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				s.eventBroker.AddToBatch(ctx, leadEvents.NewLeadStatusUpdateEvent(leadToUpdate.GetActorId(), leadToUpdate.GetLeadStatus().String(), leadToUpdate.GetClientId(), leadToUpdate.GetClientRequestId()))
			})
		}
		productTypeToLeadMap[productType] = earliestLead
	}

	// --- Construct Response ---
	return &leadsPb.GetActiveLeadsResponse{
		Status:                     rpc.StatusOk(),
		ProductTypeToActiveLeadMap: productTypeToLeadMap,
	}, nil
}

func (s *Service) SetActorId(ctx context.Context, req *leadsPb.SetActorIdRequest) (*leadsPb.SetActorIdResponse, error) {
	if req.GetActorId() == "" && req.GetEmail() == "" {
		return &leadsPb.SetActorIdResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("either actor id or email must be provided"),
		}, nil
	}

	for _, leadId := range req.GetLeadIds() {
		lead, err := s.leadDao.GetById(ctx, leadId)
		if err != nil {
			logger.Error(ctx, "error while fetching lead from dao", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &leadsPb.SetActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching lead data"),
			}, nil
		}

		var updateMask []leadsPb.UserLeadFieldMask

		if req.GetActorId() != "" && req.GetActorId() != lead.GetActorId() {
			if lead.GetActorId() != "" && req.GetActorId() != lead.GetActorId() {
				logger.Error(ctx, "different actor id is already set for the lead", zap.String(logger.ID, lead.GetId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
				return &leadsPb.SetActorIdResponse{
					Status: rpc.StatusPermissionDeniedWithDebugMsg("actor id already set for the lead"),
				}, nil
			}
			lead.ActorId = req.GetActorId()
			lead.LeadStatus = leadsPb.UserLeadStatus_USER_LEAD_STATUS_USER_CREATED
			updateMask = append(updateMask, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS)
		}
		if req.GetEmail() != "" {
			lead.Email = req.GetEmail()
			updateMask = append(updateMask, leadsPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EMAIL)
		}

		updateErr := s.leadDao.Update(ctx, lead, updateMask)
		if updateErr != nil {
			logger.Error(ctx, "error while updating lead", zap.Error(updateErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &leadsPb.SetActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while updating lead data"),
			}, nil
		}
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(ctx, leadEvents.NewLeadStatusUpdateEvent(req.GetActorId(), lead.GetLeadStatus().String(), lead.GetClientId(), lead.GetClientRequestId()))
		})

	}

	return &leadsPb.SetActorIdResponse{
		Status: rpc.StatusOk(),
	}, nil
}
