package billpay

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayenums "github.com/epifi/gamma/api/billpay/enums"
	billpayWfPb "github.com/epifi/gamma/api/billpay/workflow"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	billPayScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/billpay"
	userPb "github.com/epifi/gamma/api/user"
	rechargePb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay/billermanager"
	"github.com/epifi/gamma/billpay/config/genconf"
	"github.com/epifi/gamma/billpay/conversion"
	"github.com/epifi/gamma/billpay/dao"
	"github.com/epifi/gamma/billpay/utils"
	wireTypes "github.com/epifi/gamma/billpay/wire/types"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

var (
	nonTerminalOrderStatuses = []billpayenums.RechargeOrderStatus{
		billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
		billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED,
		billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION,
		billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_EXPIRED,
	}
)

type Service struct {
	billpaypb.UnimplementedBillPayServer
	gconf                 *genconf.Config
	billerManager         billermanager.BillerManager
	rechargeOrderDao      dao.RechargeOrderDao
	rechargeOrderStageDao dao.RechargeOrderStageDao
	rechargeClient        rechargePb.MobileRechargeServiceClient
	userClient            userPb.UsersClient
	celestialClient       celestialPb.CelestialClient
	plansCacheStorage     cache.CacheStorage
}

func NewBillPayService(
	gconf *genconf.Config,
	billerManager billermanager.BillerManager,
	rechargeOrderDao dao.RechargeOrderDao,
	rechargeOrderStageDao dao.RechargeOrderStageDao,
	rechargeClient rechargePb.MobileRechargeServiceClient,
	userClient userPb.UsersClient,
	celestialClient celestialPb.CelestialClient,
	plansCacheStorage wireTypes.PlansCacheStorage,
) *Service {
	return &Service{
		gconf:                 gconf,
		billerManager:         billerManager,
		rechargeOrderDao:      rechargeOrderDao,
		rechargeOrderStageDao: rechargeOrderStageDao,
		rechargeClient:        rechargeClient,
		userClient:            userClient,
		celestialClient:       celestialClient,
		plansCacheStorage:     plansCacheStorage,
	}
}

// FetchAndManageBillers implements the billpay.BillPay service's FetchAndManageBillers RPC method
func (s *Service) FetchAndManageBillers(ctx context.Context, req *billpaypb.FetchAndManageBillersRequest) (*billpaypb.FetchAndManageBillersResponse, error) {
	response, err := s.billerManager.FetchAndManageBillers(ctx, req)
	if err != nil {
		logger.Error(ctx, "Failed to fetch and manage billers", zap.Error(err))
		errorResponse := &billpaypb.FetchAndManageBillersResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}
		return errorResponse, nil
	}

	return response, nil
}

// UpdateBillerStatus implements the billpay.BillPay service's UpdateBillerStatus RPC method
func (s *Service) UpdateBillerStatus(ctx context.Context, req *billpaypb.UpdateBillerStatusRequest) (*billpaypb.UpdateBillerStatusResponse, error) {
	response, err := s.billerManager.UpdateBillerStatus(ctx, req)
	if err != nil {
		logger.Error(ctx, "Failed to update biller status", zap.Error(err))
		errorResponse := &billpaypb.UpdateBillerStatusResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}
		return errorResponse, nil
	}

	return response, nil
}

func (s *Service) FetchRechargeOrders(ctx context.Context, req *billpaypb.FetchRechargeOrdersRequest) (*billpaypb.FetchRechargeOrdersResponse, error) {
	pageToken, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		return &billpaypb.FetchRechargeOrdersResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	orders, pageCtx, err := s.rechargeOrderDao.GetByActorId(ctx, dao.RechargeOrderFieldMasks.All(), req.GetActorId(), pageToken, 30, nil)
	if err != nil {
		logger.Error(ctx, "failed to get recharge orders by actor id", zap.Error(err), zap.String("actor_id", req.GetActorId()))
		return &billpaypb.FetchRechargeOrdersResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &billpaypb.FetchRechargeOrdersResponse{
		Status:              rpcPb.StatusOk(),
		Orders:              orders,
		PageContextResponse: pageCtx,
	}, nil
}

func (s *Service) GetLatestRechargeOrdersForUniqueAccounts(ctx context.Context, req *billpaypb.GetLatestRechargeOrdersForUniqueAccountsRequest) (*billpaypb.GetLatestRechargeOrdersForUniqueAccountsResponse, error) {
	orders, err := s.rechargeOrderDao.GetLatestOrdersForUniqueAccountsByActor(ctx, req.GetActorId(), req.GetAccountType())
	if err != nil {
		logger.Error(ctx, "failed to get latest recharge orders by actor id", zap.Error(err), zap.String("actor_id", req.GetActorId()))
		return &billpaypb.GetLatestRechargeOrdersForUniqueAccountsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &billpaypb.GetLatestRechargeOrdersForUniqueAccountsResponse{
		Status:       rpcPb.StatusOk(),
		LatestOrders: orders,
	}, nil
}

func (s *Service) FetchRechargePlans(ctx context.Context, req *billpaypb.FetchRechargePlansRequest) (*billpaypb.FetchRechargePlansResponse, error) {
	plansData, err := s.getPlansWithCache(ctx, req.GetActorId(), req.GetAccountIdentifier())
	if err != nil {
		logger.Error(ctx, "failed to fetch plans for account", zap.Error(err), zap.String("actor_id", req.GetActorId()))
		return &billpaypb.FetchRechargePlansResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &billpaypb.FetchRechargePlansResponse{
		Status:   rpcPb.StatusOk(),
		Plans:    plansData.GetPlans(),
		Operator: plansData.GetOperator(),
	}, nil
}

func (s *Service) CreateRechargeOrder(ctx context.Context, req *billpaypb.CreateRechargeOrderRequest) (*billpaypb.CreateRechargeOrderResponse, error) {
	// Idempotency check: look for non-terminal orders with same planId
	orders, err := s.rechargeOrderDao.GetByActorIdAndAccountDetails(ctx, req.GetActorId(), req.GetAccountType(), req.GetAccountIdentifier())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		// move on
		break
	case err != nil:
		logger.Error(ctx, "error fetching recharge order details", zap.Error(err))
		return &billpaypb.CreateRechargeOrderResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	for _, o := range orders {
		if o.GetPlanDetails().GetMobileRechargePlanDetails().GetPlanId() == req.GetPlanId() {
			status := o.GetStatus()
			if !lo.Contains(nonTerminalOrderStatuses, status) {
				// Found a non-terminal order for this plan, return its client_request_id
				return &billpaypb.CreateRechargeOrderResponse{
					Status:          rpcPb.StatusOk(),
					ClientRequestId: o.GetClientRequestId(),
				}, nil
			}
		}
	}

	// Get plans from cache or fetch from vendor
	plansData, err := s.getPlansWithCache(ctx, req.GetActorId(), req.GetAccountIdentifier())
	if err != nil {
		logger.Error(ctx, "failed to get plans", zap.Error(err), zap.String("plan_id", req.GetPlanId()))
		return &billpaypb.CreateRechargeOrderResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("Failed to fetch plans"),
		}, nil
	}

	// Find the specific plan by ID
	var planDetails *billpaypb.MobileRechargePlan
	for _, plan := range plansData.GetPlans() {
		if plan.GetPlanId() == req.GetPlanId() {
			planDetails = plan
			break
		}
	}

	if planDetails == nil {
		logger.Error(ctx, "plan not found", zap.String("plan_id", req.GetPlanId()))
		return &billpaypb.CreateRechargeOrderResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("Plan not found"),
		}, nil
	}

	rechargeOrder, err := s.rechargeOrderDao.Create(ctx, &billpaypb.RechargeOrder{
		ActorId:           req.GetActorId(),
		AccountType:       req.GetAccountType(),
		AccountIdentifier: req.GetAccountIdentifier(),
		AccountOperator:   req.GetOperator(),
		AccountDetails:    plansData.GetAccountDetails(),
		PlanDetails: &billpaypb.PlanDetails{
			MobileRechargePlanDetails: planDetails,
		},
		Status:          billpayenums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
		ClientRequestId: uuid.NewString(),
	})
	if err != nil {
		logger.Error(ctx, "failed to create recharge order", zap.Error(err))
		return &billpaypb.CreateRechargeOrderResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// Initiate celestial workflow for recharge processing
	err = s.initiateRechargeWorkflow(ctx, rechargeOrder)
	if err != nil {
		logger.Error(ctx, "failed to initiate recharge workflow", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, rechargeOrder.GetClientRequestId()))
		// Don't fail the request, workflow can be retried
	}

	return &billpaypb.CreateRechargeOrderResponse{
		Status:          rpcPb.StatusOk(),
		ClientRequestId: rechargeOrder.GetClientRequestId(),
	}, nil
}

func (s *Service) GetRechargeOrderStatus(ctx context.Context, req *billpaypb.GetRechargeOrderStatusRequest) (*billpaypb.GetRechargeOrderStatusResponse, error) {

	order, err := s.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), req.GetClientRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get recharge order by client request id", zap.Error(err), zap.String("client_request_id", req.GetClientRequestId()))
		return &billpaypb.GetRechargeOrderStatusResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	queryRes, quErr := s.celestialClient.QueryWorkflow(ctx, &celestialPb.QueryWorkflowRequest{
		Identifier: &celestialPb.QueryWorkflowRequest_ClientReqId{ClientReqId: &workflowPb.ClientReqId{
			Id:     req.GetClientRequestId(),
			Client: workflowPb.Client_PAY,
		}},
		QueryType: utils.RechargePaymentStatusQueryType,
		Ownership: commontypes.Ownership_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(queryRes, quErr); te != nil {
		logger.Error(ctx, "failed to query recharge order", zap.Error(te))
		pollDl := deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_RECHARGE_POLLING_SCREEN, &billPayScreenOptions.RechargePollingScreenOptions{
			ClientRequestId: order.GetClientRequestId(),
			RetryIntervalMs: 500,
		})
		// Fallback: return status/substatus from DB order
		return &billpaypb.GetRechargeOrderStatusResponse{
			Status:         rpcPb.StatusOk(),
			OrderStatus:    order.GetStatus(),
			OrderSubStatus: order.GetSubStatus(),
			NextAction:     pollDl,
		}, nil
	}

	workflowData := &utils.RechargePaymentWorkflowData{}
	unmErr := json.Unmarshal(queryRes.GetQueryResult(), workflowData)
	if unmErr != nil {
		logger.Error(ctx, "failed to unmarshal recharge payment workflow data", zap.Error(unmErr))
		// Fallback: return status/substatus from DB order
		return &billpaypb.GetRechargeOrderStatusResponse{
			Status:         rpcPb.StatusOk(),
			OrderStatus:    order.GetStatus(),
			OrderSubStatus: order.GetSubStatus(),
			NextAction:     nil, // or order.GetNextAction() if available
		}, nil
	}

	return &billpaypb.GetRechargeOrderStatusResponse{
		Status:         rpcPb.StatusOk(),
		OrderStatus:    workflowData.OrderStatus,
		OrderSubStatus: workflowData.OrderSubStatus,
		NextAction:     workflowData.NextAction,
	}, nil
}

// getPlansWithCache is the unified function that checks cache first, then fetches from vendor if needed
func (s *Service) getPlansWithCache(ctx context.Context, actorId, accountIdentifier string) (*billpaypb.RechargePlansData, error) {
	// First try to get plans from cache
	plansData, err := s.getPlansFromCache(ctx, accountIdentifier)
	if err == nil && len(plansData.GetPlans()) > 0 {
		logger.Debug(ctx, "plans found in cache", zap.String("account_identifier", accountIdentifier))
		return plansData, nil
	}

	logger.Info(ctx, "cache miss for plans, fetching from vendor", zap.Error(err), zap.String("account_identifier", accountIdentifier))

	// Cache miss - fetch from vendor
	plansData, err = s.fetchPlansForAccount(ctx, actorId, accountIdentifier)
	if err != nil {
		return plansData, fmt.Errorf("failed to fetch plans from vendor: %w", err)
	}

	// Cache the fetched plans
	if cacheErr := s.cachePlansForAccount(ctx, accountIdentifier, plansData); cacheErr != nil {
		logger.Error(ctx, "failed to cache plans", zap.Error(cacheErr), zap.String("account_identifier", accountIdentifier))
		// Continue without failing the request
	}

	return plansData, nil
}

// fetchPlansForAccount is a shared helper function that fetches plans for a given account
func (s *Service) fetchPlansForAccount(ctx context.Context, actorId, accountIdentifier string) (*billpaypb.RechargePlansData, error) {
	userResp, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get user by actor id: %w", err)
	}

	rechargePhNo, err := commontypes.ParsePhoneNumber(accountIdentifier)
	if err != nil {
		return nil, fmt.Errorf("failed to parse phone number: %w", err)
	}

	rechargePhNo.CountryCode = commontypes.IndiaCountryCode

	opDetailResp, err := s.rechargeClient.FetchOperatorDetails(ctx, &rechargePb.FetchOperatorDetailsRequest{
		Header:               &vgPb.RequestHeader{Vendor: vgPb.Vendor_SETU},
		MobileNumber:         rechargePhNo,
		CustomerMobileNumber: userResp.GetUser().GetProfile().GetPhoneNumber(),
	})

	if te := epifigrpc.RPCError(opDetailResp, err); te != nil {
		return nil, fmt.Errorf("failed to fetch operator details: %w", te)
	}

	accountDetails := &billpaypb.RechargeAccountDetails{
		OperatorDetails: conversion.ConvertVendorGatewayOperatorDetailsToOperatorDetails(opDetailResp.GetData()),
	}
	operator := opDetailResp.GetData().GetCurrentOperator()
	location := opDetailResp.GetData().GetCurrentLocation()

	plansResp, err := s.rechargeClient.FetchPlans(ctx, &rechargePb.FetchPlansRequest{
		Header:       &vgPb.RequestHeader{Vendor: vgPb.Vendor_SETU},
		MobileNumber: rechargePhNo,
		Operator:     operator,
		Location:     location,
	})

	if te := epifigrpc.RPCError(plansResp, err); te != nil {
		return nil, fmt.Errorf("failed to fetch plans: %w", te)
	}

	// Convert vendor gateway plans to billpay plans
	plans := lo.Map(plansResp.GetPlans(), func(item *rechargePb.Plan, index int) *billpaypb.MobileRechargePlan {
		return conversion.ConvertVendorGatewayPlanToRechargePlan(item)
	})

	return &billpaypb.RechargePlansData{
		Plans:          plans,
		AccountDetails: accountDetails,
		Operator:       accountDetails.GetOperatorDetails().GetCurrentOperator(),
	}, nil
}

// initiateRechargeWorkflow initiates the celestial workflow for recharge processing
func (s *Service) initiateRechargeWorkflow(ctx context.Context, rechargeOrder *billpaypb.RechargeOrder) error {
	// Create workflow payload
	payload := &billpayWfPb.RechargePaymentRequest{
		ClientRequestId: rechargeOrder.GetClientRequestId(),
	}

	payloadBytes, err := protojson.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal workflow payload: %w", err)
	}

	initiateWorkflowRes, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: rechargeOrder.GetActorId(),
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(payNs.RechargePayment),
			Payload: payloadBytes,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     rechargeOrder.GetClientRequestId(),
				Client: workflowPb.Client_PAY, // Using PAY client for billpay workflows
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})

	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil {
		return fmt.Errorf("error while initiating workflow for client req id %s: %w", rechargeOrder.GetClientRequestId(), te)
	}

	return nil
}

// cachePlansForAccount stores all plans for an account in cache
func (s *Service) cachePlansForAccount(ctx context.Context, accountIdentifier string, plansData *billpaypb.RechargePlansData) error {
	cacheKey := s.getAccountPlansCacheKey(accountIdentifier)

	planDataBytes, err := proto.Marshal(plansData)
	if err != nil {
		return fmt.Errorf("failed to marshal plans data for caching: %w", err)
	}

	err = s.plansCacheStorage.Set(ctx, cacheKey, string(planDataBytes), s.gconf.CacheConfigs().PlansCacheConfig().CacheTTl())
	if err != nil {
		return fmt.Errorf("failed to cache plans data: %w", err)
	}

	return nil
}

// getPlansFromCache retrieves all plans for an account from cache
func (s *Service) getPlansFromCache(ctx context.Context, accountIdentifier string) (*billpaypb.RechargePlansData, error) {
	cacheKey := s.getAccountPlansCacheKey(accountIdentifier)

	planData, err := s.plansCacheStorage.Get(ctx, cacheKey)
	if err != nil {
		return nil, fmt.Errorf("cache miss: %w", err)
	}

	if planData == "" {
		return nil, fmt.Errorf("no cached plans found for account")
	}

	var rechargePlansData billpaypb.RechargePlansData
	err = proto.Unmarshal([]byte(planData), &rechargePlansData)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal cached plans data", zap.Error(err), zap.String("cache_key", cacheKey))
		return nil, fmt.Errorf("failed to unmarshal cached data: %w", err)
	}

	return &rechargePlansData, nil
}

// getAccountPlansCacheKey generates the cache key for account plans
func (s *Service) getAccountPlansCacheKey(accountIdentifier string) string {
	return s.gconf.CacheConfigs().PlansCacheConfig().Prefix() + "account:" + accountIdentifier
}
