//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/billpay/activity"
	payWorkerGenConf "github.com/epifi/gamma/pay/config/worker/genconf"

	userPb "github.com/epifi/gamma/api/user"
	billpayVgPb "github.com/epifi/gamma/api/vendorgateway/billpay"
	rechargePb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay"
	"github.com/epifi/gamma/billpay/billermanager"
	"github.com/epifi/gamma/billpay/config/genconf"
	"github.com/epifi/gamma/billpay/dao"
	wireTypes "github.com/epifi/gamma/billpay/wire/types"
)

func InitializeService(
	gconf *genconf.Config,
	db types.BillpayPGDB,
	setuClient billpayVgPb.BillPayServiceClient,
	rechargeClient rechargePb.MobileRechargeServiceClient,
	userClient userPb.UsersClient,
	celestialClient celestialPb.CelestialClient,
	billersRedisStore wireTypes.BillersRedisStore,
) *billpay.Service {
	wire.Build(
		types.BillpayPGDBGormDBProvider,
		billpay.NewBillPayService,
		idgen.NewClock,
		idgen.WireSet,
		dao.BillerDaoWireSet,
		dao.CategoryDaoWireSet,
		billermanager.BillerManagerWireSet,
		dao.RechargeOrderDaoWireSet,
		dao.RechargeOrderStageDaoWireSet,
		wireTypes.PlansCacheStorageProvider,
	)
	return &billpay.Service{}
}

func InitializeActivityProcessor(
	gconf *payWorkerGenConf.Config,
	db types.BillpayPGDB,
	orderClient order.OrderServiceClient,
	rechargeVgClient rechargePb.MobileRechargeServiceClient,
	usersClient userPb.UsersClient,
	payClient pay.PayClient) *activity.Processor {
	wire.Build(
		types.BillpayPGDBGormDBProvider,
		idgen.NewClock,
		idgen.WireSet,
		dao.RechargeOrderDaoWireSet,
		dao.RechargeOrderStageDaoWireSet,
		activity.NewProcessor,
	)
	return &activity.Processor{}
}
