package dao

import (
	"google.golang.org/protobuf/types/known/fieldmaskpb"
)

// BillFieldMasks provides functions to get commonly used field masks for Bill entity
type billFieldMasks struct{}

// All returns a field mask with all Bill fields
func (billFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "bill_fetch_request_id", "actor_id", "status", "details",
		"due_date", "created_at", "updated_at", "deleted_at",
	}}
}

// BasicInfo returns a field mask with basic Bill fields
func (billFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "status", "due_date",
	}}
}

// WithoutDetails returns a field mask with all Bill fields except details
func (billFieldMasks) WithoutDetails() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "bill_fetch_request_id", "actor_id", "status",
		"due_date", "created_at", "updated_at", "deleted_at",
	}}
}

// CategoryFieldMasks provides functions to get commonly used field masks for Category entity
type categoryFieldMasks struct{}

// All returns a field mask with all Category fields
func (categoryFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "category_name", "biller_count", "created_at", "updated_at", "deleted_at",
	}}
}

// BasicInfo returns a field mask with basic Category fields
func (categoryFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "category_name",
	}}
}

// BillerFieldMasks provides functions to get commonly used field masks for Biller entity
type billerFieldMasks struct{}

// All returns a field mask with all Biller fields
func (billerFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "category_id", "external_biller_id", "biller_name", "customer_params",
		"status", "details", "created_at", "updated_at", "deleted_at",
	}}
}

// BasicInfo returns a field mask with basic Biller fields
func (billerFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "category_id", "biller_name", "status",
	}}
}

// ActorBillerAccountFieldMasks provides functions to get commonly used field masks for ActorBillerAccount entity
type actorBillerAccountFieldMasks struct{}

// All returns a field mask with all ActorBillerAccount fields
func (actorBillerAccountFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "biller_id", "customer_params", "nickname",
		"created_at", "updated_at", "deleted_at",
	}}
}

// BasicInfo returns a field mask with basic ActorBillerAccount fields
func (actorBillerAccountFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "biller_id", "nickname",
	}}
}

// BillFetchRequestFieldMasks provides functions to get commonly used field masks for BillFetchRequest entity
type billFetchRequestFieldMasks struct{}

// All returns a field mask with all BillFetchRequest fields
func (billFetchRequestFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "actor_biller_account_id", "vendor_ref_id", "details",
		"created_at", "updated_at", "deleted_at",
	}}
}

// BasicInfo returns a field mask with basic BillFetchRequest fields
func (billFetchRequestFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "actor_biller_account_id", "status",
	}}
}

// PaymentFieldMasks provides functions to get commonly used field masks for Payment entity
type paymentFieldMasks struct{}

// All returns a field mask with all Payment fields
func (paymentFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "bills_ref_id", "actor_id", "biller_id", "request_id", "vendor_ref_id",
		"details", "created_at", "updated_at", "deleted_at", "payment_flow",
	}}
}

// BasicInfo returns a field mask with basic Payment fields
func (paymentFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "bills_ref_id", "actor_id", "biller_id", "request_id", "vendor_ref_id", "payment_flow",
	}}
}

// DisputeFieldMasks provides functions to get commonly used field masks for Dispute entity
type disputeFieldMasks struct{}

// All returns a field mask with all Dispute fields
func (disputeFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "payment_id", "vendor_ref_id", "external_dispute_id",
		"external_transaction_id", "dispute_type", "status", "user_description", "remarks",
		"created_at", "updated_at", "deleted_at",
	}}
}

// BasicInfo returns a field mask with basic Dispute fields
func (disputeFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "payment_id", "dispute_type", "status",
	}}
}

// RechargeOrderFieldMasks provides functions to get commonly used field masks for RechargeOrder entity
// Update the field names as per the recharge_order table/model
// Example fields: id, actor_id, biller_id, amount, status, details, created_at, updated_at, deleted_at
// Adjust as per actual model definition

type rechargeOrderFieldMasks struct{}

func (rechargeOrderFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "client_request_id", "account_type", "account_identifier", "account_operator", "account_details", "plan_details", "status", "sub_status", "created_at", "updated_at", "deleted_at", "completed_at",
	}}
}

func (rechargeOrderFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "actor_id", "client_request_id", "status", "sub_status", "plan_details",
	}}
}

func (rechargeOrderFieldMasks) StatusSubStatus() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"status", "sub_status",
	}}
}

// RechargeOrderStageFieldMasks provides functions to get commonly used field masks for RechargeOrderStage entity
// Update the field names as per the recharge_order_stage table/model
// Example fields: id, recharge_order_id, stage, status, created_at, updated_at, deleted_at
// Adjust as per actual model definition

type rechargeOrderStageFieldMasks struct{}

func (rechargeOrderStageFieldMasks) All() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "recharge_order_id", "client_request_id", "stage", "status", "expires_at", "data", "created_at", "updated_at", "deleted_at", "completed_at",
	}}
}

func (rechargeOrderStageFieldMasks) BasicInfo() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"id", "recharge_order_id", "client_request_id", "stage", "status", "data",
	}}
}

func (rechargeOrderStageFieldMasks) ClientRequestId() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"client_request_id",
	}}
}

func (rechargeOrderStageFieldMasks) Status() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"status",
	}}
}

func (rechargeOrderStageFieldMasks) Data() *fieldmaskpb.FieldMask {
	return &fieldmaskpb.FieldMask{Paths: []string{
		"data",
	}}
}

// Create instances of the field mask providers
var (
	BillFieldMasks               = billFieldMasks{}
	CategoryFieldMasks           = categoryFieldMasks{}
	BillerFieldMasks             = billerFieldMasks{}
	ActorBillerAccountFieldMasks = actorBillerAccountFieldMasks{}
	BillFetchRequestFieldMasks   = billFetchRequestFieldMasks{}
	PaymentFieldMasks            = paymentFieldMasks{}
	DisputeFieldMasks            = disputeFieldMasks{}
	RechargeOrderFieldMasks      = rechargeOrderFieldMasks{}
	RechargeOrderStageFieldMasks = rechargeOrderStageFieldMasks{}
)
