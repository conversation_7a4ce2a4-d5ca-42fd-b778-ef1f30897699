package conversion

import (
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
	vgRechargePb "github.com/epifi/gamma/api/vendorgateway/recharge"
)

// ConvertRechargeOperatorToVendorGatewayOperator converts recharge Operator to vendorgateway Operator
func ConvertRechargeOperatorToVendorGatewayOperator(rechargeOperator billpayEnums.Operator) vgRechargePb.Operator {
	switch rechargeOperator {
	case billpayEnums.Operator_OPERATOR_AIRTEL:
		return vgRechargePb.Operator_OPERATOR_AIRTEL
	case billpayEnums.Operator_OPERATOR_JIO:
		return vgRechargePb.Operator_OPERATOR_JIO
	case billpayEnums.Operator_OPERATOR_VI:
		return vgRechargePb.Operator_OPERATOR_VI
	case billpayEnums.Operator_OPERATOR_BSNL:
		return vgRechargePb.Operator_OPERATOR_BSNL
	case billpayEnums.Operator_OPERATOR_MTNL:
		return vgRechargePb.Operator_OPERATOR_MTNL
	default:
		return vgRechargePb.Operator_OPERATOR_UNSPECIFIED
	}
}

// ConvertVendorGatewayOperatorToRechargeOperator converts vendorgateway Operator to recharge Operator
func ConvertVendorGatewayOperatorToRechargeOperator(vgOperator vgRechargePb.Operator) billpayEnums.Operator {
	switch vgOperator {
	case vgRechargePb.Operator_OPERATOR_AIRTEL:
		return billpayEnums.Operator_OPERATOR_AIRTEL
	case vgRechargePb.Operator_OPERATOR_JIO:
		return billpayEnums.Operator_OPERATOR_JIO
	case vgRechargePb.Operator_OPERATOR_VI:
		return billpayEnums.Operator_OPERATOR_VI
	case vgRechargePb.Operator_OPERATOR_BSNL:
		return billpayEnums.Operator_OPERATOR_BSNL
	case vgRechargePb.Operator_OPERATOR_MTNL:
		return billpayEnums.Operator_OPERATOR_MTNL
	default:
		return billpayEnums.Operator_OPERATOR_UNSPECIFIED
	}
}

// ConvertVendorGatewayStatusToRechargeOrderStatus converts vendorgateway RechargeStatus to recharge RechargeOrderStatus
func ConvertVendorGatewayStatusToRechargeOrderStatus(vgStatus vgRechargePb.RechargeStatus) billpayEnums.RechargeOrderStatus {
	switch vgStatus {
	case vgRechargePb.RechargeStatus_RECHARGE_STATUS_SUCCESSFUL:
		return billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS
	case vgRechargePb.RechargeStatus_RECHARGE_STATUS_PROCESSING:
		return billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS
	case vgRechargePb.RechargeStatus_RECHARGE_STATUS_FAILURE:
		return billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED
	default:
		return billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_UNSPECIFIED
	}
}

// ConvertRechargeOrderStatusToVendorGatewayStatus converts recharge RechargeOrderStatus to vendorgateway RechargeStatus
func ConvertRechargeOrderStatusToVendorGatewayStatus(rechargeStatus billpayEnums.RechargeOrderStatus) vgRechargePb.RechargeStatus {
	switch rechargeStatus {
	case billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS:
		return vgRechargePb.RechargeStatus_RECHARGE_STATUS_SUCCESSFUL
	case billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS:
		return vgRechargePb.RechargeStatus_RECHARGE_STATUS_PROCESSING
	case billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED:
		return vgRechargePb.RechargeStatus_RECHARGE_STATUS_FAILURE
	default:
		return vgRechargePb.RechargeStatus_RECHARGE_STATUS_UNSPECIFIED
	}
}
func ConvertVendorGatewayPaymentModeToBillPayPaymentMode(vgPaymentMode vgRechargePb.PaymentMode) billpayEnums.PaymentMode {
	switch vgPaymentMode {
	case vgRechargePb.PaymentMode_PAYMENT_MODE_UNSPECIFIED:
		return billpayEnums.PaymentMode_PAYMENT_MODE_UNSPECIFIED
	case vgRechargePb.PaymentMode_PAYMENT_MODE_INTERNET_BANKING:
		return billpayEnums.PaymentMode_PAYMENT_MODE_INTERNET_BANKING
	case vgRechargePb.PaymentMode_PAYMENT_MODE_UPI:
		return billpayEnums.PaymentMode_PAYMENT_MODE_UPI
	case vgRechargePb.PaymentMode_PAYMENT_MODE_DEBIT_CARD:
		return billpayEnums.PaymentMode_PAYMENT_MODE_DEBIT_CARD
	case vgRechargePb.PaymentMode_PAYMENT_MODE_CREDIT_CARD:
		return billpayEnums.PaymentMode_PAYMENT_MODE_CREDIT_CARD
	case vgRechargePb.PaymentMode_PAYMENT_MODE_WALLET:
		return billpayEnums.PaymentMode_PAYMENT_MODE_WALLET
	default:
		return billpayEnums.PaymentMode_PAYMENT_MODE_UNSPECIFIED
	}
}

func ConvertBillPayPaymentModeToVendorGatewayPaymentMode(paymentMode billpayEnums.PaymentMode) vgRechargePb.PaymentMode {
	switch paymentMode {
	case billpayEnums.PaymentMode_PAYMENT_MODE_UNSPECIFIED:
		return vgRechargePb.PaymentMode_PAYMENT_MODE_UNSPECIFIED
	case billpayEnums.PaymentMode_PAYMENT_MODE_INTERNET_BANKING:
		return vgRechargePb.PaymentMode_PAYMENT_MODE_INTERNET_BANKING
	case billpayEnums.PaymentMode_PAYMENT_MODE_UPI:
		return vgRechargePb.PaymentMode_PAYMENT_MODE_UPI
	case billpayEnums.PaymentMode_PAYMENT_MODE_DEBIT_CARD:
		return vgRechargePb.PaymentMode_PAYMENT_MODE_DEBIT_CARD
	case billpayEnums.PaymentMode_PAYMENT_MODE_CREDIT_CARD:
		return vgRechargePb.PaymentMode_PAYMENT_MODE_CREDIT_CARD
	case billpayEnums.PaymentMode_PAYMENT_MODE_WALLET:
		return vgRechargePb.PaymentMode_PAYMENT_MODE_WALLET
	default:
		return vgRechargePb.PaymentMode_PAYMENT_MODE_UNSPECIFIED
	}
}

// ConvertVendorGatewayPlanToRechargePlan converts vendorgateway Plan to recharge MobileRechargePlan
func ConvertVendorGatewayPlanToRechargePlan(vgPlan *vgRechargePb.Plan) *billpayPb.MobileRechargePlan {
	if vgPlan == nil {
		return nil
	}

	return &billpayPb.MobileRechargePlan{
		Talktime:        vgPlan.GetTalktime(),
		PlanName:        vgPlan.GetPlanName(),
		Amount:          vgPlan.GetAmount(), // google.type.Money is shared
		Validity:        vgPlan.GetValidity(),
		PlanDescription: vgPlan.GetPlanDescription(),
		ServiceProvider: vgPlan.GetServiceProvider(),
	}
}

// ConvertRechargePlanToVendorGatewayPlan converts recharge MobileRechargePlan to vendorgateway Plan
func ConvertRechargePlanToVendorGatewayPlan(rechargePlan *billpayPb.MobileRechargePlan) *vgRechargePb.Plan {
	if rechargePlan == nil {
		return nil
	}

	return &vgRechargePb.Plan{
		Talktime:        rechargePlan.GetTalktime(),
		PlanName:        rechargePlan.GetPlanName(),
		Amount:          rechargePlan.GetAmount(), // google.type.Money is shared
		Validity:        rechargePlan.GetValidity(),
		PlanDescription: rechargePlan.GetPlanDescription(),
		ServiceProvider: rechargePlan.GetServiceProvider(),
		Operator:        ConvertRechargeOperatorToVendorGatewayOperator(rechargePlan.GetOperator()),
	}
}

// ConvertVendorGatewayOperatorDetailsToOperatorDetails converts vendorgateway OperatorDetails to billpaypb OperatorDetails
func ConvertVendorGatewayOperatorDetailsToOperatorDetails(vgPlan *vgRechargePb.OperatorDetails) *billpayPb.OperatorDetails {
	if vgPlan == nil {
		return nil
	}

	return &billpayPb.OperatorDetails{
		MobileNumber:     vgPlan.GetMobileNumber(),
		CurrentOperator:  ConvertVendorGatewayOperatorToRechargeOperator(vgPlan.GetCurrentOperator()),
		CurrentLocation:  vgPlan.GetCurrentLocation(),
		PreviousOperator: ConvertVendorGatewayOperatorToRechargeOperator(vgPlan.GetPreviousOperator()),
		PreviousLocation: vgPlan.GetPreviousLocation(),
		Ported:           vgPlan.GetPorted(),
	}
}

func ConvertRechargeOperatorDetailsToVendorGatewayOperatorDetails(rechargeOperatorDetails *billpayPb.OperatorDetails) *vgRechargePb.OperatorDetails {
	if rechargeOperatorDetails == nil {
		return nil
	}

	return &vgRechargePb.OperatorDetails{
		MobileNumber:     rechargeOperatorDetails.GetMobileNumber(),
		CurrentOperator:  ConvertRechargeOperatorToVendorGatewayOperator(rechargeOperatorDetails.GetCurrentOperator()),
		CurrentLocation:  rechargeOperatorDetails.GetCurrentLocation(),
		PreviousOperator: ConvertRechargeOperatorToVendorGatewayOperator(rechargeOperatorDetails.GetPreviousOperator()),
		PreviousLocation: rechargeOperatorDetails.GetPreviousLocation(),
		Ported:           rechargeOperatorDetails.GetPorted(),
	}
}

// ConvertVendorGatewayRechargeDetailsToRechargeDetails converts vendorgateway RechargeDetails to recharge RechargeDetails
func ConvertVendorGatewayRechargeDetailsToRechargeDetails(vgDetails *vgRechargePb.RechargeDetails) *billpayPb.RechargeDetails {
	if vgDetails == nil {
		return nil
	}

	return &billpayPb.RechargeDetails{
		MobileNumber:     vgDetails.GetMobileNumber(),
		Provider:         ConvertVendorGatewayOperatorToRechargeOperator(vgDetails.GetProvider()),
		IsPostpaid:       vgDetails.GetIsPostpaid(),
		IsSpecial:        vgDetails.GetIsSpecial(),
		Amount:           vgDetails.GetAmount(), // google.type.Money is shared
		TransactionRefId: vgDetails.GetTransactionRefId(),
		Status:           ConvertVendorGatewayStatusToRechargeOrderStatus(vgDetails.GetStatus()),
		OperatorRefId:    vgDetails.GetOperatorRefId(),
	}
}

// ConvertRechargeDetailsToVendorGatewayRechargeDetails converts recharge RechargeDetails to vendorgateway RechargeDetails
func ConvertRechargeDetailsToVendorGatewayRechargeDetails(rechargeDetails *billpayPb.RechargeDetails) *vgRechargePb.RechargeDetails {
	if rechargeDetails == nil {
		return nil
	}

	return &vgRechargePb.RechargeDetails{
		// Note: MobileNumber in vendorgateway uses PhoneNumber proto which requires more complex conversion
		// This would need to be handled in the calling code based on specific requirements
		Provider:         ConvertRechargeOperatorToVendorGatewayOperator(rechargeDetails.GetProvider()),
		IsPostpaid:       rechargeDetails.GetIsPostpaid(),
		IsSpecial:        rechargeDetails.GetIsSpecial(),
		Amount:           rechargeDetails.GetAmount(), // google.type.Money is shared
		TransactionRefId: rechargeDetails.GetTransactionRefId(),
		Status:           ConvertRechargeOrderStatusToVendorGatewayStatus(rechargeDetails.GetStatus()),
		OperatorRefId:    rechargeDetails.GetOperatorRefId(),
	}
}
