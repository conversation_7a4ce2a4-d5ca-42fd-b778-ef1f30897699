package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/billpay/conversion"
	"github.com/epifi/gamma/billpay/dao"
)

// EnquireRechargePaymentStatus activity polls for payment status using order service.
// This activity checks if payment is in terminal state by calling the GetOrder API
// with the payment stage client request ID and analyzing the order status.
//
// Error handling:
// - Returns ErrPermanent if order record not found (payment initiation may still be in progress)
// - Returns ErrTransient for order service errors or non-terminal order states
// - The workflow uses this activity's response to determine payment success/failure
func (p *Processor) EnquireRechargePaymentStatus(ctx context.Context, req *billpayActPb.EnquireRechargePaymentStatusRequest) (*billpayActPb.EnquireRechargePaymentStatusResponse, error) {
	lg := activity.GetLogger(ctx)
	paymentStageClientRequestId := req.GetPaymentStageClientRequestId()

	// Validate input parameters
	if paymentStageClientRequestId == "" {
		lg.Error("payment stage client request id is required")
		return nil, errors.Wrap(epifierrors.ErrTransient, "payment stage client request id is required")
	}

	// Get order details using the payment stage client request id
	getOrderResp, err := p.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{
			ClientReqId: paymentStageClientRequestId,
		},
	})

	switch {
	case err != nil:
		lg.Error("error while calling GetOrder() for fetching payment status",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error checking order status")

	case getOrderResp.GetStatus().IsRecordNotFound():
		lg.Info("order record not found, order creation not initiated",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "order record not found, payment initiation may still be in progress")

	case !getOrderResp.GetStatus().IsSuccess():
		lg.Error("error response from GetOrder()",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.Any("status", getOrderResp.GetStatus()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error response from order service")
	}

	order := getOrderResp.GetOrder()
	orderStatus := order.GetStatus()

	lg.Debug("order status retrieved",
		zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
		zap.String("order_id", order.GetId()),
		zap.String("order_status", orderStatus.String()))

	err = p.updateRechargeOrderStageWithOrderId(ctx, paymentStageClientRequestId, order)
	if err != nil {
		lg.Error("failed to update recharge order stage with order id",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to update recharge order stage")
	}

	// Check if order is in terminal state
	switch orderStatus {
	case orderPb.OrderStatus_PAID:
		// Payment successful
		lg.Info("payment successful",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.String("order_status", orderStatus.String()))

		return &billpayActPb.EnquireRechargePaymentStatusResponse{
			OrderId: order.GetId(),
		}, nil

	case orderPb.OrderStatus_PAYMENT_FAILED:
		// Payment failed - return permanent error
		lg.Info("payment failed",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.String("order_status", orderStatus.String()))

		return nil, errors.Wrap(epifierrors.ErrPermanent, "payment failed")

	default:
		// Payment still in progress - return transient error to retry
		lg.Info("payment still in progress",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.String("order_status", orderStatus.String()))

		return nil, errors.Wrap(epifierrors.ErrTransient, "payment still in progress")
	}
}

// updateRechargeOrderStageWithOrderId updates the recharge order stage data column with order details
func (p *Processor) updateRechargeOrderStageWithOrderId(ctx context.Context, paymentStageClientRequestId string, order *orderPb.Order) error {
	lg := activity.GetLogger(ctx)

	// Get the recharge order stage by client request id
	stage, err := p.rechargeOrderStageDao.GetByClientRequestId(ctx, dao.RechargeOrderStageFieldMasks.BasicInfo(), paymentStageClientRequestId)
	if err != nil {
		return errors.Wrap(err, "failed to get recharge order stage")
	}

	paymentMode, err := p.getPaymentMode(ctx, order)
	if err != nil {
		return errors.Wrap(err, "failed to get payment mode")
	}

	// nolint:staticcheck
	shouldUpdate := false
	if stage.GetData().GetPoolAccountPaymentDetails().GetOrderId() != order.GetId() ||
		stage.GetData().GetPoolAccountPaymentDetails().GetPaymentMode() != paymentMode {
		shouldUpdate = true
	}

	// Check if data is already updated (idempotent behavior)
	if !shouldUpdate {
		lg.Info("recharge order stage data already populated",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()))
		return nil
	}

	// Create pool account payment details with order id
	poolAccountPaymentDetails := &billpayPb.RechargePoolAccountPaymentStageData{
		OrderId:          order.GetId(),
		PaymentMode:      paymentMode,
		PaymentTimestamp: order.GetCreatedAt(),
	}

	// Create the stage data with pool account payment details
	stage.Data = &billpayPb.RechargeStageData{
		Data: &billpayPb.RechargeStageData_PoolAccountPaymentDetails{
			PoolAccountPaymentDetails: poolAccountPaymentDetails,
		},
	}

	err = p.rechargeOrderStageDao.Update(ctx, dao.RechargeOrderStageFieldMasks.Data(), stage)
	if err != nil {
		return errors.Wrap(err, "failed to update recharge order stage data")
	}

	return nil
}

func (p *Processor) getPaymentMode(ctx context.Context, order *orderPb.Order) (enums.PaymentMode, error) {
	orderWithTxnResp, orderWithTxnErr := p.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{
		OrderId: order.GetId(),
	})
	if te := epifigrpc.RPCError(orderWithTxnResp, orderWithTxnErr); te != nil {
		return enums.PaymentMode_PAYMENT_MODE_UNSPECIFIED, fmt.Errorf("failed to get order with transactions: %w", te)
	}

	transactions := orderWithTxnResp.GetOrderWithTransactions().GetTransactions()
	if len(transactions) == 0 {
		return enums.PaymentMode_PAYMENT_MODE_UNSPECIFIED, fmt.Errorf("no transactions found for order %s", order.GetId())
	}

	paymentMode, err := conversion.GetPaymentModeFromPaymentProtocol(transactions[0].GetPaymentProtocol())
	if err != nil {
		return enums.PaymentMode_PAYMENT_MODE_UNSPECIFIED, err
	}

	return paymentMode, nil
}
