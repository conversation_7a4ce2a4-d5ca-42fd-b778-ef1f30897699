package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay/conversion"
	"github.com/epifi/gamma/billpay/dao"
)

// EnquireRechargeStatusWithVendor activity enquires about the status of a previously initiated recharge transaction with the vendor.
// This activity takes the fulfilment stage client_request_id as input, validates the stage,
// fetches recharge order details, and calls the vendor API to check recharge status.
//
// Error handling:
// - Returns ErrPermanent only if the vendor indicates a failure or reversal of the recharge
//   - For Permanent failures, the workflow marks the Fulfilment stage as FAILED and proceeds with refund
//
// - Returns ErrTransient for all other cases (network issues, temporary failures, validation errors)
//   - After retries are exhausted, the workflow marks the Fulfilment stage as MANUAL_INTERVENTION for debugging
func (p *Processor) EnquireRechargeStatusWithVendor(ctx context.Context, req *billpayActPb.EnquireRechargeStatusWithVendorRequest) (*billpayActPb.EnquireRechargeStatusWithVendorResponse, error) {
	lg := activity.GetLogger(ctx)
	fulfilmentStageClientRequestId := req.GetFulfilmentStageClientRequestId()

	// Validate input parameters
	if fulfilmentStageClientRequestId == "" {
		lg.Error("fulfilment stage client request id is required")
		return nil, errors.Wrap(epifierrors.ErrTransient, "fulfilment stage client request id is required")
	}

	// Get the fulfilment stage by client request id
	stage, err := p.rechargeOrderStageDao.GetByClientRequestId(ctx, dao.RechargeOrderStageFieldMasks.All(), fulfilmentStageClientRequestId)
	if err != nil {
		lg.Error("failed to get recharge order stage by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get recharge order stage")
	}

	// Verify this is a fulfilment stage
	if stage.GetStage() != enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT {
		lg.Error("stage is not a fulfilment stage",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("stage", stage.GetStage().String()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "stage is not a fulfilment stage")
	}

	// Get the recharge order to extract necessary details
	rechargeOrder, err := p.rechargeOrderDao.GetById(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), stage.GetRechargeOrderId())
	if err != nil {
		lg.Error("failed to get recharge order by id",
			zap.String("recharge_order_id", stage.GetRechargeOrderId()),
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get recharge order")
	}

	// append actor-id to ctx
	ctx = epificontext.CtxWithActorId(ctx, rechargeOrder.GetActorId())

	// Check if we have initiate recharge trace id (required for enquiry)
	fulfillmentDetails := stage.GetData().GetFulfillmentDetails()
	if fulfillmentDetails.GetInitiateRechargeTraceId() == "" {
		lg.Error("initiate recharge trace id is missing, cannot enquire status",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId))
		return nil, errors.Wrap(epifierrors.ErrTransient, "initiate recharge trace id is missing, cannot enquire status")
	}

	// Call the vendor gateway to enquire recharge status
	resp, err := p.rechargeClient.EnquireRechargeStatus(ctx, &rechargeVgPb.EnquireRechargeStatusRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: vendorgateway.Vendor_SETU,
		},
		PaymentRefId: fulfilmentStageClientRequestId,
	})
	if err != nil {
		lg.Error("failed to call enquire recharge status vendor API",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to call enquire recharge status vendor API")
	}

	// Handle the response based on status
	err = p.handleEnquireRechargeStatusResponse(ctx, resp, stage, fulfilmentStageClientRequestId)
	if err != nil {
		return nil, err
	}

	return &billpayActPb.EnquireRechargeStatusWithVendorResponse{}, nil
}

// handleEnquireRechargeStatusResponse handles the vendor gateway response and updates stage data accordingly
func (p *Processor) handleEnquireRechargeStatusResponse(ctx context.Context, resp *rechargeVgPb.EnquireRechargeStatusResponse, stage *billpayPb.RechargeOrderStage, fulfilmentStageClientRequestId string) error {
	lg := activity.GetLogger(ctx)
	// Check the RPC status first
	switch {
	case resp.GetStatus().IsSuccess():
		// Success case - check the recharge status in the data

		// Check if we have data
		if resp.GetData() == nil {
			lg.Error("enquire recharge status response data is missing",
				zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId))
			return errors.Wrap(epifierrors.ErrTransient, "enquire recharge status response data is missing")
		}

		// Handle different recharge statuses
		rechargeStatus := resp.GetData().GetStatus()
		switch rechargeStatus {
		case rechargeVgPb.RechargeStatus_RECHARGE_STATUS_SUCCESSFUL:
			// Success - update stage data and return no error
			lg.Info("recharge status is successful",
				zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId))
			return p.updateFulfilmentStageDataWithEnquiry(ctx, stage, resp, fulfilmentStageClientRequestId)

		case rechargeVgPb.RechargeStatus_RECHARGE_STATUS_PROCESSING:
			// Processing - return transient error so we retry later
			lg.Info("recharge status is still processing",
				zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId))
			// Update stage data first to record the enquiry
			err := p.updateFulfilmentStageDataWithEnquiry(ctx, stage, resp, fulfilmentStageClientRequestId)
			if err != nil {
				return err
			}
			return errors.Wrap(epifierrors.ErrTransient, "recharge is still processing")

		case rechargeVgPb.RechargeStatus_RECHARGE_STATUS_FAILURE:
			// Failure - update stage data and return permanent failure to proceed with refund
			lg.Error("recharge status is failure, will proceed with refund",
				zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId))
			err := p.updateFulfilmentStageDataWithEnquiry(ctx, stage, resp, fulfilmentStageClientRequestId)
			if err != nil {
				return err
			}
			return errors.Wrap(epifierrors.ErrPermanent, "recharge failed at vendor")

		case rechargeVgPb.RechargeStatus_RECHARGE_STATUS_REVERSED:
			// Reversed - update stage data and return permanent failure to proceed with refund
			lg.Error("recharge status is reversed, will proceed with refund",
				zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId))
			err := p.updateFulfilmentStageDataWithEnquiry(ctx, stage, resp, fulfilmentStageClientRequestId)
			if err != nil {
				return err
			}
			return errors.Wrap(epifierrors.ErrPermanent, "recharge was reversed by vendor")

		default:
			// Unknown recharge status - treat as transient failure
			lg.Error("unknown recharge status received from vendor",
				zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
				zap.String("recharge_status", rechargeStatus.String()))
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unknown recharge status: %s", rechargeStatus.String()))
		}

	case resp.GetStatus().IsInvalidArgument():
		// Invalid argument - treat as transient failure as per requirements
		lg.Error("invalid argument for enquire recharge status",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("status_debug_msg", resp.GetStatus().GetDebugMessage()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("invalid argument: %s", resp.GetStatus().GetDebugMessage()))

	case resp.GetStatus().IsInternal() || resp.GetStatus().IsUnknown():
		// Internal server error or unknown status - transient failure
		lg.Error("internal server error or unknown status from vendor",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("status_debug_msg", resp.GetStatus().GetDebugMessage()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("vendor error: %s", resp.GetStatus().GetDebugMessage()))

	default:
		// Unknown status code - treat as transient failure
		lg.Error("unknown status code from vendor",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.Uint32("status_code", resp.GetStatus().GetCode()),
			zap.String("status_debug_msg", resp.GetStatus().GetDebugMessage()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unknown vendor status: %s", resp.GetStatus().GetDebugMessage()))
	}
}

// updateFulfilmentStageDataWithEnquiry updates the fulfilment stage data with enquiry details
func (p *Processor) updateFulfilmentStageDataWithEnquiry(ctx context.Context, stage *billpayPb.RechargeOrderStage, resp *rechargeVgPb.EnquireRechargeStatusResponse, fulfilmentStageClientRequestId string) error {
	lg := activity.GetLogger(ctx)

	// Get existing fulfillment details
	existingFulfillmentDetails := stage.GetData().GetFulfillmentDetails()
	if existingFulfillmentDetails == nil {
		lg.Error("existing fulfillment details are missing",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId))
		return errors.Wrap(epifierrors.ErrTransient, "existing fulfillment details are missing")
	}

	// Determine which recharge details to use
	rechargeDetailsToUse := existingFulfillmentDetails.GetRechargeDetails()
	enquiryRechargeDetails := conversion.ConvertVendorGatewayRechargeDetailsToRechargeDetails(resp.GetData())

	// Compare existing recharge details with enquiry response details
	// Only update if they differ
	if enquiryRechargeDetails != nil && !p.areRechargeDetailsEqual(existingFulfillmentDetails.GetRechargeDetails(), enquiryRechargeDetails) {
		lg.Info("recharge details differ, using enquiry response details",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("enquire_trace_id", resp.GetTraceId()))
		rechargeDetailsToUse = enquiryRechargeDetails
	} else if enquiryRechargeDetails != nil {
		lg.Info("recharge details are same, keeping existing details",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("enquire_trace_id", resp.GetTraceId()))
	}

	// Create updated fulfillment details
	updatedFulfillmentDetails := &billpayPb.RechargeFulfillmentStageData{
		RechargeDetails:              rechargeDetailsToUse,
		InitiateRechargeRefId:        existingFulfillmentDetails.GetInitiateRechargeRefId(),
		InitiateRechargeTraceId:      existingFulfillmentDetails.GetInitiateRechargeTraceId(),
		EnquireRechargeStatusTraceId: resp.GetTraceId(),
	}

	// Create the stage data with updated fulfillment details
	stageData := &billpayPb.RechargeStageData{
		Data: &billpayPb.RechargeStageData_FulfillmentDetails{
			FulfillmentDetails: updatedFulfillmentDetails,
		},
	}

	// Update the stage data
	stage.Data = stageData

	err := p.rechargeOrderStageDao.Update(ctx, dao.RechargeOrderStageFieldMasks.Data(), stage)
	if err != nil {
		lg.Error("failed to update recharge order stage data with enquiry details",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("enquire_trace_id", resp.GetTraceId()),
			zap.Error(err))
		return errors.Wrap(epifierrors.ErrTransient, "failed to update recharge order stage data")
	}

	return nil
}

// areRechargeDetailsEqual compares two RechargeDetails objects for equality
func (p *Processor) areRechargeDetailsEqual(existing, enquiry *billpayPb.RechargeDetails) bool {
	// If both are nil, they are equal
	if existing == nil && enquiry == nil {
		return true
	}

	// If one is nil and the other is not, they are not equal
	if existing == nil || enquiry == nil {
		return false
	}

	// Compare all relevant fields
	if existing.GetMobileNumber().ToString() != enquiry.GetMobileNumber().ToString() {
		return false
	}

	if existing.GetProvider() != enquiry.GetProvider() {
		return false
	}

	if existing.GetIsPostpaid() != enquiry.GetIsPostpaid() {
		return false
	}

	if existing.GetIsSpecial() != enquiry.GetIsSpecial() {
		return false
	}

	if isEqual, compErr := moneyPkg.IsEqual(existing.GetAmount(), enquiry.GetAmount()); compErr != nil || !isEqual {
		return false
	}

	if existing.GetTransactionRefId() != enquiry.GetTransactionRefId() {
		return false
	}

	if existing.GetStatus() != enquiry.GetStatus() {
		return false
	}

	if existing.GetOperatorRefId() != enquiry.GetOperatorRefId() {
		return false
	}

	return true
}
