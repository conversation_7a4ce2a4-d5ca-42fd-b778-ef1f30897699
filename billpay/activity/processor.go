package activity

import (
	idGen "github.com/epifi/be-common/pkg/idgen"
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
	userPb "github.com/epifi/gamma/api/user"
	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay/dao"
	payWorkerGenConf "github.com/epifi/gamma/pay/config/worker/genconf"
)

type Processor struct {
	gconf                 *payWorkerGenConf.Config
	rechargeOrderDao      dao.RechargeOrderDao
	rechargeOrderStageDao dao.RechargeOrderStageDao
	orderClient           orderPb.OrderServiceClient
	rechargeClient        rechargeVgPb.MobileRechargeServiceClient
	userClient            userPb.UsersClient
	payClient             payPb.PayClient
	idGen                 idGen.IdGenerator
}

func NewProcessor(
	gconf *payWorkerGenConf.Config,
	rechargeOrderDao dao.RechargeOrderDao,
	rechargeOrderStageDao dao.RechargeOrderStageDao,
	orderClient orderPb.OrderServiceClient,
	rechargeClient rechargeVgPb.MobileRechargeServiceClient,
	userClient userPb.UsersClient,
	payClient payPb.PayClient,
	idGen idGen.IdGenerator,
) *Processor {
	return &Processor{
		gconf:                 gconf,
		rechargeOrderDao:      rechargeOrderDao,
		rechargeOrderStageDao: rechargeOrderStageDao,
		orderClient:           orderClient,
		rechargeClient:        rechargeClient,
		userClient:            userClient,
		payClient:             payClient,
		idGen:                 idGen,
	}
}
