package catalog_file_processor

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/slack-go/slack"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	mf "github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/investment/mutualfund/metrics"
)

type DailyCatalogFileProcessor struct {
	s3Client    s3.S3Client
	mfDao       mf.MutualFundDao
	fileType    string
	conf        *genConf.Config
	slackClient *slack.Client
}

func NewDailyCatalogFileProcessor(s3Client s3.S3Client, mfDao mf.MutualFundDao, fileType string,
	conf *genConf.Config, slackClient *slack.Client) *DailyCatalogFileProcessor {
	return &DailyCatalogFileProcessor{
		s3Client:    s3Client,
		mfDao:       mfDao,
		fileType:    fileType,
		conf:        conf,
		slackClient: slackClient,
	}
}

const IND = "IND"
const MAX_PAGE_SIZE = 300

// nolint:dupl
func (d *DailyCatalogFileProcessor) ProcessCatalogFile(ctx context.Context, filePath string) error {
	logger.Debug(ctx, "File path in DailyCatalogFileProcessor", zap.String("filePath", filePath))
	// read the file from s3
	readRes, err := d.s3Client.Read(ctx, filePath)
	if err != nil {
		return errors.Wrap(err, "error while reading file from s3")
	}

	reader := csv.NewReader(bytes.NewReader(readRes))
	reader.Comma = ','
	reader.FieldsPerRecord = -1 // to avoid check for same number of cols in all rows
	feedRes, err := reader.ReadAll()
	failedUpdates := make([]string, 0)
	if err != nil {
		return errors.Wrap(err, "error while reading feed file")
	}
	catalogFileUpdates := parseMutualFundCatalogFile(ctx, feedRes)
	mstarToMutualFundMap, err := d.getMutualFundForGivenMstarId(ctx, catalogFileUpdates)
	if err != nil {
		return errors.Wrap(err, "error while getting mstar to mutual fund map")
	}
	dailyNavUpdateFailedFundIds := make([]string, 0)
	for _, catlogFile := range catalogFileUpdates {
		if lo.Contains(d.conf.BlackListedMorningStarIdsForUpdatingCatalog().ToStringArray(), catlogFile.MStarID) {
			continue
		}
		mfPObj, failedFundId, parseToMfObjErr := d.parseFileObjToMFObject(ctx, catlogFile)
		if failedFundId != "" {
			dailyNavUpdateFailedFundIds = append(dailyNavUpdateFailedFundIds, failedFundId)
		}
		if parseToMfObjErr != nil {
			logger.Error(ctx, "error while parsing file to mutual fund", zap.Error(parseToMfObjErr),
				zap.String("file_type", "daily_catalog_update_file"), zap.String(logger.VENDOR_ID, catlogFile.MStarID))
			failedUpdates = append(failedUpdates, catlogFile.MStarID)
			metrics.RecordCatalogUpdateFeedFileProcessingFailure(d.fileType)
			continue
		}
		if mstarToMutualFundMap[catlogFile.MStarID] == nil {
			logger.Error(ctx, fmt.Sprintf("unable to find catalog mstar_id in to mstarIdToMf map : %s", catlogFile.MStarID))
			failedUpdates = append(failedUpdates, catlogFile.MStarID)
			continue
		}
		updateFieldMask := d.getUpdateFieldMask(mstarToMutualFundMap[catlogFile.MStarID])
		_, mfUpdateErr := d.mfDao.UpdateByMStarId(ctx, catlogFile.MStarID, mfPObj, updateFieldMask)
		if mfUpdateErr != nil {
			logger.Error(ctx, "error while updating mutual fund from file", zap.Error(mfUpdateErr), zap.String(logger.VENDOR_ID, catlogFile.MStarID),
				zap.String("file_type", "daily_catalog_update_file"), zap.Any(logger.MUTUAL_FUND, mfPObj))
			failedUpdates = append(failedUpdates, catlogFile.MStarID)
			metrics.RecordCatalogUpdateFeedFileProcessingFailure(d.fileType)
			continue
		}
		logger.Info(ctx, "successfully updated mutualfund", zap.String(logger.VENDOR_ID, catlogFile.MStarID))
	}
	if len(failedUpdates) > 0 {
		logger.Error(ctx, fmt.Sprintf("failed to update %d mutual fund entries in daily feed file", len(failedUpdates)),
			zap.Strings("failed_mstar_ids", failedUpdates))
	}
	d.sendFailedMfUpdateSlackAlert(ctx, dailyNavUpdateFailedFundIds)
	return nil
}

func (d *DailyCatalogFileProcessor) getUpdateFieldMask(mutualFund *mfPb.MutualFund) []mfPb.MutualFundFieldMask {
	// if field map corresponding value is true then that field will be consider during update
	fieldToUpdateDuringSync := map[mfPb.MutualFundFieldMask]bool{
		mfPb.MutualFundFieldMask_NAV:                    true,
		mfPb.MutualFundFieldMask_DAILY_NAV_CHANGE:       true,
		mfPb.MutualFundFieldMask_RETURNS:                true,
		mfPb.MutualFundFieldMask_BENCHMARK_DETAILS:      true,
		mfPb.MutualFundFieldMask_FUND_INVESTMENT_STATUS: true,
	}
	for _, ignoreFieldMask := range mutualFund.GetFieldsExemptFromSync().GetFieldMasks() {
		_, ok := fieldToUpdateDuringSync[ignoreFieldMask]
		if ok {
			fieldToUpdateDuringSync[ignoreFieldMask] = false
		}
	}
	updateFieldMask := []mfPb.MutualFundFieldMask{}
	for fieldmask, shouldConsider := range fieldToUpdateDuringSync {
		if shouldConsider {
			updateFieldMask = append(updateFieldMask, fieldmask)
		}
	}
	return updateFieldMask
}

func (d *DailyCatalogFileProcessor) getMutualFundForGivenMstarId(ctx context.Context, catalogFileUpdates []*DailyCatalogFile) (map[string]*mfPb.MutualFund, error) {
	mstarIds := make([]string, 0)
	for _, catalogFile := range catalogFileUpdates {
		if lo.Contains(d.conf.BlackListedMorningStarIdsForUpdatingCatalog().ToStringArray(), catalogFile.MStarID) {
			continue
		}
		// safe check: some time vendor might send invalid format
		if err := catalogFile.Validate(); err != nil {
			return nil, fmt.Errorf("failed to validate daily catalog file: %w", err)
		}
		mstarIds = append(mstarIds, catalogFile.MStarID)
	}
	mstarIdToMfMap := make(map[string]*mfPb.MutualFund)
	for i := 0; i < len(mstarIds); i += MAX_PAGE_SIZE {
		end := i + MAX_PAGE_SIZE
		if end > len(mstarIds) {
			end = len(mstarIds)
		}
		mutualfunds, err := d.mfDao.GetByMStarIds(ctx, mstarIds[i:end], []mfPb.MutualFundFieldMask{mfPb.MutualFundFieldMask_FIELDS_EXEMPT_FROM_SYNC, mfPb.MutualFundFieldMask_VENDOR_METADATA})
		if err != nil {
			return nil, errors.Wrap(err, "error while getting mutual fund using mstarIds")
		}
		for _, mutualfund := range mutualfunds {
			mstarIdToMfMap[mutualfund.GetVendorMetadata().GetMorningstarData().GetMstarId()] = mutualfund
		}
	}
	return mstarIdToMfMap, nil
}

// parse fields from file to MutualFund obj
func (d *DailyCatalogFileProcessor) parseFileObjToMFObject(ctx context.Context, file *DailyCatalogFile) (*mfPb.MutualFund, string, error) {
	var failedFundId string
	if err := file.Validate(); err != nil {
		return nil, failedFundId, fmt.Errorf("failed to validate daily catalog file: %w", err)
	}
	var mfRes = &mfPb.MutualFund{}
	mfRes.IsinNumber = file.ISIN
	// parse
	navFromFile := file.DayEndNAV
	if navFromFile != "" {
		nav, navErr := ParseToMoney(navFromFile)
		if navErr != nil {
			return nil, failedFundId, navErr
		}
		mfRes.Nav = nav
	}

	if file.NAVChange != "" {
		dNav, dNavErr := strconv.ParseFloat(file.NAVChange, 64)
		if dNavErr != nil {
			failedFundId = file.MStarID
			return nil, failedFundId, dNavErr
		}
		mfRes.DailyNavChange = float32(dNav)
	} else {
		// if mutual fund is available, send slack alert
		// this is to remove funds which are not ingested in db
		_, err := d.mfDao.GetByMStarId(ctx, file.MStarID)
		if err == nil {
			failedFundId = file.MStarID
		}
	}

	avgReturn, avgRetErr := parseavgReturnFromFile(file)
	if avgRetErr != nil {
		return nil, failedFundId, avgRetErr
	}
	mfRes.Returns = avgReturn

	benchmarkDetais, benchmarkErr := parseBenchmarkDetails(file)
	if benchmarkErr != nil {
		return nil, failedFundId, benchmarkErr
	}
	mfRes.BenchmarkDetails = benchmarkDetais
	// if current isin doesnt contains in ignore investment status update list
	// then only update investment status
	if !lo.Contains(d.conf.ISINInvestmentStatusDailyUpdateShouldBeIgnored().ToStringArray(), file.ISIN) {
		fundInvestmentStatus, invStatusParsingErr := parseFundInvestmentStatus(file)
		if invStatusParsingErr != nil {
			return nil, failedFundId, invStatusParsingErr
		}
		mfRes.FundInvestmentStatus = fundInvestmentStatus
	}

	return mfRes, failedFundId, nil
}

func parseFundInvestmentStatus(file *DailyCatalogFile) (mfPb.FundInvestmentStatus, error) {
	if strings.TrimSpace(file.ClosedToInvestors_Country) == IND {
		if len(file.ClosedToInvestors_ClosedToNewDate) != 0 {
			_, err := time.Parse(TIME_LAYOUT, file.ClosedToInvestors_ClosedToNewDate)
			if err != nil {
				return mfPb.FundInvestmentStatus_FundInvestmentStatus_FUND_INVESTMENT_STATUS_UNSPECIFIED,
					fmt.Errorf("unable to parse ClosedToNewDate ,%v", file.ClosedToInvestors_ClosedToNewDate)
			}
			return mfPb.FundInvestmentStatus_AVAILABLE_ONLY_FOR_EXISTING_INVESTORS, nil
		}
		if len(file.ClosedToInvestors_ClosedToAllDate) != 0 {
			_, err := time.Parse(TIME_LAYOUT, file.ClosedToInvestors_ClosedToAllDate)
			if err != nil {
				return mfPb.FundInvestmentStatus_FundInvestmentStatus_FUND_INVESTMENT_STATUS_UNSPECIFIED,
					fmt.Errorf("unable to parse ClosedToAllDate ,%v", file.ClosedToInvestors_ClosedToAllDate)
			}
			return mfPb.FundInvestmentStatus_UNAVAILABLE_FOR_INVESTMENT, nil
		}
	}
	return mfPb.FundInvestmentStatus_AVAILABLE_FOR_INVESTMENT, nil
}

// parse fields from file to BenchmarkDetails
//
//nolint:dupl
func parseBenchmarkDetails(file *DailyCatalogFile) (*mfPb.BenchmarkDetails, error) {
	benchmarkDetails := &mfPb.BenchmarkDetails{}

	if file.ProspectusPrimaryIndexReturn1Yr != "" {
		benchmark1yeRet, benchmark1yeRetErr := strconv.ParseFloat(file.ProspectusPrimaryIndexReturn1Yr, 64)
		if benchmark1yeRetErr != nil {
			return nil, benchmark1yeRetErr
		}
		benchmarkDetails.BenchmarkReturnOneYear = float32(benchmark1yeRet)
	}

	if file.ProspectusPrimaryIndexReturn3Yr != "" {
		benchmark3yeRet, benchmark3yeRetErr := strconv.ParseFloat(file.ProspectusPrimaryIndexReturn3Yr, 64)
		if benchmark3yeRetErr != nil {
			return nil, benchmark3yeRetErr
		}
		benchmarkDetails.BenchmarkReturnThreeYear = float32(benchmark3yeRet)
	}

	if file.ProspectusPrimaryIndexReturn5Yr != "" {
		benchmark5yeRet, benchmark5yeRetErr := strconv.ParseFloat(file.ProspectusPrimaryIndexReturn5Yr, 64)
		if benchmark5yeRetErr != nil {
			return nil, benchmark5yeRetErr
		}
		benchmarkDetails.BenchmarkReturnFiveYear = float32(benchmark5yeRet)
	}

	if file.ProspectusPrimaryIndexReturnSinceInception != "" {
		benchmarkMaxRet, benchmarkMaxRetErr := strconv.ParseFloat(file.ProspectusPrimaryIndexReturnSinceInception, 64)
		if benchmarkMaxRetErr != nil {
			return nil, benchmarkMaxRetErr
		}
		benchmarkDetails.BenchmarkReturnMaxPeriod = float32(benchmarkMaxRet)
	}

	return benchmarkDetails, nil
}

// parse fields from file to avgReturns
//
//nolint:dupl
func parseavgReturnFromFile(file *DailyCatalogFile) (*mfPb.Returns, error) {
	avgReturns := &mfPb.Returns{}

	if file.Return1Yr != "" {
		avg1yeRet, avg1yeRetErr := strconv.ParseFloat(file.Return1Yr, 64)
		if avg1yeRetErr != nil {
			return nil, avg1yeRetErr
		}
		avgReturns.AvgFundReturnOneYear = float32(avg1yeRet)
	}

	if file.Return3Yr != "" {
		avg3yeRet, avg3yeRetErr := strconv.ParseFloat(file.Return3Yr, 64)
		if avg3yeRetErr != nil {
			return nil, avg3yeRetErr
		}
		avgReturns.AvgFundReturnThreeYear = float32(avg3yeRet)
	}

	if file.Return5Yr != "" {
		avg5yeRet, avg5yeRetErr := strconv.ParseFloat(file.Return5Yr, 64)
		if avg5yeRetErr != nil {
			return nil, avg5yeRetErr
		}
		avgReturns.AvgFundReturnFiveYear = float32(avg5yeRet)
	}

	if file.Return1Mth != "" {
		avg1mthRet, avg1mthRetErr := strconv.ParseFloat(file.Return1Mth, 64)
		if avg1mthRetErr != nil {
			return nil, avg1mthRetErr
		}
		avgReturns.AvgFundReturnOneMonth = float32(avg1mthRet)
	}

	if file.Return6Mth != "" {
		avg6mthRet, avg6mthRetErr := strconv.ParseFloat(file.Return6Mth, 64)
		if avg6mthRetErr != nil {
			return nil, avg6mthRetErr
		}
		avgReturns.AvgFundReturnSixMonth = float32(avg6mthRet)
	}

	if file.ReturnSinceInception != "" {
		avgMaxRet, avgMaxRetErr := strconv.ParseFloat(file.ReturnSinceInception, 64)
		if avgMaxRetErr != nil {
			return nil, avgMaxRetErr
		}
		avgReturns.AvgReturnMaxPeriod = float32(avgMaxRet)
	}

	return avgReturns, nil
}

// ParseToMoney converts a given string in amount to *moneyPb.Money
func ParseToMoney(amount string) (*moneyPb.Money, error) {
	amt, err := money.ParseString(amount, "INR")
	if err != nil {
		return nil, err
	}
	return amt, nil
}

// convert CSV rows into list of DailyCatalogFile structs
// nolint:dupl
func parseMutualFundCatalogFile(ctx context.Context, feedRes [][]string) []*DailyCatalogFile {
	var dailyCatalogUpdates []*DailyCatalogFile
	keyFields := make(map[int]string)
	for row, feed := range feedRes {
		catalogFile := DailyCatalogFile{}
		if row == 0 {
			logger.Info(ctx, fmt.Sprintf("num of columns in csv: %d", len(feed)))
			for keyIdx, value := range feed {
				if strings.TrimSpace(value) == "" {
					continue
				}
				if reflect.ValueOf(&catalogFile).Elem().FieldByName(value) == (reflect.Value{}) {
					logger.Error(ctx, fmt.Sprintf("error finding column name in struct: %s", value))
					continue
				}
				keyFields[keyIdx] = value
			}
			logger.Info(ctx, fmt.Sprintf("num of matching columns b/w struct and csv: %d", len(keyFields)))
			continue // ignore row with header
		}
		for keyIdx, value := range feed {
			keyName, keyOk := keyFields[keyIdx]
			if !keyOk {
				continue
			}
			// setting the value as string, parsing to apt data type will be done during sending out request for updates.
			reflect.ValueOf(&catalogFile).Elem().FieldByName(keyName).SetString(strings.TrimSpace(value))
		}
		logger.Debug(ctx, "value of struct", zap.Any("struct", catalogFile))
		dailyCatalogUpdates = append(dailyCatalogUpdates, &catalogFile)
	}
	return dailyCatalogUpdates
}

func (d *DailyCatalogFileProcessor) sendFailedMfUpdateSlackAlert(ctx context.Context, failedFundIDs []string) {
	var pretext, text, color string
	var err error

	if len(failedFundIDs) == 0 {
		pretext = "✅ Daily Nav Change Update Completed Successfully"
		text = "All funds were successfully updated."
		color = "good"
	} else {
		pretext = fmt.Sprintf("⚠️ Daily Nav Change Alert: %d Fund(s) Failed", len(failedFundIDs))
		text = fmt.Sprintf("The following mutual funds failed to update:\n• %s", strings.Join(failedFundIDs, "\n• "))
		color = "danger"
	}

	if d.slackClient != nil {
		_, _, err = d.slackClient.PostMessage(
			"C04S8KRHHD5",
			slack.MsgOptionAttachments(slack.Attachment{
				Pretext:    pretext,
				Text:       text,
				Color:      color,
				MarkdownIn: []string{"text"},
			}),
		)
	}

	if err != nil {
		// Skipping error as slack message is not mandatory
		logger.Error(ctx, "failed to send slack alert", zap.Error(err))
	}
}
