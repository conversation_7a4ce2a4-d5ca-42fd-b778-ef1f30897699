Application:
  Environment: "prod"
  Name: "pal"
  Namespace: "prod-pre-approved-loan"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FeatureEngineeringDb:
  StatementTimeout: 60s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 50
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/feature_engineering_dev_user"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FederalPgDb:
  DbType: "PGDB"
  AppName: "preapprovedloan"
  StatementTimeout: 10s
  Name: "loans_federal"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/loans_federal_dev_user"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FederalDb:
  Username: "federal_dev_user"
  Password: ""
  Name: "federal"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.federal_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.federal_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_federal_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LIQUILOANS_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_liquiloans_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  IDFC_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_idfc_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  FIFTYFIN_LAMF:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_fiftyfin"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_fiftyfin_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  MONEYVIEW_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 10s
    Name: "loans_moneyview"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_moneyview_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_ABFL:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_abfl"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_abfl_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_LENDEN:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_lenden"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_lenden_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_stock_guardian_lsp"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_stock_guardian_lsp_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "preapprovedloan-script"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  CONNECTED_ACCOUNT_WEALTH:
    DbType: "PGDB"
    AppName: "connectedaccount"
    StatementTimeout: 10s
    Name: "connected_account"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 20
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/connected-account"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLog: false


DbConfigMap:
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "federal_dev_user"
    Password: ""
    Name: "federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.federal_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.federal_dev_user.key"
    MaxOpenConn: 1
    MaxIdleConn: 1
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LIQUILOANS_PL:
    DBType: "CRDB"
    Username: "pl_liquiloans_dev_user"
    Password: ""
    Name: "pl_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_liquiloans_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_liquiloans_dev_user.key"
    MaxOpenConn: 1
    MaxIdleConn: 1
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  IDFC_PL:
    DBType: "CRDB"
    Username: "pl_idfc_dev_user"
    Password: ""
    Name: "pl_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_idfc_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_idfc_dev_user.key"
    MaxOpenConn: 1
    MaxIdleConn: 1
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FIFTYFIN_LAMF:
    DBType: "CRDB"
    Username: "loans_fiftyfin_crdb_dev_user"
    Password: ""
    Name: "loans_fiftyfin_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.loans_fiftyfin_crdb_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.loans_fiftyfin_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pre-approved-loan-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

EpifiPgdb:
  DbType: "PGDB"
  AppName: "vendormapping"
  StatementTimeout: 2m
  Name: "vendormapping"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/vendormapping"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
  S3:
    EligibleUsersBucketName: "epifi-prod-preapprovedloan"
    PreApprovedLoanBucketName: "epifi-prod-preapprovedloan"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    # Experian
    EpifiExperianPgpKeySet: "prod/pgp/epifi-experian-key-set"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"
    LentraSecrets: "prod/vendorgateway/lentra-secrets"
    PreApprovedLoanSecrets: "prod/vendorgateway/lending-preapprovedloans-secrets"
    EpiFiFederalClientSslCert: "prod/vg-vgpci/tls-client-cert-for-federal-2024"
    EpiFiFederalClientSslKey: "prod/vg-vgpci/tls-client-priv-key-for-federal-2024"

PgdbMigrationFlag: true

RawBucketScienapticBreDataFilePath: "vendor/bre_scienaptic/bre_data/%s/%s-.csv"

RawDataDevBucketName: "epifi-raw"

CreditReportFlattenPublisher:
  QueueName: "prod-lending-credit-report-flattening-queue"

PalTemporalNamespace: "prod-pre-approved-loan"

ITRFileConf:
  ITRS3Bucket: "epifi-prod-itr-intimation"
  S3PrefixPath: "ITR_INT_PDF/"
  OutputS3Bucket: "epifi-prod-preapprovedloan-liquiloans-download"
  OutputS3PfxPath: "prod-preapprovedloan-liqlns-d/ITR-Docs/"

RedisStore:
  IsSecureRedis: true
  Options:
    Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/cards/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"

CredgenicsNACHPaymentsPostingConf:
  PreApprovedLoanBucketName: "epifi-prod-preapprovedloan"

CreditReportPurgeConfig:
  SoftDeletionMinAge: "4152h" # 173 days
  HardDeletionMinDuration: "1m"
  # This configuration ensures deletion occurs 20 days before the consent expires, providing a buffer in case the job fails and also ensuring that the 15 days backup will be under the consent period.
  CibilSoftDeletionMinAge: "-480h"
  CibilHardDeletionMinDuration: "1m"

DSAIdToEmailMap:
  loantap:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  mymoneymantra:
    - "<EMAIL>"
    - "<EMAIL>"
  mymoneymantrab2c:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  cashkuber:
    - "<EMAIL>"
    - "<EMAIL>"
  keshvacredit:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_cpadvisor_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_universaldsa_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dsconsultants_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_pnsassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_samayfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_dineshkokadiya_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vaishalikokadiya_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_chitracapital_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_atlantagroup_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_astarsolutions_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_ananyaenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_sathyamassociates_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mdfcfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_androfinancesolution_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_blrfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_mmfincorp_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_manikantaenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_quickindiainsuranceandloanservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_fastxloans_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_amgothenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_upliftadvisors_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_planetmoney_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_finance4u_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_rvfinserv_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_atharvamarketingandfinancialservices_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_bhorshafintech_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_whitepearlfinance_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_loancubedsa_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_omkarenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_amaninternational_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_shyamsundaryaligar_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_chaitanyarealty_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_vivekenterprises_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  dsa_saleemkhan_PL:
    - "<EMAIL>"
    - "<EMAIL>"
  fintifi:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  switchmyloan:
    - "<EMAIL>"
    - "<EMAIL>"
