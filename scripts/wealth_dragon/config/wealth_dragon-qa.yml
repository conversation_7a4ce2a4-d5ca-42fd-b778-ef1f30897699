Application:
  Environment: "qa"

Aws:
  Region: "ap-south-1"
  S3:
    BaseBucketName: "epifi-qa-dev-users-test"
    OutputPath: "wealth-dragon/trading-account-details"

BrokerFirmAccountDetailsForForeignRemittance:
  OutwardRemittanceAccount: "OutwardFirmAccountId"

USStocksAlpacaDb:
  DbType: "PGDB"
  AppName: "usstocks"
  StatementTimeout: 1s
  Username: "usstocks_alpaca_dev_user"
  Password: ""
  Name: "usstocks_alpaca"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "qa/rds/epifimetis/usstocks_alpaca_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiWealthDb:
  DbType: "CRDB"
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

StocksDb:
  DBType: "PGDB"
  AppName: "stocks"
  StatementTimeout: 5m
  EnableDebug: true
  Name: "stocks"
  SSLMode: "disable"
  SecretName: "qa/rds/epifimetis/stocks_dev_user"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true


StocksRefreshCatalogPublisher:
  QueueName: "qa-stocks-catalog-refresh-queue"

SecuritiesHistoricalPricePublisher:
  QueueName: "qa-securities-historical-price-queue"

AddNewSecuritiesPublisher:
  QueueName: "qa-securities-catalog-addition-queue"

CaNewDataFetchPublisher:
  QueueName: "qa-insights-ca-data-new-data-fetch-event-queue"

Secrets:
  Ids:
    SlackBotOauthToken: "qa/ift/slack-bot-oauth-token"

PgdbMigrationConf:
  UsePgdb: true
  PgdbConnAlias: "usstocks_alpaca_pgdb"

USStocksRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 0
