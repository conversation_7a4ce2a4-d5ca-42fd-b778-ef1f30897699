Application:
  Environment: "prod"

Aws:
  Region: "ap-south-1"
  S3:
    BaseBucketName: "epifi-prod-onboarding"
    OutputPath: "trading-account-details"

BrokerFirmAccountDetailsForForeignRemittance:
  OutwardRemittanceAccount: "4f53eb9f-b13d-3748-912b-a14b6fa4ed9e"

USStocksAlpacaDb:
  DbType: "PGDB"
  AppName: "usstocks"
  StatementTimeout: 10s
  Username: "usstocks_alpaca_dev_user"
  Password: ""
  Name: "usstocks_alpaca"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  SecretName: "prod/rds/epifimetis/usstocks_alpaca_dev_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

EpifiWealthDb:
  DbType: "CRDB"
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

StocksDb:
  DBType: "PGDB"
  AppName: "stocks"
  StatementTimeout: 10s
  EnableDebug: false
  Name: "stocks"
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/stocks_dev_user"
  GormV2:
    LogLevelGormV2: "ERROR"
    UseInsecureLog: false

StocksRefreshCatalogPublisher:
  QueueName: "prod-stocks-catalog-refresh-queue"

SecuritiesHistoricalPricePublisher:
  QueueName: "prod-securities-historical-price-queue"

AddNewSecuritiesPublisher:
  QueueName: "prod-securities-catalog-addition-queue"

CaNewDataFetchPublisher:
  QueueName: "prod-insights-ca-data-new-data-fetch-event-queue"

Secrets:
  Ids:
    SlackBotOauthToken: "prod/ift/slack-bot-oauth-token"

PgdbMigrationConf:
  UsePgdb: true
  PgdbConnAlias: "usstocks_alpaca_pgdb"

USStocksRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-11355.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:11355"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/wealth/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
