Servers:
  "atlas":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    ServersToStart:
      ServerTypes:
        - "custom"
      ServerStartFunc:
        PkgPath: "github.com/epifi/gamma/atlas/servergenhook"
        MethodName: "StartAtlasServer"
    ServiceGroups:
      - ServiceGroup: "atlas"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "NewEdgeActorRequestTracingServerInterceptor"

  "card":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: true
    UnaryClientInterceptors:
      CardCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewCardRequestClientInterceptor"
      CreditCardVgClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/firefly/interceptor/servergen_wire"
          MethodName: "NewVgTenantClientInterceptor"
      CreditCardClientWithInterceptorsToVendorGatewayPCIServer:
        - PkgPath: "github.com/epifi/gamma/firefly/interceptor/servergen_wire"
          MethodName: "NewVgTenantClientInterceptor"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddRudderEventsUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/firefly/interceptor/servergen_wire"
        MethodName: "NewUnaryCreditCardRequestValidatorInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/card/hook"
        MethodName: "BeforeCardServerStart"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    ServiceGroups:
      - ServiceGroup: "card"
      - ServiceGroup: "creditlimitestimator"
      - ServiceGroup: "firefly"

  "central-growth":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    UnaryClientInterceptors:
      CaCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewConnectedAccountRequestClientInterceptor"
      SavingsCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewSavingsRequestClientInterceptor"
      InAppTargetedCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewSavingsRequestClientInterceptor"
      DocsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewSavingsRequestClientInterceptor"
      ESignClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewSavingsRequestClientInterceptor"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "RateLimitServerInterceptorV2WithDefaultKeyGen"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/central-growth/hook"
        MethodName: "InitCentralGrowthServer"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    ServiceGroups:
      - ServiceGroup: "connectedaccount"
      - ServiceGroup: "connected_account_securities"
        ConfPkgPath: "github.com/epifi/gamma/connectedaccount/securities/config"
      - ServiceGroup: "tiering"
      - ServiceGroup: "salaryprogram"
      - ServiceGroup: "salaryestimation"
      - ServiceGroup: "datasharing"
      - ServiceGroup: "savings"

  "cx":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: true
    ServersToStart:
      ServerTypes:
        - "grpc-web"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/cx/interceptor/servergen_wire"
        MethodName: "NewRateLimitInterceptor"
      - PkgPath: "github.com/epifi/gamma/cx/interceptor/servergen_wire"
        MethodName: "NewUnaryAuthInterceptor"
      - PkgPath: "github.com/epifi/gamma/cx/interceptor/servergen_wire"
        MethodName: "NewTicketValidationInterceptor"
      - PkgPath: "github.com/epifi/gamma/cx/interceptor/servergen_wire"
        MethodName: "NewEnricherInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    GRPCStreamInterceptors:
      - PkgPath: "github.com/epifi/gamma/cx/interceptor/servergen_wire"
        MethodName: "AuthStreamInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/cx/hook"
        MethodName: "InitCxServer"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/cx/hook"
        MethodName: "InitCxPoliciesInCasbinServer"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    GRPCWebUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "JarvisAuthInterceptor"
    ServiceGroups:
      - ServiceGroup: "actor_activity"
      - ServiceGroup: "casbin"
      - ServiceGroup: "cx"
      - ServiceGroup: "inapphelp"

  "frontend":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    ServersToStart:
      InitInsecureGrpcServer: true
      ServerTypes:
        - "custom"
      ServerStartFunc:
        PkgPath: "github.com/epifi/gamma/frontend/servergenhook"
        MethodName: "StartFrontendServer"
    UnaryClientInterceptors:
      CommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewFrontendRequestClientInterceptor"
      FCMDeviceTokenClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewFrontendRequestClientInterceptor"
      DynamicElementsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewFrontendRequestClientInterceptor"
      UserPreferenceClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewFrontendRequestClientInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/frontend/servergenhook"
        MethodName: "FrontendBeforeServerStartHook"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "SetupWireInitDependenciesHook"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/frontend/collapser"
        MethodName: "LoadCollapserWithGrpcServer"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "UnaryMockFEReqContextInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "ReqTimeoutUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "RequestHeaderUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "RPCFeatureControlInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "HandshakeInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "FrontendRateLimitServerInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddFERudderEventsUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AuthUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "DeviceIntegrityCheckerInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "RateLimitServerInterceptorV2WithDefaultKeyGen"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LocationInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "UnaryIPInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "DeviceCheckInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddNonceUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "PartnerLogUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "RequestHeaderInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "ResponseHeaderInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "NewEdgeActorRequestTracingServerInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/frontend/collapser"
        MethodName: "CollapserInterceptor"
    GRPCStreamInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "StreamMockFEReqContextInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "RequestTimeoutStreamInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AuthStreamInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "RequestHeaderStreamInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "StreamIPInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "FrontendRequestHeaderStreamInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CustomLoggingStreamInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LocationStreamInterceptor"
    ServiceGroups:
      - ServiceGroup: "frontend"
      - ServiceGroup: "webfe"

  "growth-infra":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: true
    ServersToStart:
      ServerTypes:
        - "grpc-web"
    UnaryClientInterceptors:
      CommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewCommsRequestClientInterceptor"
      UserPreferenceClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewCommsRequestClientInterceptor"
      InAppTargetedCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewCommsRequestClientInterceptor"
      WaBotCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewWhatsappBotRequestClientInterceptor"
      WaBotUserPreferenceClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewWhatsappBotRequestClientInterceptor"
      RewardsCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewRewardsRequestClientInterceptor"
      CasperCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewRewardsRequestClientInterceptor"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    GRPCWebUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "JarvisAuthInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/growth-infra/hook"
        MethodName: "InitCustomDelayQueueService"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/growth-infra/hook"
        MethodName: "InitCommsServer"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/growth-infra/hook"
        MethodName: "RegisterProcessRudderEventMethodToSubscriber"
        SkipErrOnInit: true
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/growth-infra/hook"
        MethodName: "LoadJarvisAuthDescriptors"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    ServiceGroups:
      - ServiceGroup: "accrual"
      - ServiceGroup: "casper"
      - ServiceGroup: "customdelayqueue"
      - ServiceGroup: "nudge"
      - ServiceGroup: "rewards"
      - ServiceGroup: "segment"
      - ServiceGroup: "cms"
      - ServiceGroup: "comms"
      - ServiceGroup: "dynamicelements"
      - ServiceGroup: "whatsappbot"
      - ServiceGroup: "quest"

  "lending":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: true
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/preapprovedloan/interceptors/servergen_wire"
        MethodName: "NewUnaryLoanHeaderInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/lending/hook"
        MethodName: "InitLendingServer"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    ServiceGroups:
      - ServiceGroup: "bre"
      - ServiceGroup: "inhousebre"
      - ServiceGroup: "preapprovedloan"
      - ServiceGroup: "creditreportv2"
      - ServiceGroup: "collection"
      - ServiceGroup: "epfo"
      - ServiceGroup: "leads"

  "nebula":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/nebula/hook"
        MethodName: "InitVarysHttpServer"
    ServiceGroups:
      - ServiceGroup: "varys"
      - ServiceGroup: "celestial"
      - ServiceGroup: "slack_bot"
        SkipErrOnInit: true

  "pay":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: true
    ServersToStart:
      ServerTypes:
        - "http"
        - "grpc"
    UnaryClientInterceptors:
      AccountCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewSavingsRequestClientInterceptor"
      DocsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewSavingsRequestClientInterceptor"
      RecPaymentCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewOrderRequestClientInterceptor"
      OrderCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewOrderRequestClientInterceptor"
      UpiCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewOrderRequestClientInterceptor"
      VgPaymentClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
      VgB2CPaymentClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
      VgAccountsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
      VgParserClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
      SIVGClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
      UNNameCheckClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
      MerchantResolutionClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
      VgUpiClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
      VgEnachClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/upi/interceptor/servergen_wire"
          MethodName: "NewReverseLocationClientInterceptor"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "OrderRateLimiterInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "PayRateLimiterInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "RateLimitServerInterceptorV2WithDefaultKeyGen"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/pay/hook"
        MethodName: "InitPayServer"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/pay/hook"
        MethodName: "InitOrderServiceGroupWorkflowConfigMap"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/pay/hook"
        MethodName: "InitialisePgProgramToAuthParamsMap"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    ServiceGroups:
      - ServiceGroup: "accounts"
      - ServiceGroup: "actor"
      - ServiceGroup: "merchant"
      - ServiceGroup: "docs"
      - ServiceGroup: "paymentinstrument"
      - ServiceGroup: "timeline"
      - ServiceGroup: "parser"
      - ServiceGroup: "order"
      - ServiceGroup: "pay"
        ConfPkgPath: "github.com/epifi/gamma/pay/config/server"
      - ServiceGroup: "recurringpayment"
        ConfPkgPath: "github.com/epifi/gamma/recurringpayment/config/server"
      - ServiceGroup: "upi"
      - ServiceGroup: "health_engine"
      - ServiceGroup: "billpay"

  "simulator":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    ServersToStart:
      InitInsecureGrpcServer: true
      ServerTypes:
        - "custom"
      ServerStartFunc:
        PkgPath: "github.com/epifi/gamma/simulator/servergenhook"
        MethodName: "InitSimulatorServer"
    ServiceGroups:
      - ServiceGroup: "simulator"

  "tokenizer":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/tokenizer/servergenhook"
        MethodName: "InitTokenizerServer"
    ServiceGroups:
      - ServiceGroup: "tokenizer"

  "onboarding":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: true
    UnaryClientInterceptors:
      AuthCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewAuthRequestClientInterceptor"
      FCMDeviceTokenClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewAuthRequestClientInterceptor"
      KycCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewKycRequestClientInterceptor"
      PanCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewKycRequestClientInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/onboarding/hook"
        MethodName: "RegisterProcessRudderEventMethodToSubscriber"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/onboarding/hook"
        MethodName: "InitKycServer"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddRudderEventsUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    ServiceGroups:
      - ServiceGroup: "alfred"
      - ServiceGroup: "acquisition"
      - ServiceGroup: "auth"
      - ServiceGroup: "bankcust"
      - ServiceGroup: "employment"
      - ServiceGroup: "inappreferral"
      - ServiceGroup: "referral"
      - ServiceGroup: "product"
      - ServiceGroup: "screener"
      - ServiceGroup: "user"
      - ServiceGroup: "useractions"
      - ServiceGroup: "userintel"
      - ServiceGroup: "kyc"
      - ServiceGroup: "pan"
      - ServiceGroup: "vendordata"
      - ServiceGroup: "shipment"
      - ServiceGroup: "omegle"
      - ServiceGroup: "tspuser"
      - ServiceGroup: "liveness"
      - ServiceGroup: "vkyccall"

  "userrisk":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: true
    ServiceGroups:
      - ServiceGroup: "risk"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/userrisk/hook"
        MethodName: "InitRiskHttpServer"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/userrisk/hook"
        MethodName: "InitCRDBTxn"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"

  "vendorgateway":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/vendorgateway/servergenhook"
        MethodName: "InitVendorgatewayServer"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "TokenizerServerInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "VGRateLimitServerInterceptor"
      - PkgPath: "github.com/epifi/gamma/vendorgateway/interceptor/servergen_wire"
        MethodName: "NewVgTimeoutInterceptor"
      - PkgPath: "github.com/epifi/gamma/vendorgateway/interceptor/servergen_wire"
        MethodName: "NewVgDowntimeInterceptor"
      - PkgPath: "github.com/epifi/gamma/vendorgateway/interceptor/servergen_wire"
        MethodName: "NewVgInstrumentBillingInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    ServiceGroups:
      - ServiceGroup: "vendorgateway"

  "vendorgateway-pci":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/vendorgateway-pci/servergenhook"
        MethodName: "BeforeVendorgatewayPciServerStartHook"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "TokenizerServerInterceptor"
    ServiceGroups:
      - ServiceGroup: "vendorgateway-pci"

  "vendormapping":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    ServiceGroups:
      - ServiceGroup: "vendormapping"

  "vendornotification":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "NewJwtUnaryServerInterceptor"
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "VNRateLimitServerInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/vendornotification/servergenhook"
        MethodName: "InitVendorNotificationServer"
    ServiceGroups:
      - ServiceGroup: "vendornotification"
      - ServiceGroup: "tsp"

  "vnotificationgw":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    ServersToStart:
      InitInsecureGrpcServer: true
      ServerTypes:
        - "custom"
      ServerStartFunc:
        PkgPath: "github.com/epifi/gamma/vnotificationgw/servergenhook"
        MethodName: "StartVNotificationServer"
    ServiceGroups:
      - ServiceGroup: "vnotificationgw"

  "wealthdmf":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: true
    UnaryClientInterceptors:
      DepositCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewSavingsRequestClientInterceptor"
      InvestmentCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewInvestmentRequestClientInterceptor"
      FitttCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewFitttRequestClientInterceptor"
      InsightsCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewInsightsRequestClientInterceptor"
      AnalyserCommsClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewAnalyserRequestClientInterceptor"
      VgP2pInvestmentClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewMaxResponseSizeClientInterceptor"
      VgStocksClientWithInterceptors:
        - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
          MethodName: "NewMaxResponseSizeClientInterceptor"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "AddQuestUserContextUnaryInterceptor"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "CollapserInterceptor"
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/wealthdmf/hook"
        MethodName: "InitInvestmentAndUsstocksServiceGroups"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/wealthdmf/hook"
        MethodName: "LoadKmsKeysForLocalStackEnv"
      - PkgPath: "github.com/epifi/gamma/cmd/servers/staging/wealthdmf/hook"
        MethodName: "InitSearchServiceGroup"
      - PkgPath: "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
        MethodName: "LoadCollapserWithGrpcServer"
    ServiceGroups:
      - ServiceGroup: "aml"
      - ServiceGroup: "deposit"
      - ServiceGroup: "goals"
      - ServiceGroup: "investment"
      - ServiceGroup: "p2pinvestment"
      - ServiceGroup: "usstocks"
      - ServiceGroup: "fittt"
      - ServiceGroup: "rms"
      - ServiceGroup: "analyser"
      - ServiceGroup: "categorizer"
      - ServiceGroup: "insights"
      - ServiceGroup: "upcomingtransactions"
      - ServiceGroup: "indexer"
      - ServiceGroup: "search"
      - ServiceGroup: "budgeting"
      - ServiceGroup: "gplace"
      - ServiceGroup: "wealthonboarding"
      - ServiceGroup: "securities"
      - ServiceGroup: "nps"


  "sgexternalgateway":
    Repository: "gringott"
    DeploymentAccountAlias: "stockguardian"
    NeedDeeplinkDependencies: false
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gringott/pkg/grpc/interceptors/servergen_wire"
        MethodName: "SetupWireInitDependenciesHook"
      - PkgPath: "github.com/epifi/gringott/sgexternalgateway/servergenhook"
        MethodName: "StartSgExternalGatewayHttpServer"
    ServiceGroups:
      - ServiceGroup: "sgexternalgateway"
    GRPCUnaryInterceptors:
      - PkgPath: "github.com/epifi/gringott/pkg/grpc/interceptors/servergen_wire"
        MethodName: "NewIPWhitelistUnaryServerInterceptor"
      - PkgPath: "github.com/epifi/gringott/pkg/grpc/interceptors/servergen_wire"
        MethodName: "NewJwtUnaryServerInterceptor"
      - PkgPath: "github.com/epifi/gringott/pkg/grpc/interceptors/servergen_wire"
        MethodName: "NewRequestHeaderUnaryServerInterceptor"
      - PkgPath: "github.com/epifi/gringott/pkg/grpc/interceptors/servergen_wire"
        MethodName: "NewSgExternalGatewayRateLimitServerInterceptor"

  "goblin":
    Repository: "gringott"
    DeploymentAccountAlias: "epifi"
    NeedDeeplinkDependencies: false
    BeforeGRPCServerStartHooks:
      - PkgPath: "github.com/epifi/gringott/cmd/servers/staging/goblin/hook"
        MethodName: "InitGoblinServer"
    GRPCPreferences:
      ProtoPkgToServerMap:
        "github.com/epifi/gringott/api/external/omegle": onboarding
        "github.com/epifi/gringott/api/external/vkyccall": onboarding
    ServiceGroups:
      - ServiceGroup: "sgauth"
      - ServiceGroup: "creditreport"
      - ServiceGroup: "sgbre"
      - ServiceGroup: "creditrisk"
      - ServiceGroup: "sgdocs"
      - ServiceGroup: "esign"
      - ServiceGroup: "sginhousebre"
      - ServiceGroup: "sgapplication"
      - ServiceGroup: "ckyc"
      - ServiceGroup: "matrix"
      - ServiceGroup: "applicant"
      - ServiceGroup: "address"
      - ServiceGroup: "customer"
      - ServiceGroup: "sgkyc"
      - ServiceGroup: "sgpan"
      - ServiceGroup: "lms"
      - ServiceGroup: "sgvendorgateway"
      - ServiceGroup: "sgsimulator"
      - ServiceGroup: "sgapigateway"
      - ServiceGroup: "sgdigilocker"

  "httpgw":
    Repository: "gamma"
    DeploymentAccountAlias: "epifi"
    ServersToStart:
      InitInsecureGrpcServer: true
      ServerTypes:
        - "custom"
      ServerStartFunc:
        PkgPath: "github.com/epifi/gamma/httpgw/servergenhook"
        MethodName: "StartHttpGWServer"
    ServiceGroups:
      - ServiceGroup: "httpgw"