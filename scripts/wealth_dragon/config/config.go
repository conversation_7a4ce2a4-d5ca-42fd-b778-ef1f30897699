package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
	cfgConf "github.com/epifi/be-common/pkg/cfg/genconf"
)

// nolint: gosec
const (
	SlackBotOauthToken = "SlackBotOauthToken"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	viper, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "wealth_dragon")
	if err != nil {
		return nil, fmt.Errorf("failed to load viper config: %w", err)
	}

	conf := &Config{}
	err = viper.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.Aws.Region, conf.USStocksAlpacaDb, conf.EpifiWealthDb, conf.StocksDb)
	if err != nil {
		return nil, err
	}

	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	return configPath
}

type Config struct {
	Application                                  *Application
	USStocksAlpacaDb                             *cfg.DB
	EpifiWealthDb                                *cfg.DB
	StocksDb                                     *cfg.DB
	Aws                                          *Aws
	Secrets                                      *cfg.Secrets
	PgdbMigrationConf                            *cfgConf.PgdbConn
	USStocksRedisOptions                         *cfg.RedisOptions
	StocksRefreshCatalogPublisher                *cfg.SqsPublisher
	SecuritiesHistoricalPricePublisher           *cfg.SqsPublisher
	AddNewSecuritiesPublisher                    *cfg.SqsPublisher
	CaNewDataFetchPublisher                      *cfg.SqsPublisher
	BrokerFirmAccountDetailsForForeignRemittance *BrokerFirmAccountDetailsForForeignRemittance
}

type Application struct {
	Environment string
}

type Aws struct {
	Region string
	S3     *S3Config
}

type S3Config struct {
	BaseBucketName string
	OutputPath     string
}

type BrokerFirmAccountDetailsForForeignRemittance struct {
	OutwardRemittanceAccount string
}
