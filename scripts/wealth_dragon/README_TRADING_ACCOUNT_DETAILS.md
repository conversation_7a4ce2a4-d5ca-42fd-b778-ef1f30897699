# Trading Account Details Job

## Overview
The `GET_TRADING_ACCOUNT_DETAILS` job fetches trading account details from the GetTradingAccount API for all active accounts in the database. It retrieves the Account ID, Actor ID, Cash, and Equity information for each account and uploads the results to S3.

## Usage

### Command Line
```bash
go run main.go -JobName=GET_TRADING_ACCOUNT_DETAILS
```

### No Parameters Required
The job automatically fetches all active accounts from the database, so no input parameters are needed.

## Database Query
The job queries the US Stocks Alpaca database to get all active accounts:

```sql
SELECT ID, ACTOR_ID, VENDOR_ACCOUNT_ID 
FROM ACCOUNTS 
WHERE ACCOUNT_STATUS = 'ACTIVE' 
AND DELETED_AT IS NULL 
AND VENDOR_ACCOUNT_ID IS NOT NULL
```

## Output

### S3 Output File
The job generates a timestamped CSV file and uploads it to S3 with the following columns:
- **Account ID**: The internal account identifier
- **Actor ID**: The actor/user identifier
- **Cash (USD)**: Available cash balance in USD
- **Equity (USD)**: Total equity value in USD
- **Error**: Error message if the account details couldn't be fetched

Example output:
```csv
Account ID,Actor ID,Cash (USD),Equity (USD),Error
USSWO42cUbiUz1S250409,AC210906KVfLE3JyTB6ooZ5O9VYmVw==,1000.50,1500.75,
USSWO3F8sA5rSwA250409,AC3oXEVbZA4q250624,,,Account not found
```

### S3 Path Structure
The output files are uploaded to S3 with the following path structure:
```
s3://{bucket-name}/{output-path}/trading_account_details_{timestamp}.csv
```

Example S3 path:
```
s3://epifi-prod-onboarding/wealth-dragon/trading-account-details/trading_account_details_20250115_143022.csv
```

### Console Output
The job provides real-time progress updates and a final summary:
```
=== JOB SUMMARY ===
Total Accounts: 100
Successful: 95
Failed: 5
S3 Path: s3://epifi-prod-onboarding/wealth-dragon/trading-account-details/trading_account_details_20250115_143022.csv
==================
```

## Features

### Database Integration
- Direct query to US Stocks Alpaca database
- Fetches all active accounts automatically
- No manual CSV file preparation required
- Filters for accounts with valid vendor account IDs

### Batch Processing
- Processes accounts in batches of 50 to avoid overwhelming the API
- Concurrent processing within each batch for better performance
- Small delays between batches to respect API rate limits

### Error Handling
- Graceful handling of API failures
- Continues processing even if some accounts fail
- Detailed error logging for failed accounts
- Error messages included in the output CSV

### S3 Integration
- Automatic upload to configured S3 bucket
- Environment-specific bucket configuration
- Timestamped file names to avoid conflicts
- Proper content-type headers for CSV files

### Logging
- Structured logging with Zap logger
- Progress tracking for each batch
- Summary statistics at the end
- Failed account IDs logged for debugging

## API Details

### GetTradingAccount API
The job uses the `GetTradingAccount` API from the vendor gateway stocks service:
- **Vendor**: ALPACA
- **Endpoint**: `/trading/accounts/{accountId}/account`
- **Response Fields**: AccountId, Cash, Equity, BuyingPower, etc.

### Response Structure
```protobuf
message TradingAccount {
    string account_id = 1;
    Money cash = 3;
    Money equity = 7;
    // ... other fields
}
```

## Configuration

### Job Settings
- **Valid Till**: December 31, 2025
- **Production Allowed**: Yes
- **Batch Size**: 50 accounts per batch
- **Database**: US Stocks Alpaca database

### S3 Configuration
The job uses environment-specific S3 configuration:

#### Production
```yaml
Aws:
  Region: "ap-south-1"
  S3:
    BaseBucketName: "epifi-prod-onboarding"
    OutputPath: "wealth-dragon/trading-account-details"
```

#### Staging
```yaml
Aws:
  Region: "ap-south-1"
  S3:
    BaseBucketName: "epifi-staging-onboarding"
    OutputPath: "wealth-dragon/trading-account-details"
```

#### QA
```yaml
Aws:
  Region: "ap-south-1"
  S3:
    BaseBucketName: "epifi-qa-onboarding"
    OutputPath: "wealth-dragon/trading-account-details"
```

### Dependencies
- `vgStocksClient`: Vendor gateway stocks client for API calls
- `s3Client`: S3 client for file uploads
- `ussAlpacaDB`: US Stocks Alpaca database connection
- `epifigrpc`: gRPC error handling utilities
- `errgroup`: Concurrent processing utilities
- `money`: Money type handling for financial values

## Error Scenarios

### Common Errors
1. **Account Not Found**: Account ID doesn't exist in the system
2. **API Timeout**: Network or service timeout
3. **Invalid Account ID**: Malformed account identifier
4. **Service Unavailable**: Vendor gateway service down
5. **S3 Upload Failure**: Network issues or insufficient permissions
6. **Database Connection Error**: Unable to connect to US Stocks Alpaca database

### Error Recovery
- Failed accounts are logged but don't stop the job
- Each account is processed independently
- Detailed error messages are captured in the output CSV
- S3 upload failures are logged with specific error details

## Performance Considerations

### Rate Limiting
- 50 accounts processed per batch
- 100ms delay between batches
- Concurrent processing within batches (up to 50 goroutines)

### Memory Usage
- Results are written to temporary CSV file
- File content is read and uploaded to S3
- Temporary files are automatically cleaned up
- Efficient channel-based concurrent processing

### S3 Upload
- Files are uploaded with proper content-type headers
- Timestamped filenames prevent conflicts
- Environment-specific bucket configuration
- Automatic cleanup of temporary files

## Monitoring

### Logs to Monitor
- Database query results
- Batch processing progress
- Failed account details
- API response times
- S3 upload success/failure
- Memory usage patterns

### Metrics to Track
- Success rate per batch
- Average processing time per account
- API error rates
- S3 upload success rate
- File generation and upload times
- Database query performance

## Troubleshooting

### Common Issues
1. **Database connection failure**: Check US Stocks Alpaca database connectivity
2. **S3 permission errors**: Ensure proper IAM permissions for S3 upload
3. **API failures**: Check vendor gateway service status
4. **Memory issues**: Reduce batch size if processing large datasets
5. **S3 upload failures**: Check network connectivity and bucket permissions

### Debug Steps
1. Verify database connectivity and permissions
2. Check if active accounts exist in the database
3. Review error logs for specific failure reasons
4. Test with a small subset of accounts first
5. Verify S3 bucket permissions and configuration

### S3 Access
To access the uploaded files:
1. Use AWS CLI: `aws s3 ls s3://{bucket-name}/wealth-dragon/trading-account-details/`
2. Use AWS Console: Navigate to the S3 bucket and folder
3. Download files: `aws s3 cp s3://{bucket-name}/wealth-dragon/trading-account-details/{filename} .` 