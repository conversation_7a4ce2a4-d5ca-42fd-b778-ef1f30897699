package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/scripts/wealth_dragon/config"
)

type AccountInfo struct {
	ActorID  string `gorm:"column:actor_id"`
	VendorID string `gorm:"column:vendor_account_id"`
}

type TradingAccountDetail struct {
	ActorId string
	Cash    *money.Money
	Equity  *money.Money
	Error   string
}

type jobGetTradingAccountDetails struct {
	vgStocksClient vgStocksPb.StocksClient
	s3Client       s3.S3Client
	config         *config.Config
	ussAlpacaDB    *gorm.DB
}

func (j *jobGetTradingAccountDetails) PerformJob(ctx context.Context, req *JobRequest) error {
	// Get all active account IDs from database
	accounts, err := j.getActiveAccountIdsFromDB(ctx)
	logger.Info(ctx, "accounts", zap.Any("accounts", accounts))
	if err != nil {
		return errors.Wrap(err, "failed to get active account IDs from database")
	}

	if len(accounts) == 0 {
		return fmt.Errorf("no active accounts found in the database")
	}

	logger.Info(ctx, "Starting to fetch trading account details", zap.Int("totalAccounts", len(accounts)))

	var allAccountDetails []TradingAccountDetail
	var failedAccounts []string

	// Process accounts sequentially
	for i, account := range accounts {
		// Skip accounts with empty IDs
		if account.ActorID == "" || account.VendorID == "" {
			logger.Warn("Skipping account with empty IDs",
				zap.Int("index", i),
				zap.String("actorId", account.ActorID),
				zap.String("vendorId", account.VendorID))
			failedAccounts = append(failedAccounts, account.ActorID)
			allAccountDetails = append(allAccountDetails, TradingAccountDetail{
				ActorId: account.ActorID,
				Error:   "Empty ActorID or VendorID",
			})
			continue
		}

		logger.Info(ctx, "Processing account",
			zap.Int("progress", i+1),
			zap.Int("total", len(accounts)),
			zap.String("actorId", account.ActorID),
			zap.String("vendorId", account.VendorID))

		detail, err := j.fetchTradingAccountDetail(ctx, account)
		if err != nil {
			logger.Error(ctx, "Failed to fetch trading account detail",
				zap.String("actorId", account.ActorID),
				zap.String("vendorAccountId", account.VendorID),
				zap.Error(err))
			failedAccounts = append(failedAccounts, account.ActorID)
			allAccountDetails = append(allAccountDetails, TradingAccountDetail{
				ActorId: account.ActorID,
				Error:   err.Error(),
			})
			continue
		}

		allAccountDetails = append(allAccountDetails, detail)

		// Add a small delay between requests to avoid overwhelming the API
		//if i < len(accounts)-1 {
		//	time.Sleep(50 * time.Millisecond)
		//}
	}

	// Write results to CSV file and upload to S3
	outputFileName := fmt.Sprintf("trading_account_details_%s.csv", time.Now().Format("20060102_150405"))
	s3Path, err := j.writeResultsToCSVAndUploadToS3(ctx, allAccountDetails, outputFileName)
	if err != nil {
		return errors.Wrap(err, "failed to write results to CSV file and upload to S3")
	}

	// Log summary
	successfulAccounts := len(allAccountDetails) - len(failedAccounts)
	j.logSummary(ctx, len(accounts), successfulAccounts, failedAccounts, s3Path)

	return nil
}

func (j *jobGetTradingAccountDetails) getActiveAccountIdsFromDB(ctx context.Context) ([]AccountInfo, error) {
	var accounts []AccountInfo

	// Let's check what columns actually exist and have data
	columnQuery := `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
					WHERE TABLE_NAME = 'ACCOUNTS'
					AND COLUMN_NAME LIKE '%ACTOR%' OR COLUMN_NAME LIKE '%VENDOR%' OR COLUMN_NAME LIKE '%ACCOUNT%'`
	var columnNames []string
	if err := j.ussAlpacaDB.Raw(columnQuery).Pluck("COLUMN_NAME", &columnNames).Error; err != nil {
		logger.Info(ctx, "Could not get column names", zap.Error(err))
	} else {
		logger.Info(ctx, "Available columns", zap.Strings("columns", columnNames))
	}

	// Let's try a raw query to see all columns for a few records
	rawQuery := `SELECT * FROM ACCOUNTS WHERE ACCOUNT_STATUS = 'ACTIVE' LIMIT 3`
	var rawResults []map[string]interface{}
	if err := j.ussAlpacaDB.Raw(rawQuery).Scan(&rawResults).Error; err != nil {
		logger.Info(ctx, "Could not get raw data", zap.Error(err))
	} else {
		logger.Info(ctx, "Raw account data", zap.Any("rawData", rawResults))
	}

	// Query to get all active accounts with their vendor account IDs
	query := `SELECT ACTOR_ID, VENDOR_ACCOUNT_ID
			  FROM ACCOUNTS
			  WHERE ACCOUNT_STATUS = 'ACTIVE'
			  LIMIT 10`

	if err := j.ussAlpacaDB.Raw(query).Scan(&accounts).Error; err != nil {
		return nil, errors.Wrap(err, "failed to query active accounts from database")
	}

	logger.Info(ctx, "Retrieved active accounts from database", zap.Any("totalAccounts", accounts))
	// Log sample data for debugging
	logger.Info(ctx, "Retrieved active accounts from database", zap.Int("totalAccounts", len(accounts)))
	for i, account := range accounts {
		if i < 3 { // Log first 3 accounts for debugging
			logger.Info(ctx, "Sample account data",
				zap.Int("index", i),
				zap.String("actorId", account.ActorID),
				zap.String("vendorId", account.VendorID))
		}
	}

	return accounts, nil
}

func (j *jobGetTradingAccountDetails) fetchTradingAccountDetail(ctx context.Context, account AccountInfo) (TradingAccountDetail, error) {
	resp, err := j.vgStocksClient.GetTradingAccount(ctx, &vgStocksPb.GetTradingAccountRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		AccountId: account.VendorID,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return TradingAccountDetail{
			ActorId: account.ActorID,
			Error:   rpcErr.Error(),
		}, rpcErr
	}

	tradingAccount := resp.GetTradingAccount()
	if tradingAccount == nil {
		return TradingAccountDetail{
			ActorId: account.ActorID,
			Error:   "No trading account data received",
		}, fmt.Errorf("no trading account data received for actor ID: %s", account.ActorID)
	}

	// Convert protobuf money to common money type
	var cashMoney, equityMoney *money.Money
	if tradingAccount.GetCash() != nil {
		cashMoney = money.NewMoney(tradingAccount.GetCash())
	}
	if tradingAccount.GetEquity() != nil {
		equityMoney = money.NewMoney(tradingAccount.GetEquity())
	}

	return TradingAccountDetail{
		ActorId: account.ActorID,
		Cash:    cashMoney,
		Equity:  equityMoney,
	}, nil
}

func (j *jobGetTradingAccountDetails) writeResultsToCSVAndUploadToS3(ctx context.Context, accountDetails []TradingAccountDetail, outputFileName string) (string, error) {
	// Create temporary file
	tempFile, err := os.CreateTemp("", "trading_account_details_*.csv")
	if err != nil {
		return "", errors.Wrap(err, "failed to create temporary file")
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	writer := csv.NewWriter(tempFile)
	defer writer.Flush()

	// Write header
	header := []string{"Actor ID", "Cash (USD)", "Equity (USD)", "Error"}
	if err := writer.Write(header); err != nil {
		return "", errors.Wrap(err, "failed to write CSV header")
	}

	// Write data rows
	for _, detail := range accountDetails {
		var cashStr, equityStr string

		if detail.Cash != nil {
			cashStr = money.ToDecimal(detail.Cash.GetPb()).String()
		}

		if detail.Equity != nil {
			equityStr = money.ToDecimal(detail.Equity.GetPb()).String()
		}

		row := []string{
			detail.ActorId,
			cashStr,
			equityStr,
			detail.Error,
		}

		if err := writer.Write(row); err != nil {
			return "", errors.Wrap(err, "failed to write CSV row")
		}
	}

	// Close the file before uploading
	tempFile.Close()

	// Read the file content
	fileContent, err := os.ReadFile(tempFile.Name())
	if err != nil {
		return "", errors.Wrap(err, "failed to read temporary file")
	}

	// Upload to S3
	s3Key := fmt.Sprintf("%s/%s", j.config.Aws.S3.OutputPath, outputFileName)
	_, err = j.s3Client.WriteAndGetPreSignedUrl(s3Key, fileContent, 3600)
	if err != nil {
		return "", errors.Wrap(err, "failed to upload file to S3")
	}

	s3Path := fmt.Sprintf("s3://%s/%s", j.config.Aws.S3.BaseBucketName, s3Key)
	logger.Info(ctx, "Successfully uploaded results to S3", zap.String("s3Path", s3Path))
	return s3Path, nil
}

func (j *jobGetTradingAccountDetails) logSummary(ctx context.Context, totalAccounts, successfulAccounts int, failedAccounts []string, s3Path string) {
	logger.Info(ctx, "Trading Account Details Job Summary",
		zap.Int("totalAccounts", totalAccounts),
		zap.Int("successfulAccounts", successfulAccounts),
		zap.Int("failedAccounts", len(failedAccounts)),
		zap.String("s3Path", s3Path),
	)

	if len(failedAccounts) > 0 {
		logger.Error(ctx, "Failed accounts", zap.Strings("failedActorIds", failedAccounts))
	}

	fmt.Printf("\n=== JOB SUMMARY ===\n")
	fmt.Printf("Total Accounts: %d\n", totalAccounts)
	fmt.Printf("Successful: %d\n", successfulAccounts)
	fmt.Printf("Failed: %d\n", len(failedAccounts))
	fmt.Printf("S3 Path: %s\n", s3Path)
	fmt.Printf("==================\n")
}
