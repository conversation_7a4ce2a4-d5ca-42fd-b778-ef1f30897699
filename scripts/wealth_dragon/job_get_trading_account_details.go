package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/scripts/wealth_dragon/config"
)

type AccountInfo struct {
	ActorID  string `gorm:"column:actor_id"`
	VendorID string `gorm:"column:vendor_account_id"`
}

type TradingAccountDetail struct {
	ActorId string
	Cash    *money.Money
	Equity  *money.Money
	Error   string
}

type jobGetTradingAccountDetails struct {
	vgStocksClient vgStocksPb.StocksClient
	s3Client       s3.S3Client
	config         *config.Config
	ussAlpacaDB    *gorm.DB
}

func (j *jobGetTradingAccountDetails) PerformJob(ctx context.Context, req *JobRequest) error {
	// Get all active account IDs from database
	accounts, err := j.getActiveAccountIdsFromDB(ctx)
	if err != nil {
		return errors.Wrap(err, "failed to get active account IDs from database")
	}

	if len(accounts) == 0 {
		return fmt.Errorf("no active accounts found in the database")
	}

	logger.Info(ctx, "Starting to fetch trading account details", zap.Int("totalAccounts", len(accounts)))

	var allAccountDetails []TradingAccountDetail
	var failedAccounts []string

	// Process accounts sequentially
	for i, account := range accounts {
		// Skip accounts with empty IDs
		if account.ActorID == "" || account.VendorID == "" {
			logger.Warn("Skipping account with empty IDs",
				zap.Int("index", i),
				zap.String("actorId", account.ActorID),
				zap.String("vendorId", account.VendorID))
			failedAccounts = append(failedAccounts, account.ActorID)
			allAccountDetails = append(allAccountDetails, TradingAccountDetail{
				ActorId: account.ActorID,
				Error:   "Empty ActorID or VendorID",
			})
			continue
		}

		//logger.Info(ctx, "Processing account",
		//	zap.Int("progress", i+1),
		//	zap.Int("total", len(accounts)),
		//	zap.String("actorId", account.ActorID),
		//	zap.String("vendorId", account.VendorID),
		//	zap.Int("actorIdLen", len(account.ActorID)),
		//	zap.Int("vendorIdLen", len(account.VendorID)))

		detail, err := j.fetchTradingAccountDetail(ctx, account)
		if err != nil {
			logger.Error(ctx, "Failed to fetch trading account detail",
				zap.String("actorId", account.ActorID),
				zap.String("vendorAccountId", account.VendorID),
				zap.Error(err))
			failedAccounts = append(failedAccounts, account.ActorID)
			allAccountDetails = append(allAccountDetails, TradingAccountDetail{
				ActorId: account.ActorID,
				Error:   err.Error(),
			})
			continue
		}

		allAccountDetails = append(allAccountDetails, detail)

		// Add a small delay between requests to avoid overwhelming the API
		// if i < len(accounts)-1 {
		// 	time.Sleep(50 * time.Millisecond)
		// }
	}

	// Write results to CSV file and upload to S3
	outputFileName := fmt.Sprintf("trading_account_details_%s.csv", time.Now().Format("20060102_150405"))
	s3Path, err := j.writeResultsToCSVAndUploadToS3(ctx, allAccountDetails, outputFileName)
	if err != nil {
		return errors.Wrap(err, "failed to write results to CSV file and upload to S3")
	}

	// Log summary
	successfulAccounts := len(allAccountDetails) - len(failedAccounts)
	j.logSummary(ctx, len(accounts), successfulAccounts, failedAccounts, s3Path)

	return nil
}

func (j *jobGetTradingAccountDetails) getActiveAccountIdsFromDB(ctx context.Context) ([]AccountInfo, error) {
	var accounts []AccountInfo

	// Query to get all active accounts with their vendor account IDs
	query := `SELECT actor_id, vendor_account_id
			  FROM accounts
			  WHERE account_status = 'ACTIVE'
			  AND actor_id IS NOT NULL
			  AND vendor_account_id IS NOT NULL
			  AND actor_id != ''
			  AND vendor_account_id != ''`

	if err := j.ussAlpacaDB.Raw(query).Scan(&accounts).Error; err != nil {
		return nil, errors.Wrap(err, "failed to query active accounts from database")
	}

	return accounts, nil
}

func (j *jobGetTradingAccountDetails) fetchTradingAccountDetail(ctx context.Context, account AccountInfo) (TradingAccountDetail, error) {
	resp, err := j.vgStocksClient.GetTradingAccount(ctx, &vgStocksPb.GetTradingAccountRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		AccountId: account.VendorID,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Debug(ctx, "Creating error response",
			zap.String("actorId", account.ActorID),
			zap.String("vendorId", account.VendorID),
			zap.String("error", rpcErr.Error()))
		return TradingAccountDetail{
			ActorId: account.ActorID,
			Error:   rpcErr.Error(),
		}, rpcErr
	}

	tradingAccount := resp.GetTradingAccount()
	if tradingAccount == nil {
		return TradingAccountDetail{
			ActorId: account.ActorID,
			Error:   "No trading account data received",
		}, fmt.Errorf("no trading account data received for actor ID: %s", account.ActorID)
	}

	// Convert protobuf money to common money type
	var cashMoney, equityMoney *money.Money
	if tradingAccount.GetCash() != nil {
		cashMoney = money.NewMoney(tradingAccount.GetCash())
	}
	if tradingAccount.GetEquity() != nil {
		equityMoney = money.NewMoney(tradingAccount.GetEquity())
	}

	return TradingAccountDetail{
		ActorId: account.ActorID,
		Cash:    cashMoney,
		Equity:  equityMoney,
	}, nil
}

func (j *jobGetTradingAccountDetails) writeResultsToCSVAndUploadToS3(ctx context.Context, accountDetails []TradingAccountDetail, outputFileName string) (string, error) {
	// Create temporary file
	tempFile, err := os.CreateTemp("", "trading_account_details_*.csv")
	if err != nil {
		return "", errors.Wrap(err, "failed to create temporary file")
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	writer := csv.NewWriter(tempFile)

	// Write header
	header := []string{"Actor ID", "Cash (USD)", "Equity (USD)", "Error"}
	if err := writer.Write(header); err != nil {
		return "", errors.Wrap(err, "failed to write CSV header")
	}

	// Write data rows
	logger.Info(ctx, "Writing CSV data", zap.Int("totalDetails", len(accountDetails)))
	for i, detail := range accountDetails {
		var cashStr, equityStr string

		if detail.Cash != nil {
			cashStr = money.ToDecimal(detail.Cash.GetPb()).String()
		}

		if detail.Equity != nil {
			equityStr = money.ToDecimal(detail.Equity.GetPb()).String()
		}

		row := []string{
			detail.ActorId,
			cashStr,
			equityStr,
			detail.Error,
		}

		logger.Debug(ctx, "Writing CSV row",
			zap.Int("rowIndex", i),
			zap.String("actorId", detail.ActorId),
			zap.String("cash", cashStr),
			zap.String("equity", equityStr),
			zap.String("error", detail.Error))

		if err := writer.Write(row); err != nil {
			return "", errors.Wrap(err, "failed to write CSV row")
		}
	}

	// Flush the writer and close the file before uploading
	writer.Flush()
	if err := writer.Error(); err != nil {
		return "", errors.Wrap(err, "failed to flush CSV writer")
	}
	tempFile.Close()

	// Read the file content
	fileContent, err := os.ReadFile(tempFile.Name())
	if err != nil {
		return "", errors.Wrap(err, "failed to read temporary file")
	}

	previewLen := len(fileContent)
	if previewLen > 200 {
		previewLen = 200
	}
	logger.Info(ctx, "File content ready for upload",
		zap.Int("fileSize", len(fileContent)),
		zap.String("filePreview", string(fileContent[:previewLen])))

	if len(fileContent) == 0 {
		return "", fmt.Errorf("generated CSV file is empty")
	}

	// Upload to S3
	s3Key := fmt.Sprintf("%s/%s", j.config.Aws.S3.OutputPath, outputFileName)
	_, err = j.s3Client.WriteAndGetPreSignedUrl(s3Key, fileContent, 3600)
	if err != nil {
		return "", errors.Wrap(err, "failed to upload file to S3")
	}

	s3Path := fmt.Sprintf("s3://%s/%s", j.config.Aws.S3.BaseBucketName, s3Key)
	logger.Info(ctx, "Successfully uploaded results to S3", zap.String("s3Path", s3Path))
	return s3Path, nil
}

func (j *jobGetTradingAccountDetails) logSummary(ctx context.Context, totalAccounts, successfulAccounts int, failedAccounts []string, s3Path string) {
	logger.Info(ctx, "Trading Account Details Job Summary",
		zap.Int("totalAccounts", totalAccounts),
		zap.Int("successfulAccounts", successfulAccounts),
		zap.Int("failedAccounts", len(failedAccounts)),
		zap.String("s3Path", s3Path),
	)

	if len(failedAccounts) > 0 {
		logger.Error(ctx, "Failed accounts", zap.Strings("failedActorIds", failedAccounts))
	}

	fmt.Printf("\n=== JOB SUMMARY ===\n")
	fmt.Printf("Total Accounts: %d\n", totalAccounts)
	fmt.Printf("Successful: %d\n", successfulAccounts)
	fmt.Printf("Failed: %d\n", len(failedAccounts))
	fmt.Printf("S3 Path: %s\n", s3Path)
	fmt.Printf("==================\n")
}
