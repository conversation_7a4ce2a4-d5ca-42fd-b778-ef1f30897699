package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/scripts/wealth_dragon/config"
)

const (
	batchSize = 50
)

type AccountInfo struct {
	ActorID  string `gorm:"column:ACTOR_ID"`
	VendorID string `gorm:"column:VENDOR_ACCOUNT_ID"`
}

type TradingAccountDetail struct {
	ActorId string
	Cash    *money.Money
	Equity  *money.Money
	Error   string
}

type jobGetTradingAccountDetails struct {
	vgStocksClient vgStocksPb.StocksClient
	s3Client       s3.S3Client
	config         *config.Config
	ussAlpacaDB    *gorm.DB
}

func (j *jobGetTradingAccountDetails) PerformJob(ctx context.Context, req *JobRequest) error {
	// Get all active account IDs from database
	accounts, err := j.getActiveAccountIdsFromDB(ctx)
	if err != nil {
		return errors.Wrap(err, "failed to get active account IDs from database")
	}

	if len(accounts) == 0 {
		return fmt.Errorf("no active accounts found in the database")
	}

	logger.Info(ctx, "Starting to fetch trading account details", zap.Int("totalAccounts", len(accounts)))

	var allAccountDetails []TradingAccountDetail
	var failedAccounts []string

	// Process accounts in batches
	for i := 0; i < len(accounts); i += batchSize {
		end := i + batchSize
		if end > len(accounts) {
			end = len(accounts)
		}
		batchAccounts := accounts[i:end]

		logger.Info(ctx, "Processing batch", zap.Int("batchStart", i), zap.Int("batchEnd", end), zap.Int("batchSize", len(batchAccounts)))

		batchDetails, batchFailed := j.processBatch(ctx, batchAccounts)
		allAccountDetails = append(allAccountDetails, batchDetails...)
		failedAccounts = append(failedAccounts, batchFailed...)

		// Add a small delay between batches to avoid overwhelming the API
		if end < len(accounts) {
			time.Sleep(100 * time.Millisecond)
		}
	}

	// Write results to CSV file and upload to S3
	outputFileName := fmt.Sprintf("trading_account_details_%s.csv", time.Now().Format("20060102_150405"))
	s3Path, err := j.writeResultsToCSVAndUploadToS3(ctx, allAccountDetails, outputFileName)
	if err != nil {
		return errors.Wrap(err, "failed to write results to CSV file and upload to S3")
	}

	// Log summary
	j.logSummary(ctx, len(accounts), len(allAccountDetails), failedAccounts, s3Path)

	return nil
}

func (j *jobGetTradingAccountDetails) getActiveAccountIdsFromDB(ctx context.Context) ([]AccountInfo, error) {
	var accounts []AccountInfo

	// Query to get all active accounts with their vendor account IDs
	query := `SELECT ACTOR_ID, VENDOR_ACCOUNT_ID 
			  FROM ACCOUNTS 
			  WHERE ACCOUNT_STATUS = 'ACTIVE'`

	if err := j.ussAlpacaDB.Raw(query).Scan(&accounts).Error; err != nil {
		return nil, errors.Wrap(err, "failed to query active accounts from database")
	}

	logger.Info(ctx, "Retrieved active accounts from database", zap.Int("totalAccounts", len(accounts)))
	return accounts, nil
}

func (j *jobGetTradingAccountDetails) processBatch(ctx context.Context, accounts []AccountInfo) ([]TradingAccountDetail, []string) {
	var accountDetails []TradingAccountDetail
	var failedAccounts []string

	// Create channels for concurrent processing
	detailsChan := make(chan TradingAccountDetail, len(accounts))
	failedChan := make(chan string, len(accounts))

	// Process accounts concurrently
	grp, gCtx := errgroup.WithContext(ctx)
	for _, account := range accounts {
		account := account // capture variable for goroutine
		grp.Go(func() error {
			detail, err := j.fetchTradingAccountDetail(gCtx, account)
			if err != nil {
				logger.Error(ctx, "Failed to fetch trading account detail",
					zap.String("actorId", account.ActorID),
					zap.String("vendorAccountId", account.VendorID),
					zap.Error(err))
				failedChan <- account.ActorID
				return nil // Don't return error to avoid stopping other goroutines
			}
			detailsChan <- detail
			return nil
		})
	}

	// Wait for all goroutines to complete
	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "Error in batch processing", zap.Error(err))
	}

	// Close channels and collect results
	close(detailsChan)
	close(failedChan)

	// Collect successful results
	for detail := range detailsChan {
		accountDetails = append(accountDetails, detail)
	}

	// Collect failed accounts
	for failedAccount := range failedChan {
		failedAccounts = append(failedAccounts, failedAccount)
	}

	return accountDetails, failedAccounts
}

func (j *jobGetTradingAccountDetails) fetchTradingAccountDetail(ctx context.Context, account AccountInfo) (TradingAccountDetail, error) {
	resp, err := j.vgStocksClient.GetTradingAccount(ctx, &vgStocksPb.GetTradingAccountRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		AccountId: account.VendorID,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return TradingAccountDetail{
			ActorId: account.ActorID,
			Error:   rpcErr.Error(),
		}, rpcErr
	}

	tradingAccount := resp.GetTradingAccount()
	if tradingAccount == nil {
		return TradingAccountDetail{
			ActorId: account.ActorID,
			Error:   "No trading account data received",
		}, fmt.Errorf("no trading account data received for actor ID: %s", account.ActorID)
	}

	// Convert protobuf money to common money type
	var cashMoney, equityMoney *money.Money
	if tradingAccount.GetCash() != nil {
		cashMoney = &money.Money{
			CurrencyCode: tradingAccount.GetCash().GetCurrencyCode(),
			Units:        tradingAccount.GetCash().GetUnits(),
			Nanos:        tradingAccount.GetCash().GetNanos(),
		}
	}
	if tradingAccount.GetEquity() != nil {
		equityMoney = &money.Money{
			CurrencyCode: tradingAccount.GetEquity().GetCurrencyCode(),
			Units:        tradingAccount.GetEquity().GetUnits(),
			Nanos:        tradingAccount.GetEquity().GetNanos(),
		}
	}

	return TradingAccountDetail{
		ActorId: account.ActorID,
		Cash:    cashMoney,
		Equity:  equityMoney,
	}, nil
}

func (j *jobGetTradingAccountDetails) writeResultsToCSVAndUploadToS3(ctx context.Context, accountDetails []TradingAccountDetail, outputFileName string) (string, error) {
	// Create temporary file
	tempFile, err := os.CreateTemp("", "trading_account_details_*.csv")
	if err != nil {
		return "", errors.Wrap(err, "failed to create temporary file")
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	writer := csv.NewWriter(tempFile)
	defer writer.Flush()

	// Write header
	header := []string{"Actor ID", "Cash (USD)", "Equity (USD)", "Error"}
	if err := writer.Write(header); err != nil {
		return "", errors.Wrap(err, "failed to write CSV header")
	}

	// Write data rows
	for _, detail := range accountDetails {
		var cashStr, equityStr string

		if detail.Cash != nil {
			cashStr = money.ToDecimal(detail.Cash).String()
		}

		if detail.Equity != nil {
			equityStr = money.ToDecimal(detail.Equity).String()
		}

		row := []string{
			detail.ActorId,
			cashStr,
			equityStr,
			detail.Error,
		}

		if err := writer.Write(row); err != nil {
			return "", errors.Wrap(err, "failed to write CSV row")
		}
	}

	// Close the file before uploading
	tempFile.Close()

	// Read the file content
	fileContent, err := os.ReadFile(tempFile.Name())
	if err != nil {
		return "", errors.Wrap(err, "failed to read temporary file")
	}

	// Upload to S3
	s3Key := fmt.Sprintf("%s/%s", j.config.Aws.S3.OutputPath, outputFileName)
	err = j.s3Client.Upload(ctx, s3Key, fileContent, "text/csv")
	if err != nil {
		return "", errors.Wrap(err, "failed to upload file to S3")
	}

	s3Path := fmt.Sprintf("s3://%s/%s", j.config.Aws.S3.BaseBucketName, s3Key)
	logger.Info(ctx, "Successfully uploaded results to S3", zap.String("s3Path", s3Path))
	return s3Path, nil
}

func (j *jobGetTradingAccountDetails) logSummary(ctx context.Context, totalAccounts, successfulAccounts int, failedAccounts []string, s3Path string) {
	logger.Info(ctx, "Trading Account Details Job Summary",
		zap.Int("totalAccounts", totalAccounts),
		zap.Int("successfulAccounts", successfulAccounts),
		zap.Int("failedAccounts", len(failedAccounts)),
		zap.String("s3Path", s3Path),
	)

	if len(failedAccounts) > 0 {
		logger.Error(ctx, "Failed accounts", zap.Strings("failedActorIds", failedAccounts))
	}

	fmt.Printf("\n=== JOB SUMMARY ===\n")
	fmt.Printf("Total Accounts: %d\n", totalAccounts)
	fmt.Printf("Successful: %d\n", successfulAccounts)
	fmt.Printf("Failed: %d\n", len(failedAccounts))
	fmt.Printf("S3 Path: %s\n", s3Path)
	fmt.Printf("==================\n")
}
