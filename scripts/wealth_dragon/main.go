package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/mohae/deepcopy"
	"github.com/slack-go/slack"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc/metadata"

	"github.com/epifi/be-common/pkg/aws/s3"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	sqsPkg "github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	investmentAnalyserPb "github.com/epifi/gamma/api/analyser/investment"
	commsPb "github.com/epifi/gamma/api/comms"
	networthpb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog/scheduler"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	fgPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/api/usstocks/account"
	usscatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usStocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	mfVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	vgAnalyticsPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/analytics"
	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	insightsconf "github.com/epifi/gamma/insights/config"
	insightsgconf "github.com/epifi/gamma/insights/config/genconf"
	"github.com/epifi/gamma/investment/mutualfund/dao/impl"
	pkgllmWire "github.com/epifi/gamma/pkg/llm/wire"
	"github.com/epifi/gamma/scripts/wealth_dragon/config"
	catalogDao "github.com/epifi/gamma/securities/catalog/dao"
	orderDaoImpl "github.com/epifi/gamma/usstocks/order/dao/impl"
)

var (
	inputJob = flag.String("JobName", "", "job name, refer to jobNames for accepted values")
	jobArgs1 = flag.String("Args1", "", "input args for the job (refer to job requirements)")
	jobArgs2 = flag.String("Args2", "", "input args for the job (refer to job requirements)")
)

func main() {
	_ = os.Setenv("DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP", "TRUE")
	flag.Parse()
	logger.Init(cfg.DevelopmentEnv)
	defer func() { _ = logger.Log.Sync() }()

	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		defer os.Exit(1)
		return
	}
	clock := idgen.NewClock()
	idGen := idgen.NewDomainIdGenerator(clock)

	ussAlpacaDB, err := storagev2.NewGormDB(conf.USStocksAlpacaDb)
	if err != nil {
		logger.ErrorNoCtx("Failed to initialize USS Alpaca DB", zap.Error(err))
		defer os.Exit(1)
		return
	}

	epifiWealthDB, err := storagev2.NewGormDB(conf.EpifiWealthDb)
	if err != nil {
		logger.ErrorNoCtx("Failed to initialize Epifi Wealth DB", zap.Error(err))
		defer os.Exit(1)
		return
	}

	mfDao := impl.NewMutualFundCrdb(epifiWealthDB, nil)

	WalletOrderDao := orderDaoImpl.NewWalletOrderPgdb(conf.PgdbMigrationConf, ussAlpacaDB, idGen, 10)

	stocksDb, err1 := storagev2.NewGormDB(conf.StocksDb)
	if err1 != nil {
		logger.ErrorNoCtx("Failed to initialize Stocks DB", zap.Error(err1))
		defer os.Exit(1)
		return
	}

	securitiesDao := catalogDao.NewSecuritiesDaoPGDB(stocksDb)
	securityListingsDao := catalogDao.NewSecurityListingsDaoPGDB(stocksDb)

	awsConf, err2 := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, false)
	if err2 != nil {
		logger.ErrorNoCtx("Failed to initialize aws config", zap.Error(err1))
		defer os.Exit(1)
		return
	}

	sqsClient := sqsPkg.InitSQSClient(awsConf)

	// Initialize S3 client
	awsSession, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		logger.ErrorNoCtx("Failed to initialize aws session", zap.Error(err))
		defer os.Exit(1)
		return
	}
	s3Client := s3.NewClient(awsSession, conf.Aws.S3.BaseBucketName)
	stocksRefreshCatalogPublisher := initPublisher(ctx, sqsClient, conf.StocksRefreshCatalogPublisher)
	securitiesHistoricalPricePublisher := initPublisher(ctx, sqsClient, conf.SecuritiesHistoricalPricePublisher)
	caNewDataFetchEventPublisher := initPublisher(ctx, sqsClient, conf.CaNewDataFetchPublisher)

	payConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)
	defer epifigrpc.CloseConn(payConn)
	iftFileGenClient := fgPb.NewFileGeneratorClient(payConn)

	investmentConn := epifigrpc.NewConnByService(cfg.INVESTMENT_SERVICE)
	defer epifigrpc.CloseConn(investmentConn)
	netWorthClient := networthpb.NewNetWorthClient(investmentConn)
	mfExternalOrdersClient := mfExternalPb.NewMFExternalOrdersClient(investmentConn)
	catalogSchClient := scheduler.NewSchedulerClient(investmentConn)

	analyserConn := epifigrpc.NewConnByService(cfg.ANALYSER_SERVICE)
	defer epifigrpc.CloseConn(analyserConn)
	investmentAnalyticsClient := investmentAnalyserPb.NewInvestmentAnalyticsClient(analyserConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	mfAnalyticsVg := vgAnalyticsPb.NewMFAnalyticsClient(vgConn)
	mfVGClient := mfVgPb.NewMutualFundClient(vgConn)
	vgStocksClient := vgStocksPb.NewStocksClient(vgConn)

	vgNotificationConn := epifigrpc.NewConnByService(cfg.VENDOR_NOTIFI_SERVICE)
	defer epifigrpc.CloseConn(vgNotificationConn)
	moengageClient := moengageVnPb.NewMoengageClient(vgNotificationConn)

	ussConn := epifigrpc.NewConnByService(cfg.US_STOCKS_SERVICE)
	defer epifigrpc.CloseConn(ussConn)
	orderMangerClient := usStocksOrderPb.NewOrderManagerClient(ussConn)
	accountManagerClient := account.NewAccountManagerClient(ussConn)
	catalogManagerClient := usscatalogPb.NewCatalogManagerClient(ussConn)

	securitiesConn := epifigrpc.NewConnByService(cfg.SECURITIES_SERVICE)
	defer epifigrpc.CloseConn(securitiesConn)
	catalogClient := catalogPb.NewSecuritiesCatalogClient(securitiesConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actorPb.NewActorClient(actorConn)

	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	commsClient := commsPb.NewCommsClient(commsConn)

	redisClient := storage.NewRedisClientFromConfig(conf.USStocksRedisOptions, true)
	ussRedisStore := cache.NewRedisCacheStorage(redisClient)

	staticConf, err := insightsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INSIGHTS_SERVICE))
		defer os.Exit(1)
		return
	}
	staticConfClone := deepcopy.Copy(staticConf).(*insightsconf.Config)
	insightsGenConf, _ := insightsgconf.NewConfig()
	err = insightsGenConf.Set(staticConfClone, false, nil)
	if err != nil {
		logger.Error(ctx, "failed to static config in dynamic config", zap.Error(err), zap.Any("serviceGroup", cfg.INSIGHTS_SERVICE))
		defer os.Exit(1)
		return
	}

	llmHandler := pkgllmWire.InitialiseLLMHandler(insightsGenConf.LLMHandlerConfig())

	slackClient := slack.New(conf.Secrets.Ids[config.SlackBotOauthToken], slack.OptionDebug(false))

	// ---- ADD YOUR JOB PROCESSOR DETAILS HERE ----
	var jobProcessors = map[string]*JobConfig{
		"GENERATE_TCS_REPORTING_FILES": {
			validTill:     &date.Date{Year: 2025, Month: 4, Day: 15},
			isProdAllowed: true,
			processor: &jobGenerateTCSReportingFiles{
				iftFileGenClient: iftFileGenClient,
			},
		},
		"VERIFY_PORTFOLIO_VALUE": {
			validTill:     &date.Date{Year: 2025, Month: 4, Day: 30},
			isProdAllowed: true,
			processor: &jobVerifyPortfolioValue{
				netWorthClient:            netWorthClient,
				investmentAnalyticsClient: investmentAnalyticsClient,
				mfExternalOrdersClient:    mfExternalOrdersClient,
				mfAnalyticsVg:             mfAnalyticsVg,
			},
		},
		"MF_CATALOG_CATEGORY_AVG": {
			validTill:     &date.Date{Year: 2025, Month: 12, Day: 30},
			isProdAllowed: true,
			processor: &jobMfCatalogCategoryAvg{
				catalogSchClient: catalogSchClient,
			},
		},
		"VERIFY_MOENGAGE_CONTENT_API": {
			validTill:     &date.Date{Year: 2025, Month: 4, Day: 24},
			isProdAllowed: true,
			processor: &jobVerifyContentApi{
				moengageClient: moengageClient,
			},
		},
		"USS_STOCK_ID_MIGRATION": {
			validTill:     &date.Date{Year: 2025, Month: 4, Day: 30},
			isProdAllowed: true,
			processor: &jobUssStockIdMigration{
				ussAlpacaDB: ussAlpacaDB,
			},
		},
		"MF_OBSOLETE_FUNDS_BACKFILL": {
			validTill:     &date.Date{Year: 2030, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobMfObsoleteFundsBackfill{
				mfVGClient: mfVGClient,
				mfDao:      mfDao,
			},
		},
		"CANCEL_USS_ORDERS": {
			validTill:     &date.Date{Year: 2025, Month: 05, Day: 13},
			isProdAllowed: true,
			processor: &jobCancelOrders{
				orderManagerClient: orderMangerClient,
			},
		},
		"STORE_USS_HISTORICAL_PRICES": {
			validTill:     &date.Date{Year: 2025, Month: 07, Day: 30},
			isProdAllowed: true,
			processor: &storeUssHistoricalPricesInRedis{
				vgStocksClient: vgStocksClient,
				ussRedisStore:  ussRedisStore,
			},
		},
		"REFRESH_SECURITY_DETAILS": {
			validTill:     &date.Date{Year: 2035, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobRefreshSecuritiesCatalog{
				stocksDb:                      stocksDb,
				stocksRefreshCatalogPublisher: stocksRefreshCatalogPublisher,
			},
		},
		"SECURITY_HISTORICAL_PRICE": {
			validTill:     &date.Date{Year: 2035, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobSecurityHistoricalPrice{
				stocksDb:                           stocksDb,
				securitiesHistoricalPricePublisher: securitiesHistoricalPricePublisher,
			},
		},
		"ADD_NEW_SECURITIES": {
			validTill:     &date.Date{Year: 2035, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobAddNewSecurities{
				catalogClient: catalogClient,
			},
		},
		"UPDATE_SECURITIES_LISTINGS_ISIN": {
			validTill:     &date.Date{Year: 2035, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobUpdateSecuritiesListingsISIN{
				StocksDb: stocksDb,
			},
		},
		"UPDATE_INVESTOR_ADDRESS": {
			validTill:     &date.Date{Year: 2025, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &updateInvestorAddressJob{
				accountManagerSvc: accountManagerClient,
			},
		},
		"USS_WALLET_FUND_TRANSFER_JOURNAL": {
			validTill:     &date.Date{Year: 2025, Month: 06, Day: 10},
			isProdAllowed: true,
			processor: &jobUssWalletFundTransferJournal{
				walletOrderDao: WalletOrderDao,
				vgStocksClient: vgStocksClient,
				config:         conf,
			},
		},
		"PUBLISH_CA_NEW_DATA_FETCH_EVENT": {
			validTill:     &date.Date{Year: 2025, Month: 07, Day: 01},
			isProdAllowed: true,
			processor: &jobPublishCaNewDataFetchEvent{
				caNewDataFetchEventPublisher: caNewDataFetchEventPublisher,
			},
		},
		"MAGIC_IMPORT_CACHE_SYSTEM_CONTEXT": {
			validTill:     &date.Date{Year: 2030, Month: 07, Day: 01},
			isProdAllowed: true,
			processor: &jobMagicImportCacheSystemContext{
				gconf:      insightsGenConf,
				llmHandler: llmHandler,
			},
		},
		"INDIAN_STOCK_LOGO_URLS": {
			validTill:     &date.Date{Year: 2025, Month: 07, Day: 31},
			isProdAllowed: true,
			processor: &jobIndianStocksLogoUrls{
				SecuritiesDao:       securitiesDao,
				SecurityListingsDao: securityListingsDao,
			},
		},
		"ROAST_AND_TOAST_SEND_PN": {
			validTill:     &date.Date{Year: 2030, Month: 07, Day: 01},
			isProdAllowed: true,
			processor: &jobRoastAndToastSendPN{
				netWorthClient: netWorthClient,
				llmHandler:     llmHandler,
				commsClient:    commsClient,
				actorClient:    actorClient,
				slackClient:    slackClient,
			},
		},
		"USS_SELL_ALL_FOR_SYMBOL": {
			validTill:     &date.Date{Year: 2025, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobUssSellAllForSymbol{
				catalogManagerClient: catalogManagerClient,
				orderManagerClient:   orderMangerClient,
			},
		},
		"GET_TRADING_ACCOUNT_DETAILS": {
			validTill:     &date.Date{Year: 2025, Month: 12, Day: 31},
			isProdAllowed: true,
			processor: &jobGetTradingAccountDetails{
				vgStocksClient: vgStocksClient,
				s3Client:       s3Client,
				config:         conf,
				ussAlpacaDB:    ussAlpacaDB,
			},
		},
	}

	cleanedInputJob := *inputJob
	fmt.Printf("\n JOB: '%v' \n", *inputJob)

	jobDetails := jobProcessors[cleanedInputJob]
	if jobDetails == nil {
		fmt.Printf("\n JOB PROCESSOR DETAILS NOT FOUND FOR JOB: '%v' \n", cleanedInputJob)
		defer os.Exit(1)
		return
	}

	if err = jobDetails.validateJobDetails(); err != nil {
		fmt.Printf("\n JOB VALIDATION ERROR: '%v' \n", err)
		defer os.Exit(1)
		return
	}

	fmt.Println("---------------------------------- JOB START ----------------------------------")
	jobProcessor := jobDetails.processor
	if err = jobProcessor.PerformJob(ctx, &JobRequest{
		Job:   cleanedInputJob,
		Args1: *jobArgs1,
		Args2: *jobArgs2,
	}); err != nil {
		logger.Error(ctx, fmt.Sprintf("error in job: %v", cleanedInputJob), zap.Error(err))
		defer os.Exit(1)
		return
	}
	fmt.Println("---------------------------------- JOB END ------------------------------------")
}

func initPublisher(ctx context.Context, sqsClient *sqs.Client, pubConf *cfg.SqsPublisher) queue.Publisher {
	pub, err := sqsPkg.NewPublisherWithConfig(ctx, pubConf, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to initialize publisher", zap.Error(err))
	}
	return pub
}
