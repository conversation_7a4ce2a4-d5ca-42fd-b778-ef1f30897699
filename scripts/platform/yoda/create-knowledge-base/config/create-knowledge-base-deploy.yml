Environment: "deploy"

YodaDb:
  DbServerAlias: "JARVIS_RDS"
  DbType: "PGDB"
  AppName: "yoda"
  StatementTimeout: 10m
  EnableDebug: true
  Name: "yoda"
  SSLMode: "verify-full"
  SecretName: "deploy/rds/deploy-jarvis/yoda_admin_user"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  SSLRootCert: "deploy/rds/rds-ca-root-2061"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

YodaBucketName: "epifi-deploy-yoda-knowledge-base"

Gemini:
  GeminiSecret: "deploy/gemini-secret"
  FoundationModel: "gemini-2.5-flash-preview-05-20"
  EmbeddingModel: "gemini-embedding-001"

OpensearchConfig:
  Address:    "https://opensearch.deploy.pointz.in"
  OpensearchSecret: "deploy/opensearch-yoda-secret"
