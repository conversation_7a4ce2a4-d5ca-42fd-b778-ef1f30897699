# the endpoints mentioned in this file are to be used for inter service communication strictly
# *.staging.pointz.in doesn't work outside env vpc.
# In order to access any service outside env vpc separate public DNS entry must be created
# against respective service
AtlasEndpoint:
  Host: "atlas.staging.pointz.in"
  Port: 8000
  IsSecure: true
CardEndPoint:
  Host: "card.staging.pointz.in"
  Port: 9504
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-card"
    Options:
      - Key: "secure-fallback"
        Value: "card.staging.pointz.in:9504"
CentralGrowthEndpoint:
  Host: "central-growth.staging.pointz.in"
  Port: 9533
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-central-growth"
    Options:
      - Key: "secure-fallback"
        Value: "central-growth.staging.pointz.in:9533"
CollateralMgrTspEndpoint:
  Host: "collateralmgrtsp.staging.pointz.in"
  Port: 9537
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-collateralmgrtsp"
    Options:
      - Key: "secure-fallback"
        Value: "collateralmgrtsp.staging.pointz.in:9531"
CxEndPoint:
  Host: "cx.staging.pointz.in"
  Port: 9508
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-cx"
    Options:
      - Key: "secure-fallback"
        Value: "cx.staging.pointz.in:9508"
FrontendEndpoint:
  Host: "frontend.staging.pointz.in"
  Port: 8082
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-frontend"
    Options:
      - Key: "secure-fallback"
        Value: "frontend.staging.pointz.in:8082"
GrowthInfraEndpoint:
  Host: "growth-infra.staging.pointz.in"
  Port: 9534
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-growth-infra"
    Options:
      - Key: "secure-fallback"
        Value: "growth-infra.staging.pointz.in:9534"
OnboardingEndpoint:
  Host: "onboarding.staging.pointz.in"
  Port: 9535
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-onboarding"
    Options:
      - Key: "secure-fallback"
        Value: "onboarding.staging.pointz.in:9535"
PayEndpoint:
  Host: "pay.staging.pointz.in"
  Port: 9536
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-pay"
    Options:
      - Key: "secure-fallback"
        Value: "pay.staging.pointz.in:9536"
RudderEndPoint:
  Host: "internal-int-staging-dataplatform-1668070325.ap-south-1.elb.amazonaws.com"
  Port: 7001
NluEndpoint:
  Host: "nlu-engine.data-dev.pointz.in"
SmartGptEndpoint:
  Host: "smart-gpt.data-dev.pointz.in"
SimulatorGrpcEndpoint:
  Host: "simulator.staging.pointz.in"
  Port: 9090
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-simulator"
    Options:
      - Key: "secure-fallback"
        Value: "simulator.staging.pointz.in:9090"
SimulatorHttpEndpoint:
  Host: "simulator.staging.pointz.in"
  Port: 8080
TokenizerEndpoint:
  Host: "tokenizer.staging.pointz.in"
  Port: 9520
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-tokenizer"
    Options:
      - Key: "secure-fallback"
        Value: "tokenizer.staging.pointz.in:9520"
UserriskEndpoint:
  Host: "userrisk.staging.pointz.in"
  Port: 9531
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-userrisk"
    Options:
      - Key: "secure-fallback"
        Value: "userrisk.staging.pointz.in:9531"
VendorgatewayEndpoint:
  Host: "vendorgateway.staging.pointz.in"
  Port: 9522
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-vendorgateway"
    Options:
      - Key: "secure-fallback"
        Value: "vendorgateway.staging.pointz.in:9522"
VendormappingEndpoint:
  Host: "vendormapping.staging.pointz.in"
  Port: 9523
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-vendormapping"
    Options:
      - Key: "secure-fallback"
        Value: "vendormapping.staging.pointz.in:9523"
# VNotificationGw's GRPC server runs on 9098 port but it's load balancer maps port 443 to port 9098
VNotificationGwEndPoint:
  Host: "vnotificationgw.staging.pointz.in"
VendorNotificationEndPoint:
  Host: "vendornotification.staging.pointz.in"
  Port: 9524
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-vendornotification"
    Options:
      - Key: "secure-fallback"
        Value: "vendornotification.staging.pointz.in:9524"

VendorGatewayPCIEndPoint:
  Host: "vendorgateway-pci.staging.pointz.in"
  Port: 9527
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-vendorgateway-pci"
    Options:
      - Key: "secure-fallback"
        Value: "vendorgateway-pci.staging.pointz.in:9527"
OpenTelemetryCollector:
  Host: "otel-collector.pointz.in"
  Port: 443
  IsSecure: true
PyroscopeProfilingEndPoint:
  Host: "pyroscope.deploy.pointz.in"
  IsSecure: true
TemporalFrontend:
  Host: "temporal-frontend-nlb.deploy.pointz.in"
  Port: 7233
TemporalNewFrontend:
  Host: "temporal-frontend-v2-nlb.deploy.pointz.in"
  Port: 7233
LendingEndPoint:
  Host: "lending.staging.pointz.in"
  Port: 9528
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-lending"
    Options:
      - Key: "secure-fallback"
        Value: "lending.staging.pointz.in:9528"
NebulaEndPoint:
  Host: "nebula.staging.pointz.in"
  Port: 9529
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-nebula"
    Options:
      - Key: "secure-fallback"
        Value: "nebula.staging.pointz.in:9529"

WealthDmfEndpoint:
  Host: "wealthdmf.staging.pointz.in"
  Port: 9532
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-wealthdmf"
    Options:
      - Key: "secure-fallback"
        Value: "wealthdmf.staging.pointz.in:9532"
AuthWorkerEndpoint:
  Host: "staging-auth-worker-green"
  Port: 9000
  IsSecure: false
BankCustomerWorkerEndpoint:
  Host: "staging-bank-customer-worker-green"
  Port: 9000
  IsSecure: false
CardWorkerEndpoint:
  Host: "staging-card-worker-green"
  Port: 9000
  IsSecure: false
CreditReportWorkerEndpoint:
  Host: "staging-credit-report-worker-green"
  Port: 9000
  IsSecure: false
CxWorkerEndpoint:
  Host: "staging-cx-worker-green"
  Port: 9000
  IsSecure: false
DepositWorkerEndpoint:
  Host: "staging-deposit-worker-green"
  Port: 9000
  IsSecure: false
FireflyWorkerEndpoint:
  Host: "staging-firefly-worker-green"
  Port: 9000
  IsSecure: false
HealthEngineWorkerEndpoint:
  Host: "staging-health-engine-worker-green"
  Port: 9000
  IsSecure: false
NebulaWorkerEndpoint:
  Host: "staging-nebula-worker-green"
  Port: 9000
  IsSecure: false
P2PInvestmentWorkerEndpoint:
  Host: "staging-p2pinvestment-worker-green"
  Port: 9000
  IsSecure: false
PanWorkerEndpoint:
  Host: "staging-pan-worker-green"
  Port: 9000
  IsSecure: false
ParserWorkerEndpoint:
  Host: "staging-parser-worker-green"
  Port: 9000
  IsSecure: false
PayWorkerEndpoint:
  Host: "staging-pay-worker-green"
  Port: 9000
  IsSecure: false
PreApprovedLoanWorkerEndpoint:
  Host: "staging-pre-approved-loan-worker-green"
  Port: 9000
  IsSecure: false
RecurringPaymentWorkerEndpoint:
  Host: "staging-recurring-payment-worker-green"
  Port: 9000
  IsSecure: false
RiskWorkerEndpoint:
  Host: "staging-risk-worker-green"
  Port: 9000
  IsSecure: false
SavingsWorkerEndpoint:
  Host: "staging-savings-worker-green"
  Port: 9000
  IsSecure: false
UpiWorkerEndpoint:
  Host: "staging-upi-worker-green"
  Port: 9000
  IsSecure: false
UsstocksWorkerEndpoint:
  Host: "staging-usstocks-worker-green"
  Port: 9000
  IsSecure: false
WealthOnboardingWorkerEndpoint:
  Host: "staging-wealth-onboarding-worker-green"
  Port: 9000
  IsSecure: false
ConnectedAccountWorkerEndpoint:
  Host: "staging-connected-account-worker-green"
  Port: 9000
  IsSecure: false

GoblinEndpoint:
  Host: "goblin.staging.pointz.in"
  Port: 9543
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-goblin"
    Options:
      - Key: "secure-fallback"
        Value: "goblin.staging.pointz.in:9543"
AurorEndpoint:
  Host: "auror.staging.stockguardian.in"
  Port: 9544
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-auror"
    Options:
      - Key: "secure-fallback"
        Value: "auror.staging.stockguardian.in:9544"
SGVendorGatewayEndpoint:
  Host: "sgvendorgateway.staging.stockguardian.in"
  Port: 9545
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-sgvendorgateway"
    Options:
      - Key: "secure-fallback"
        Value: "sgvendorgateway.staging.stockguardian.in:9545"
SGApiGatewayEndpoint:
  Host: "sgapigateway.staging.stockguardian.in"
  Port: 9546
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-sgapigateway"
    Options:
      - Key: "secure-fallback"
        Value: "sgapigateway.staging.stockguardian.in:9546"
SGExternalGatewayEndpoint:
  Host: "sgexternalgateway.staging.stockguardian.in"
  Port: 9547
  IsSecure: true
  GrpcNameResolution:
    Scheme: "consul"
    Authority: "127.0.0.1:8500"
    Path: "staging-sgexternalgateway"
    Options:
      - Key: "secure-fallback"
        Value: "sgexternalgateway.staging.stockguardian.in:9547"
HttpGwEndPoint:
  Host: "httpgw.staging.pointz.in"