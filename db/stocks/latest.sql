CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.historical_prices (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    security_listing_id character varying NOT NULL,
    price_date date NOT NULL,
    close_price jsonb NOT NULL,
    vendor character varying NOT NULL,
    price_derived_date date,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at_unix bigint DEFAULT 0 NOT NULL
);
COMMENT ON TABLE public.historical_prices IS '{"proto_type": "securities.catalog.HistoricalPrice", "comment":"Table to store historical price records for security listings."}';
COMMENT ON COLUMN public.historical_prices.id IS 'Unique identifier for this historical price record.';
COMMENT ON COLUMN public.historical_prices.security_listing_id IS 'Foreign key referencing the security listing this price belongs to.';
COMMENT ON COLUMN public.historical_prices.price_date IS 'Date for which this price is recorded (YYYY-MM-DD).';
COMMENT ON COLUMN public.historical_prices.close_price IS 'Closing price for the security on the given date, stored as JSONB (Money proto).';
COMMENT ON COLUMN public.historical_prices.vendor IS 'Vendor name enum (e.g., ONE_MONEY, ALPACA)';
COMMENT ON COLUMN public.historical_prices.price_derived_date IS 'Date when the price was derived or calculated. For non-trading days, this is the previous trading day; for trading days, it is the same day.';
COMMENT ON COLUMN public.historical_prices.created_at IS 'Timestamp when the record was created.';
COMMENT ON COLUMN public.historical_prices.updated_at IS 'Timestamp when the record was last updated.';
COMMENT ON COLUMN public.historical_prices.deleted_at_unix IS 'Unix timestamp for soft deletion; 0 if not deleted.';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.securities (
    id character varying NOT NULL,
    security_type character varying DEFAULT 'SECURITY_TYPE_UNSPECIFIED'::character varying NOT NULL,
    security_name character varying NOT NULL,
    vendor character varying DEFAULT 'VENDOR_UNSPECIFIED'::character varying NOT NULL,
    vendor_security_id character varying NOT NULL,
    logo_url character varying,
    security_details jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at_unix bigint DEFAULT 0 NOT NULL,
    financial_info jsonb
);
COMMENT ON TABLE public.securities IS '{"proto_type": "stocks.catalog.model.Security", "comment":"table to store security related information"}';
COMMENT ON COLUMN public.securities.financial_info IS 'Financial information for this security (e.g., PE, PB, etc.), stored as JSONB';
CREATE TABLE public.security_listings (
    internal_id character varying NOT NULL,
    external_id character varying NOT NULL,
    security_id character varying NOT NULL,
    exchange character varying NOT NULL,
    symbol character varying NOT NULL,
    is_primary_listing boolean NOT NULL,
    status character varying NOT NULL,
    isin character varying,
    vendor character varying NOT NULL,
    vendor_listing_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at_unix bigint DEFAULT 0 NOT NULL
);
COMMENT ON TABLE public.security_listings IS '{"proto_type": "securities.catalog.SecurityListing", "comment":"Table to store information related to listings of securities"}';
COMMENT ON COLUMN public.security_listings.internal_id IS 'Unique identifier for this security listing (e.g., \"SL\" + random string)';
COMMENT ON COLUMN public.security_listings.external_id IS 'External id, unique identifier in the usstocks table (e.g., \"USS/INS\" + random string)';
COMMENT ON COLUMN public.security_listings.security_id IS 'Foreign key referencing the Security object this listing belongs to';
COMMENT ON COLUMN public.security_listings.exchange IS 'The exchange where this security is listed (e.g., NSE, NYSE, NASDAQ)';
COMMENT ON COLUMN public.security_listings.symbol IS 'The trading symbol/ticker for this listing on the exchange';
COMMENT ON COLUMN public.security_listings.is_primary_listing IS 'True if this is the primary listing for the security';
COMMENT ON COLUMN public.security_listings.status IS 'The current status of this listing (e.g., active, inactive)';
COMMENT ON COLUMN public.security_listings.isin IS 'International Securities Identification Number (ISIN) for this listing';
COMMENT ON COLUMN public.security_listings.vendor IS 'Vendor name enum (e.g., ONE_MONEY, ALPACA)';
COMMENT ON COLUMN public.security_listings.vendor_listing_id IS 'The vendor-side unique identifier for this listing';
ALTER TABLE ONLY public.historical_prices
    ADD CONSTRAINT historical_prices_pkey PRIMARY KEY (security_listing_id, price_date, deleted_at_unix);
COMMENT ON CONSTRAINT historical_prices_pkey ON public.historical_prices IS 'Primary key: ensures uniqueness for each (security_listing_id, price_date, deleted_at_unix) tuple.';
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.securities
    ADD CONSTRAINT securities_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.security_listings
    ADD CONSTRAINT security_listings_pkey PRIMARY KEY (internal_id);
COMMENT ON CONSTRAINT security_listings_pkey ON public.security_listings IS 'Primary key constraint on internal_id';
CREATE UNIQUE INDEX historical_prices_id_idx ON public.historical_prices USING btree (id);
CREATE INDEX historical_prices_updated_at_idx ON public.historical_prices USING btree (updated_at DESC);
CREATE UNIQUE INDEX securities_vendor_vendor_security_id_deleted_at_unq_idx ON public.securities USING btree (vendor, vendor_security_id, deleted_at_unix);
CREATE UNIQUE INDEX security_listings_exchange_symbol_deletedat_unq_idx ON public.security_listings USING btree (exchange, symbol, deleted_at_unix);
CREATE UNIQUE INDEX security_listings_externalid_deletedat_unq_idx ON public.security_listings USING btree (external_id, deleted_at_unix);
CREATE UNIQUE INDEX security_listings_isin_exchange_deletedat_unq_idx ON public.security_listings USING btree (isin, exchange, deleted_at_unix);
CREATE INDEX security_listings_updated_at_idx ON public.security_listings USING btree (updated_at DESC);
CREATE UNIQUE INDEX security_listings_vendor_vendorlistingid_deletedat_unq_idx ON public.security_listings USING btree (vendor, vendor_listing_id, deleted_at_unix);
ALTER TABLE ONLY public.security_listings
    ADD CONSTRAINT fk_security FOREIGN KEY (security_id) REFERENCES public.securities(id) ON UPDATE CASCADE ON DELETE CASCADE;
COMMENT ON CONSTRAINT fk_security ON public.security_listings IS 'Foreign key from security_listings.security_id to securities.id.';
ALTER TABLE ONLY public.historical_prices
    ADD CONSTRAINT fk_security_listing FOREIGN KEY (security_listing_id, deleted_at_unix) REFERENCES public.security_listings(external_id, deleted_at_unix) ON DELETE CASCADE;
