// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/insights/networth/service.proto

package networth

import (
	context "context"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NetWorth_GetNetWorthValue_FullMethodName                     = "/insights.networth.NetWorth/GetNetWorthValue"
	NetWorth_DeclareInvestment_FullMethodName                    = "/insights.networth.NetWorth/DeclareInvestment"
	NetWorth_UpdateInvestmentDeclaration_FullMethodName          = "/insights.networth.NetWorth/UpdateInvestmentDeclaration"
	NetWorth_GetInvestmentDeclaration_FullMethodName             = "/insights.networth.NetWorth/GetInvestmentDeclaration"
	NetWorth_GetInvestmentDeclarations_FullMethodName            = "/insights.networth.NetWorth/GetInvestmentDeclarations"
	NetWorth_DeleteInvestmentDeclaration_FullMethodName          = "/insights.networth.NetWorth/DeleteInvestmentDeclaration"
	NetWorth_UpdateBulkManualAssetsCurrentValue_FullMethodName   = "/insights.networth.NetWorth/UpdateBulkManualAssetsCurrentValue"
	NetWorth_CreateNetWorthRefreshSession_FullMethodName         = "/insights.networth.NetWorth/CreateNetWorthRefreshSession"
	NetWorth_UpdateNetWorthRefreshSession_FullMethodName         = "/insights.networth.NetWorth/UpdateNetWorthRefreshSession"
	NetWorth_GetNetWorthRefreshSession_FullMethodName            = "/insights.networth.NetWorth/GetNetWorthRefreshSession"
	NetWorth_GetNetWorthInstrumentsRefreshDetails_FullMethodName = "/insights.networth.NetWorth/GetNetWorthInstrumentsRefreshDetails"
	NetWorth_SearchAssetFormFieldOptions_FullMethodName          = "/insights.networth.NetWorth/SearchAssetFormFieldOptions"
	NetWorth_StoreSnapshot_FullMethodName                        = "/insights.networth.NetWorth/StoreSnapshot"
	NetWorth_FetchDynamicElements_FullMethodName                 = "/insights.networth.NetWorth/FetchDynamicElements"
	NetWorth_DynamicElementCallback_FullMethodName               = "/insights.networth.NetWorth/DynamicElementCallback"
	NetWorth_GetPortfolioChangeSummary_FullMethodName            = "/insights.networth.NetWorth/GetPortfolioChangeSummary"
	NetWorth_DeleteAllInvestmentDeclaration_FullMethodName       = "/insights.networth.NetWorth/DeleteAllInvestmentDeclaration"
	NetWorth_GetAssetsDayChange_FullMethodName                   = "/insights.networth.NetWorth/GetAssetsDayChange"
	NetWorth_MagicImportFiles_FullMethodName                     = "/insights.networth.NetWorth/MagicImportFiles"
	NetWorth_GetNetworthDataFile_FullMethodName                  = "/insights.networth.NetWorth/GetNetworthDataFile"
	NetWorth_GetFilesFromBucket_FullMethodName                   = "/insights.networth.NetWorth/GetFilesFromBucket"
)

// NetWorthClient is the client API for NetWorth service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NetWorthClient interface {
	// GetNetWorthValue computes current value for given assets and liabilityes
	// It returns detailed reason in case the computation fails
	// Response values may not follow request order
	GetNetWorthValue(ctx context.Context, in *GetNetWorthValueRequest, opts ...grpc.CallOption) (*GetNetWorthValueResponse, error)
	// DeclareInvestment collects declaration data from the user saves the data in DB
	DeclareInvestment(ctx context.Context, in *DeclareInvestmentRequest, opts ...grpc.CallOption) (*DeclareInvestmentResponse, error)
	// UpdateInvestmentDeclaration updates investment declared by the user
	UpdateInvestmentDeclaration(ctx context.Context, in *UpdateInvestmentDeclarationRequest, opts ...grpc.CallOption) (*UpdateInvestmentDeclarationResponse, error)
	// GetInvestmentDeclaration returns investment declaration for a given id
	GetInvestmentDeclaration(ctx context.Context, in *GetInvestmentDeclarationRequest, opts ...grpc.CallOption) (*GetInvestmentDeclarationResponse, error)
	// GetInvestmentDeclarations returns all investment(paginated) declarations for a given actor
	GetInvestmentDeclarations(ctx context.Context, in *GetInvestmentDeclarationsRequest, opts ...grpc.CallOption) (*GetInvestmentDeclarationsResponse, error)
	// DeleteInvestmentDeclaration soft deletes the investment declaration
	DeleteInvestmentDeclaration(ctx context.Context, in *DeleteInvestmentDeclarationRequest, opts ...grpc.CallOption) (*DeclareInvestmentResponse, error)
	// UpdateBulkManualAssetsCurrentValue updates current value in declaration details of manual assets
	UpdateBulkManualAssetsCurrentValue(ctx context.Context, in *UpdateBulkManualAssetsCurrentValueRequest, opts ...grpc.CallOption) (*UpdateBulkManualAssetsCurrentValueResponse, error)
	// CreateNetWorthRefreshSession collects netWorth refresh session data from the user saves the data in DB
	CreateNetWorthRefreshSession(ctx context.Context, in *CreateNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*CreateNetWorthRefreshSessionResponse, error)
	// UpdateNetWorthRefreshSession updates netWorth refresh session by the user
	UpdateNetWorthRefreshSession(ctx context.Context, in *UpdateNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*UpdateNetWorthRefreshSessionResponse, error)
	// GetNetWorthRefreshSession returns netWorth refresh session for a given id
	GetNetWorthRefreshSession(ctx context.Context, in *GetNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*GetNetWorthRefreshSessionResponse, error)
	// GetNetWorthInstrumentsRefreshDetails returns all the instrument refresh details for net worth refresh v2
	GetNetWorthInstrumentsRefreshDetails(ctx context.Context, in *GetNetWorthInstrumentsRefreshDetailsRequest, opts ...grpc.CallOption) (*GetNetWorthInstrumentsRefreshDetailsResponse, error)
	// SearchAssetFormFieldOptions returns a list of options for a form field based on a search text
	// E.g., a list of PMS provider names to be used for declaring a PMS asset
	SearchAssetFormFieldOptions(ctx context.Context, in *SearchAssetFormFieldOptionsRequest, opts ...grpc.CallOption) (*SearchAssetFormFieldOptionsResponse, error)
	// StoreSnapshot stores the snapshot of the net worth for the given actor
	// This doesn't currently support selective asset types to store snapshot and considers all asset types(excluding savings account)
	StoreSnapshot(ctx context.Context, in *StoreSnapshotRequest, opts ...grpc.CallOption) (*StoreSnapshotResponse, error)
	// RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
	// ActorId is a mandatory parameter in the Request
	// Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no elements found for the given user on this screen
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the list of targeted comms elements is fetched successfully
	FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// RPC used by the Dynamic Elements service to callback on user action on a dynamic element
	// ActorId and ElementId are mandatory parameters in the Request
	// Response contains status code
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no element exists with the given ElementId
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the callback is registered successfully
	DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error)
	// GetPortfolioChangeSummary summarizes the net worth change based on the duration provided in absolute value and percentage.
	// This rpc will respond based on the assets provided. If assets are not provided, it will respond with all assets.
	GetPortfolioChangeSummary(ctx context.Context, in *GetPortfolioChangeSummaryRequest, opts ...grpc.CallOption) (*GetPortfolioChangeSummaryResponse, error)
	// DeleteAllInvestmentDeclaration rpc will soft delete all the investment declarations for the given actor
	DeleteAllInvestmentDeclaration(ctx context.Context, in *DeleteAllInvestmentDeclarationRequest, opts ...grpc.CallOption) (*DeleteAllInvestmentDeclarationResponse, error)
	// GetAssetsDayChange returns portfolio value at given two dates and assetTypes
	// Also returns instrument distribution for each asset
	// If value for any assetType is not present, value for that asset will not be in present response map
	GetAssetsDayChange(ctx context.Context, in *GetAssetsDayChangeRequest, opts ...grpc.CallOption) (*GetAssetsDayChangeResponse, error)
	MagicImportFiles(ctx context.Context, in *MagicImportFilesRequest, opts ...grpc.CallOption) (*MagicImportFilesResponse, error)
	// GetNetworthDataFile generates a comprehensive financial data file containing user's networth summary,
	// mutual fund holdings, account aggregator data, credit reports, EPF details, and transaction history.
	// Returns base64-encoded JSON data as a downloadable text file, with concurrent data fetching for performance.
	GetNetworthDataFile(ctx context.Context, in *GetNetworthDataFileRequest, opts ...grpc.CallOption) (*GetNetworthDataFileResponse, error)
	// GetFilesFromBucket is used to get presigned urls for files stored in S3 bucket shared in the request
	// This API will be used to fetch files from S3 bucket which belongs to insights.networth service
	GetFilesFromBucket(ctx context.Context, in *GetFilesFromBucketRequest, opts ...grpc.CallOption) (*GetFilesFromBucketResponse, error)
}

type netWorthClient struct {
	cc grpc.ClientConnInterface
}

func NewNetWorthClient(cc grpc.ClientConnInterface) NetWorthClient {
	return &netWorthClient{cc}
}

func (c *netWorthClient) GetNetWorthValue(ctx context.Context, in *GetNetWorthValueRequest, opts ...grpc.CallOption) (*GetNetWorthValueResponse, error) {
	out := new(GetNetWorthValueResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetNetWorthValue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) DeclareInvestment(ctx context.Context, in *DeclareInvestmentRequest, opts ...grpc.CallOption) (*DeclareInvestmentResponse, error) {
	out := new(DeclareInvestmentResponse)
	err := c.cc.Invoke(ctx, NetWorth_DeclareInvestment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) UpdateInvestmentDeclaration(ctx context.Context, in *UpdateInvestmentDeclarationRequest, opts ...grpc.CallOption) (*UpdateInvestmentDeclarationResponse, error) {
	out := new(UpdateInvestmentDeclarationResponse)
	err := c.cc.Invoke(ctx, NetWorth_UpdateInvestmentDeclaration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetInvestmentDeclaration(ctx context.Context, in *GetInvestmentDeclarationRequest, opts ...grpc.CallOption) (*GetInvestmentDeclarationResponse, error) {
	out := new(GetInvestmentDeclarationResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetInvestmentDeclaration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetInvestmentDeclarations(ctx context.Context, in *GetInvestmentDeclarationsRequest, opts ...grpc.CallOption) (*GetInvestmentDeclarationsResponse, error) {
	out := new(GetInvestmentDeclarationsResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetInvestmentDeclarations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) DeleteInvestmentDeclaration(ctx context.Context, in *DeleteInvestmentDeclarationRequest, opts ...grpc.CallOption) (*DeclareInvestmentResponse, error) {
	out := new(DeclareInvestmentResponse)
	err := c.cc.Invoke(ctx, NetWorth_DeleteInvestmentDeclaration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) UpdateBulkManualAssetsCurrentValue(ctx context.Context, in *UpdateBulkManualAssetsCurrentValueRequest, opts ...grpc.CallOption) (*UpdateBulkManualAssetsCurrentValueResponse, error) {
	out := new(UpdateBulkManualAssetsCurrentValueResponse)
	err := c.cc.Invoke(ctx, NetWorth_UpdateBulkManualAssetsCurrentValue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) CreateNetWorthRefreshSession(ctx context.Context, in *CreateNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*CreateNetWorthRefreshSessionResponse, error) {
	out := new(CreateNetWorthRefreshSessionResponse)
	err := c.cc.Invoke(ctx, NetWorth_CreateNetWorthRefreshSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) UpdateNetWorthRefreshSession(ctx context.Context, in *UpdateNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*UpdateNetWorthRefreshSessionResponse, error) {
	out := new(UpdateNetWorthRefreshSessionResponse)
	err := c.cc.Invoke(ctx, NetWorth_UpdateNetWorthRefreshSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetNetWorthRefreshSession(ctx context.Context, in *GetNetWorthRefreshSessionRequest, opts ...grpc.CallOption) (*GetNetWorthRefreshSessionResponse, error) {
	out := new(GetNetWorthRefreshSessionResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetNetWorthRefreshSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetNetWorthInstrumentsRefreshDetails(ctx context.Context, in *GetNetWorthInstrumentsRefreshDetailsRequest, opts ...grpc.CallOption) (*GetNetWorthInstrumentsRefreshDetailsResponse, error) {
	out := new(GetNetWorthInstrumentsRefreshDetailsResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetNetWorthInstrumentsRefreshDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) SearchAssetFormFieldOptions(ctx context.Context, in *SearchAssetFormFieldOptionsRequest, opts ...grpc.CallOption) (*SearchAssetFormFieldOptionsResponse, error) {
	out := new(SearchAssetFormFieldOptionsResponse)
	err := c.cc.Invoke(ctx, NetWorth_SearchAssetFormFieldOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) StoreSnapshot(ctx context.Context, in *StoreSnapshotRequest, opts ...grpc.CallOption) (*StoreSnapshotResponse, error) {
	out := new(StoreSnapshotResponse)
	err := c.cc.Invoke(ctx, NetWorth_StoreSnapshot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	out := new(dynamic_elements.FetchDynamicElementsResponse)
	err := c.cc.Invoke(ctx, NetWorth_FetchDynamicElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	out := new(dynamic_elements.DynamicElementCallbackResponse)
	err := c.cc.Invoke(ctx, NetWorth_DynamicElementCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetPortfolioChangeSummary(ctx context.Context, in *GetPortfolioChangeSummaryRequest, opts ...grpc.CallOption) (*GetPortfolioChangeSummaryResponse, error) {
	out := new(GetPortfolioChangeSummaryResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetPortfolioChangeSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) DeleteAllInvestmentDeclaration(ctx context.Context, in *DeleteAllInvestmentDeclarationRequest, opts ...grpc.CallOption) (*DeleteAllInvestmentDeclarationResponse, error) {
	out := new(DeleteAllInvestmentDeclarationResponse)
	err := c.cc.Invoke(ctx, NetWorth_DeleteAllInvestmentDeclaration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetAssetsDayChange(ctx context.Context, in *GetAssetsDayChangeRequest, opts ...grpc.CallOption) (*GetAssetsDayChangeResponse, error) {
	out := new(GetAssetsDayChangeResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetAssetsDayChange_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) MagicImportFiles(ctx context.Context, in *MagicImportFilesRequest, opts ...grpc.CallOption) (*MagicImportFilesResponse, error) {
	out := new(MagicImportFilesResponse)
	err := c.cc.Invoke(ctx, NetWorth_MagicImportFiles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetNetworthDataFile(ctx context.Context, in *GetNetworthDataFileRequest, opts ...grpc.CallOption) (*GetNetworthDataFileResponse, error) {
	out := new(GetNetworthDataFileResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetNetworthDataFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *netWorthClient) GetFilesFromBucket(ctx context.Context, in *GetFilesFromBucketRequest, opts ...grpc.CallOption) (*GetFilesFromBucketResponse, error) {
	out := new(GetFilesFromBucketResponse)
	err := c.cc.Invoke(ctx, NetWorth_GetFilesFromBucket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NetWorthServer is the server API for NetWorth service.
// All implementations should embed UnimplementedNetWorthServer
// for forward compatibility
type NetWorthServer interface {
	// GetNetWorthValue computes current value for given assets and liabilityes
	// It returns detailed reason in case the computation fails
	// Response values may not follow request order
	GetNetWorthValue(context.Context, *GetNetWorthValueRequest) (*GetNetWorthValueResponse, error)
	// DeclareInvestment collects declaration data from the user saves the data in DB
	DeclareInvestment(context.Context, *DeclareInvestmentRequest) (*DeclareInvestmentResponse, error)
	// UpdateInvestmentDeclaration updates investment declared by the user
	UpdateInvestmentDeclaration(context.Context, *UpdateInvestmentDeclarationRequest) (*UpdateInvestmentDeclarationResponse, error)
	// GetInvestmentDeclaration returns investment declaration for a given id
	GetInvestmentDeclaration(context.Context, *GetInvestmentDeclarationRequest) (*GetInvestmentDeclarationResponse, error)
	// GetInvestmentDeclarations returns all investment(paginated) declarations for a given actor
	GetInvestmentDeclarations(context.Context, *GetInvestmentDeclarationsRequest) (*GetInvestmentDeclarationsResponse, error)
	// DeleteInvestmentDeclaration soft deletes the investment declaration
	DeleteInvestmentDeclaration(context.Context, *DeleteInvestmentDeclarationRequest) (*DeclareInvestmentResponse, error)
	// UpdateBulkManualAssetsCurrentValue updates current value in declaration details of manual assets
	UpdateBulkManualAssetsCurrentValue(context.Context, *UpdateBulkManualAssetsCurrentValueRequest) (*UpdateBulkManualAssetsCurrentValueResponse, error)
	// CreateNetWorthRefreshSession collects netWorth refresh session data from the user saves the data in DB
	CreateNetWorthRefreshSession(context.Context, *CreateNetWorthRefreshSessionRequest) (*CreateNetWorthRefreshSessionResponse, error)
	// UpdateNetWorthRefreshSession updates netWorth refresh session by the user
	UpdateNetWorthRefreshSession(context.Context, *UpdateNetWorthRefreshSessionRequest) (*UpdateNetWorthRefreshSessionResponse, error)
	// GetNetWorthRefreshSession returns netWorth refresh session for a given id
	GetNetWorthRefreshSession(context.Context, *GetNetWorthRefreshSessionRequest) (*GetNetWorthRefreshSessionResponse, error)
	// GetNetWorthInstrumentsRefreshDetails returns all the instrument refresh details for net worth refresh v2
	GetNetWorthInstrumentsRefreshDetails(context.Context, *GetNetWorthInstrumentsRefreshDetailsRequest) (*GetNetWorthInstrumentsRefreshDetailsResponse, error)
	// SearchAssetFormFieldOptions returns a list of options for a form field based on a search text
	// E.g., a list of PMS provider names to be used for declaring a PMS asset
	SearchAssetFormFieldOptions(context.Context, *SearchAssetFormFieldOptionsRequest) (*SearchAssetFormFieldOptionsResponse, error)
	// StoreSnapshot stores the snapshot of the net worth for the given actor
	// This doesn't currently support selective asset types to store snapshot and considers all asset types(excluding savings account)
	StoreSnapshot(context.Context, *StoreSnapshotRequest) (*StoreSnapshotResponse, error)
	// RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
	// ActorId is a mandatory parameter in the Request
	// Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no elements found for the given user on this screen
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the list of targeted comms elements is fetched successfully
	FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// RPC used by the Dynamic Elements service to callback on user action on a dynamic element
	// ActorId and ElementId are mandatory parameters in the Request
	// Response contains status code
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no element exists with the given ElementId
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the callback is registered successfully
	DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error)
	// GetPortfolioChangeSummary summarizes the net worth change based on the duration provided in absolute value and percentage.
	// This rpc will respond based on the assets provided. If assets are not provided, it will respond with all assets.
	GetPortfolioChangeSummary(context.Context, *GetPortfolioChangeSummaryRequest) (*GetPortfolioChangeSummaryResponse, error)
	// DeleteAllInvestmentDeclaration rpc will soft delete all the investment declarations for the given actor
	DeleteAllInvestmentDeclaration(context.Context, *DeleteAllInvestmentDeclarationRequest) (*DeleteAllInvestmentDeclarationResponse, error)
	// GetAssetsDayChange returns portfolio value at given two dates and assetTypes
	// Also returns instrument distribution for each asset
	// If value for any assetType is not present, value for that asset will not be in present response map
	GetAssetsDayChange(context.Context, *GetAssetsDayChangeRequest) (*GetAssetsDayChangeResponse, error)
	MagicImportFiles(context.Context, *MagicImportFilesRequest) (*MagicImportFilesResponse, error)
	// GetNetworthDataFile generates a comprehensive financial data file containing user's networth summary,
	// mutual fund holdings, account aggregator data, credit reports, EPF details, and transaction history.
	// Returns base64-encoded JSON data as a downloadable text file, with concurrent data fetching for performance.
	GetNetworthDataFile(context.Context, *GetNetworthDataFileRequest) (*GetNetworthDataFileResponse, error)
	// GetFilesFromBucket is used to get presigned urls for files stored in S3 bucket shared in the request
	// This API will be used to fetch files from S3 bucket which belongs to insights.networth service
	GetFilesFromBucket(context.Context, *GetFilesFromBucketRequest) (*GetFilesFromBucketResponse, error)
}

// UnimplementedNetWorthServer should be embedded to have forward compatible implementations.
type UnimplementedNetWorthServer struct {
}

func (UnimplementedNetWorthServer) GetNetWorthValue(context.Context, *GetNetWorthValueRequest) (*GetNetWorthValueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNetWorthValue not implemented")
}
func (UnimplementedNetWorthServer) DeclareInvestment(context.Context, *DeclareInvestmentRequest) (*DeclareInvestmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeclareInvestment not implemented")
}
func (UnimplementedNetWorthServer) UpdateInvestmentDeclaration(context.Context, *UpdateInvestmentDeclarationRequest) (*UpdateInvestmentDeclarationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInvestmentDeclaration not implemented")
}
func (UnimplementedNetWorthServer) GetInvestmentDeclaration(context.Context, *GetInvestmentDeclarationRequest) (*GetInvestmentDeclarationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvestmentDeclaration not implemented")
}
func (UnimplementedNetWorthServer) GetInvestmentDeclarations(context.Context, *GetInvestmentDeclarationsRequest) (*GetInvestmentDeclarationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvestmentDeclarations not implemented")
}
func (UnimplementedNetWorthServer) DeleteInvestmentDeclaration(context.Context, *DeleteInvestmentDeclarationRequest) (*DeclareInvestmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInvestmentDeclaration not implemented")
}
func (UnimplementedNetWorthServer) UpdateBulkManualAssetsCurrentValue(context.Context, *UpdateBulkManualAssetsCurrentValueRequest) (*UpdateBulkManualAssetsCurrentValueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBulkManualAssetsCurrentValue not implemented")
}
func (UnimplementedNetWorthServer) CreateNetWorthRefreshSession(context.Context, *CreateNetWorthRefreshSessionRequest) (*CreateNetWorthRefreshSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNetWorthRefreshSession not implemented")
}
func (UnimplementedNetWorthServer) UpdateNetWorthRefreshSession(context.Context, *UpdateNetWorthRefreshSessionRequest) (*UpdateNetWorthRefreshSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNetWorthRefreshSession not implemented")
}
func (UnimplementedNetWorthServer) GetNetWorthRefreshSession(context.Context, *GetNetWorthRefreshSessionRequest) (*GetNetWorthRefreshSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNetWorthRefreshSession not implemented")
}
func (UnimplementedNetWorthServer) GetNetWorthInstrumentsRefreshDetails(context.Context, *GetNetWorthInstrumentsRefreshDetailsRequest) (*GetNetWorthInstrumentsRefreshDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNetWorthInstrumentsRefreshDetails not implemented")
}
func (UnimplementedNetWorthServer) SearchAssetFormFieldOptions(context.Context, *SearchAssetFormFieldOptionsRequest) (*SearchAssetFormFieldOptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAssetFormFieldOptions not implemented")
}
func (UnimplementedNetWorthServer) StoreSnapshot(context.Context, *StoreSnapshotRequest) (*StoreSnapshotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreSnapshot not implemented")
}
func (UnimplementedNetWorthServer) FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchDynamicElements not implemented")
}
func (UnimplementedNetWorthServer) DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DynamicElementCallback not implemented")
}
func (UnimplementedNetWorthServer) GetPortfolioChangeSummary(context.Context, *GetPortfolioChangeSummaryRequest) (*GetPortfolioChangeSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPortfolioChangeSummary not implemented")
}
func (UnimplementedNetWorthServer) DeleteAllInvestmentDeclaration(context.Context, *DeleteAllInvestmentDeclarationRequest) (*DeleteAllInvestmentDeclarationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAllInvestmentDeclaration not implemented")
}
func (UnimplementedNetWorthServer) GetAssetsDayChange(context.Context, *GetAssetsDayChangeRequest) (*GetAssetsDayChangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssetsDayChange not implemented")
}
func (UnimplementedNetWorthServer) MagicImportFiles(context.Context, *MagicImportFilesRequest) (*MagicImportFilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MagicImportFiles not implemented")
}
func (UnimplementedNetWorthServer) GetNetworthDataFile(context.Context, *GetNetworthDataFileRequest) (*GetNetworthDataFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNetworthDataFile not implemented")
}
func (UnimplementedNetWorthServer) GetFilesFromBucket(context.Context, *GetFilesFromBucketRequest) (*GetFilesFromBucketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFilesFromBucket not implemented")
}

// UnsafeNetWorthServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NetWorthServer will
// result in compilation errors.
type UnsafeNetWorthServer interface {
	mustEmbedUnimplementedNetWorthServer()
}

func RegisterNetWorthServer(s grpc.ServiceRegistrar, srv NetWorthServer) {
	s.RegisterService(&NetWorth_ServiceDesc, srv)
}

func _NetWorth_GetNetWorthValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNetWorthValueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetNetWorthValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetNetWorthValue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetNetWorthValue(ctx, req.(*GetNetWorthValueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_DeclareInvestment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeclareInvestmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).DeclareInvestment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_DeclareInvestment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).DeclareInvestment(ctx, req.(*DeclareInvestmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_UpdateInvestmentDeclaration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInvestmentDeclarationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).UpdateInvestmentDeclaration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_UpdateInvestmentDeclaration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).UpdateInvestmentDeclaration(ctx, req.(*UpdateInvestmentDeclarationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetInvestmentDeclaration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvestmentDeclarationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetInvestmentDeclaration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetInvestmentDeclaration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetInvestmentDeclaration(ctx, req.(*GetInvestmentDeclarationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetInvestmentDeclarations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvestmentDeclarationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetInvestmentDeclarations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetInvestmentDeclarations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetInvestmentDeclarations(ctx, req.(*GetInvestmentDeclarationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_DeleteInvestmentDeclaration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInvestmentDeclarationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).DeleteInvestmentDeclaration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_DeleteInvestmentDeclaration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).DeleteInvestmentDeclaration(ctx, req.(*DeleteInvestmentDeclarationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_UpdateBulkManualAssetsCurrentValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBulkManualAssetsCurrentValueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).UpdateBulkManualAssetsCurrentValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_UpdateBulkManualAssetsCurrentValue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).UpdateBulkManualAssetsCurrentValue(ctx, req.(*UpdateBulkManualAssetsCurrentValueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_CreateNetWorthRefreshSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNetWorthRefreshSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).CreateNetWorthRefreshSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_CreateNetWorthRefreshSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).CreateNetWorthRefreshSession(ctx, req.(*CreateNetWorthRefreshSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_UpdateNetWorthRefreshSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNetWorthRefreshSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).UpdateNetWorthRefreshSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_UpdateNetWorthRefreshSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).UpdateNetWorthRefreshSession(ctx, req.(*UpdateNetWorthRefreshSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetNetWorthRefreshSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNetWorthRefreshSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetNetWorthRefreshSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetNetWorthRefreshSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetNetWorthRefreshSession(ctx, req.(*GetNetWorthRefreshSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetNetWorthInstrumentsRefreshDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNetWorthInstrumentsRefreshDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetNetWorthInstrumentsRefreshDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetNetWorthInstrumentsRefreshDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetNetWorthInstrumentsRefreshDetails(ctx, req.(*GetNetWorthInstrumentsRefreshDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_SearchAssetFormFieldOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAssetFormFieldOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).SearchAssetFormFieldOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_SearchAssetFormFieldOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).SearchAssetFormFieldOptions(ctx, req.(*SearchAssetFormFieldOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_StoreSnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreSnapshotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).StoreSnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_StoreSnapshot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).StoreSnapshot(ctx, req.(*StoreSnapshotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_FetchDynamicElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.FetchDynamicElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).FetchDynamicElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_FetchDynamicElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).FetchDynamicElements(ctx, req.(*dynamic_elements.FetchDynamicElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_DynamicElementCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.DynamicElementCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).DynamicElementCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_DynamicElementCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).DynamicElementCallback(ctx, req.(*dynamic_elements.DynamicElementCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetPortfolioChangeSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPortfolioChangeSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetPortfolioChangeSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetPortfolioChangeSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetPortfolioChangeSummary(ctx, req.(*GetPortfolioChangeSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_DeleteAllInvestmentDeclaration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAllInvestmentDeclarationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).DeleteAllInvestmentDeclaration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_DeleteAllInvestmentDeclaration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).DeleteAllInvestmentDeclaration(ctx, req.(*DeleteAllInvestmentDeclarationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetAssetsDayChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssetsDayChangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetAssetsDayChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetAssetsDayChange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetAssetsDayChange(ctx, req.(*GetAssetsDayChangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_MagicImportFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MagicImportFilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).MagicImportFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_MagicImportFiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).MagicImportFiles(ctx, req.(*MagicImportFilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetNetworthDataFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNetworthDataFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetNetworthDataFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetNetworthDataFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetNetworthDataFile(ctx, req.(*GetNetworthDataFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NetWorth_GetFilesFromBucket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilesFromBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NetWorthServer).GetFilesFromBucket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NetWorth_GetFilesFromBucket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NetWorthServer).GetFilesFromBucket(ctx, req.(*GetFilesFromBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NetWorth_ServiceDesc is the grpc.ServiceDesc for NetWorth service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NetWorth_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "insights.networth.NetWorth",
	HandlerType: (*NetWorthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNetWorthValue",
			Handler:    _NetWorth_GetNetWorthValue_Handler,
		},
		{
			MethodName: "DeclareInvestment",
			Handler:    _NetWorth_DeclareInvestment_Handler,
		},
		{
			MethodName: "UpdateInvestmentDeclaration",
			Handler:    _NetWorth_UpdateInvestmentDeclaration_Handler,
		},
		{
			MethodName: "GetInvestmentDeclaration",
			Handler:    _NetWorth_GetInvestmentDeclaration_Handler,
		},
		{
			MethodName: "GetInvestmentDeclarations",
			Handler:    _NetWorth_GetInvestmentDeclarations_Handler,
		},
		{
			MethodName: "DeleteInvestmentDeclaration",
			Handler:    _NetWorth_DeleteInvestmentDeclaration_Handler,
		},
		{
			MethodName: "UpdateBulkManualAssetsCurrentValue",
			Handler:    _NetWorth_UpdateBulkManualAssetsCurrentValue_Handler,
		},
		{
			MethodName: "CreateNetWorthRefreshSession",
			Handler:    _NetWorth_CreateNetWorthRefreshSession_Handler,
		},
		{
			MethodName: "UpdateNetWorthRefreshSession",
			Handler:    _NetWorth_UpdateNetWorthRefreshSession_Handler,
		},
		{
			MethodName: "GetNetWorthRefreshSession",
			Handler:    _NetWorth_GetNetWorthRefreshSession_Handler,
		},
		{
			MethodName: "GetNetWorthInstrumentsRefreshDetails",
			Handler:    _NetWorth_GetNetWorthInstrumentsRefreshDetails_Handler,
		},
		{
			MethodName: "SearchAssetFormFieldOptions",
			Handler:    _NetWorth_SearchAssetFormFieldOptions_Handler,
		},
		{
			MethodName: "StoreSnapshot",
			Handler:    _NetWorth_StoreSnapshot_Handler,
		},
		{
			MethodName: "FetchDynamicElements",
			Handler:    _NetWorth_FetchDynamicElements_Handler,
		},
		{
			MethodName: "DynamicElementCallback",
			Handler:    _NetWorth_DynamicElementCallback_Handler,
		},
		{
			MethodName: "GetPortfolioChangeSummary",
			Handler:    _NetWorth_GetPortfolioChangeSummary_Handler,
		},
		{
			MethodName: "DeleteAllInvestmentDeclaration",
			Handler:    _NetWorth_DeleteAllInvestmentDeclaration_Handler,
		},
		{
			MethodName: "GetAssetsDayChange",
			Handler:    _NetWorth_GetAssetsDayChange_Handler,
		},
		{
			MethodName: "MagicImportFiles",
			Handler:    _NetWorth_MagicImportFiles_Handler,
		},
		{
			MethodName: "GetNetworthDataFile",
			Handler:    _NetWorth_GetNetworthDataFile_Handler,
		},
		{
			MethodName: "GetFilesFromBucket",
			Handler:    _NetWorth_GetFilesFromBucket_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/insights/networth/service.proto",
}
