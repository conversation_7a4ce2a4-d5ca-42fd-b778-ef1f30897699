// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/networth/service.proto

package networth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/insights/networth/enums"

	frontend "github.com/epifi/gamma/api/insights/networth/frontend"

	model "github.com/epifi/gamma/api/insights/networth/model"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.AssetType(0)

	_ = frontend.AssetFormFieldSearchIdentifier(0)

	_ = model.InvestmentDeclarationFieldMask(0)

	_ = typesv2.InvestmentInstrumentType(0)
)

// Validate checks the field values on GetFilesFromBucketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFilesFromBucketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFilesFromBucketRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFilesFromBucketRequestMultiError, or nil if none found.
func (m *GetFilesFromBucketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFilesFromBucketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BucketName

	if len(errors) > 0 {
		return GetFilesFromBucketRequestMultiError(errors)
	}

	return nil
}

// GetFilesFromBucketRequestMultiError is an error wrapping multiple validation
// errors returned by GetFilesFromBucketRequest.ValidateAll() if the
// designated constraints aren't met.
type GetFilesFromBucketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFilesFromBucketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFilesFromBucketRequestMultiError) AllErrors() []error { return m }

// GetFilesFromBucketRequestValidationError is the validation error returned by
// GetFilesFromBucketRequest.Validate if the designated constraints aren't met.
type GetFilesFromBucketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFilesFromBucketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFilesFromBucketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFilesFromBucketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFilesFromBucketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFilesFromBucketRequestValidationError) ErrorName() string {
	return "GetFilesFromBucketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFilesFromBucketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFilesFromBucketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFilesFromBucketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFilesFromBucketRequestValidationError{}

// Validate checks the field values on GetFilesFromBucketResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFilesFromBucketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFilesFromBucketResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFilesFromBucketResponseMultiError, or nil if none found.
func (m *GetFilesFromBucketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFilesFromBucketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFilesFromBucketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFilesFromBucketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFilesFromBucketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFilesFromBucketResponseMultiError(errors)
	}

	return nil
}

// GetFilesFromBucketResponseMultiError is an error wrapping multiple
// validation errors returned by GetFilesFromBucketResponse.ValidateAll() if
// the designated constraints aren't met.
type GetFilesFromBucketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFilesFromBucketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFilesFromBucketResponseMultiError) AllErrors() []error { return m }

// GetFilesFromBucketResponseValidationError is the validation error returned
// by GetFilesFromBucketResponse.Validate if the designated constraints aren't met.
type GetFilesFromBucketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFilesFromBucketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFilesFromBucketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFilesFromBucketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFilesFromBucketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFilesFromBucketResponseValidationError) ErrorName() string {
	return "GetFilesFromBucketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFilesFromBucketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFilesFromBucketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFilesFromBucketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFilesFromBucketResponseValidationError{}

// Validate checks the field values on MagicImportFilesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MagicImportFilesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MagicImportFilesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MagicImportFilesRequestMultiError, or nil if none found.
func (m *MagicImportFilesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MagicImportFilesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MagicImportFilesRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MagicImportFilesRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MagicImportFilesRequestValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MagicImportFilesRequestMultiError(errors)
	}

	return nil
}

// MagicImportFilesRequestMultiError is an error wrapping multiple validation
// errors returned by MagicImportFilesRequest.ValidateAll() if the designated
// constraints aren't met.
type MagicImportFilesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MagicImportFilesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MagicImportFilesRequestMultiError) AllErrors() []error { return m }

// MagicImportFilesRequestValidationError is the validation error returned by
// MagicImportFilesRequest.Validate if the designated constraints aren't met.
type MagicImportFilesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MagicImportFilesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MagicImportFilesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MagicImportFilesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MagicImportFilesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MagicImportFilesRequestValidationError) ErrorName() string {
	return "MagicImportFilesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MagicImportFilesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMagicImportFilesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MagicImportFilesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MagicImportFilesRequestValidationError{}

// Validate checks the field values on MagicImportFilesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MagicImportFilesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MagicImportFilesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MagicImportFilesResponseMultiError, or nil if none found.
func (m *MagicImportFilesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MagicImportFilesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportFilesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportFilesResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMagicImportDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "MagicImportDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "MagicImportDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMagicImportDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportFilesResponseValidationError{
				field:  "MagicImportDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MagicImportFilesResponseMultiError(errors)
	}

	return nil
}

// MagicImportFilesResponseMultiError is an error wrapping multiple validation
// errors returned by MagicImportFilesResponse.ValidateAll() if the designated
// constraints aren't met.
type MagicImportFilesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MagicImportFilesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MagicImportFilesResponseMultiError) AllErrors() []error { return m }

// MagicImportFilesResponseValidationError is the validation error returned by
// MagicImportFilesResponse.Validate if the designated constraints aren't met.
type MagicImportFilesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MagicImportFilesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MagicImportFilesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MagicImportFilesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MagicImportFilesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MagicImportFilesResponseValidationError) ErrorName() string {
	return "MagicImportFilesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MagicImportFilesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMagicImportFilesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MagicImportFilesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MagicImportFilesResponseValidationError{}

// Validate checks the field values on GetAssetsDayChangeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetsDayChangeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetsDayChangeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetsDayChangeRequestMultiError, or nil if none found.
func (m *GetAssetsDayChangeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetsDayChangeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetAssetsDayChangeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInitialDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAssetsDayChangeRequestValidationError{
					field:  "InitialDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAssetsDayChangeRequestValidationError{
					field:  "InitialDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInitialDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAssetsDayChangeRequestValidationError{
				field:  "InitialDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinalDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAssetsDayChangeRequestValidationError{
					field:  "FinalDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAssetsDayChangeRequestValidationError{
					field:  "FinalDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinalDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAssetsDayChangeRequestValidationError{
				field:  "FinalDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAssetsDayChangeRequestMultiError(errors)
	}

	return nil
}

// GetAssetsDayChangeRequestMultiError is an error wrapping multiple validation
// errors returned by GetAssetsDayChangeRequest.ValidateAll() if the
// designated constraints aren't met.
type GetAssetsDayChangeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetsDayChangeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetsDayChangeRequestMultiError) AllErrors() []error { return m }

// GetAssetsDayChangeRequestValidationError is the validation error returned by
// GetAssetsDayChangeRequest.Validate if the designated constraints aren't met.
type GetAssetsDayChangeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetsDayChangeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetsDayChangeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetsDayChangeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetsDayChangeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetsDayChangeRequestValidationError) ErrorName() string {
	return "GetAssetsDayChangeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetsDayChangeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetsDayChangeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetsDayChangeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetsDayChangeRequestValidationError{}

// Validate checks the field values on GetAssetsDayChangeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetsDayChangeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetsDayChangeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetsDayChangeResponseMultiError, or nil if none found.
func (m *GetAssetsDayChangeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetsDayChangeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAssetsDayChangeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAssetsDayChangeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAssetsDayChangeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetAssetTypeToDayChangeResponseMap()))
		i := 0
		for key := range m.GetAssetTypeToDayChangeResponseMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAssetTypeToDayChangeResponseMap()[key]
			_ = val

			// no validation rules for AssetTypeToDayChangeResponseMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetAssetsDayChangeResponseValidationError{
							field:  fmt.Sprintf("AssetTypeToDayChangeResponseMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetAssetsDayChangeResponseValidationError{
							field:  fmt.Sprintf("AssetTypeToDayChangeResponseMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetAssetsDayChangeResponseValidationError{
						field:  fmt.Sprintf("AssetTypeToDayChangeResponseMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetAssetsDayChangeResponseMultiError(errors)
	}

	return nil
}

// GetAssetsDayChangeResponseMultiError is an error wrapping multiple
// validation errors returned by GetAssetsDayChangeResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAssetsDayChangeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetsDayChangeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetsDayChangeResponseMultiError) AllErrors() []error { return m }

// GetAssetsDayChangeResponseValidationError is the validation error returned
// by GetAssetsDayChangeResponse.Validate if the designated constraints aren't met.
type GetAssetsDayChangeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetsDayChangeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetsDayChangeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetsDayChangeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetsDayChangeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetsDayChangeResponseValidationError) ErrorName() string {
	return "GetAssetsDayChangeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetsDayChangeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetsDayChangeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetsDayChangeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetsDayChangeResponseValidationError{}

// Validate checks the field values on AssetTypeDayChangeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssetTypeDayChangeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetTypeDayChangeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssetTypeDayChangeResponseMultiError, or nil if none found.
func (m *AssetTypeDayChangeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetTypeDayChangeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInitialDateTotalValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetTypeDayChangeResponseValidationError{
					field:  "InitialDateTotalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetTypeDayChangeResponseValidationError{
					field:  "InitialDateTotalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInitialDateTotalValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetTypeDayChangeResponseValidationError{
				field:  "InitialDateTotalValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinalDateTotalValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetTypeDayChangeResponseValidationError{
					field:  "FinalDateTotalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetTypeDayChangeResponseValidationError{
					field:  "FinalDateTotalValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinalDateTotalValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetTypeDayChangeResponseValidationError{
				field:  "FinalDateTotalValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TotalChange

	for idx, item := range m.GetAssetsValueChange() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AssetTypeDayChangeResponseValidationError{
						field:  fmt.Sprintf("AssetsValueChange[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AssetTypeDayChangeResponseValidationError{
						field:  fmt.Sprintf("AssetsValueChange[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AssetTypeDayChangeResponseValidationError{
					field:  fmt.Sprintf("AssetsValueChange[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AssetTypeDayChangeResponseMultiError(errors)
	}

	return nil
}

// AssetTypeDayChangeResponseMultiError is an error wrapping multiple
// validation errors returned by AssetTypeDayChangeResponse.ValidateAll() if
// the designated constraints aren't met.
type AssetTypeDayChangeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetTypeDayChangeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetTypeDayChangeResponseMultiError) AllErrors() []error { return m }

// AssetTypeDayChangeResponseValidationError is the validation error returned
// by AssetTypeDayChangeResponse.Validate if the designated constraints aren't met.
type AssetTypeDayChangeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetTypeDayChangeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetTypeDayChangeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetTypeDayChangeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetTypeDayChangeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetTypeDayChangeResponseValidationError) ErrorName() string {
	return "AssetTypeDayChangeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AssetTypeDayChangeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetTypeDayChangeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetTypeDayChangeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetTypeDayChangeResponseValidationError{}

// Validate checks the field values on AssetValueChange with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AssetValueChange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetValueChange with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssetValueChangeMultiError, or nil if none found.
func (m *AssetValueChange) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetValueChange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetId

	if all {
		switch v := interface{}(m.GetInitialDateValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetValueChangeValidationError{
					field:  "InitialDateValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetValueChangeValidationError{
					field:  "InitialDateValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInitialDateValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetValueChangeValidationError{
				field:  "InitialDateValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinalDateValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetValueChangeValidationError{
					field:  "FinalDateValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetValueChangeValidationError{
					field:  "FinalDateValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinalDateValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetValueChangeValidationError{
				field:  "FinalDateValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Change

	if len(errors) > 0 {
		return AssetValueChangeMultiError(errors)
	}

	return nil
}

// AssetValueChangeMultiError is an error wrapping multiple validation errors
// returned by AssetValueChange.ValidateAll() if the designated constraints
// aren't met.
type AssetValueChangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetValueChangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetValueChangeMultiError) AllErrors() []error { return m }

// AssetValueChangeValidationError is the validation error returned by
// AssetValueChange.Validate if the designated constraints aren't met.
type AssetValueChangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetValueChangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetValueChangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetValueChangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetValueChangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetValueChangeValidationError) ErrorName() string { return "AssetValueChangeValidationError" }

// Error satisfies the builtin error interface
func (e AssetValueChangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetValueChange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetValueChangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetValueChangeValidationError{}

// Validate checks the field values on DeleteAllInvestmentDeclarationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeleteAllInvestmentDeclarationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAllInvestmentDeclarationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteAllInvestmentDeclarationRequestMultiError, or nil if none found.
func (m *DeleteAllInvestmentDeclarationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAllInvestmentDeclarationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := DeleteAllInvestmentDeclarationRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteAllInvestmentDeclarationRequestMultiError(errors)
	}

	return nil
}

// DeleteAllInvestmentDeclarationRequestMultiError is an error wrapping
// multiple validation errors returned by
// DeleteAllInvestmentDeclarationRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteAllInvestmentDeclarationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAllInvestmentDeclarationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAllInvestmentDeclarationRequestMultiError) AllErrors() []error { return m }

// DeleteAllInvestmentDeclarationRequestValidationError is the validation error
// returned by DeleteAllInvestmentDeclarationRequest.Validate if the
// designated constraints aren't met.
type DeleteAllInvestmentDeclarationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAllInvestmentDeclarationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAllInvestmentDeclarationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAllInvestmentDeclarationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAllInvestmentDeclarationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAllInvestmentDeclarationRequestValidationError) ErrorName() string {
	return "DeleteAllInvestmentDeclarationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAllInvestmentDeclarationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAllInvestmentDeclarationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAllInvestmentDeclarationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAllInvestmentDeclarationRequestValidationError{}

// Validate checks the field values on DeleteAllInvestmentDeclarationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeleteAllInvestmentDeclarationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeleteAllInvestmentDeclarationResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DeleteAllInvestmentDeclarationResponseMultiError, or nil if none found.
func (m *DeleteAllInvestmentDeclarationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAllInvestmentDeclarationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteAllInvestmentDeclarationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteAllInvestmentDeclarationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteAllInvestmentDeclarationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteAllInvestmentDeclarationResponseMultiError(errors)
	}

	return nil
}

// DeleteAllInvestmentDeclarationResponseMultiError is an error wrapping
// multiple validation errors returned by
// DeleteAllInvestmentDeclarationResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteAllInvestmentDeclarationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAllInvestmentDeclarationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAllInvestmentDeclarationResponseMultiError) AllErrors() []error { return m }

// DeleteAllInvestmentDeclarationResponseValidationError is the validation
// error returned by DeleteAllInvestmentDeclarationResponse.Validate if the
// designated constraints aren't met.
type DeleteAllInvestmentDeclarationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAllInvestmentDeclarationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAllInvestmentDeclarationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAllInvestmentDeclarationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAllInvestmentDeclarationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAllInvestmentDeclarationResponseValidationError) ErrorName() string {
	return "DeleteAllInvestmentDeclarationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAllInvestmentDeclarationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAllInvestmentDeclarationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAllInvestmentDeclarationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAllInvestmentDeclarationResponseValidationError{}

// Validate checks the field values on GetInvestmentDeclarationsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInvestmentDeclarationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentDeclarationsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentDeclarationsRequestMultiError, or nil if none found.
func (m *GetInvestmentDeclarationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentDeclarationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentDeclarationsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentDeclarationsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentDeclarationsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetInvestmentDeclarationsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetInvestmentDeclarationsRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentDeclarationsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentDeclarationsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentDeclarationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentDeclarationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentDeclarationsRequestMultiError) AllErrors() []error { return m }

// GetInvestmentDeclarationsRequestValidationError is the validation error
// returned by GetInvestmentDeclarationsRequest.Validate if the designated
// constraints aren't met.
type GetInvestmentDeclarationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentDeclarationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentDeclarationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentDeclarationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentDeclarationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentDeclarationsRequestValidationError) ErrorName() string {
	return "GetInvestmentDeclarationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentDeclarationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentDeclarationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentDeclarationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentDeclarationsRequestValidationError{}

// Validate checks the field values on GetInvestmentDeclarationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInvestmentDeclarationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentDeclarationsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetInvestmentDeclarationsResponseMultiError, or nil if none found.
func (m *GetInvestmentDeclarationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentDeclarationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentDeclarationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentDeclarationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentDeclarationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentDeclarationsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentDeclarationsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentDeclarationsResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInvestmentDeclaration() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInvestmentDeclarationsResponseValidationError{
						field:  fmt.Sprintf("InvestmentDeclaration[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInvestmentDeclarationsResponseValidationError{
						field:  fmt.Sprintf("InvestmentDeclaration[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInvestmentDeclarationsResponseValidationError{
					field:  fmt.Sprintf("InvestmentDeclaration[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetInvestmentDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInvestmentDeclarationsResponseValidationError{
						field:  fmt.Sprintf("InvestmentDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInvestmentDeclarationsResponseValidationError{
						field:  fmt.Sprintf("InvestmentDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInvestmentDeclarationsResponseValidationError{
					field:  fmt.Sprintf("InvestmentDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetInvestmentDeclarationsResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentDeclarationsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentDeclarationsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentDeclarationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentDeclarationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentDeclarationsResponseMultiError) AllErrors() []error { return m }

// GetInvestmentDeclarationsResponseValidationError is the validation error
// returned by GetInvestmentDeclarationsResponse.Validate if the designated
// constraints aren't met.
type GetInvestmentDeclarationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentDeclarationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentDeclarationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentDeclarationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentDeclarationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentDeclarationsResponseValidationError) ErrorName() string {
	return "GetInvestmentDeclarationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentDeclarationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentDeclarationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentDeclarationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentDeclarationsResponseValidationError{}

// Validate checks the field values on StoreSnapshotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoreSnapshotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoreSnapshotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoreSnapshotRequestMultiError, or nil if none found.
func (m *StoreSnapshotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreSnapshotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := StoreSnapshotRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return StoreSnapshotRequestMultiError(errors)
	}

	return nil
}

// StoreSnapshotRequestMultiError is an error wrapping multiple validation
// errors returned by StoreSnapshotRequest.ValidateAll() if the designated
// constraints aren't met.
type StoreSnapshotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreSnapshotRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreSnapshotRequestMultiError) AllErrors() []error { return m }

// StoreSnapshotRequestValidationError is the validation error returned by
// StoreSnapshotRequest.Validate if the designated constraints aren't met.
type StoreSnapshotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreSnapshotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoreSnapshotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoreSnapshotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoreSnapshotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoreSnapshotRequestValidationError) ErrorName() string {
	return "StoreSnapshotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StoreSnapshotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreSnapshotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreSnapshotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreSnapshotRequestValidationError{}

// Validate checks the field values on StoreSnapshotResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoreSnapshotResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoreSnapshotResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoreSnapshotResponseMultiError, or nil if none found.
func (m *StoreSnapshotResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreSnapshotResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreSnapshotResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreSnapshotResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreSnapshotResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoreSnapshotResponseMultiError(errors)
	}

	return nil
}

// StoreSnapshotResponseMultiError is an error wrapping multiple validation
// errors returned by StoreSnapshotResponse.ValidateAll() if the designated
// constraints aren't met.
type StoreSnapshotResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreSnapshotResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreSnapshotResponseMultiError) AllErrors() []error { return m }

// StoreSnapshotResponseValidationError is the validation error returned by
// StoreSnapshotResponse.Validate if the designated constraints aren't met.
type StoreSnapshotResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreSnapshotResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoreSnapshotResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoreSnapshotResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoreSnapshotResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoreSnapshotResponseValidationError) ErrorName() string {
	return "StoreSnapshotResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StoreSnapshotResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreSnapshotResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreSnapshotResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreSnapshotResponseValidationError{}

// Validate checks the field values on DeclareInvestmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeclareInvestmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeclareInvestmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeclareInvestmentRequestMultiError, or nil if none found.
func (m *DeclareInvestmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeclareInvestmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := DeclareInvestmentRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for InstrumentType

	if all {
		switch v := interface{}(m.GetInvestedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeclareInvestmentRequestValidationError{
					field:  "InvestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeclareInvestmentRequestValidationError{
					field:  "InvestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeclareInvestmentRequestValidationError{
				field:  "InvestedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeclareInvestmentRequestValidationError{
					field:  "InvestedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeclareInvestmentRequestValidationError{
					field:  "InvestedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeclareInvestmentRequestValidationError{
				field:  "InvestedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaturityTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeclareInvestmentRequestValidationError{
					field:  "MaturityTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeclareInvestmentRequestValidationError{
					field:  "MaturityTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeclareInvestmentRequestValidationError{
				field:  "MaturityTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InterestRate

	if all {
		switch v := interface{}(m.GetDeclarationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeclareInvestmentRequestValidationError{
					field:  "DeclarationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeclareInvestmentRequestValidationError{
					field:  "DeclarationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeclarationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeclareInvestmentRequestValidationError{
				field:  "DeclarationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConsentId

	if len(errors) > 0 {
		return DeclareInvestmentRequestMultiError(errors)
	}

	return nil
}

// DeclareInvestmentRequestMultiError is an error wrapping multiple validation
// errors returned by DeclareInvestmentRequest.ValidateAll() if the designated
// constraints aren't met.
type DeclareInvestmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeclareInvestmentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeclareInvestmentRequestMultiError) AllErrors() []error { return m }

// DeclareInvestmentRequestValidationError is the validation error returned by
// DeclareInvestmentRequest.Validate if the designated constraints aren't met.
type DeclareInvestmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeclareInvestmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeclareInvestmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeclareInvestmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeclareInvestmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeclareInvestmentRequestValidationError) ErrorName() string {
	return "DeclareInvestmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeclareInvestmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeclareInvestmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeclareInvestmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeclareInvestmentRequestValidationError{}

// Validate checks the field values on DeclareInvestmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeclareInvestmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeclareInvestmentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeclareInvestmentResponseMultiError, or nil if none found.
func (m *DeclareInvestmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeclareInvestmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeclareInvestmentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeclareInvestmentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeclareInvestmentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentDeclaration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeclareInvestmentResponseValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeclareInvestmentResponseValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDeclaration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeclareInvestmentResponseValidationError{
				field:  "InvestmentDeclaration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeclareInvestmentResponseMultiError(errors)
	}

	return nil
}

// DeclareInvestmentResponseMultiError is an error wrapping multiple validation
// errors returned by DeclareInvestmentResponse.ValidateAll() if the
// designated constraints aren't met.
type DeclareInvestmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeclareInvestmentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeclareInvestmentResponseMultiError) AllErrors() []error { return m }

// DeclareInvestmentResponseValidationError is the validation error returned by
// DeclareInvestmentResponse.Validate if the designated constraints aren't met.
type DeclareInvestmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeclareInvestmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeclareInvestmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeclareInvestmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeclareInvestmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeclareInvestmentResponseValidationError) ErrorName() string {
	return "DeclareInvestmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeclareInvestmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeclareInvestmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeclareInvestmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeclareInvestmentResponseValidationError{}

// Validate checks the field values on UpdateInvestmentDeclarationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateInvestmentDeclarationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateInvestmentDeclarationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateInvestmentDeclarationRequestMultiError, or nil if none found.
func (m *UpdateInvestmentDeclarationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateInvestmentDeclarationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedDeclaration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentDeclarationRequestValidationError{
					field:  "UpdatedDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentDeclarationRequestValidationError{
					field:  "UpdatedDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedDeclaration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentDeclarationRequestValidationError{
				field:  "UpdatedDeclaration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateInvestmentDeclarationRequestMultiError(errors)
	}

	return nil
}

// UpdateInvestmentDeclarationRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateInvestmentDeclarationRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateInvestmentDeclarationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateInvestmentDeclarationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateInvestmentDeclarationRequestMultiError) AllErrors() []error { return m }

// UpdateInvestmentDeclarationRequestValidationError is the validation error
// returned by UpdateInvestmentDeclarationRequest.Validate if the designated
// constraints aren't met.
type UpdateInvestmentDeclarationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateInvestmentDeclarationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateInvestmentDeclarationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateInvestmentDeclarationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateInvestmentDeclarationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateInvestmentDeclarationRequestValidationError) ErrorName() string {
	return "UpdateInvestmentDeclarationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateInvestmentDeclarationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateInvestmentDeclarationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateInvestmentDeclarationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateInvestmentDeclarationRequestValidationError{}

// Validate checks the field values on UpdateInvestmentDeclarationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateInvestmentDeclarationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateInvestmentDeclarationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateInvestmentDeclarationResponseMultiError, or nil if none found.
func (m *UpdateInvestmentDeclarationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateInvestmentDeclarationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentDeclarationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentDeclarationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentDeclarationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentDeclaration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateInvestmentDeclarationResponseValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateInvestmentDeclarationResponseValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDeclaration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateInvestmentDeclarationResponseValidationError{
				field:  "InvestmentDeclaration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateInvestmentDeclarationResponseMultiError(errors)
	}

	return nil
}

// UpdateInvestmentDeclarationResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateInvestmentDeclarationResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateInvestmentDeclarationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateInvestmentDeclarationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateInvestmentDeclarationResponseMultiError) AllErrors() []error { return m }

// UpdateInvestmentDeclarationResponseValidationError is the validation error
// returned by UpdateInvestmentDeclarationResponse.Validate if the designated
// constraints aren't met.
type UpdateInvestmentDeclarationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateInvestmentDeclarationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateInvestmentDeclarationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateInvestmentDeclarationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateInvestmentDeclarationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateInvestmentDeclarationResponseValidationError) ErrorName() string {
	return "UpdateInvestmentDeclarationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateInvestmentDeclarationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateInvestmentDeclarationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateInvestmentDeclarationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateInvestmentDeclarationResponseValidationError{}

// Validate checks the field values on GetInvestmentDeclarationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInvestmentDeclarationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentDeclarationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentDeclarationRequestMultiError, or nil if none found.
func (m *GetInvestmentDeclarationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentDeclarationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetInvestmentDeclarationRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetExternalId()) < 1 {
		err := GetInvestmentDeclarationRequestValidationError{
			field:  "ExternalId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetInvestmentDeclarationRequestMultiError(errors)
	}

	return nil
}

// GetInvestmentDeclarationRequestMultiError is an error wrapping multiple
// validation errors returned by GetInvestmentDeclarationRequest.ValidateAll()
// if the designated constraints aren't met.
type GetInvestmentDeclarationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentDeclarationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentDeclarationRequestMultiError) AllErrors() []error { return m }

// GetInvestmentDeclarationRequestValidationError is the validation error
// returned by GetInvestmentDeclarationRequest.Validate if the designated
// constraints aren't met.
type GetInvestmentDeclarationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentDeclarationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentDeclarationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentDeclarationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentDeclarationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentDeclarationRequestValidationError) ErrorName() string {
	return "GetInvestmentDeclarationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentDeclarationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentDeclarationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentDeclarationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentDeclarationRequestValidationError{}

// Validate checks the field values on GetInvestmentDeclarationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetInvestmentDeclarationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInvestmentDeclarationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetInvestmentDeclarationResponseMultiError, or nil if none found.
func (m *GetInvestmentDeclarationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInvestmentDeclarationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentDeclarationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentDeclarationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentDeclarationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentDeclaration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentDeclarationResponseValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentDeclarationResponseValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDeclaration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentDeclarationResponseValidationError{
				field:  "InvestmentDeclaration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetInvestmentDeclarationResponseValidationError{
					field:  "InvestmentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetInvestmentDeclarationResponseValidationError{
					field:  "InvestmentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetInvestmentDeclarationResponseValidationError{
				field:  "InvestmentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetInvestmentDeclarationResponseMultiError(errors)
	}

	return nil
}

// GetInvestmentDeclarationResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetInvestmentDeclarationResponse.ValidateAll() if the designated
// constraints aren't met.
type GetInvestmentDeclarationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInvestmentDeclarationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInvestmentDeclarationResponseMultiError) AllErrors() []error { return m }

// GetInvestmentDeclarationResponseValidationError is the validation error
// returned by GetInvestmentDeclarationResponse.Validate if the designated
// constraints aren't met.
type GetInvestmentDeclarationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInvestmentDeclarationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInvestmentDeclarationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInvestmentDeclarationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInvestmentDeclarationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInvestmentDeclarationResponseValidationError) ErrorName() string {
	return "GetInvestmentDeclarationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInvestmentDeclarationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInvestmentDeclarationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInvestmentDeclarationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInvestmentDeclarationResponseValidationError{}

// Validate checks the field values on DeleteInvestmentDeclarationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteInvestmentDeclarationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteInvestmentDeclarationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteInvestmentDeclarationRequestMultiError, or nil if none found.
func (m *DeleteInvestmentDeclarationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteInvestmentDeclarationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := DeleteInvestmentDeclarationRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetExternalId()) < 1 {
		err := DeleteInvestmentDeclarationRequestValidationError{
			field:  "ExternalId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteInvestmentDeclarationRequestMultiError(errors)
	}

	return nil
}

// DeleteInvestmentDeclarationRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeleteInvestmentDeclarationRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteInvestmentDeclarationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteInvestmentDeclarationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteInvestmentDeclarationRequestMultiError) AllErrors() []error { return m }

// DeleteInvestmentDeclarationRequestValidationError is the validation error
// returned by DeleteInvestmentDeclarationRequest.Validate if the designated
// constraints aren't met.
type DeleteInvestmentDeclarationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteInvestmentDeclarationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteInvestmentDeclarationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteInvestmentDeclarationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteInvestmentDeclarationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteInvestmentDeclarationRequestValidationError) ErrorName() string {
	return "DeleteInvestmentDeclarationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteInvestmentDeclarationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteInvestmentDeclarationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteInvestmentDeclarationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteInvestmentDeclarationRequestValidationError{}

// Validate checks the field values on DeleteInvestmentDeclarationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteInvestmentDeclarationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteInvestmentDeclarationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteInvestmentDeclarationResponseMultiError, or nil if none found.
func (m *DeleteInvestmentDeclarationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteInvestmentDeclarationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteInvestmentDeclarationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteInvestmentDeclarationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteInvestmentDeclarationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteInvestmentDeclarationResponseMultiError(errors)
	}

	return nil
}

// DeleteInvestmentDeclarationResponseMultiError is an error wrapping multiple
// validation errors returned by
// DeleteInvestmentDeclarationResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteInvestmentDeclarationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteInvestmentDeclarationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteInvestmentDeclarationResponseMultiError) AllErrors() []error { return m }

// DeleteInvestmentDeclarationResponseValidationError is the validation error
// returned by DeleteInvestmentDeclarationResponse.Validate if the designated
// constraints aren't met.
type DeleteInvestmentDeclarationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteInvestmentDeclarationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteInvestmentDeclarationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteInvestmentDeclarationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteInvestmentDeclarationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteInvestmentDeclarationResponseValidationError) ErrorName() string {
	return "DeleteInvestmentDeclarationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteInvestmentDeclarationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteInvestmentDeclarationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteInvestmentDeclarationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteInvestmentDeclarationResponseValidationError{}

// Validate checks the field values on InvestmentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InvestmentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentDetailsMultiError, or nil if none found.
func (m *InvestmentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInvestmentDeclaration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDetailsValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDetailsValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDeclaration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDetailsValidationError{
				field:  "InvestmentDeclaration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDetailsValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDetailsValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDetailsValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetComputedInvestmentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDetailsValidationError{
					field:  "ComputedInvestmentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDetailsValidationError{
					field:  "ComputedInvestmentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComputedInvestmentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDetailsValidationError{
				field:  "ComputedInvestmentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InvestmentDetailsMultiError(errors)
	}

	return nil
}

// InvestmentDetailsMultiError is an error wrapping multiple validation errors
// returned by InvestmentDetails.ValidateAll() if the designated constraints
// aren't met.
type InvestmentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentDetailsMultiError) AllErrors() []error { return m }

// InvestmentDetailsValidationError is the validation error returned by
// InvestmentDetails.Validate if the designated constraints aren't met.
type InvestmentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentDetailsValidationError) ErrorName() string {
	return "InvestmentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentDetailsValidationError{}

// Validate checks the field values on ComputedInvestmentDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ComputedInvestmentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComputedInvestmentDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComputedInvestmentDetailsMultiError, or nil if none found.
func (m *ComputedInvestmentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ComputedInvestmentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.ComputedDetails.(type) {
	case *ComputedInvestmentDetails_EsopDetails:
		if v == nil {
			err := ComputedInvestmentDetailsValidationError{
				field:  "ComputedDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEsopDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComputedInvestmentDetailsValidationError{
						field:  "EsopDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComputedInvestmentDetailsValidationError{
						field:  "EsopDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEsopDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComputedInvestmentDetailsValidationError{
					field:  "EsopDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ComputedInvestmentDetailsMultiError(errors)
	}

	return nil
}

// ComputedInvestmentDetailsMultiError is an error wrapping multiple validation
// errors returned by ComputedInvestmentDetails.ValidateAll() if the
// designated constraints aren't met.
type ComputedInvestmentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComputedInvestmentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComputedInvestmentDetailsMultiError) AllErrors() []error { return m }

// ComputedInvestmentDetailsValidationError is the validation error returned by
// ComputedInvestmentDetails.Validate if the designated constraints aren't met.
type ComputedInvestmentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComputedInvestmentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComputedInvestmentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComputedInvestmentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComputedInvestmentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComputedInvestmentDetailsValidationError) ErrorName() string {
	return "ComputedInvestmentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ComputedInvestmentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComputedInvestmentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComputedInvestmentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComputedInvestmentDetailsValidationError{}

// Validate checks the field values on EsopDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EsopDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EsopDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EsopDetailsMultiError, or
// nil if none found.
func (m *EsopDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *EsopDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalVestedEsops

	if len(errors) > 0 {
		return EsopDetailsMultiError(errors)
	}

	return nil
}

// EsopDetailsMultiError is an error wrapping multiple validation errors
// returned by EsopDetails.ValidateAll() if the designated constraints aren't met.
type EsopDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EsopDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EsopDetailsMultiError) AllErrors() []error { return m }

// EsopDetailsValidationError is the validation error returned by
// EsopDetails.Validate if the designated constraints aren't met.
type EsopDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EsopDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EsopDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EsopDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EsopDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EsopDetailsValidationError) ErrorName() string { return "EsopDetailsValidationError" }

// Error satisfies the builtin error interface
func (e EsopDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEsopDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EsopDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EsopDetailsValidationError{}

// Validate checks the field values on GetNetWorthValueRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNetWorthValueRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetWorthValueRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNetWorthValueRequestMultiError, or nil if none found.
func (m *GetNetWorthValueRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthValueRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetNetWorthValueRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetNetWorthValueRequestMultiError(errors)
	}

	return nil
}

// GetNetWorthValueRequestMultiError is an error wrapping multiple validation
// errors returned by GetNetWorthValueRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNetWorthValueRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthValueRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthValueRequestMultiError) AllErrors() []error { return m }

// GetNetWorthValueRequestValidationError is the validation error returned by
// GetNetWorthValueRequest.Validate if the designated constraints aren't met.
type GetNetWorthValueRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthValueRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthValueRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthValueRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthValueRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthValueRequestValidationError) ErrorName() string {
	return "GetNetWorthValueRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthValueRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthValueRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthValueRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthValueRequestValidationError{}

// Validate checks the field values on GetNetWorthValueResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNetWorthValueResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetWorthValueResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNetWorthValueResponseMultiError, or nil if none found.
func (m *GetNetWorthValueResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthValueResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthValueResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthValueResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthValueResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAssetValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNetWorthValueResponseValidationError{
						field:  fmt.Sprintf("AssetValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNetWorthValueResponseValidationError{
						field:  fmt.Sprintf("AssetValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNetWorthValueResponseValidationError{
					field:  fmt.Sprintf("AssetValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLiabilityValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNetWorthValueResponseValidationError{
						field:  fmt.Sprintf("LiabilityValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNetWorthValueResponseValidationError{
						field:  fmt.Sprintf("LiabilityValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNetWorthValueResponseValidationError{
					field:  fmt.Sprintf("LiabilityValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetTotalNetWorthValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthValueResponseValidationError{
					field:  "TotalNetWorthValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthValueResponseValidationError{
					field:  "TotalNetWorthValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalNetWorthValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthValueResponseValidationError{
				field:  "TotalNetWorthValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNetWorthValueResponseMultiError(errors)
	}

	return nil
}

// GetNetWorthValueResponseMultiError is an error wrapping multiple validation
// errors returned by GetNetWorthValueResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNetWorthValueResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthValueResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthValueResponseMultiError) AllErrors() []error { return m }

// GetNetWorthValueResponseValidationError is the validation error returned by
// GetNetWorthValueResponse.Validate if the designated constraints aren't met.
type GetNetWorthValueResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthValueResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthValueResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthValueResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthValueResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthValueResponseValidationError) ErrorName() string {
	return "GetNetWorthValueResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthValueResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthValueResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthValueResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthValueResponseValidationError{}

// Validate checks the field values on AssetValue with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AssetValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetValue with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AssetValueMultiError, or
// nil if none found.
func (m *AssetValue) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NetWorthAttribute

	// no validation rules for AssetType

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetValueValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetValueValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetValueValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComputationStatus

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return AssetValueMultiError(errors)
	}

	return nil
}

// AssetValueMultiError is an error wrapping multiple validation errors
// returned by AssetValue.ValidateAll() if the designated constraints aren't met.
type AssetValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetValueMultiError) AllErrors() []error { return m }

// AssetValueValidationError is the validation error returned by
// AssetValue.Validate if the designated constraints aren't met.
type AssetValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetValueValidationError) ErrorName() string { return "AssetValueValidationError" }

// Error satisfies the builtin error interface
func (e AssetValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetValueValidationError{}

// Validate checks the field values on LiabilityValue with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LiabilityValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LiabilityValue with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LiabilityValueMultiError,
// or nil if none found.
func (m *LiabilityValue) ValidateAll() error {
	return m.validate(true)
}

func (m *LiabilityValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NetWorthAttribute

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LiabilityValueValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LiabilityValueValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LiabilityValueValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComputationStatus

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return LiabilityValueMultiError(errors)
	}

	return nil
}

// LiabilityValueMultiError is an error wrapping multiple validation errors
// returned by LiabilityValue.ValidateAll() if the designated constraints
// aren't met.
type LiabilityValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LiabilityValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LiabilityValueMultiError) AllErrors() []error { return m }

// LiabilityValueValidationError is the validation error returned by
// LiabilityValue.Validate if the designated constraints aren't met.
type LiabilityValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LiabilityValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LiabilityValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LiabilityValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LiabilityValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LiabilityValueValidationError) ErrorName() string { return "LiabilityValueValidationError" }

// Error satisfies the builtin error interface
func (e LiabilityValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLiabilityValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LiabilityValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LiabilityValueValidationError{}

// Validate checks the field values on
// UpdateBulkManualAssetsCurrentValueRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateBulkManualAssetsCurrentValueRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateBulkManualAssetsCurrentValueRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateBulkManualAssetsCurrentValueRequestMultiError, or nil if none found.
func (m *UpdateBulkManualAssetsCurrentValueRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBulkManualAssetsCurrentValueRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := UpdateBulkManualAssetsCurrentValueRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetUpdatedAssetCurrentValues()))
		i := 0
		for key := range m.GetUpdatedAssetCurrentValues() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetUpdatedAssetCurrentValues()[key]
			_ = val

			// no validation rules for UpdatedAssetCurrentValues[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, UpdateBulkManualAssetsCurrentValueRequestValidationError{
							field:  fmt.Sprintf("UpdatedAssetCurrentValues[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, UpdateBulkManualAssetsCurrentValueRequestValidationError{
							field:  fmt.Sprintf("UpdatedAssetCurrentValues[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return UpdateBulkManualAssetsCurrentValueRequestValidationError{
						field:  fmt.Sprintf("UpdatedAssetCurrentValues[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return UpdateBulkManualAssetsCurrentValueRequestMultiError(errors)
	}

	return nil
}

// UpdateBulkManualAssetsCurrentValueRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateBulkManualAssetsCurrentValueRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateBulkManualAssetsCurrentValueRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBulkManualAssetsCurrentValueRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBulkManualAssetsCurrentValueRequestMultiError) AllErrors() []error { return m }

// UpdateBulkManualAssetsCurrentValueRequestValidationError is the validation
// error returned by UpdateBulkManualAssetsCurrentValueRequest.Validate if the
// designated constraints aren't met.
type UpdateBulkManualAssetsCurrentValueRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBulkManualAssetsCurrentValueRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBulkManualAssetsCurrentValueRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBulkManualAssetsCurrentValueRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBulkManualAssetsCurrentValueRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBulkManualAssetsCurrentValueRequestValidationError) ErrorName() string {
	return "UpdateBulkManualAssetsCurrentValueRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBulkManualAssetsCurrentValueRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBulkManualAssetsCurrentValueRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBulkManualAssetsCurrentValueRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBulkManualAssetsCurrentValueRequestValidationError{}

// Validate checks the field values on
// UpdateBulkManualAssetsCurrentValueResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateBulkManualAssetsCurrentValueResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateBulkManualAssetsCurrentValueResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateBulkManualAssetsCurrentValueResponseMultiError, or nil if none found.
func (m *UpdateBulkManualAssetsCurrentValueResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBulkManualAssetsCurrentValueResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBulkManualAssetsCurrentValueResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBulkManualAssetsCurrentValueResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBulkManualAssetsCurrentValueResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBulkManualAssetsCurrentValueResponseMultiError(errors)
	}

	return nil
}

// UpdateBulkManualAssetsCurrentValueResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateBulkManualAssetsCurrentValueResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateBulkManualAssetsCurrentValueResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBulkManualAssetsCurrentValueResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBulkManualAssetsCurrentValueResponseMultiError) AllErrors() []error { return m }

// UpdateBulkManualAssetsCurrentValueResponseValidationError is the validation
// error returned by UpdateBulkManualAssetsCurrentValueResponse.Validate if
// the designated constraints aren't met.
type UpdateBulkManualAssetsCurrentValueResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBulkManualAssetsCurrentValueResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBulkManualAssetsCurrentValueResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBulkManualAssetsCurrentValueResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBulkManualAssetsCurrentValueResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBulkManualAssetsCurrentValueResponseValidationError) ErrorName() string {
	return "UpdateBulkManualAssetsCurrentValueResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBulkManualAssetsCurrentValueResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBulkManualAssetsCurrentValueResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBulkManualAssetsCurrentValueResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBulkManualAssetsCurrentValueResponseValidationError{}

// Validate checks the field values on CreateNetWorthRefreshSessionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateNetWorthRefreshSessionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNetWorthRefreshSessionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateNetWorthRefreshSessionRequestMultiError, or nil if none found.
func (m *CreateNetWorthRefreshSessionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNetWorthRefreshSessionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := CreateNetWorthRefreshSessionRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetAssetRefreshDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateNetWorthRefreshSessionRequestValidationError{
						field:  fmt.Sprintf("AssetRefreshDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateNetWorthRefreshSessionRequestValidationError{
						field:  fmt.Sprintf("AssetRefreshDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateNetWorthRefreshSessionRequestValidationError{
					field:  fmt.Sprintf("AssetRefreshDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateNetWorthRefreshSessionRequestMultiError(errors)
	}

	return nil
}

// CreateNetWorthRefreshSessionRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateNetWorthRefreshSessionRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateNetWorthRefreshSessionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNetWorthRefreshSessionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNetWorthRefreshSessionRequestMultiError) AllErrors() []error { return m }

// CreateNetWorthRefreshSessionRequestValidationError is the validation error
// returned by CreateNetWorthRefreshSessionRequest.Validate if the designated
// constraints aren't met.
type CreateNetWorthRefreshSessionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNetWorthRefreshSessionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNetWorthRefreshSessionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNetWorthRefreshSessionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNetWorthRefreshSessionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNetWorthRefreshSessionRequestValidationError) ErrorName() string {
	return "CreateNetWorthRefreshSessionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNetWorthRefreshSessionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNetWorthRefreshSessionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNetWorthRefreshSessionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNetWorthRefreshSessionRequestValidationError{}

// Validate checks the field values on CreateNetWorthRefreshSessionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateNetWorthRefreshSessionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNetWorthRefreshSessionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateNetWorthRefreshSessionResponseMultiError, or nil if none found.
func (m *CreateNetWorthRefreshSessionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNetWorthRefreshSessionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNetWorthRefreshSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNetWorthRefreshSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNetWorthRefreshSessionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNetWorthRefreshSession()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNetWorthRefreshSessionResponseValidationError{
					field:  "NetWorthRefreshSession",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNetWorthRefreshSessionResponseValidationError{
					field:  "NetWorthRefreshSession",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetWorthRefreshSession()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNetWorthRefreshSessionResponseValidationError{
				field:  "NetWorthRefreshSession",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNetWorthRefreshSessionResponseMultiError(errors)
	}

	return nil
}

// CreateNetWorthRefreshSessionResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateNetWorthRefreshSessionResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateNetWorthRefreshSessionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNetWorthRefreshSessionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNetWorthRefreshSessionResponseMultiError) AllErrors() []error { return m }

// CreateNetWorthRefreshSessionResponseValidationError is the validation error
// returned by CreateNetWorthRefreshSessionResponse.Validate if the designated
// constraints aren't met.
type CreateNetWorthRefreshSessionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNetWorthRefreshSessionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNetWorthRefreshSessionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNetWorthRefreshSessionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNetWorthRefreshSessionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNetWorthRefreshSessionResponseValidationError) ErrorName() string {
	return "CreateNetWorthRefreshSessionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNetWorthRefreshSessionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNetWorthRefreshSessionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNetWorthRefreshSessionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNetWorthRefreshSessionResponseValidationError{}

// Validate checks the field values on UpdateNetWorthRefreshSessionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateNetWorthRefreshSessionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNetWorthRefreshSessionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateNetWorthRefreshSessionRequestMultiError, or nil if none found.
func (m *UpdateNetWorthRefreshSessionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNetWorthRefreshSessionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedNetWorthRefreshSession()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNetWorthRefreshSessionRequestValidationError{
					field:  "UpdatedNetWorthRefreshSession",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNetWorthRefreshSessionRequestValidationError{
					field:  "UpdatedNetWorthRefreshSession",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedNetWorthRefreshSession()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNetWorthRefreshSessionRequestValidationError{
				field:  "UpdatedNetWorthRefreshSession",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateNetWorthRefreshSessionRequestMultiError(errors)
	}

	return nil
}

// UpdateNetWorthRefreshSessionRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateNetWorthRefreshSessionRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateNetWorthRefreshSessionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNetWorthRefreshSessionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNetWorthRefreshSessionRequestMultiError) AllErrors() []error { return m }

// UpdateNetWorthRefreshSessionRequestValidationError is the validation error
// returned by UpdateNetWorthRefreshSessionRequest.Validate if the designated
// constraints aren't met.
type UpdateNetWorthRefreshSessionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNetWorthRefreshSessionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNetWorthRefreshSessionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNetWorthRefreshSessionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNetWorthRefreshSessionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNetWorthRefreshSessionRequestValidationError) ErrorName() string {
	return "UpdateNetWorthRefreshSessionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNetWorthRefreshSessionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNetWorthRefreshSessionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNetWorthRefreshSessionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNetWorthRefreshSessionRequestValidationError{}

// Validate checks the field values on UpdateNetWorthRefreshSessionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateNetWorthRefreshSessionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNetWorthRefreshSessionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateNetWorthRefreshSessionResponseMultiError, or nil if none found.
func (m *UpdateNetWorthRefreshSessionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNetWorthRefreshSessionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNetWorthRefreshSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNetWorthRefreshSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNetWorthRefreshSessionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNetWorthRefreshSession()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNetWorthRefreshSessionResponseValidationError{
					field:  "NetWorthRefreshSession",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNetWorthRefreshSessionResponseValidationError{
					field:  "NetWorthRefreshSession",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetWorthRefreshSession()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNetWorthRefreshSessionResponseValidationError{
				field:  "NetWorthRefreshSession",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateNetWorthRefreshSessionResponseMultiError(errors)
	}

	return nil
}

// UpdateNetWorthRefreshSessionResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateNetWorthRefreshSessionResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateNetWorthRefreshSessionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNetWorthRefreshSessionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNetWorthRefreshSessionResponseMultiError) AllErrors() []error { return m }

// UpdateNetWorthRefreshSessionResponseValidationError is the validation error
// returned by UpdateNetWorthRefreshSessionResponse.Validate if the designated
// constraints aren't met.
type UpdateNetWorthRefreshSessionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNetWorthRefreshSessionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNetWorthRefreshSessionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNetWorthRefreshSessionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNetWorthRefreshSessionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNetWorthRefreshSessionResponseValidationError) ErrorName() string {
	return "UpdateNetWorthRefreshSessionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNetWorthRefreshSessionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNetWorthRefreshSessionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNetWorthRefreshSessionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNetWorthRefreshSessionResponseValidationError{}

// Validate checks the field values on GetNetWorthRefreshSessionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetNetWorthRefreshSessionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetWorthRefreshSessionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNetWorthRefreshSessionRequestMultiError, or nil if none found.
func (m *GetNetWorthRefreshSessionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthRefreshSessionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NetWorthRefreshId

	if len(errors) > 0 {
		return GetNetWorthRefreshSessionRequestMultiError(errors)
	}

	return nil
}

// GetNetWorthRefreshSessionRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetNetWorthRefreshSessionRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNetWorthRefreshSessionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthRefreshSessionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthRefreshSessionRequestMultiError) AllErrors() []error { return m }

// GetNetWorthRefreshSessionRequestValidationError is the validation error
// returned by GetNetWorthRefreshSessionRequest.Validate if the designated
// constraints aren't met.
type GetNetWorthRefreshSessionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthRefreshSessionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthRefreshSessionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthRefreshSessionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthRefreshSessionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthRefreshSessionRequestValidationError) ErrorName() string {
	return "GetNetWorthRefreshSessionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthRefreshSessionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthRefreshSessionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthRefreshSessionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthRefreshSessionRequestValidationError{}

// Validate checks the field values on GetNetWorthRefreshSessionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetNetWorthRefreshSessionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetWorthRefreshSessionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetNetWorthRefreshSessionResponseMultiError, or nil if none found.
func (m *GetNetWorthRefreshSessionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthRefreshSessionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthRefreshSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthRefreshSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthRefreshSessionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNetWorthRefreshSession()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthRefreshSessionResponseValidationError{
					field:  "NetWorthRefreshSession",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthRefreshSessionResponseValidationError{
					field:  "NetWorthRefreshSession",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetWorthRefreshSession()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthRefreshSessionResponseValidationError{
				field:  "NetWorthRefreshSession",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNetWorthRefreshSessionResponseMultiError(errors)
	}

	return nil
}

// GetNetWorthRefreshSessionResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetNetWorthRefreshSessionResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNetWorthRefreshSessionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthRefreshSessionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthRefreshSessionResponseMultiError) AllErrors() []error { return m }

// GetNetWorthRefreshSessionResponseValidationError is the validation error
// returned by GetNetWorthRefreshSessionResponse.Validate if the designated
// constraints aren't met.
type GetNetWorthRefreshSessionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthRefreshSessionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthRefreshSessionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthRefreshSessionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthRefreshSessionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthRefreshSessionResponseValidationError) ErrorName() string {
	return "GetNetWorthRefreshSessionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthRefreshSessionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthRefreshSessionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthRefreshSessionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthRefreshSessionResponseValidationError{}

// Validate checks the field values on
// GetNetWorthInstrumentsRefreshDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNetWorthInstrumentsRefreshDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNetWorthInstrumentsRefreshDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNetWorthInstrumentsRefreshDetailsRequestMultiError, or nil if none found.
func (m *GetNetWorthInstrumentsRefreshDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthInstrumentsRefreshDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetNetWorthInstrumentsRefreshDetailsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetNetWorthInstrumentsRefreshDetailsRequestMultiError(errors)
	}

	return nil
}

// GetNetWorthInstrumentsRefreshDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetNetWorthInstrumentsRefreshDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNetWorthInstrumentsRefreshDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthInstrumentsRefreshDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthInstrumentsRefreshDetailsRequestMultiError) AllErrors() []error { return m }

// GetNetWorthInstrumentsRefreshDetailsRequestValidationError is the validation
// error returned by GetNetWorthInstrumentsRefreshDetailsRequest.Validate if
// the designated constraints aren't met.
type GetNetWorthInstrumentsRefreshDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthInstrumentsRefreshDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthInstrumentsRefreshDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthInstrumentsRefreshDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthInstrumentsRefreshDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthInstrumentsRefreshDetailsRequestValidationError) ErrorName() string {
	return "GetNetWorthInstrumentsRefreshDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthInstrumentsRefreshDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthInstrumentsRefreshDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthInstrumentsRefreshDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthInstrumentsRefreshDetailsRequestValidationError{}

// Validate checks the field values on
// GetNetWorthInstrumentsRefreshDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNetWorthInstrumentsRefreshDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNetWorthInstrumentsRefreshDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNetWorthInstrumentsRefreshDetailsResponseMultiError, or nil if none found.
func (m *GetNetWorthInstrumentsRefreshDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthInstrumentsRefreshDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthInstrumentsRefreshDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthInstrumentsRefreshDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthInstrumentsRefreshDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInstrumentRefreshSummary() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNetWorthInstrumentsRefreshDetailsResponseValidationError{
						field:  fmt.Sprintf("InstrumentRefreshSummary[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNetWorthInstrumentsRefreshDetailsResponseValidationError{
						field:  fmt.Sprintf("InstrumentRefreshSummary[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNetWorthInstrumentsRefreshDetailsResponseValidationError{
					field:  fmt.Sprintf("InstrumentRefreshSummary[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNetWorthInstrumentsRefreshDetailsResponseMultiError(errors)
	}

	return nil
}

// GetNetWorthInstrumentsRefreshDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetNetWorthInstrumentsRefreshDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetNetWorthInstrumentsRefreshDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthInstrumentsRefreshDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthInstrumentsRefreshDetailsResponseMultiError) AllErrors() []error { return m }

// GetNetWorthInstrumentsRefreshDetailsResponseValidationError is the
// validation error returned by
// GetNetWorthInstrumentsRefreshDetailsResponse.Validate if the designated
// constraints aren't met.
type GetNetWorthInstrumentsRefreshDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthInstrumentsRefreshDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthInstrumentsRefreshDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthInstrumentsRefreshDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthInstrumentsRefreshDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthInstrumentsRefreshDetailsResponseValidationError) ErrorName() string {
	return "GetNetWorthInstrumentsRefreshDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthInstrumentsRefreshDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthInstrumentsRefreshDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthInstrumentsRefreshDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthInstrumentsRefreshDetailsResponseValidationError{}

// Validate checks the field values on InstrumentRefreshDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InstrumentRefreshDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstrumentRefreshDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InstrumentRefreshDetailsMultiError, or nil if none found.
func (m *InstrumentRefreshDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *InstrumentRefreshDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetName

	if all {
		switch v := interface{}(m.GetLastRefreshedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InstrumentRefreshDetailsValidationError{
					field:  "LastRefreshedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InstrumentRefreshDetailsValidationError{
					field:  "LastRefreshedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastRefreshedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InstrumentRefreshDetailsValidationError{
				field:  "LastRefreshedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastRefreshedDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InstrumentRefreshDetailsValidationError{
					field:  "LastRefreshedDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InstrumentRefreshDetailsValidationError{
					field:  "LastRefreshedDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastRefreshedDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InstrumentRefreshDetailsValidationError{
				field:  "LastRefreshedDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsRefreshRequired

	// no validation rules for HasError

	{
		sorted_keys := make([]string, len(m.GetManualAssetRefreshDetails()))
		i := 0
		for key := range m.GetManualAssetRefreshDetails() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetManualAssetRefreshDetails()[key]
			_ = val

			// no validation rules for ManualAssetRefreshDetails[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, InstrumentRefreshDetailsValidationError{
							field:  fmt.Sprintf("ManualAssetRefreshDetails[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, InstrumentRefreshDetailsValidationError{
							field:  fmt.Sprintf("ManualAssetRefreshDetails[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return InstrumentRefreshDetailsValidationError{
						field:  fmt.Sprintf("ManualAssetRefreshDetails[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for RefreshStatus

	if len(errors) > 0 {
		return InstrumentRefreshDetailsMultiError(errors)
	}

	return nil
}

// InstrumentRefreshDetailsMultiError is an error wrapping multiple validation
// errors returned by InstrumentRefreshDetails.ValidateAll() if the designated
// constraints aren't met.
type InstrumentRefreshDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstrumentRefreshDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstrumentRefreshDetailsMultiError) AllErrors() []error { return m }

// InstrumentRefreshDetailsValidationError is the validation error returned by
// InstrumentRefreshDetails.Validate if the designated constraints aren't met.
type InstrumentRefreshDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstrumentRefreshDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstrumentRefreshDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstrumentRefreshDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstrumentRefreshDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstrumentRefreshDetailsValidationError) ErrorName() string {
	return "InstrumentRefreshDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e InstrumentRefreshDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstrumentRefreshDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstrumentRefreshDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstrumentRefreshDetailsValidationError{}

// Validate checks the field values on ManualAssetRefreshDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManualAssetRefreshDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualAssetRefreshDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ManualAssetRefreshDetailsMultiError, or nil if none found.
func (m *ManualAssetRefreshDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualAssetRefreshDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLastRefreshedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualAssetRefreshDetailsValidationError{
					field:  "LastRefreshedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualAssetRefreshDetailsValidationError{
					field:  "LastRefreshedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastRefreshedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualAssetRefreshDetailsValidationError{
				field:  "LastRefreshedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastRefreshedDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualAssetRefreshDetailsValidationError{
					field:  "LastRefreshedDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualAssetRefreshDetailsValidationError{
					field:  "LastRefreshedDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastRefreshedDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualAssetRefreshDetailsValidationError{
				field:  "LastRefreshedDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsRefreshRequired

	for idx, item := range m.GetInvestmentDeclarations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ManualAssetRefreshDetailsValidationError{
						field:  fmt.Sprintf("InvestmentDeclarations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ManualAssetRefreshDetailsValidationError{
						field:  fmt.Sprintf("InvestmentDeclarations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ManualAssetRefreshDetailsValidationError{
					field:  fmt.Sprintf("InvestmentDeclarations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for HasError

	// no validation rules for RefreshStatus

	if len(errors) > 0 {
		return ManualAssetRefreshDetailsMultiError(errors)
	}

	return nil
}

// ManualAssetRefreshDetailsMultiError is an error wrapping multiple validation
// errors returned by ManualAssetRefreshDetails.ValidateAll() if the
// designated constraints aren't met.
type ManualAssetRefreshDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualAssetRefreshDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualAssetRefreshDetailsMultiError) AllErrors() []error { return m }

// ManualAssetRefreshDetailsValidationError is the validation error returned by
// ManualAssetRefreshDetails.Validate if the designated constraints aren't met.
type ManualAssetRefreshDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualAssetRefreshDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualAssetRefreshDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualAssetRefreshDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualAssetRefreshDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualAssetRefreshDetailsValidationError) ErrorName() string {
	return "ManualAssetRefreshDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ManualAssetRefreshDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualAssetRefreshDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualAssetRefreshDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualAssetRefreshDetailsValidationError{}

// Validate checks the field values on SearchAssetFormFieldOptionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchAssetFormFieldOptionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchAssetFormFieldOptionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchAssetFormFieldOptionsRequestMultiError, or nil if none found.
func (m *SearchAssetFormFieldOptionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchAssetFormFieldOptionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SearchIdentifier

	// no validation rules for SearchText

	if len(errors) > 0 {
		return SearchAssetFormFieldOptionsRequestMultiError(errors)
	}

	return nil
}

// SearchAssetFormFieldOptionsRequestMultiError is an error wrapping multiple
// validation errors returned by
// SearchAssetFormFieldOptionsRequest.ValidateAll() if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchAssetFormFieldOptionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchAssetFormFieldOptionsRequestMultiError) AllErrors() []error { return m }

// SearchAssetFormFieldOptionsRequestValidationError is the validation error
// returned by SearchAssetFormFieldOptionsRequest.Validate if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchAssetFormFieldOptionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchAssetFormFieldOptionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchAssetFormFieldOptionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchAssetFormFieldOptionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchAssetFormFieldOptionsRequestValidationError) ErrorName() string {
	return "SearchAssetFormFieldOptionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SearchAssetFormFieldOptionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchAssetFormFieldOptionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchAssetFormFieldOptionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchAssetFormFieldOptionsRequestValidationError{}

// Validate checks the field values on SearchAssetFormFieldOptionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchAssetFormFieldOptionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchAssetFormFieldOptionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchAssetFormFieldOptionsResponseMultiError, or nil if none found.
func (m *SearchAssetFormFieldOptionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchAssetFormFieldOptionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchAssetFormFieldOptionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.SearchResults.(type) {
	case *SearchAssetFormFieldOptionsResponse_PmsProviderList:
		if v == nil {
			err := SearchAssetFormFieldOptionsResponseValidationError{
				field:  "SearchResults",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPmsProviderList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "PmsProviderList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "PmsProviderList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPmsProviderList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchAssetFormFieldOptionsResponseValidationError{
					field:  "PmsProviderList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SearchAssetFormFieldOptionsResponse_AifList:
		if v == nil {
			err := SearchAssetFormFieldOptionsResponseValidationError{
				field:  "SearchResults",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAifList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "AifList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "AifList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAifList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchAssetFormFieldOptionsResponseValidationError{
					field:  "AifList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SearchAssetFormFieldOptionsResponse_CompanyList:
		if v == nil {
			err := SearchAssetFormFieldOptionsResponseValidationError{
				field:  "SearchResults",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCompanyList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "CompanyList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "CompanyList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompanyList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchAssetFormFieldOptionsResponseValidationError{
					field:  "CompanyList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SearchAssetFormFieldOptionsResponseMultiError(errors)
	}

	return nil
}

// SearchAssetFormFieldOptionsResponseMultiError is an error wrapping multiple
// validation errors returned by
// SearchAssetFormFieldOptionsResponse.ValidateAll() if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchAssetFormFieldOptionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchAssetFormFieldOptionsResponseMultiError) AllErrors() []error { return m }

// SearchAssetFormFieldOptionsResponseValidationError is the validation error
// returned by SearchAssetFormFieldOptionsResponse.Validate if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchAssetFormFieldOptionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchAssetFormFieldOptionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchAssetFormFieldOptionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchAssetFormFieldOptionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchAssetFormFieldOptionsResponseValidationError) ErrorName() string {
	return "SearchAssetFormFieldOptionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SearchAssetFormFieldOptionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchAssetFormFieldOptionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchAssetFormFieldOptionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchAssetFormFieldOptionsResponseValidationError{}

// Validate checks the field values on PmsProviderList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PmsProviderList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PmsProviderList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PmsProviderListMultiError, or nil if none found.
func (m *PmsProviderList) ValidateAll() error {
	return m.validate(true)
}

func (m *PmsProviderList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPmsProviders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PmsProviderListValidationError{
						field:  fmt.Sprintf("PmsProviders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PmsProviderListValidationError{
						field:  fmt.Sprintf("PmsProviders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PmsProviderListValidationError{
					field:  fmt.Sprintf("PmsProviders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PmsProviderListMultiError(errors)
	}

	return nil
}

// PmsProviderListMultiError is an error wrapping multiple validation errors
// returned by PmsProviderList.ValidateAll() if the designated constraints
// aren't met.
type PmsProviderListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PmsProviderListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PmsProviderListMultiError) AllErrors() []error { return m }

// PmsProviderListValidationError is the validation error returned by
// PmsProviderList.Validate if the designated constraints aren't met.
type PmsProviderListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PmsProviderListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PmsProviderListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PmsProviderListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PmsProviderListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PmsProviderListValidationError) ErrorName() string { return "PmsProviderListValidationError" }

// Error satisfies the builtin error interface
func (e PmsProviderListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPmsProviderList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PmsProviderListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PmsProviderListValidationError{}

// Validate checks the field values on AifList with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AifList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AifList with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AifListMultiError, or nil if none found.
func (m *AifList) ValidateAll() error {
	return m.validate(true)
}

func (m *AifList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAlternativeInvestmentFunds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AifListValidationError{
						field:  fmt.Sprintf("AlternativeInvestmentFunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AifListValidationError{
						field:  fmt.Sprintf("AlternativeInvestmentFunds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AifListValidationError{
					field:  fmt.Sprintf("AlternativeInvestmentFunds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AifListMultiError(errors)
	}

	return nil
}

// AifListMultiError is an error wrapping multiple validation errors returned
// by AifList.ValidateAll() if the designated constraints aren't met.
type AifListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AifListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AifListMultiError) AllErrors() []error { return m }

// AifListValidationError is the validation error returned by AifList.Validate
// if the designated constraints aren't met.
type AifListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AifListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AifListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AifListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AifListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AifListValidationError) ErrorName() string { return "AifListValidationError" }

// Error satisfies the builtin error interface
func (e AifListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAifList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AifListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AifListValidationError{}

// Validate checks the field values on CompanyList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CompanyList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CompanyList with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CompanyListMultiError, or
// nil if none found.
func (m *CompanyList) ValidateAll() error {
	return m.validate(true)
}

func (m *CompanyList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEmployerInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CompanyListValidationError{
						field:  fmt.Sprintf("EmployerInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CompanyListValidationError{
						field:  fmt.Sprintf("EmployerInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CompanyListValidationError{
					field:  fmt.Sprintf("EmployerInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CompanyListMultiError(errors)
	}

	return nil
}

// CompanyListMultiError is an error wrapping multiple validation errors
// returned by CompanyList.ValidateAll() if the designated constraints aren't met.
type CompanyListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CompanyListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CompanyListMultiError) AllErrors() []error { return m }

// CompanyListValidationError is the validation error returned by
// CompanyList.Validate if the designated constraints aren't met.
type CompanyListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CompanyListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CompanyListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CompanyListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CompanyListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CompanyListValidationError) ErrorName() string { return "CompanyListValidationError" }

// Error satisfies the builtin error interface
func (e CompanyListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCompanyList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CompanyListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CompanyListValidationError{}

// Validate checks the field values on GetPortfolioChangeSummaryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPortfolioChangeSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPortfolioChangeSummaryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPortfolioChangeSummaryRequestMultiError, or nil if none found.
func (m *GetPortfolioChangeSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPortfolioChangeSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetPortfolioChangeSummaryRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHistoryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryRequestValidationError{
					field:  "HistoryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryRequestValidationError{
					field:  "HistoryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHistoryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioChangeSummaryRequestValidationError{
				field:  "HistoryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPortfolioChangeSummaryRequestMultiError(errors)
	}

	return nil
}

// GetPortfolioChangeSummaryRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPortfolioChangeSummaryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPortfolioChangeSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPortfolioChangeSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPortfolioChangeSummaryRequestMultiError) AllErrors() []error { return m }

// GetPortfolioChangeSummaryRequestValidationError is the validation error
// returned by GetPortfolioChangeSummaryRequest.Validate if the designated
// constraints aren't met.
type GetPortfolioChangeSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPortfolioChangeSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPortfolioChangeSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPortfolioChangeSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPortfolioChangeSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPortfolioChangeSummaryRequestValidationError) ErrorName() string {
	return "GetPortfolioChangeSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPortfolioChangeSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPortfolioChangeSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPortfolioChangeSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPortfolioChangeSummaryRequestValidationError{}

// Validate checks the field values on GetPortfolioChangeSummaryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPortfolioChangeSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPortfolioChangeSummaryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPortfolioChangeSummaryResponseMultiError, or nil if none found.
func (m *GetPortfolioChangeSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPortfolioChangeSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioChangeSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAggregatedNetworthAtDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryResponseValidationError{
					field:  "AggregatedNetworthAtDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryResponseValidationError{
					field:  "AggregatedNetworthAtDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAggregatedNetworthAtDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioChangeSummaryResponseValidationError{
				field:  "AggregatedNetworthAtDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentNetworthValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryResponseValidationError{
					field:  "CurrentNetworthValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryResponseValidationError{
					field:  "CurrentNetworthValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentNetworthValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioChangeSummaryResponseValidationError{
				field:  "CurrentNetworthValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountChange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryResponseValidationError{
					field:  "AmountChange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortfolioChangeSummaryResponseValidationError{
					field:  "AmountChange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountChange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortfolioChangeSummaryResponseValidationError{
				field:  "AmountChange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PercentageChange

	if len(errors) > 0 {
		return GetPortfolioChangeSummaryResponseMultiError(errors)
	}

	return nil
}

// GetPortfolioChangeSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetPortfolioChangeSummaryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPortfolioChangeSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPortfolioChangeSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPortfolioChangeSummaryResponseMultiError) AllErrors() []error { return m }

// GetPortfolioChangeSummaryResponseValidationError is the validation error
// returned by GetPortfolioChangeSummaryResponse.Validate if the designated
// constraints aren't met.
type GetPortfolioChangeSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPortfolioChangeSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPortfolioChangeSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPortfolioChangeSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPortfolioChangeSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPortfolioChangeSummaryResponseValidationError) ErrorName() string {
	return "GetPortfolioChangeSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPortfolioChangeSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPortfolioChangeSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPortfolioChangeSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPortfolioChangeSummaryResponseValidationError{}

// Validate checks the field values on GetNetworthDataFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNetworthDataFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetworthDataFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNetworthDataFileRequestMultiError, or nil if none found.
func (m *GetNetworthDataFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetworthDataFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for NetworthDataFileType

	if len(errors) > 0 {
		return GetNetworthDataFileRequestMultiError(errors)
	}

	return nil
}

// GetNetworthDataFileRequestMultiError is an error wrapping multiple
// validation errors returned by GetNetworthDataFileRequest.ValidateAll() if
// the designated constraints aren't met.
type GetNetworthDataFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetworthDataFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetworthDataFileRequestMultiError) AllErrors() []error { return m }

// GetNetworthDataFileRequestValidationError is the validation error returned
// by GetNetworthDataFileRequest.Validate if the designated constraints aren't met.
type GetNetworthDataFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetworthDataFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetworthDataFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetworthDataFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetworthDataFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetworthDataFileRequestValidationError) ErrorName() string {
	return "GetNetworthDataFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetworthDataFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetworthDataFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetworthDataFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetworthDataFileRequestValidationError{}

// Validate checks the field values on GetNetworthDataFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNetworthDataFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetworthDataFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNetworthDataFileResponseMultiError, or nil if none found.
func (m *GetNetworthDataFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetworthDataFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetworthDataFileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetworthDataFileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetworthDataFileResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNetworthFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetworthDataFileResponseValidationError{
					field:  "NetworthFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetworthDataFileResponseValidationError{
					field:  "NetworthFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetworthFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetworthDataFileResponseValidationError{
				field:  "NetworthFile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNetworthDataFileResponseMultiError(errors)
	}

	return nil
}

// GetNetworthDataFileResponseMultiError is an error wrapping multiple
// validation errors returned by GetNetworthDataFileResponse.ValidateAll() if
// the designated constraints aren't met.
type GetNetworthDataFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetworthDataFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetworthDataFileResponseMultiError) AllErrors() []error { return m }

// GetNetworthDataFileResponseValidationError is the validation error returned
// by GetNetworthDataFileResponse.Validate if the designated constraints
// aren't met.
type GetNetworthDataFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetworthDataFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetworthDataFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetworthDataFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetworthDataFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetworthDataFileResponseValidationError) ErrorName() string {
	return "GetNetworthDataFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetworthDataFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetworthDataFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetworthDataFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetworthDataFileResponseValidationError{}
