syntax = "proto3";

package cx.developer.actions;

option go_package = "github.com/epifi/gamma/api/cx/developer/actions";
option java_package = "com.github.epifi.gamma.api.cx.developer.actions";

// List of available actions in sherlock for developers
enum DeveloperActions {
  DEVELOPER_ACTIONS_UNSPECIFIED = 0;

  RETRY_LIVENESS = 1;

  // https://github.com/epiFi/protos/blob/bb52b54cec939223c9342c83f7b58ea891005c52/api/user/service.proto#L253
  RETRY_CREATE_BANK_CUSTOMER = 2;

  // https://github.com/epiFi/protos/blob/bb52b54cec939223c9342c83f7b58ea891005c52/api/savings/service.proto#L19
  RETRY_CREATE_ACCOUNT = 3;

  // Calls KYC Service's InitiateEKYC RPC with force retry flag enabled
  RETRY_EKYC = 4;

  // Calls KYC Service's InitiateCKYC RPC
  RETRY_CKYC = 5;

  // creates new card for customer
  CREATE_CARD = 6;

  // creates a new reward offer
  CREATE_REWARD_OFFER = 7;

  // updates the status of reward offer to APPROVED/ACTIVE/IN_ACTIVE
  UPDATE_REWARD_OFFER_STATUS = 8;

  // creates a new lucky draw campaign
  CREATE_LUCKY_DRAW_CAMPAIGN = 9;

  // creates a new lucky draw
  CREATE_LUCKY_DRAW = 10;

  PULL_APP_LOGS = 11;

  ADD_USER_GROUP_MAPPING = 12;

  // Calls onboarding service and delete user.
  // It allow a user to begin onboarding again from scratch.
  DELETE_USER = 13;

  RECOVER_AUTH_FACTOR_UPDATE = 14;

  // Reallow user to retry waitlist.
  REALLOW_USER_TO_TRY_WAITLIST = 15;

  // updates the display properties of existing reward offer.
  UPDATE_REWARD_OFFER_DISPLAY = 16;

  // Retrigger failed waitlist emails to users
  TRIGGER_FAILED_WAITLIST_EMAIL = 17;

  // retrigger indexing pis for actor
  TRIGGER_FAILED_PIS_INDEXING = 18;

  // creates an offer
  CREATE_OFFER = 19;

  // creates an offer listing.
  CREATE_OFFER_LISTING = 20;

  // updates an offer listing.
  UPDATE_OFFER_LISTING = 21;

  // deletes an offer listing.
  DELETE_OFFER_LISTING = 22;

  // creates an offer inventory
  CREATE_OFFER_INVENTORY = 23;

  // adds offers to inventory.
  ADD_OFFER_TO_INVENTORY = 24;

  // deletes an offer inventory.
  DELETE_OFFER_INVENTORY = 25;

  // force trigger recon for an account from a given date
  FORCE_TRIGGER_RECON = 26;

  // retrigger gmail data onboarded actor and waitlist actor sync
  TRIGGER_GMAIL_ACTOR_SYNC = 27;

  // remove access to read mail data for an emailId
  UNLINK_GMAIL_ACCOUNT = 28;

  // Delete waitlist user from user ID.
  DELETE_WAITLIST_USER_FROM_USER_ID = 29;

  // Mark waitlist users early access based on current day ranking.
  MARK_WAITLIST_USERS_EARLY_ACCESS = 30;

  // Send app access emails to early access waitlisted users.
  SEND_EMAIL_TO_EARLY_ACCESS = 31;

  // sync onboarding states and gets next action
  SYNC_ONBOARDING = 32;

  FITTT_CREATE_NEW_RULE = 33;

  FITTT_UPDATE_RULE = 34;

  FITTT_GET_RULES_FOR_CLIENT = 35;

  // force update status of an onboarding state
  UPDATE_ONBOARDING_STAGE = 36;

  SEND_COMMS_TO_WAITLIST_USER = 37;

  // updates the display properties of existing offer.
  UPDATE_OFFER_DISPLAY = 38;

  // Seed fi.nite codes to waitlist tables.
  SEED_FINITE_CODES = 39;

  // Action to initate match update for cricket rules
  FITTT_INITIATE_MATCH_UPDATE = 40;

  // Action to trigger card notifications
  INITIATE_CARD_NOTIFICATIONS = 41;

  // Action to mark liveness as passed for actorId
  MARK_LIVENESS_PASSED = 43;

  CREATE_PREFERENCE = 44;

  // updates shipping address at vendor - gives option whether or not do to card creation
  UPDATE_SHIPPING_ADDRESS_AT_VENDOR = 45;

  // Crates a new shipping preference for a user for an item
  CREATE_SHIPPING_PREFERENCE = 46;

  // Retry payment order event in rule manager service(rms)
  RETRY_PAY_ORDER_RMS_EVENT = 47;

  // Send introductory welcome whatsapp message to users
  SEND_WELCOME_WHATSAPP = 48;

  // Send comm for reward campaigns.
  SEND_REWARDS_CAMPAIGN_COMM = 49;

  // Fetch unredacted user details using user id or mob num or email
  UNREDACTED_USER = 50;

  // Action to process the order
  // To be used to fetch the latest status of the order irrespective of its state
  FORCE_PROCESS_ORDER = 51;

  // Action to retry processing of a stuck reward.
  RETRY_REWARD_PROCESSING = 52;

  // action to get txn aggregate for fit txns from search
  GET_FIT_TXN_AGGREGATE_SEARCH = 53;

  // action to change user's revoke access
  // deprecated in favour of UPDATE_ACCOUNT_FREEZE_STATUS
  UPDATE_USER_ACCESS_REVOKE_STATE = 54 [deprecated = true];

  // action to update user's firstName and lastName in user profile
  UPDATE_USER_PROFILE_NAME = 55;

  // Action to generate account statement for a specific account id within a given time period
  GENERATE_ACCOUNT_STATEMENT = 56;

  // Action to fetch active onboarding alerts
  ONBOARDING_SNAPSHOT = 57;

  // Action to backfill contact details for a ticket in case webhook flow failed for that ticket for some reason
  BACKFILL_FRESHDESK_TICKET_CONTACTS = 58;

  // Action to mark an onboarding stage success after manual review
  PASS_ONBOARDING_STAGE = 59;

  // Action to create a reward offer group.
  CREATE_REWARD_OFFER_GROUP = 60;

  // Action to reset user kyc name dob retries
  RESET_KYC_NAME_DOB_RETRY = 61;

  // Action to retry processing of a stuck offer redemption.
  RETRY_OFFER_REDEMPTION = 62;

  // Action to resolve user in manual screening stage
  MANUAL_SCREENING_UPDATE = 63;

  // Action to unblock UN NAME check user
  UN_NAME_CHECK_UNBLOCK = 64;

  // Action to reset user debit card name retries
  RESET_DEBIT_CARD_NAME_RETRY = 65;

  // Action to stop schedules and cancel jobs
  FITTT_STOP_SCHEDULES_JOBS = 66;

  // Action to check vkyc status and refresh based on scheme code
  REFRESH_VKYC_STATUS = 67;

  // Action to process card pin set for users who have already set their card pin using Federal IVR
  UPDATE_CARD_PIN_SET = 68;

  // Raise AA consent given customer ID and actor ID
  RAISE_AA_CONSENT = 69;

  // Action to trigger enquiry for card creation
  FORCE_CARD_CREATION_ENQUIRY = 70;

  // Mark facematch status as passed
  MARK_FACEMATCH_PASSED = 71;

  // Action to trigger manual giveaway event for giving giveaway reward to user.
  TRIGGER_REWARDS_MANUAL_GIVEAWAY_EVENT = 72;

  // Action to delete user group mapping
  DELETE_USER_GROUP_MAPPING = 73;

  // Actor to trigger next action of wealth onboarding
  SYNC_WEALTH_ONBOARDING = 74;

  // Manual In-App-Referral unlock for testing purposes
  UNLOCK_IN_APP_REFERRAL = 75;

  // trigger vkyc agent/auditor callback
  TRIGGER_VKYC_CALLBACK = 76;

  // Action to mark savings account closed in our db.
  HANDLE_SAVINGS_ACCOUNT_CLOSURE = 77;

  // Add/Remove gmail insights merchants
  UPDATE_GMAIL_INSIGHTS_MERCHANTS = 78;

  // Add/Remove gmail insights merchant queries
  UPDATE_GMAIL_INSIGHTS_MERCHANT_QUERIES = 79;

  // Update FAQs (Category, Folder, Articles)
  UPDATE_INAPPHELP_FAQ = 80;

  // Non prod action for QA to update consent status in simulator
  AA_CONSENT_STATUS_UPDATE = 81;

  // Non prod action for QA to delink AA account from simulator
  AA_ACCOUNT_DELINK = 82;

  // Action to reactivate device of a user at vendor's end
  REACTIVATE_DEVICE = 83;

  // weightage is used for ordering rules on UI, this action will help change the ordering of rules on app using sherlock
  FITTT_UPDATE_RULES_WEIGHTAGE = 84;

  // Action to re-open a savings account. This will only mark the account as CREATED in our DB.
  // Does not make any calls to Federal.
  REOPEN_SAVINGS_ACCOUNT_IN_DB = 85;

  // Action to update status of wealth onboarding
  UPDATE_WEALTH_ONBOARDING_STATUS = 86;

  // create fittt home card
  FITTT_CREATE_HOME_CARD = 87;

  // update fittt home card
  FITTT_UPDATE_HOME_CARD = 88;

  // Action to update pan name check verdict
  UPDATE_PAN_NAME_REVIEW = 89;

  // Action to add new to media playlist
  ADD_MEDIA_PLAYLIST = 90;

  // Action to update existing media playlist
  UPDATE_MEDIA_PLAYLIST = 91;

  // Action to add new media content story
  ADD_MEDIA_CONTENT_STORY = 92;

  // Action to update existing media content story
  UPDATE_MEDIA_CONTENT_STORY = 93;

  // Action to create new schedule in fittt's scheduler service
  FITTT_CREATE_SCHEDULE = 94;

  // Action to add UIContext to Media Playlist mapping
  ADD_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING = 95;

  // Action to delete UIContext to Media Playlist mapping
  DELETE_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING = 96;

  // Action to add Media Playlist to Media Content mapping
  ADD_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING = 97;

  // Action to delete Media Playlist to Media Content mapping
  DELETE_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING = 98;

  // Action to add meta data like reference number for credit mis report for the corresponding file in the db.
  ADD_CREDIT_MIS_FILE_META_DATA = 99;

  // Action to bulk update subscriptions state for a rule
  FITTT_BULK_UPDATE_SUBSCRIPTIONS_STATE = 100;
  // Action to add new Mutual Fund to mutual fund catalog
  CREATE_MUTUAL_FUND = 103;

  // Action to update Mutual Fund in mutual fund catalog
  UPDATE_MUTUAL_FUND = 102;

  // Action to upload a mutual fund reverse feed file.
  MUTUAL_FUND_UPLOAD_REVERSE_FEED_FILE = 101;

  // Replay AA Account Events
  REPLAY_AA_ACCOUNT_EVENTS = 104;

  // action to create an exchanger offer
  CREATE_EXCHANGER_OFFER = 105;

  // action to create a listing for an exchanger offer
  CREATE_EXCHANGER_OFFER_LISTING = 106;

  // action to update display of an exchanger offer
  UPDATE_EXCHANGER_OFFER_DISPLAY = 107;

  // action to update an exchanger offer listing
  UPDATE_EXCHANGER_OFFER_LISTING = 108;

  // action to delete an exchanger offer listing
  DELETE_EXCHANGER_OFFER_LISTING = 109;

  // Replay AA Transaction Events
  REPLAY_AA_TXN_EVENTS = 110;

  // archives requested rules and subscriptions
  FITTT_ARCHIVE_RULES_AND_SUBSCRIPTIONS = 111;

  // action to force process a deposit request
  FORCE_PROCESS_DEPOSIT_REQUEST = 112;
  // search parse query base
  SEARCH_PARSE_QUERY_BASE = 113;

  // mark wealthonboarding livness passed
  MARK_WEALTH_ONBOARDING_LIVENESS_PASSED = 114;

  MUTUAL_FUND_DEACTIVATE_ENTITY_FROM_FILE = 115;

  // action to create new fittt collection
  FITTT_CREATE_COLLECTION = 116;

  // action to update existing fittt collection
  FITTT_UPDATE_COLLECTION = 117;

  // mark wealthonboarding redaction review passed
  MARK_WEALTH_ONBOARDING_REDACTION_PASSED = 118;

  // updates order status of a list of mutual funds
  UPDATE_MUTUAL_FUND_ORDER_STATUS = 119;

  // trigger Recurring Payment Execution
  TRIGGER_RECURRING_PAYMENT_EXECUTION = 120;

  // Action to upload a csv for custom user campaign usecase
  UPLOAD_MARKETING_CAMPAIGN_USERS_LIST = 121;

  // mark wealthonboarding expiry review passed
  MARK_WEALTH_ONBOARDING_EXPIRY_PASSED = 122;

  // Action to download credit mis report
  MUTUAL_FUND_DOWNLOAD_CREDIT_MIS_REPORT = 123;

  // Action to add In App targeted comms elements like banner, bottom sheet etc.
  ADD_IN_APP_TARGETED_COMMS_ELEMENT = 124;

  UPDATE_IN_APP_TARGETED_COMMS_ELEMENT = 125;

  ADD_IN_APP_TARGETED_COMMS_MAPPING = 126;

  DELETE_IN_APP_TARGETED_COMMS_MAPPING = 127;

  CREATE_EXCHANGER_OFFER_GROUP = 128;

  // Action to generate and download mutual fund ops file
  MF_DOWNLOAD_OPS_FILE = 129;

  // Action to generate and download mutual fund ops file
  MF_RE_TRIGGER_PRE_REQUISITES = 130;

  // Action to un-suspend/activate card
  MANUAL_CARD_UNSUSPEND = 131;

  // Action to update insight framework
  UPDATE_INSIGHT_FRAMEWORK = 132;

  // Action to update insight segment
  UPDATE_INSIGHT_SEGMENT = 133;

  // Action to update insight content template
  UPDATE_INSIGHT_CONTENT_TEMPLATE = 134;

  // action to bulk create/update mutual fund entries
  // action will upload a file to s3 bucket
  MF_UPLOAD_CATALOG_UPDATE = 135;

  // action to update offer display ranks
  UPDATE_OFFER_DISPLAY_RANK = 136;

  // Action to mock uploading of Credit MIS Report to vendor in non-prod envs
  MF_UPLOAD_CREDIT_MIS_NON_PROD = 137;

  CREATE_EXCHANGER_OFFER_INVENTORY = 138;

  // action to update reward display rank
  UPDATE_REWARD_OFFER_DISPLAY_RANK = 139;

  // action to add user to VKYC priority list
  ADD_USER_TO_VKYC_PRIORITY = 140;

  // action to update user DOB
  UPDATE_USER_DOB = 141;

  // action to update investment transaction status
  UPDATE_P2P_INVESTMENT_TRANSACTION_STATUS = 142;

  // action to update user's photo. Note :- This photo is used to do facematch while AFU
  UPDATE_USER_PHOTO = 143;

  // action to update investor total investment count
  UPDATE_P2P_INVESTOR_TOTAL_INVESTMENT_COUNT = 144;

  // Action to create a mutual fund collection
  MF_CREATE_COLLECTION = 145;

  // Action to update details of a mutual fund collection
  MF_UPDATE_COLLECTION = 146;

  // Action to add a mutual fund to a collection
  MF_ADD_FUND_TO_COLLECTION = 147;

  // Action to update mutual funds info in a collection
  MF_UPDATE_FUND_IN_COLLECTION = 148;

  // Action to remove mutual funds from a collection
  MF_REMOVE_FUNDS_FROM_COLLECTION = 149;

  // action to process a reverse feed file for mutual fund.
  MF_PROCESS_REVERSE_FEED_FILE = 150;

  // Action to update liquiloans approval status for P2P
  UPDATE_P2P_VENDOR_RESPONSE_APPROVAL_STATUS = 151;

  // action to request for a physical card dispatch for user who currently has a digital card
  PHYSICAL_CARD_REQUEST = 152;

  // Increment exchanger offer inventory
  INCREMENT_EXCHANGER_OFFER_INVENTORY = 153;

  // action to create a new progressive referral rewards season
  CREATE_REFERRALS_SEASON = 154;

  // action to update existing progressive referral rewards season with new values
  UPDATE_REFERRALS_SEASON = 155;

  // action to delete segment from rdb
  DELETE_SEGMENT = 156;

  // action to mark step stale for wealthOnboarding
  MARK_STEP_STALE_WEALTH_ONBOARDING = 157;

  // action to blocking the existing card and request a new card with address type sent in the request
  REQUEST_NEW_CARD = 158;

  // trigger VPA Creation
  TRIGGER_VPA_CREATION = 159;

  // add manual call routing mappings
  ADD_MANUAL_CALL_ROUTING_MAPPINGS = 160;

  // action to trigger segment export
  TRIGGER_SEGMENT_EXPORT = 161;

  // action to perform different reconciliation for mutual funds
  MF_RECONCILIATION = 162;

  // action to create a ticket category tags to title, description transformation for showing tickets in the app
  CREATE_TICKET_DETAILS_TRANSFORMATION = 163;

  // action to update a ticket category tags to title, description transformation for showing tickets in the app
  UPDATE_TICKET_DETAILS_TRANSFORMATION = 164;

  // action to update a ticket category tags to title, description transformation for showing tickets in the app
  DELETE_TICKET_DETAILS_TRANSFORMATION = 165;

  //action to convert deeplink to base64 encoding
  DEEPLINK_BASE64_ENCODER = 166;

  // action for Op agents to manually review and approve/reject liveness/facematch and other checks for preapprovedloan
  PRE_APPROVED_LOAN_MANUAL_REVIEW = 167;

  // action to create a loon offer for a user.
  PRE_APPROVED_LOAN_CREATE_OFFER = 168;

  // action to be used to approve a loan request for a user. Will be used only in non-prod envs
  PRE_APPROVED_LOAN_UPDATE_LOAN_STATUS = 169;

  // CATEGORISE_SCREENER_DOMAINS dev action is used to allow/block work/student email domains in screener
  CATEGORISE_SCREENER_DOMAINS = 170;

  // action to create a salaryprogram referrals season
  CREATE_SALARY_PROGRAM_REFERRALS_SEASON = 171;

  // action to start a new user action
  START_USER_ACTION = 172;

  // action to create user nudge
  CREATE_NUDGE = 173;

  // Edit nudge for user
  EDIT_NUDGE = 174;

  // used to perform risk related savings account activities like freezing, credit freezing, unfreezing etc.
  SAVINGS_RISK_BANK_ACTIONS = 175;

  // action to update father name
  UPDATE_USER_FATHER_NAME = 176;

  // used to update savings account freeze status in account simulator db
  UPDATE_ACCOUNT_FREEZE_STATUS_IN_SIMULATOR = 177;

  // action to update lrs limit for users
  INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE = 178;

  // action to acknowledge approved international fund transfer transactions and mark them as completed
  INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_SWIFT_TRANSFER = 179;

  // action to acknowledge approved inward fund transfer transactions and mark them as completed
  INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER = 180;

  // action to fetch all users for whom LRS is to be checked
  PAY_DOWNLOAD_LRS_CHECK_FILE = 181;

  // action to process inward remittance flow from us stock broker and foreign remittance partner
  FOREIGN_REMITTANCE_PROCESS_INWARD_REMITTANCE = 182;

  // used to update savings account freeze status (full/credit freeze/unfreeze)in savings and revoke
  // app access if applicable
  UPDATE_ACCOUNT_FREEZE_STATUS = 183;

  // action to create a new discount
  CREATE_DISCOUNT = 184;

  // action to delete discount
  DELETE_DISCOUNT = 185;

  // action to upload user specific info to be displayed to agents
  UPLOAD_USER_ISSUE_INFO_FOR_AGENTS = 186;

  // action to update users employment data (salary and occupation)
  UPDATE_USER_EMPLOYMENT = 187;

  // action to create or update segment mapping
  CREATE_OR_UPDATE_SEGMENT_MAPPING = 188;

  // action to add new deposit interest rates
  DEPOSIT_ADD_INTEREST_RATE = 189;

  // action to update the deposit interest rates of existing entries
  DEPOSIT_UPDATE_INTEREST_RATE = 190;

  // action to delete the deposit interest rate of existing entry
  DEPOSIT_DELETE_INTEREST_RATE = 191;

  // action to get docket urls for given pan numbers
  GET_DOCKET_URL_WEALTH_ONBOARDING = 192 [deprecated = true];

  // update exchanger offer status
  UPDATE_EXCHANGER_OFFER_STATUS = 193;

  // action to change the status to Hunter api response
  PROFILE_EVALUATOR_ACTIONS = 194;

  // action to update verification status for income and occupation discrepancy
  INCOME_OCCUPATION_DISCREPANCY_VERIFICATION = 195;

  // action to create a segmented component for referrals
  CREATE_REFERRALS_SEGMENTED_COMPONENT = 196;

  // action to update a segmented component for referrals
  UPDATE_REFERRALS_SEGMENTED_COMPONENT = 197;

  // action to delete a segmented component for referrals
  DELETE_REFERRALS_SEGMENTED_COMPONENT = 198;
  // action to upload list of risk cases to be reviewed
  // this dev action will take case of all the transformations and steps required to push the cases in the queue for review by ops team
  UPLOAD_RISK_CASES = 199;

  // action to create a segment
  CREATE_SEGMENT = 200;

  // dev action to be used for taking action on a txn manual review case
  RISK_TXN_CASE_MANUAL_REVIEW = 201;

  // dev action for removing red list entry
  REMOVE_RED_LIST_ENTRY = 202;

  // dev action for adding new red list entry
  ADD_RED_LIST_ENTRY = 203;

  // DEV ACTION TO FETCH INVESTOR JUMP DASHBOARD
  P2P_GET_INVESTOR_DASHBOARD = 204;

  // DEV ACTION TO FETCH JUMP INVESTMENT SUMMARY FOR INVESTOR
  P2P_GET_INVESTMENT_SUMMARY = 205;

  // DEV ACTION TO GET P2P RECON FILE FOR OPS
  P2P_DOWNLOAD_RECON_FILE = 206;

  // dev action to get signed url for given s3 path for wealth onboarding bucket
  GET_SIGNED_URL_WEALTH = 207;

  // dev action for updating credit card request status
  CREDIT_CARD_UPDATE_CARD_REQUEST_STATUS = 208;

  // to refresh stock details like market cap, revenue, etc.
  USSTOCKS_REFRESH_STOCK_DETAILS = 209;

  // to update status of a nudge
  UPDATE_NUDGE_STATUS = 210;

  // dev action to upload aml reports
  UPLOAD_AML_REPORT = 211;

  // dev action to deactivate device
  DEACTIVATE_DEVICE = 212;

  // action for Federal agents to manually review and approve/reject liveness for preapprovedloan FOR CKYC users
  FEDERAL_LOAN_LIVENESS_REVIEW = 213;

  // dev action to create reward offer in bulk
  CREATE_REWARD_OFFER_IN_BULK = 214;

  // dev action to publish shark tank event, this event will trigger relevant rule execution
  FITTT_PUBLISH_SHARK_TANK_EVENT = 215;

  // dev action to create a new forex rate entry
  CREATE_FOREX_RATE = 216;

  // dev action to update a forex rate entry
  UPDATE_FOREX_RATE = 217;

  // dev action to give another chance to user to re attempt vkyc
  REPRIEVE_VKYC = 218;

  // dev action to get gross summary report for given dates (us stocks)
  USSTOCKS_GROSS_SUMMARY = 219;

  // dev action to update file generation status
  AML_UPDATE_FILE_GEN_STATUS = 220;

  // dev action to download file from s3 bucket
  MF_DOWNLOAD_FILE_FROM_BUCKET = 221;

  // dev action to perform action for risk transaction review
  PERFORM_RISK_REVIEW_ACTION = 222;

  // dev action to generate and get forex rate report for the given dates
  FOREX_RATE_REPORT = 223;

  // dev action to get the current live forex rate
  CURRENT_FOREX_RATE = 224;

  // dev action to upload corrected wealth kyc images for an actor
  UPLOAD_WEALTH_DOCUMENT = 225;

  // dev action to update number of days remaining to update
  UPDATE_P2P_VENDOR_RESPONSE_MATURITY_TRANSACTION_DAYS_TO_EXPIRE = 226;

  // dev action to fetch forex rates with filters
  GET_FOREX_RATES = 227;

  // dev action to be used to approve a applicant/mandate status for pre-approved loan created on Liquiloans side
  PRE_APPROVED_LOAN_LL_UPDATE_ENTITY_STATUS = 228;

  // dev action to create temporal schedule which triggers a workflow periodically
  // NOTE : Currently we have added support for interval based scheduling action
  CREATE_TEMPORAL_SCHEDULE = 229;

  // Dev action to add new promo banner
  ADD_HOME_PROMO_BANNER = 230;

  // dev action to get account statement for given dates for us stocks pool accounts (inward and outward)
  USSTOCKS_ACCOUNT_STATEMENT = 231;

  // dev action to retry mutual fund orders
  RETRY_MUTUAL_FUND_ORDER = 232;

  // dev action to create allowed annotation for case management
  RISK_CREATE_ALLOWED_ANNOTATION = 233;

  // dev action to add manual override states under risk bank action
  RISK_BANK_ACTION_MANUAL_OVERRIDE = 234;

  // dev action to fetch s3 from usstocks bucket
  USSTOCKS_DOWNLOAD_FILE_FROM_BUCKET = 235;

  // dev action to create nudges in bulk
  CREATE_NUDGES_IN_BULK = 236;

  // dev action for vendor account penny drop verification
  VENDOR_ACCOUNT_PENNY_DROP = 237;

  // dev action to update pending user data status
  UPDATE_WEALTH_USER_INPUT_PENDING_DATA = 238;

  // action to create offers in bulk
  CREATE_OFFERS_IN_BULK = 239;

  RESET_CELESTIAL_WORKFLOW_EXECUTION = 240;

  // action to create notification config infos
  CREATE_REFERRAL_NOTIFICATION_CONFIG = 241;

  // action to update notification config status
  UPDATE_REFERRAL_NOTIFICATION_CONFIG_STATUS = 242;

  // action to update notification config content
  UPDATE_REFERRAL_NOTIFICATION_CONFIG_CONTENT = 243;

  // action to delete notification config infos
  DELETE_REFERRAL_NOTIFICATION_CONFIG = 244;

  // action for uploading lea report csvs
  UPLOAD_LEA_COMPLAINTS = 245;

  // Create pop up for home
  ADD_HOME_POP_UP_BANNER = 246;

  // action to list all the deposit accounts for an actor from a vendor
  DEPOSIT_LIST_ACCOUNTS_VENDOR = 247;

  // dev action to mark loan request as cancelled will be used in prod
  MARK_LOAN_REQUEST_CANCEL = 248;

  // Generate aggregated tax report over a time period
  IFT_GENERATE_AGGREGATED_TAX_REPORT = 249;

  // create or update dynamic ui element variant
  CREATE_OR_UPDATE_DYNAMIC_UI_ELEMENT_VARIANT = 250;

  // updates the dynamic ui element evaluator config
  UPDATE_DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG = 251;

  // dev action to create a product for cms
  CREATE_CMS_PRODUCT = 252;

  // dev action to create a sku for cms
  CREATE_CMS_SKU = 253;

  // dev action to create a product for cms
  CREATE_CMS_COUPON_IN_BULK = 254;

  // Create home layout
  CREATE_HOME_LAYOUT = 255;

  // Create layout with mapping on segments
  CREATE_HOME_LAYOUT_SEGMENT_MAPPING = 256;

  // Delete layout and all associated segment mappings
  DELETE_HOME_LAYOUT = 257;

  // Delete a given segment mapping
  DELETE_HOME_LAYOUT_SEGMENT_MAPPING = 258;

  MARK_USERS_BY_ACQUISITION_INFO = 259;

  // Get home layout
  GET_HOME_LAYOUT = 260;

  // Get home layout segment mapping
  GET_HOME_LAYOUT_SEGMENT_MAPPING = 261;

  // Add a new survey in feedback engine
  ADD_INAPPHELP_FEEDBACK_ENGINE_SURVEY = 262;

  // Update an existing survey in feedback engine
  UPDATE_INAPPHELP_FEEDBACK_ENGINE_SURVEY = 263;

  // Add a new question in feedback engine
  ADD_INAPPHELP_FEEDBACK_ENGINE_QUESTION = 264;

  // Update an existing question in feedback engine
  UPDATE_INAPPHELP_FEEDBACK_ENGINE_QUESTION = 265;

  // dev action to mark card delivery tracking state to received
  MARK_CARD_DELIVERY_TRACKING_STATE_RECEIVED = 266;

  // dev action to mark any loan step success of fail
  LOAN_UPDATE_LOAN_STEP_STATUS = 267;

  // Action to create a usstocks  collection
  USSTOCKS_CREATE_COLLECTION = 268;

  // Action to update details of a usstocks collection
  USSTOCKS_UPDATE_COLLECTION = 269;

  // Action to add a usstocks to a collection
  USSTOCKS_ADD_STOCK_TO_COLLECTION = 270;

  // Action to update usstocks info in a collection
  USSTOCKS_UPDATE_STOCK_IN_COLLECTION = 271;

  // Action to remove usstocks from a collection
  USSTOCKS_REMOVE_STOCK_FROM_COLLECTION = 272;

  // action to update a segment
  UPDATE_SEGMENT = 273;

  // dev action to add and index employers to employer database.
  // Employer db is used by salaryprogram and onboarding to store the company which a user works in
  ADD_EMPLOYERS = 274;

  // Reject transactions in bulk outward SWIFT transfer file
  IFT_REJECT_TRANSACTIONS_IN_OUTWARD_SWIFT_FILE = 275;

  // action to whitelist an ephemeral email id in QA
  WHITELIST_EMAIL_ID = 276;

  // dev action to create a new kyc agent.
  // Kyc Agent creation involves creating entries in agent, casbin, actor. It also generates initial login credentials
  // and mails them to mentioned email id.
  CREATE_KYC_AGENT = 277;

  // data extraction tool for various data extraction requests
  DATA_EXTRACTION = 278;

  // dev action to create issue config
  // new record is created in issue_configs table, containing a payload corresponding to issue_category_id and config_type
  CREATE_ISSUE_CONFIG = 279;

  // action to update reward Offer details
  UPDATE_REWARD_OFFER = 280;

  // action to generate chatbot access token for debugging purpose
  GENERATE_CHATBOT_ACCESS_TOKEN = 281;

  // action to register banking details for a jump investor
  P2P_REGISTER_BANKING_DETAILS = 282;

  // To upload narrations for lea complaints.
  UPLOAD_LEA_COMPLAINT_NARRATIONS = 283;

  // To update the employer details.
  UPDATE_EMPLOYER_DETAILS = 284;

  // Bulk setup referral segmented components
  BULK_SETUP_REFERRAL_SEGMENTED_COMPONENTS = 285;

  // To upload lea complaint source details
  // i.e law enforcement agency details from which the complaint was received.
  UPLOAD_LEA_COMPLAINT_SOURCE_DETAILS = 286;

  // Control user communications
  USER_COMMS_CONTROL = 287;

  // To Update Card Req stage status for credit card workflows
  CREDIT_CARD_UPDATE_CARD_REQUEST_STAGE_STATUS = 288;

  // Dev action to simulate outward fund transfer for testing in non-prod
  // NOTE: this dev action will not be present in Prod env
  SIMULATE_USSTOCKS_OUTWARD_FUND_TRANSFER_NON_PROD = 289;

  // To create mapping of list of FAQs corresponding to a context value
  CREATE_FAQ_CONTEXT_MAPPING = 290;
  // To update any field in p2p investment transaction
  P2P_UPDATE_INVESTMENT_TRANSACTION = 291;
  // Fetch current account status from bank api
  FETCH_ACCOUNT_STATUS = 292;

  // dev action to create home simulator layout
  CREATE_HOME_SIMULATOR_LAYOUT = 293;

  // Get wealth onboarding details by PAN for CX agents
  GET_WEALTH_ONBOARDING_DETAILS_BY_PAN = 294;

  // dev action to create mapping of ticket details required to be populated for specific Watson use-case
  CREATE_WATSON_TICKET_DETAILS = 295;

  // dev action to update the Watson ticket details mapping
  UPDATE_WATSON_TICKET_DETAILS = 296;

  // dev action to link bank account with firm account at us stocks broker
  // associated bank account is used for inward fund transfer
  USSTOCKS_CREATE_BANK_RELATIONSHIP_WITH_BROKER = 297;

  // dev action for anything releated to ABFL changes in simulator vg of PAL
  LOANS_ABFL_ACTIONS = 298;

  // dev action to trigger kyc name dob validation at Federal
  TRIGGER_KYC_NAME_DOB_VALIDATION = 299;

  // dev action to get onboarding troubleshooting details
  ONBOARDING_TROUBLESHOOTING_DETAILS = 300;

  // dev action to create config mapped against specific event in Sherlock DB
  CREATE_EVENT_CONFIG = 301;

  // dev action to update config mapped against specific event in Sherlock DB
  UPDATE_EVENT_CONFIG = 302;

  // dev action to fail bank customer to retry
  FAIL_BANK_CUSTOMER = 303;

  //dev action for P2P CASH LEDGER
  P2P_GET_CASH_LEDGER = 304;

  //dev action to show all folio from actorId
  GET_MF_FOLIO_BALANCE = 305;

  // Initiate refund for one or more international outward remittances backed by concrete reasons
  IFT_INITIATE_REFUND = 306;

  // dev action for raising manual salary verification requests in bulk from csv as input
  RAISE_MANUAL_SALARY_VERIFICATION_REQUESTS_IN_BULK = 307;

  // dev action to simulate reward generation flow and publish rewards if not generated for some reason from event id and offer id
  SIMULATE_REWARD_GENERATION = 308;

  // dev action to update salaryprogram referrals season
  UPDATE_SALARY_PROGRAM_REFERRALS_SEASON = 309;

  // dev action to process itc points hand back file and update corresponding redeemed offer status
  PROCESS_ITC_POINTS_HANDBACK_FILE = 310;

  // Dev action to upload fraud/false disputes shared by federal.
  RISK_UPLOAD_DISPUTES = 311;

  // dev action for fetching, updating and creating segment metadata
  SEGMENT_METADATA_ACTION = 312;

  // dev action for approving the segments metadata
  SET_SEGMENT_METADATA_APPROVAL_STATUS = 313;

  // dev action for overriding payment health engine status
  OVERRIDE_PAYMENT_HEALTH_ENGINE_STATUS = 314;

  // dev action for overriding CSIS health engine status
  OVERRIDE_CSIS_HEALTH_ENGINE_STATUS = 315;

  // dev action to manual override sof limits (to be used by ops post sof review of users)
  SOF_LIMIT_MANUAL_OVERRIDE = 316;

  // Dev action to pass screener attempt for an actor
  PASS_RISK_SCREENER_ATTEMPT = 317;

  // Dev action to add a new entry in pin code master table
  ADD_PIN_CODE_ENTRY = 318;

  // Dev action to extract PAN detected manually in KRA-verified KYC docket of a user
  EXTRACT_PAN_FROM_KRA_DOCKET = 319;

  // Dev action to reset us stocks onboarding data
  USSTOCKS_RESET_ONBOARDING_DATA = 320;

  // dev action to add or remove multiple members from whitelist
  RISK_MANAGE_WHITELIST = 321;

  // Dev action to manually add investor address in uss
  USSTOCKS_ADD_INVESTOR_ADDRESS = 322;

  // dev action to create activity metadata record into DB
  CREATE_ACTIVITY_METADATA = 323;

  // Dev action to manually trigger categorisation for a set of transactions
  TRIGGER_TXN_CATEGORISATION = 324;

  // Dev action to get a us stocks account activities in csv format
  GET_US_STOCK_ACCOUNT_ACTIVITIES_CSV = 325;

  // Dev Action to manually trigger renewal fee reversal for unsecured credit card
  TRIGGER_UNSECURED_CC_RENEWAL_FEE_REVERSAL = 326;

  // For remitting transactions which were excluded in
  // the original batch inward remittance process
  // because of the accounts being credit-frozen, etc.
  USSTOCKS_GENERATE_ADHOC_INWARD_REMITTANCE_FILES = 327;

  // dev-action to respond any user's query by leveraging user's context and epifi knowledge-base
  // this dev-action will be used by internal Fi-employees for testing purpose only
  GENERATE_MODEL_RESPONSE_FOR_USERS_CX_QUERY = 328;

  // For excluding any actors txns from inward file and regenerate
  USSTOCKS_REGENERATE_INWARD_FILE = 329;

  // Dev action to manually map a child txn id to parent txn id once
  // refund is completed for the txn
  MAP_DEBIT_CARD_FOREX_TXN = 330;

  // dev action to create journey record in DB
  CREATE_JOURNEY = 331;

  // dev action to update journey record in DB
  UPDATE_JOURNEY = 332;

  // dev action to update activity metadata in db
  UPDATE_ACTIVITY_METADATA = 333;

  // dev action to handle vkyc call state
  HANDLE_VKYC_CALL_STATE = 334;

  // dev action to upload risk related unified lea complaints
  UPLOAD_UNIFIED_LEA_COMPLAINTS = 335;

  // dev action to soft delete service request in non-prod environment
  DELETE_SERVICE_REQUEST = 336;

  // dev action for failing debit card creation request
  FAIL_DEBIT_CARD_CREATION = 337;

  // dev action to get trading account details for account closure
  USSTOCKS_TRADING_ACCOUNT_SUMMARY = 338;

  // dev action to cache model response for standard questions in CX contact us flow
  CACHE_CONTACT_US_MODEL_RESPONSE = 339;

  // dev action for b2b users onboarding status tracking
  B2B_USERS_ONBOARDING_STATUS_TRACKING = 340;

  // dev action to update internal status of usstocks
  USSTOCKS_UPDATE_DETAILS = 341;

  // dev action for downloading mutual fund file (ex: feed file) by vendor order id
  MF_DOWNLOAD_FILE_BY_VENDOR_ORDER_IDS = 342;

  STOCK_GUARDIAN_REDACT_CKYC_DOCUMENT = 343;

  // Dev Action to download Lead Mgmt Excel file
  LEAD_MGMT_DOWNLOAD_FILE = 344;

  // dev action for changing the state of Deposit Account by depositID or depositAccountNumber
  DEPOSIT_UPDATE_STATE = 345;

  // dev action to execute EMI mandate for stock guardian loan accounts
  STOCK_GUARDIAN_EXECUTE_EMI_MANDATE = 346;

  // dev action to get sa closure eligibility for list of users
  GET_SAVINGS_ACCOUNT_CLOSURE_ELIGIBILITY_IN_BULK = 347;

  // dev action to soft delete kyc agent.
  DELETE_KYC_AGENT = 348;

  //dev action for updating or creating dynamic ui element variant
  SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_VARIANT = 349;

  //dev action for updating dynamic ui element evaluator config
  SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_EVALUATOR_CONFIG = 350;

  // dev action to process cross validation manual review for non-resident users
  PROCESS_NON_RESIDENT_CROSS_VALIDATION_MANUAL_REVIEW = 351;
  // dev action to review the passport which is issued outside india manually by ops team
  // ops team will review the passport and mark the stage as success/failure
  NR_ONBOARDING_PASSPORT_MANUAL_REVIEW = 352;

  // dev action for adding stock in catalog
  ADD_STOCK_WITH_ISINS = 353;
  STOCK_GUARDIAN_EXECUTE_DISBURSAL_RETRY = 354;
  // dev action to bulk upload redlist in db
  UPLOAD_RISK_REDLIST = 355;
  // dev action to trigger simulator for recurring payment validation
  SEND_ENACH_MANDATE_NOTIFICATION_CALLBACK = 356;
  // dev action for b2b users onboarding limited details
  B2B_USERS_ONBOARDING_STATUS_LIMITED_DETAILS = 357;
  // dev action to update loans outcall ticket
  UPDATE_LOANS_OUTCALL_TICKET = 358;

  CREATE_SUGGESTED_ACTION_FOR_RULE = 359;

  // dev action to create rule review type mapping
  CREATE_RULE_REVIEW_TYPE_MAPPING = 360;

  FEDERAL_ESCALATION_CREATION = 361;

  // dev action for b2b rms to send email for UN name check failure
  B2B_UN_NAME_CHECK_FAILURE_EMAIL = 362;

  // dev action to update user name and dob from partner bank
  REFRESH_USER_INFO_FROM_PARTNER_BANK = 363;

  // dev action to delete user assets
  DELETE_USER_ASSETS = 364;

  // dev action to update image to Epifi Icons S3 bucket
  UPLOAD_IMAGE_TO_EPIFI_ICONS_S3_BUCKET = 365;

 // dev action to update loec status to expired
  EXPIRE_LOEC = 366;

  // Dev action to update employment details in loan application entity
  STOCK_GUARDIAN_UPDATE_EMPLOYMENT_DETAILS = 367;

  // Dev action to fetch utrs from log
  FETCH_UTRS_FROM_LOG = 368;
}

// list of action response type
enum ActionResponseType {
  ACTION_RESPONSE_TYPE_UNSPECIFIED = 0;
  // response will be shown as raw string on UI as result
  STRING = 1;
  // response json will be beautified and shown on UI
  JSON = 2;
  // when response contains a html table
  TABLE = 3;
  // downloadable url
  DOWNLOAD_URL = 4;
}
