// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/rewards/projector/service.proto

package projector

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	rewards "github.com/epifi/gamma/api/rewards"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = rewards.CollectedDataType(0)
)

// Validate checks the field values on GetRewardsProjectionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardsProjectionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardsProjectionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardsProjectionsRequestMultiError, or nil if none found.
func (m *GetRewardsProjectionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardsProjectionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := GetRewardsProjectionsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardsProjectionsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardsProjectionsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardsProjectionsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FetchAggregates

	if all {
		switch v := interface{}(m.GetPageCtxRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardsProjectionsRequestValidationError{
					field:  "PageCtxRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardsProjectionsRequestValidationError{
					field:  "PageCtxRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageCtxRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardsProjectionsRequestValidationError{
				field:  "PageCtxRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardsProjectionsRequestMultiError(errors)
	}

	return nil
}

// GetRewardsProjectionsRequestMultiError is an error wrapping multiple
// validation errors returned by GetRewardsProjectionsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRewardsProjectionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardsProjectionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardsProjectionsRequestMultiError) AllErrors() []error { return m }

// GetRewardsProjectionsRequestValidationError is the validation error returned
// by GetRewardsProjectionsRequest.Validate if the designated constraints
// aren't met.
type GetRewardsProjectionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardsProjectionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardsProjectionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardsProjectionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardsProjectionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardsProjectionsRequestValidationError) ErrorName() string {
	return "GetRewardsProjectionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardsProjectionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardsProjectionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardsProjectionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardsProjectionsRequestValidationError{}

// Validate checks the field values on GetRewardsProjectionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardsProjectionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardsProjectionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRewardsProjectionsResponseMultiError, or nil if none found.
func (m *GetRewardsProjectionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardsProjectionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardsProjectionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardsProjectionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardsProjectionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageCtxResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardsProjectionsResponseValidationError{
					field:  "PageCtxResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardsProjectionsResponseValidationError{
					field:  "PageCtxResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageCtxResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardsProjectionsResponseValidationError{
				field:  "PageCtxResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Projections.(type) {
	case *GetRewardsProjectionsResponse_IndividualProjections_:
		if v == nil {
			err := GetRewardsProjectionsResponseValidationError{
				field:  "Projections",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIndividualProjections()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardsProjectionsResponseValidationError{
						field:  "IndividualProjections",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardsProjectionsResponseValidationError{
						field:  "IndividualProjections",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIndividualProjections()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardsProjectionsResponseValidationError{
					field:  "IndividualProjections",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetRewardsProjectionsResponse_AggregateProjections_:
		if v == nil {
			err := GetRewardsProjectionsResponseValidationError{
				field:  "Projections",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAggregateProjections()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardsProjectionsResponseValidationError{
						field:  "AggregateProjections",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardsProjectionsResponseValidationError{
						field:  "AggregateProjections",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAggregateProjections()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardsProjectionsResponseValidationError{
					field:  "AggregateProjections",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetRewardsProjectionsResponseMultiError(errors)
	}

	return nil
}

// GetRewardsProjectionsResponseMultiError is an error wrapping multiple
// validation errors returned by GetRewardsProjectionsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRewardsProjectionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardsProjectionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardsProjectionsResponseMultiError) AllErrors() []error { return m }

// GetRewardsProjectionsResponseValidationError is the validation error
// returned by GetRewardsProjectionsResponse.Validate if the designated
// constraints aren't met.
type GetRewardsProjectionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardsProjectionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardsProjectionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardsProjectionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardsProjectionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardsProjectionsResponseValidationError) ErrorName() string {
	return "GetRewardsProjectionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardsProjectionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardsProjectionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardsProjectionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardsProjectionsResponseValidationError{}

// Validate checks the field values on GetProjectionAggregatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProjectionAggregatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProjectionAggregatesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetProjectionAggregatesRequestMultiError, or nil if none found.
func (m *GetProjectionAggregatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProjectionAggregatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetActorId()); l < 4 || l > 100 {
		err := GetProjectionAggregatesRequestValidationError{
			field:  "ActorId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProjectionAggregatesRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProjectionAggregatesRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProjectionAggregatesRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetProjectionAggregatesRequestMultiError(errors)
	}

	return nil
}

// GetProjectionAggregatesRequestMultiError is an error wrapping multiple
// validation errors returned by GetProjectionAggregatesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetProjectionAggregatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProjectionAggregatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProjectionAggregatesRequestMultiError) AllErrors() []error { return m }

// GetProjectionAggregatesRequestValidationError is the validation error
// returned by GetProjectionAggregatesRequest.Validate if the designated
// constraints aren't met.
type GetProjectionAggregatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProjectionAggregatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProjectionAggregatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProjectionAggregatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProjectionAggregatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProjectionAggregatesRequestValidationError) ErrorName() string {
	return "GetProjectionAggregatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetProjectionAggregatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProjectionAggregatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProjectionAggregatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProjectionAggregatesRequestValidationError{}

// Validate checks the field values on GetProjectionAggregatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProjectionAggregatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProjectionAggregatesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetProjectionAggregatesResponseMultiError, or nil if none found.
func (m *GetProjectionAggregatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProjectionAggregatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProjectionAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProjectionAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProjectionAggregatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAggregates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetProjectionAggregatesResponseValidationError{
						field:  fmt.Sprintf("Aggregates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetProjectionAggregatesResponseValidationError{
						field:  fmt.Sprintf("Aggregates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetProjectionAggregatesResponseValidationError{
					field:  fmt.Sprintf("Aggregates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetProjectionAggregatesResponseMultiError(errors)
	}

	return nil
}

// GetProjectionAggregatesResponseMultiError is an error wrapping multiple
// validation errors returned by GetProjectionAggregatesResponse.ValidateAll()
// if the designated constraints aren't met.
type GetProjectionAggregatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProjectionAggregatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProjectionAggregatesResponseMultiError) AllErrors() []error { return m }

// GetProjectionAggregatesResponseValidationError is the validation error
// returned by GetProjectionAggregatesResponse.Validate if the designated
// constraints aren't met.
type GetProjectionAggregatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProjectionAggregatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProjectionAggregatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProjectionAggregatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProjectionAggregatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProjectionAggregatesResponseValidationError) ErrorName() string {
	return "GetProjectionAggregatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetProjectionAggregatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProjectionAggregatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProjectionAggregatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProjectionAggregatesResponseValidationError{}

// Validate checks the field values on GetRewardsProjectionsRequest_Filters
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetRewardsProjectionsRequest_Filters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardsProjectionsRequest_Filters
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetRewardsProjectionsRequest_FiltersMultiError, or nil if none found.
func (m *GetRewardsProjectionsRequest_Filters) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardsProjectionsRequest_Filters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if all {
		switch v := interface{}(m.GetTimeWindow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardsProjectionsRequest_FiltersValidationError{
					field:  "TimeWindow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardsProjectionsRequest_FiltersValidationError{
					field:  "TimeWindow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeWindow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardsProjectionsRequest_FiltersValidationError{
				field:  "TimeWindow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FetchDeletedProjections

	if len(errors) > 0 {
		return GetRewardsProjectionsRequest_FiltersMultiError(errors)
	}

	return nil
}

// GetRewardsProjectionsRequest_FiltersMultiError is an error wrapping multiple
// validation errors returned by
// GetRewardsProjectionsRequest_Filters.ValidateAll() if the designated
// constraints aren't met.
type GetRewardsProjectionsRequest_FiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardsProjectionsRequest_FiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardsProjectionsRequest_FiltersMultiError) AllErrors() []error { return m }

// GetRewardsProjectionsRequest_FiltersValidationError is the validation error
// returned by GetRewardsProjectionsRequest_Filters.Validate if the designated
// constraints aren't met.
type GetRewardsProjectionsRequest_FiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardsProjectionsRequest_FiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardsProjectionsRequest_FiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardsProjectionsRequest_FiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardsProjectionsRequest_FiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardsProjectionsRequest_FiltersValidationError) ErrorName() string {
	return "GetRewardsProjectionsRequest_FiltersValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardsProjectionsRequest_FiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardsProjectionsRequest_Filters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardsProjectionsRequest_FiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardsProjectionsRequest_FiltersValidationError{}

// Validate checks the field values on
// GetRewardsProjectionsResponse_IndividualProjections with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetRewardsProjectionsResponse_IndividualProjections) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRewardsProjectionsResponse_IndividualProjections with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRewardsProjectionsResponse_IndividualProjectionsMultiError, or nil if
// none found.
func (m *GetRewardsProjectionsResponse_IndividualProjections) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardsProjectionsResponse_IndividualProjections) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetProjections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardsProjectionsResponse_IndividualProjectionsValidationError{
						field:  fmt.Sprintf("Projections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardsProjectionsResponse_IndividualProjectionsValidationError{
						field:  fmt.Sprintf("Projections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardsProjectionsResponse_IndividualProjectionsValidationError{
					field:  fmt.Sprintf("Projections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRewardsProjectionsResponse_IndividualProjectionsMultiError(errors)
	}

	return nil
}

// GetRewardsProjectionsResponse_IndividualProjectionsMultiError is an error
// wrapping multiple validation errors returned by
// GetRewardsProjectionsResponse_IndividualProjections.ValidateAll() if the
// designated constraints aren't met.
type GetRewardsProjectionsResponse_IndividualProjectionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardsProjectionsResponse_IndividualProjectionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardsProjectionsResponse_IndividualProjectionsMultiError) AllErrors() []error { return m }

// GetRewardsProjectionsResponse_IndividualProjectionsValidationError is the
// validation error returned by
// GetRewardsProjectionsResponse_IndividualProjections.Validate if the
// designated constraints aren't met.
type GetRewardsProjectionsResponse_IndividualProjectionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardsProjectionsResponse_IndividualProjectionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRewardsProjectionsResponse_IndividualProjectionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRewardsProjectionsResponse_IndividualProjectionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRewardsProjectionsResponse_IndividualProjectionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardsProjectionsResponse_IndividualProjectionsValidationError) ErrorName() string {
	return "GetRewardsProjectionsResponse_IndividualProjectionsValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardsProjectionsResponse_IndividualProjectionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardsProjectionsResponse_IndividualProjections.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardsProjectionsResponse_IndividualProjectionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardsProjectionsResponse_IndividualProjectionsValidationError{}

// Validate checks the field values on
// GetRewardsProjectionsResponse_AggregateProjections with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetRewardsProjectionsResponse_AggregateProjections) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRewardsProjectionsResponse_AggregateProjections with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetRewardsProjectionsResponse_AggregateProjectionsMultiError, or nil if
// none found.
func (m *GetRewardsProjectionsResponse_AggregateProjections) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardsProjectionsResponse_AggregateProjections) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRewardUnitsDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardsProjectionsResponse_AggregateProjectionsValidationError{
						field:  fmt.Sprintf("RewardUnitsDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardsProjectionsResponse_AggregateProjectionsValidationError{
						field:  fmt.Sprintf("RewardUnitsDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardsProjectionsResponse_AggregateProjectionsValidationError{
					field:  fmt.Sprintf("RewardUnitsDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRewardsProjectionsResponse_AggregateProjectionsMultiError(errors)
	}

	return nil
}

// GetRewardsProjectionsResponse_AggregateProjectionsMultiError is an error
// wrapping multiple validation errors returned by
// GetRewardsProjectionsResponse_AggregateProjections.ValidateAll() if the
// designated constraints aren't met.
type GetRewardsProjectionsResponse_AggregateProjectionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardsProjectionsResponse_AggregateProjectionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardsProjectionsResponse_AggregateProjectionsMultiError) AllErrors() []error { return m }

// GetRewardsProjectionsResponse_AggregateProjectionsValidationError is the
// validation error returned by
// GetRewardsProjectionsResponse_AggregateProjections.Validate if the
// designated constraints aren't met.
type GetRewardsProjectionsResponse_AggregateProjectionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardsProjectionsResponse_AggregateProjectionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRewardsProjectionsResponse_AggregateProjectionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRewardsProjectionsResponse_AggregateProjectionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRewardsProjectionsResponse_AggregateProjectionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardsProjectionsResponse_AggregateProjectionsValidationError) ErrorName() string {
	return "GetRewardsProjectionsResponse_AggregateProjectionsValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardsProjectionsResponse_AggregateProjectionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardsProjectionsResponse_AggregateProjections.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardsProjectionsResponse_AggregateProjectionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardsProjectionsResponse_AggregateProjectionsValidationError{}

// Validate checks the field values on
// GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsMultiError,
// or nil if none found.
func (m *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardType

	// no validation rules for ProjectedRewardUnits

	// no validation rules for ActualRewardUnits

	if len(errors) > 0 {
		return GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsMultiError(errors)
	}

	return nil
}

// GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsMultiError
// is an error wrapping multiple validation errors returned by
// GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails.ValidateAll()
// if the designated constraints aren't met.
type GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsMultiError) AllErrors() []error {
	return m
}

// GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError
// is the validation error returned by
// GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails.Validate
// if the designated constraints aren't met.
type GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError) ErrorName() string {
	return "GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetailsValidationError{}

// Validate checks the field values on GetProjectionAggregatesRequest_Filters
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetProjectionAggregatesRequest_Filters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetProjectionAggregatesRequest_Filters with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetProjectionAggregatesRequest_FiltersMultiError, or nil if none found.
func (m *GetProjectionAggregatesRequest_Filters) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProjectionAggregatesRequest_Filters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	for idx, item := range m.GetTimeWindows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetProjectionAggregatesRequest_FiltersValidationError{
						field:  fmt.Sprintf("TimeWindows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetProjectionAggregatesRequest_FiltersValidationError{
						field:  fmt.Sprintf("TimeWindows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetProjectionAggregatesRequest_FiltersValidationError{
					field:  fmt.Sprintf("TimeWindows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetProjectionAggregatesRequest_FiltersMultiError(errors)
	}

	return nil
}

// GetProjectionAggregatesRequest_FiltersMultiError is an error wrapping
// multiple validation errors returned by
// GetProjectionAggregatesRequest_Filters.ValidateAll() if the designated
// constraints aren't met.
type GetProjectionAggregatesRequest_FiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProjectionAggregatesRequest_FiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProjectionAggregatesRequest_FiltersMultiError) AllErrors() []error { return m }

// GetProjectionAggregatesRequest_FiltersValidationError is the validation
// error returned by GetProjectionAggregatesRequest_Filters.Validate if the
// designated constraints aren't met.
type GetProjectionAggregatesRequest_FiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProjectionAggregatesRequest_FiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProjectionAggregatesRequest_FiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProjectionAggregatesRequest_FiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProjectionAggregatesRequest_FiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProjectionAggregatesRequest_FiltersValidationError) ErrorName() string {
	return "GetProjectionAggregatesRequest_FiltersValidationError"
}

// Error satisfies the builtin error interface
func (e GetProjectionAggregatesRequest_FiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProjectionAggregatesRequest_Filters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProjectionAggregatesRequest_FiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProjectionAggregatesRequest_FiltersValidationError{}

// Validate checks the field values on
// GetProjectionAggregatesResponse_RewardProjectionAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProjectionAggregatesResponse_RewardProjectionAggregate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetProjectionAggregatesResponse_RewardProjectionAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProjectionAggregatesResponse_RewardProjectionAggregateMultiError, or nil
// if none found.
func (m *GetProjectionAggregatesResponse_RewardProjectionAggregate) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProjectionAggregatesResponse_RewardProjectionAggregate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardType

	// no validation rules for ProjectedRewardUnits

	// no validation rules for ActualRewardUnits

	if len(errors) > 0 {
		return GetProjectionAggregatesResponse_RewardProjectionAggregateMultiError(errors)
	}

	return nil
}

// GetProjectionAggregatesResponse_RewardProjectionAggregateMultiError is an
// error wrapping multiple validation errors returned by
// GetProjectionAggregatesResponse_RewardProjectionAggregate.ValidateAll() if
// the designated constraints aren't met.
type GetProjectionAggregatesResponse_RewardProjectionAggregateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProjectionAggregatesResponse_RewardProjectionAggregateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProjectionAggregatesResponse_RewardProjectionAggregateMultiError) AllErrors() []error {
	return m
}

// GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError is
// the validation error returned by
// GetProjectionAggregatesResponse_RewardProjectionAggregate.Validate if the
// designated constraints aren't met.
type GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError) ErrorName() string {
	return "GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError"
}

// Error satisfies the builtin error interface
func (e GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProjectionAggregatesResponse_RewardProjectionAggregate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProjectionAggregatesResponse_RewardProjectionAggregateValidationError{}
