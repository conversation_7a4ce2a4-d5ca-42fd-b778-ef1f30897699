// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/rewards/projector/service.proto

package projector

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	rewards "github.com/epifi/gamma/api/rewards"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetRewardsProjectionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for which we want to fetch projections
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// filters for fetching projections
	Filters *GetRewardsProjectionsRequest_Filters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
	// flag to decide if we want to fetch aggregate (sum) of all the projections obtained after applying filters
	// or individual projection values
	// note: time_window OR list of ref_ids filter is mandatory when this flag is set.
	// deprecated: please use GetProjectionAggregates for fetching aggregates
	//
	// Deprecated: Marked as deprecated in api/rewards/projector/service.proto.
	FetchAggregates bool                    `protobuf:"varint,3,opt,name=fetch_aggregates,json=fetchAggregates,proto3" json:"fetch_aggregates,omitempty"`
	PageCtxRequest  *rpc.PageContextRequest `protobuf:"bytes,4,opt,name=page_ctx_request,json=pageCtxRequest,proto3" json:"page_ctx_request,omitempty"`
}

func (x *GetRewardsProjectionsRequest) Reset() {
	*x = GetRewardsProjectionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsProjectionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsProjectionsRequest) ProtoMessage() {}

func (x *GetRewardsProjectionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsProjectionsRequest.ProtoReflect.Descriptor instead.
func (*GetRewardsProjectionsRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetRewardsProjectionsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardsProjectionsRequest) GetFilters() *GetRewardsProjectionsRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

// Deprecated: Marked as deprecated in api/rewards/projector/service.proto.
func (x *GetRewardsProjectionsRequest) GetFetchAggregates() bool {
	if x != nil {
		return x.FetchAggregates
	}
	return false
}

func (x *GetRewardsProjectionsRequest) GetPageCtxRequest() *rpc.PageContextRequest {
	if x != nil {
		return x.PageCtxRequest
	}
	return nil
}

type GetRewardsProjectionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// projection of reward
	//
	// Types that are assignable to Projections:
	//
	//	*GetRewardsProjectionsResponse_IndividualProjections_
	//	*GetRewardsProjectionsResponse_AggregateProjections_
	Projections     isGetRewardsProjectionsResponse_Projections `protobuf_oneof:"Projections"`
	PageCtxResponse *rpc.PageContextResponse                    `protobuf:"bytes,4,opt,name=page_ctx_response,json=pageCtxResponse,proto3" json:"page_ctx_response,omitempty"`
}

func (x *GetRewardsProjectionsResponse) Reset() {
	*x = GetRewardsProjectionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsProjectionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsProjectionsResponse) ProtoMessage() {}

func (x *GetRewardsProjectionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsProjectionsResponse.ProtoReflect.Descriptor instead.
func (*GetRewardsProjectionsResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetRewardsProjectionsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (m *GetRewardsProjectionsResponse) GetProjections() isGetRewardsProjectionsResponse_Projections {
	if m != nil {
		return m.Projections
	}
	return nil
}

func (x *GetRewardsProjectionsResponse) GetIndividualProjections() *GetRewardsProjectionsResponse_IndividualProjections {
	if x, ok := x.GetProjections().(*GetRewardsProjectionsResponse_IndividualProjections_); ok {
		return x.IndividualProjections
	}
	return nil
}

func (x *GetRewardsProjectionsResponse) GetAggregateProjections() *GetRewardsProjectionsResponse_AggregateProjections {
	if x, ok := x.GetProjections().(*GetRewardsProjectionsResponse_AggregateProjections_); ok {
		return x.AggregateProjections
	}
	return nil
}

func (x *GetRewardsProjectionsResponse) GetPageCtxResponse() *rpc.PageContextResponse {
	if x != nil {
		return x.PageCtxResponse
	}
	return nil
}

type isGetRewardsProjectionsResponse_Projections interface {
	isGetRewardsProjectionsResponse_Projections()
}

type GetRewardsProjectionsResponse_IndividualProjections_ struct {
	IndividualProjections *GetRewardsProjectionsResponse_IndividualProjections `protobuf:"bytes,2,opt,name=individual_projections,json=individualProjections,proto3,oneof"`
}

type GetRewardsProjectionsResponse_AggregateProjections_ struct {
	AggregateProjections *GetRewardsProjectionsResponse_AggregateProjections `protobuf:"bytes,3,opt,name=aggregate_projections,json=aggregateProjections,proto3,oneof"`
}

func (*GetRewardsProjectionsResponse_IndividualProjections_) isGetRewardsProjectionsResponse_Projections() {
}

func (*GetRewardsProjectionsResponse_AggregateProjections_) isGetRewardsProjectionsResponse_Projections() {
}

type GetProjectionAggregatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for which we want to fetch projections
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// filters for fetching projections
	Filters *GetProjectionAggregatesRequest_Filters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetProjectionAggregatesRequest) Reset() {
	*x = GetProjectionAggregatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectionAggregatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectionAggregatesRequest) ProtoMessage() {}

func (x *GetProjectionAggregatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectionAggregatesRequest.ProtoReflect.Descriptor instead.
func (*GetProjectionAggregatesRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetProjectionAggregatesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetProjectionAggregatesRequest) GetFilters() *GetProjectionAggregatesRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type GetProjectionAggregatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// includes aggregates of reward projections
	Aggregates []*GetProjectionAggregatesResponse_RewardProjectionAggregate `protobuf:"bytes,2,rep,name=aggregates,proto3" json:"aggregates,omitempty"`
}

func (x *GetProjectionAggregatesResponse) Reset() {
	*x = GetProjectionAggregatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectionAggregatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectionAggregatesResponse) ProtoMessage() {}

func (x *GetProjectionAggregatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectionAggregatesResponse.ProtoReflect.Descriptor instead.
func (*GetProjectionAggregatesResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetProjectionAggregatesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetProjectionAggregatesResponse) GetAggregates() []*GetProjectionAggregatesResponse_RewardProjectionAggregate {
	if x != nil {
		return x.Aggregates
	}
	return nil
}

type GetRewardsProjectionsRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account identifier in case multiple accounts are possible for an actor (for example, in CC)
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// action types for which projections are to be fetched
	ActionType []rewards.CollectedDataType `protobuf:"varint,2,rep,packed,name=action_type,json=actionType,proto3,enum=rewards.CollectedDataType" json:"action_type,omitempty"`
	// offer types for which projections are to be fetched
	OfferType []rewards.RewardOfferType `protobuf:"varint,3,rep,packed,name=offer_type,json=offerType,proto3,enum=rewards.RewardOfferType" json:"offer_type,omitempty"`
	// reference IDs of the event that we want to fetch the projection for
	RefIds []string `protobuf:"bytes,4,rep,name=ref_ids,json=refIds,proto3" json:"ref_ids,omitempty"`
	// time window in which we want to fetch projections
	TimeWindow *rewards.TimeWindow `protobuf:"bytes,5,opt,name=time_window,json=timeWindow,proto3" json:"time_window,omitempty"`
	// offer IDs for which we want to fetch the projections
	OfferIds []string `protobuf:"bytes,6,rep,name=offer_ids,json=offerIds,proto3" json:"offer_ids,omitempty"`
	// reward IDs for which we want to fetch the projections
	RewardIds []string `protobuf:"bytes,7,rep,name=reward_ids,json=rewardIds,proto3" json:"reward_ids,omitempty"`
	// If true, includes deleted projections in the response.
	// By default, deleted projections are excluded from query results.
	FetchDeletedProjections bool `protobuf:"varint,8,opt,name=fetch_deleted_projections,json=fetchDeletedProjections,proto3" json:"fetch_deleted_projections,omitempty"`
}

func (x *GetRewardsProjectionsRequest_Filters) Reset() {
	*x = GetRewardsProjectionsRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsProjectionsRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsProjectionsRequest_Filters) ProtoMessage() {}

func (x *GetRewardsProjectionsRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsProjectionsRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetRewardsProjectionsRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetRewardsProjectionsRequest_Filters) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetRewardsProjectionsRequest_Filters) GetActionType() []rewards.CollectedDataType {
	if x != nil {
		return x.ActionType
	}
	return nil
}

func (x *GetRewardsProjectionsRequest_Filters) GetOfferType() []rewards.RewardOfferType {
	if x != nil {
		return x.OfferType
	}
	return nil
}

func (x *GetRewardsProjectionsRequest_Filters) GetRefIds() []string {
	if x != nil {
		return x.RefIds
	}
	return nil
}

func (x *GetRewardsProjectionsRequest_Filters) GetTimeWindow() *rewards.TimeWindow {
	if x != nil {
		return x.TimeWindow
	}
	return nil
}

func (x *GetRewardsProjectionsRequest_Filters) GetOfferIds() []string {
	if x != nil {
		return x.OfferIds
	}
	return nil
}

func (x *GetRewardsProjectionsRequest_Filters) GetRewardIds() []string {
	if x != nil {
		return x.RewardIds
	}
	return nil
}

func (x *GetRewardsProjectionsRequest_Filters) GetFetchDeletedProjections() bool {
	if x != nil {
		return x.FetchDeletedProjections
	}
	return false
}

// includes a list of projections fetched for the applied filters
type GetRewardsProjectionsResponse_IndividualProjections struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Projections []*Projection `protobuf:"bytes,1,rep,name=projections,proto3" json:"projections,omitempty"`
}

func (x *GetRewardsProjectionsResponse_IndividualProjections) Reset() {
	*x = GetRewardsProjectionsResponse_IndividualProjections{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsProjectionsResponse_IndividualProjections) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsProjectionsResponse_IndividualProjections) ProtoMessage() {}

func (x *GetRewardsProjectionsResponse_IndividualProjections) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsProjectionsResponse_IndividualProjections.ProtoReflect.Descriptor instead.
func (*GetRewardsProjectionsResponse_IndividualProjections) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetRewardsProjectionsResponse_IndividualProjections) GetProjections() []*Projection {
	if x != nil {
		return x.Projections
	}
	return nil
}

// includes aggregates of reward projections
type GetRewardsProjectionsResponse_AggregateProjections struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardUnitsDetails []*GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails `protobuf:"bytes,1,rep,name=reward_units_details,json=rewardUnitsDetails,proto3" json:"reward_units_details,omitempty"`
}

func (x *GetRewardsProjectionsResponse_AggregateProjections) Reset() {
	*x = GetRewardsProjectionsResponse_AggregateProjections{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsProjectionsResponse_AggregateProjections) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsProjectionsResponse_AggregateProjections) ProtoMessage() {}

func (x *GetRewardsProjectionsResponse_AggregateProjections) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsProjectionsResponse_AggregateProjections.ProtoReflect.Descriptor instead.
func (*GetRewardsProjectionsResponse_AggregateProjections) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{1, 1}
}

func (x *GetRewardsProjectionsResponse_AggregateProjections) GetRewardUnitsDetails() []*GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails {
	if x != nil {
		return x.RewardUnitsDetails
	}
	return nil
}

// details of projected and actual reward units aggregated on reward type
type GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType           rewards.RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	ProjectedRewardUnits float32            `protobuf:"fixed32,2,opt,name=projected_reward_units,json=projectedRewardUnits,proto3" json:"projected_reward_units,omitempty"`
	ActualRewardUnits    float32            `protobuf:"fixed32,3,opt,name=actual_reward_units,json=actualRewardUnits,proto3" json:"actual_reward_units,omitempty"`
}

func (x *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) Reset() {
	*x = GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) ProtoMessage() {}

func (x *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails.ProtoReflect.Descriptor instead.
func (*GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{1, 1, 0}
}

func (x *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) GetProjectedRewardUnits() float32 {
	if x != nil {
		return x.ProjectedRewardUnits
	}
	return 0
}

func (x *GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails) GetActualRewardUnits() float32 {
	if x != nil {
		return x.ActualRewardUnits
	}
	return 0
}

type GetProjectionAggregatesRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account identifier in case multiple accounts are possible for an actor (for example, in CC)
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// action types for which projections are to be fetched
	ActionTypes []rewards.CollectedDataType `protobuf:"varint,2,rep,packed,name=action_types,json=actionTypes,proto3,enum=rewards.CollectedDataType" json:"action_types,omitempty"`
	// offer types for which projections are to be fetched
	OfferTypes []rewards.RewardOfferType `protobuf:"varint,3,rep,packed,name=offer_types,json=offerTypes,proto3,enum=rewards.RewardOfferType" json:"offer_types,omitempty"`
	// offer IDs for which we want to fetch the projections
	OfferIds []string `protobuf:"bytes,4,rep,name=offer_ids,json=offerIds,proto3" json:"offer_ids,omitempty"`
	// list of time ranges in which we want to fetch the projections/aggregations
	TimeWindows []*rewards.TimeWindow `protobuf:"bytes,5,rep,name=time_windows,json=timeWindows,proto3" json:"time_windows,omitempty"`
}

func (x *GetProjectionAggregatesRequest_Filters) Reset() {
	*x = GetProjectionAggregatesRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectionAggregatesRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectionAggregatesRequest_Filters) ProtoMessage() {}

func (x *GetProjectionAggregatesRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectionAggregatesRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetProjectionAggregatesRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetProjectionAggregatesRequest_Filters) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetProjectionAggregatesRequest_Filters) GetActionTypes() []rewards.CollectedDataType {
	if x != nil {
		return x.ActionTypes
	}
	return nil
}

func (x *GetProjectionAggregatesRequest_Filters) GetOfferTypes() []rewards.RewardOfferType {
	if x != nil {
		return x.OfferTypes
	}
	return nil
}

func (x *GetProjectionAggregatesRequest_Filters) GetOfferIds() []string {
	if x != nil {
		return x.OfferIds
	}
	return nil
}

func (x *GetProjectionAggregatesRequest_Filters) GetTimeWindows() []*rewards.TimeWindow {
	if x != nil {
		return x.TimeWindows
	}
	return nil
}

// details of projected and actual reward units aggregated on reward type
type GetProjectionAggregatesResponse_RewardProjectionAggregate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType           rewards.RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	ProjectedRewardUnits float32            `protobuf:"fixed32,2,opt,name=projected_reward_units,json=projectedRewardUnits,proto3" json:"projected_reward_units,omitempty"`
	ActualRewardUnits    float32            `protobuf:"fixed32,3,opt,name=actual_reward_units,json=actualRewardUnits,proto3" json:"actual_reward_units,omitempty"`
}

func (x *GetProjectionAggregatesResponse_RewardProjectionAggregate) Reset() {
	*x = GetProjectionAggregatesResponse_RewardProjectionAggregate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_projector_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectionAggregatesResponse_RewardProjectionAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectionAggregatesResponse_RewardProjectionAggregate) ProtoMessage() {}

func (x *GetProjectionAggregatesResponse_RewardProjectionAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_projector_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectionAggregatesResponse_RewardProjectionAggregate.ProtoReflect.Descriptor instead.
func (*GetProjectionAggregatesResponse_RewardProjectionAggregate) Descriptor() ([]byte, []int) {
	return file_api_rewards_projector_service_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetProjectionAggregatesResponse_RewardProjectionAggregate) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *GetProjectionAggregatesResponse_RewardProjectionAggregate) GetProjectedRewardUnits() float32 {
	if x != nil {
		return x.ProjectedRewardUnits
	}
	return 0
}

func (x *GetProjectionAggregatesResponse_RewardProjectionAggregate) GetActualRewardUnits() float32 {
	if x != nil {
		return x.ActualRewardUnits
	}
	return 0
}

var File_api_rewards_projector_service_proto protoreflect.FileDescriptor

var file_api_rewards_projector_service_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe9, 0x04, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x2d,
	0x0a, 0x10, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x41, 0x0a,
	0x10, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x74, 0x78, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0e, 0x70, 0x61, 0x67, 0x65, 0x43, 0x74, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0xe5, 0x02, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x66, 0x49, 0x64, 0x73, 0x12, 0x34, 0x0a, 0x0b, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x57, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x12, 0x3a, 0x0a, 0x19,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x17, 0x66, 0x65, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xab, 0x06, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x77, 0x0a, 0x16, 0x69, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3e, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x76, 0x69,
	0x64, 0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48,
	0x00, 0x52, 0x15, 0x69, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x74, 0x0a, 0x15, 0x61, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x14, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44,
	0x0a, 0x11, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x74, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0f, 0x70, 0x61, 0x67, 0x65, 0x43, 0x74, 0x78, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x50, 0x0a, 0x15, 0x49, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75,
	0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a,
	0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xce, 0x02, 0x0a, 0x14, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x82, 0x01, 0x0a, 0x14, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50,
	0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x12, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x1a, 0xb0, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x34, 0x0a, 0x0b, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x34, 0x0a, 0x16, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x14, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x63, 0x74, 0x75, 0x61,
	0x6c, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x8d, 0x03, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x4b, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0xf7, 0x01, 0x0a,
	0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x36,
	0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x57,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x22, 0xe6, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x64, 0x0a, 0x0a, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x1a, 0xb7, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e,
	0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12,
	0x2e, 0x0a, 0x13, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x61, 0x63,
	0x74, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x32,
	0xf0, 0x01, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x6a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x27, 0x2e,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x70, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x29, 0x2e, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rewards_projector_service_proto_rawDescOnce sync.Once
	file_api_rewards_projector_service_proto_rawDescData = file_api_rewards_projector_service_proto_rawDesc
)

func file_api_rewards_projector_service_proto_rawDescGZIP() []byte {
	file_api_rewards_projector_service_proto_rawDescOnce.Do(func() {
		file_api_rewards_projector_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rewards_projector_service_proto_rawDescData)
	})
	return file_api_rewards_projector_service_proto_rawDescData
}

var file_api_rewards_projector_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_rewards_projector_service_proto_goTypes = []interface{}{
	(*GetRewardsProjectionsRequest)(nil),                                          // 0: projector.GetRewardsProjectionsRequest
	(*GetRewardsProjectionsResponse)(nil),                                         // 1: projector.GetRewardsProjectionsResponse
	(*GetProjectionAggregatesRequest)(nil),                                        // 2: projector.GetProjectionAggregatesRequest
	(*GetProjectionAggregatesResponse)(nil),                                       // 3: projector.GetProjectionAggregatesResponse
	(*GetRewardsProjectionsRequest_Filters)(nil),                                  // 4: projector.GetRewardsProjectionsRequest.Filters
	(*GetRewardsProjectionsResponse_IndividualProjections)(nil),                   // 5: projector.GetRewardsProjectionsResponse.IndividualProjections
	(*GetRewardsProjectionsResponse_AggregateProjections)(nil),                    // 6: projector.GetRewardsProjectionsResponse.AggregateProjections
	(*GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails)(nil), // 7: projector.GetRewardsProjectionsResponse.AggregateProjections.RewardUnitsDetails
	(*GetProjectionAggregatesRequest_Filters)(nil),                                // 8: projector.GetProjectionAggregatesRequest.Filters
	(*GetProjectionAggregatesResponse_RewardProjectionAggregate)(nil),             // 9: projector.GetProjectionAggregatesResponse.RewardProjectionAggregate
	(*rpc.PageContextRequest)(nil),                                                // 10: rpc.PageContextRequest
	(*rpc.Status)(nil),                                                            // 11: rpc.Status
	(*rpc.PageContextResponse)(nil),                                               // 12: rpc.PageContextResponse
	(rewards.CollectedDataType)(0),                                                // 13: rewards.CollectedDataType
	(rewards.RewardOfferType)(0),                                                  // 14: rewards.RewardOfferType
	(*rewards.TimeWindow)(nil),                                                    // 15: rewards.TimeWindow
	(*Projection)(nil),                                                            // 16: projector.Projection
	(rewards.RewardType)(0),                                                       // 17: rewards.RewardType
}
var file_api_rewards_projector_service_proto_depIdxs = []int32{
	4,  // 0: projector.GetRewardsProjectionsRequest.filters:type_name -> projector.GetRewardsProjectionsRequest.Filters
	10, // 1: projector.GetRewardsProjectionsRequest.page_ctx_request:type_name -> rpc.PageContextRequest
	11, // 2: projector.GetRewardsProjectionsResponse.status:type_name -> rpc.Status
	5,  // 3: projector.GetRewardsProjectionsResponse.individual_projections:type_name -> projector.GetRewardsProjectionsResponse.IndividualProjections
	6,  // 4: projector.GetRewardsProjectionsResponse.aggregate_projections:type_name -> projector.GetRewardsProjectionsResponse.AggregateProjections
	12, // 5: projector.GetRewardsProjectionsResponse.page_ctx_response:type_name -> rpc.PageContextResponse
	8,  // 6: projector.GetProjectionAggregatesRequest.filters:type_name -> projector.GetProjectionAggregatesRequest.Filters
	11, // 7: projector.GetProjectionAggregatesResponse.status:type_name -> rpc.Status
	9,  // 8: projector.GetProjectionAggregatesResponse.aggregates:type_name -> projector.GetProjectionAggregatesResponse.RewardProjectionAggregate
	13, // 9: projector.GetRewardsProjectionsRequest.Filters.action_type:type_name -> rewards.CollectedDataType
	14, // 10: projector.GetRewardsProjectionsRequest.Filters.offer_type:type_name -> rewards.RewardOfferType
	15, // 11: projector.GetRewardsProjectionsRequest.Filters.time_window:type_name -> rewards.TimeWindow
	16, // 12: projector.GetRewardsProjectionsResponse.IndividualProjections.projections:type_name -> projector.Projection
	7,  // 13: projector.GetRewardsProjectionsResponse.AggregateProjections.reward_units_details:type_name -> projector.GetRewardsProjectionsResponse.AggregateProjections.RewardUnitsDetails
	17, // 14: projector.GetRewardsProjectionsResponse.AggregateProjections.RewardUnitsDetails.reward_type:type_name -> rewards.RewardType
	13, // 15: projector.GetProjectionAggregatesRequest.Filters.action_types:type_name -> rewards.CollectedDataType
	14, // 16: projector.GetProjectionAggregatesRequest.Filters.offer_types:type_name -> rewards.RewardOfferType
	15, // 17: projector.GetProjectionAggregatesRequest.Filters.time_windows:type_name -> rewards.TimeWindow
	17, // 18: projector.GetProjectionAggregatesResponse.RewardProjectionAggregate.reward_type:type_name -> rewards.RewardType
	0,  // 19: projector.ProjectorService.GetRewardsProjections:input_type -> projector.GetRewardsProjectionsRequest
	2,  // 20: projector.ProjectorService.GetProjectionAggregates:input_type -> projector.GetProjectionAggregatesRequest
	1,  // 21: projector.ProjectorService.GetRewardsProjections:output_type -> projector.GetRewardsProjectionsResponse
	3,  // 22: projector.ProjectorService.GetProjectionAggregates:output_type -> projector.GetProjectionAggregatesResponse
	21, // [21:23] is the sub-list for method output_type
	19, // [19:21] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_api_rewards_projector_service_proto_init() }
func file_api_rewards_projector_service_proto_init() {
	if File_api_rewards_projector_service_proto != nil {
		return
	}
	file_api_rewards_projector_projection_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_rewards_projector_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsProjectionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsProjectionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectionAggregatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectionAggregatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsProjectionsRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsProjectionsResponse_IndividualProjections); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsProjectionsResponse_AggregateProjections); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsProjectionsResponse_AggregateProjections_RewardUnitsDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectionAggregatesRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_projector_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectionAggregatesResponse_RewardProjectionAggregate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_rewards_projector_service_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*GetRewardsProjectionsResponse_IndividualProjections_)(nil),
		(*GetRewardsProjectionsResponse_AggregateProjections_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rewards_projector_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_rewards_projector_service_proto_goTypes,
		DependencyIndexes: file_api_rewards_projector_service_proto_depIdxs,
		MessageInfos:      file_api_rewards_projector_service_proto_msgTypes,
	}.Build()
	File_api_rewards_projector_service_proto = out.File
	file_api_rewards_projector_service_proto_rawDesc = nil
	file_api_rewards_projector_service_proto_goTypes = nil
	file_api_rewards_projector_service_proto_depIdxs = nil
}
