//go:generate gen_sql -types=SecurityDetails,StockDetails,FundDetails,FinancialInfo,FundamentalParameters

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/securities/catalog/model_security.proto

package catalog

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SecurityFieldMask int32

const (
	SecurityFieldMask_SECURITY_FIELD_MASK_UNSPECIFIED        SecurityFieldMask = 0
	SecurityFieldMask_SECURITY_FIELD_MASK_ID                 SecurityFieldMask = 1
	SecurityFieldMask_SECURITY_FIELD_MASK_SECURITY_TYPE      SecurityFieldMask = 2
	SecurityFieldMask_SECURITY_FIELD_MASK_NAME               SecurityFieldMask = 3
	SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR             SecurityFieldMask = 4
	SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID SecurityFieldMask = 5
	SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL           SecurityFieldMask = 6
	SecurityFieldMask_SECURITY_FIELD_MASK_DETAILS            SecurityFieldMask = 7
	SecurityFieldMask_SECURITY_FIELD_MASK_CREATED_AT         SecurityFieldMask = 8
	SecurityFieldMask_SECURITY_FIELD_MASK_UPDATED_AT         SecurityFieldMask = 9
	SecurityFieldMask_SECURITY_FIELD_MASK_DELETED_AT         SecurityFieldMask = 10
	SecurityFieldMask_SECURITY_FIELD_MASK_FINANCIAL_INFO     SecurityFieldMask = 11
)

// Enum value maps for SecurityFieldMask.
var (
	SecurityFieldMask_name = map[int32]string{
		0:  "SECURITY_FIELD_MASK_UNSPECIFIED",
		1:  "SECURITY_FIELD_MASK_ID",
		2:  "SECURITY_FIELD_MASK_SECURITY_TYPE",
		3:  "SECURITY_FIELD_MASK_NAME",
		4:  "SECURITY_FIELD_MASK_VENDOR",
		5:  "SECURITY_FIELD_MASK_VENDOR_SECURITY_ID",
		6:  "SECURITY_FIELD_MASK_LOGO_URL",
		7:  "SECURITY_FIELD_MASK_DETAILS",
		8:  "SECURITY_FIELD_MASK_CREATED_AT",
		9:  "SECURITY_FIELD_MASK_UPDATED_AT",
		10: "SECURITY_FIELD_MASK_DELETED_AT",
		11: "SECURITY_FIELD_MASK_FINANCIAL_INFO",
	}
	SecurityFieldMask_value = map[string]int32{
		"SECURITY_FIELD_MASK_UNSPECIFIED":        0,
		"SECURITY_FIELD_MASK_ID":                 1,
		"SECURITY_FIELD_MASK_SECURITY_TYPE":      2,
		"SECURITY_FIELD_MASK_NAME":               3,
		"SECURITY_FIELD_MASK_VENDOR":             4,
		"SECURITY_FIELD_MASK_VENDOR_SECURITY_ID": 5,
		"SECURITY_FIELD_MASK_LOGO_URL":           6,
		"SECURITY_FIELD_MASK_DETAILS":            7,
		"SECURITY_FIELD_MASK_CREATED_AT":         8,
		"SECURITY_FIELD_MASK_UPDATED_AT":         9,
		"SECURITY_FIELD_MASK_DELETED_AT":         10,
		"SECURITY_FIELD_MASK_FINANCIAL_INFO":     11,
	}
)

func (x SecurityFieldMask) Enum() *SecurityFieldMask {
	p := new(SecurityFieldMask)
	*p = x
	return p
}

func (x SecurityFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecurityFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_securities_catalog_model_security_proto_enumTypes[0].Descriptor()
}

func (SecurityFieldMask) Type() protoreflect.EnumType {
	return &file_api_securities_catalog_model_security_proto_enumTypes[0]
}

func (x SecurityFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecurityFieldMask.Descriptor instead.
func (SecurityFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{0}
}

type Security struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SecurityType     SecurityType           `protobuf:"varint,2,opt,name=security_type,json=securityType,proto3,enum=api.securities.catalog.SecurityType" json:"security_type,omitempty"`
	SecurityName     string                 `protobuf:"bytes,3,opt,name=security_name,json=securityName,proto3" json:"security_name,omitempty"`
	Vendor           vendorgateway.Vendor   `protobuf:"varint,4,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	VendorSecurityId string                 `protobuf:"bytes,5,opt,name=vendor_security_id,json=vendorSecurityId,proto3" json:"vendor_security_id,omitempty"`
	LogoUrl          string                 `protobuf:"bytes,6,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	SecurityDetails  *SecurityDetails       `protobuf:"bytes,7,opt,name=security_details,json=securityDetails,proto3" json:"security_details,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt        *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// Financial information for this listing like PE, PB, etc.
	FinancialInfo *FinancialInfo `protobuf:"bytes,11,opt,name=financial_info,json=financialInfo,proto3" json:"financial_info,omitempty"`
}

func (x *Security) Reset() {
	*x = Security{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Security) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Security) ProtoMessage() {}

func (x *Security) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Security.ProtoReflect.Descriptor instead.
func (*Security) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{0}
}

func (x *Security) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Security) GetSecurityType() SecurityType {
	if x != nil {
		return x.SecurityType
	}
	return SecurityType_SECURITY_TYPE_UNSPECIFIED
}

func (x *Security) GetSecurityName() string {
	if x != nil {
		return x.SecurityName
	}
	return ""
}

func (x *Security) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *Security) GetVendorSecurityId() string {
	if x != nil {
		return x.VendorSecurityId
	}
	return ""
}

func (x *Security) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *Security) GetSecurityDetails() *SecurityDetails {
	if x != nil {
		return x.SecurityDetails
	}
	return nil
}

func (x *Security) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Security) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Security) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Security) GetFinancialInfo() *FinancialInfo {
	if x != nil {
		return x.FinancialInfo
	}
	return nil
}

type SecurityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to SecurityType:
	//
	//	*SecurityDetails_StockDetails
	//	*SecurityDetails_FundDetails
	SecurityType isSecurityDetails_SecurityType `protobuf_oneof:"security_type"`
}

func (x *SecurityDetails) Reset() {
	*x = SecurityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityDetails) ProtoMessage() {}

func (x *SecurityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityDetails.ProtoReflect.Descriptor instead.
func (*SecurityDetails) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{1}
}

func (m *SecurityDetails) GetSecurityType() isSecurityDetails_SecurityType {
	if m != nil {
		return m.SecurityType
	}
	return nil
}

func (x *SecurityDetails) GetStockDetails() *StockDetails {
	if x, ok := x.GetSecurityType().(*SecurityDetails_StockDetails); ok {
		return x.StockDetails
	}
	return nil
}

func (x *SecurityDetails) GetFundDetails() *FundDetails {
	if x, ok := x.GetSecurityType().(*SecurityDetails_FundDetails); ok {
		return x.FundDetails
	}
	return nil
}

type isSecurityDetails_SecurityType interface {
	isSecurityDetails_SecurityType()
}

type SecurityDetails_StockDetails struct {
	// StockDetails contains the stock related information and metadata
	StockDetails *StockDetails `protobuf:"bytes,1,opt,name=stock_details,json=stockDetails,proto3,oneof"`
}

type SecurityDetails_FundDetails struct {
	// FundDetails contains fund related information and metadata
	FundDetails *FundDetails `protobuf:"bytes,2,opt,name=fund_details,json=fundDetails,proto3,oneof"`
}

func (*SecurityDetails_StockDetails) isSecurityDetails_SecurityType() {}

func (*SecurityDetails_FundDetails) isSecurityDetails_SecurityType() {}

// StockDetails contains the stock related information and metadata
type StockDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockName                string                `protobuf:"bytes,1,opt,name=stock_name,json=stockName,proto3" json:"stock_name,omitempty"`
	StockShortName           string                `protobuf:"bytes,2,opt,name=stock_short_name,json=stockShortName,proto3" json:"stock_short_name,omitempty"`
	WebsiteUrl               string                `protobuf:"bytes,3,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`
	RegionName               string                `protobuf:"bytes,4,opt,name=region_name,json=regionName,proto3" json:"region_name,omitempty"`
	IncorporationCountryName string                `protobuf:"bytes,5,opt,name=incorporation_country_name,json=incorporationCountryName,proto3" json:"incorporation_country_name,omitempty"`
	GicsSectorType           GICSSectorType        `protobuf:"varint,6,opt,name=gics_sector_type,json=gicsSectorType,proto3,enum=api.securities.catalog.GICSSectorType" json:"gics_sector_type,omitempty"`
	GicsIndustryGroupType    GICSIndustryGroupType `protobuf:"varint,7,opt,name=gics_industry_group_type,json=gicsIndustryGroupType,proto3,enum=api.securities.catalog.GICSIndustryGroupType" json:"gics_industry_group_type,omitempty"`
	GicsIndustryType         GICSIndustryType      `protobuf:"varint,8,opt,name=gics_industry_type,json=gicsIndustryType,proto3,enum=api.securities.catalog.GICSIndustryType" json:"gics_industry_type,omitempty"`
	StockDescription         string                `protobuf:"bytes,9,opt,name=stock_description,json=stockDescription,proto3" json:"stock_description,omitempty"`
}

func (x *StockDetails) Reset() {
	*x = StockDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StockDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StockDetails) ProtoMessage() {}

func (x *StockDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StockDetails.ProtoReflect.Descriptor instead.
func (*StockDetails) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{2}
}

func (x *StockDetails) GetStockName() string {
	if x != nil {
		return x.StockName
	}
	return ""
}

func (x *StockDetails) GetStockShortName() string {
	if x != nil {
		return x.StockShortName
	}
	return ""
}

func (x *StockDetails) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

func (x *StockDetails) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *StockDetails) GetIncorporationCountryName() string {
	if x != nil {
		return x.IncorporationCountryName
	}
	return ""
}

func (x *StockDetails) GetGicsSectorType() GICSSectorType {
	if x != nil {
		return x.GicsSectorType
	}
	return GICSSectorType_GICS_SECTOR_TYPE_UNSPECIFIED
}

func (x *StockDetails) GetGicsIndustryGroupType() GICSIndustryGroupType {
	if x != nil {
		return x.GicsIndustryGroupType
	}
	return GICSIndustryGroupType_GICS_INDUSTRY_GROUP_TYPE_UNSPECIFIED
}

func (x *StockDetails) GetGicsIndustryType() GICSIndustryType {
	if x != nil {
		return x.GicsIndustryType
	}
	return GICSIndustryType_GICS_INDUSTRY_TYPE_UNSPECIFIED
}

func (x *StockDetails) GetStockDescription() string {
	if x != nil {
		return x.StockDescription
	}
	return ""
}

// FundDetails contains fund related information and metadata
type FundDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FundName             string                `protobuf:"bytes,1,opt,name=fund_name,json=fundName,proto3" json:"fund_name,omitempty"`
	FundNameShort        string                `protobuf:"bytes,2,opt,name=fund_name_short,json=fundNameShort,proto3" json:"fund_name_short,omitempty"`
	RegionName           string                `protobuf:"bytes,3,opt,name=region_name,json=regionName,proto3" json:"region_name,omitempty"`
	CountryName          string                `protobuf:"bytes,4,opt,name=country_name,json=countryName,proto3" json:"country_name,omitempty"`
	BenchmarkName        string                `protobuf:"bytes,5,opt,name=benchmark_name,json=benchmarkName,proto3" json:"benchmark_name,omitempty"`
	BenchmarkNameShort   string                `protobuf:"bytes,6,opt,name=benchmark_name_short,json=benchmarkNameShort,proto3" json:"benchmark_name_short,omitempty"`
	EtfHoldings          *Holdings             `protobuf:"bytes,7,opt,name=etf_holdings,json=etfHoldings,proto3" json:"etf_holdings,omitempty"`
	EquitySectorHoldings *EquitySectorHoldings `protobuf:"bytes,8,opt,name=equity_sector_holdings,json=equitySectorHoldings,proto3" json:"equity_sector_holdings,omitempty"`
	FundDescription      string                `protobuf:"bytes,9,opt,name=fund_description,json=fundDescription,proto3" json:"fund_description,omitempty"`
}

func (x *FundDetails) Reset() {
	*x = FundDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundDetails) ProtoMessage() {}

func (x *FundDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundDetails.ProtoReflect.Descriptor instead.
func (*FundDetails) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{3}
}

func (x *FundDetails) GetFundName() string {
	if x != nil {
		return x.FundName
	}
	return ""
}

func (x *FundDetails) GetFundNameShort() string {
	if x != nil {
		return x.FundNameShort
	}
	return ""
}

func (x *FundDetails) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *FundDetails) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *FundDetails) GetBenchmarkName() string {
	if x != nil {
		return x.BenchmarkName
	}
	return ""
}

func (x *FundDetails) GetBenchmarkNameShort() string {
	if x != nil {
		return x.BenchmarkNameShort
	}
	return ""
}

func (x *FundDetails) GetEtfHoldings() *Holdings {
	if x != nil {
		return x.EtfHoldings
	}
	return nil
}

func (x *FundDetails) GetEquitySectorHoldings() *EquitySectorHoldings {
	if x != nil {
		return x.EquitySectorHoldings
	}
	return nil
}

func (x *FundDetails) GetFundDescription() string {
	if x != nil {
		return x.FundDescription
	}
	return ""
}

// Holdings are the securities which constitute the ETF
type Holdings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Key is the security name and value would be it's weight in the fund
	Holdings map[string]float64 `protobuf:"bytes,1,rep,name=holdings,proto3" json:"holdings,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
}

func (x *Holdings) Reset() {
	*x = Holdings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Holdings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Holdings) ProtoMessage() {}

func (x *Holdings) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Holdings.ProtoReflect.Descriptor instead.
func (*Holdings) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{4}
}

func (x *Holdings) GetHoldings() map[string]float64 {
	if x != nil {
		return x.Holdings
	}
	return nil
}

type EquitySectorHoldings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key would be string value of GICSSectorType enum and value would be percentage of this sector
	EquitySectors map[string]float64 `protobuf:"bytes,1,rep,name=equity_sectors,json=equitySectors,proto3" json:"equity_sectors,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
}

func (x *EquitySectorHoldings) Reset() {
	*x = EquitySectorHoldings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquitySectorHoldings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquitySectorHoldings) ProtoMessage() {}

func (x *EquitySectorHoldings) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquitySectorHoldings.ProtoReflect.Descriptor instead.
func (*EquitySectorHoldings) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{5}
}

func (x *EquitySectorHoldings) GetEquitySectors() map[string]float64 {
	if x != nil {
		return x.EquitySectors
	}
	return nil
}

// FinancialInfo contains the financial information for the stock
type FinancialInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Market Capitalization (Market Cap) is the total market value of a company's outstanding shares of stock.
	// It is calculated as share price times the number of shares outstanding and is used to measure a company's size and investment risk.
	MarketCap *money.Money `protobuf:"bytes,1,opt,name=market_cap,json=marketCap,proto3" json:"market_cap,omitempty"`
	// Fundamental parameters for the stock based on the latest trailing twelve months (TTM) data
	TtmFundamentalParameters *FundamentalParameters `protobuf:"bytes,2,opt,name=ttm_fundamental_parameters,json=ttmFundamentalParameters,proto3" json:"ttm_fundamental_parameters,omitempty"`
}

func (x *FinancialInfo) Reset() {
	*x = FinancialInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinancialInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinancialInfo) ProtoMessage() {}

func (x *FinancialInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinancialInfo.ProtoReflect.Descriptor instead.
func (*FinancialInfo) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{6}
}

func (x *FinancialInfo) GetMarketCap() *money.Money {
	if x != nil {
		return x.MarketCap
	}
	return nil
}

func (x *FinancialInfo) GetTtmFundamentalParameters() *FundamentalParameters {
	if x != nil {
		return x.TtmFundamentalParameters
	}
	return nil
}

type FundamentalParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Book Value Per Share (BVPS) represents the equity available to common shareholders divided by the number of outstanding shares. It indicates the per-share value of a company's net assets and is used to assess whether a stock is undervalued or overvalued compared to its market price.
	BookValuePerShare *money.Money `protobuf:"bytes,1,opt,name=book_value_per_share,json=bookValuePerShare,proto3" json:"book_value_per_share,omitempty"`
	// Price-to-Earnings (P/E) Ratio is calculated as the market price per share divided by earnings per share. It measures how much investors are willing to pay for each dollar of earnings and is widely used to value companies and compare them across industries.
	PeRatio float64 `protobuf:"fixed64,2,opt,name=pe_ratio,json=peRatio,proto3" json:"pe_ratio,omitempty"`
	// Price-to-Book (P/B) Ratio compares a company's market value to its book value. It is calculated as the market price per share divided by book value per share. A lower P/B ratio may indicate an undervalued stock, while a higher ratio may suggest overvaluation.
	PbRatio float64 `protobuf:"fixed64,3,opt,name=pb_ratio,json=pbRatio,proto3" json:"pb_ratio,omitempty"`
	// Dividend Yield is the ratio of a company's annual dividend per share to its share price. It shows the return on investment from dividends alone and is important for income-focused investors.
	DividendYield float64 `protobuf:"fixed64,4,opt,name=dividend_yield,json=dividendYield,proto3" json:"dividend_yield,omitempty"`
	// Return on Equity (ROE) measures a company's profitability by showing how much profit it generates with the money shareholders have invested. It is calculated as net income divided by shareholder equity.
	ReturnOnEquity float64 `protobuf:"fixed64,5,opt,name=return_on_equity,json=returnOnEquity,proto3" json:"return_on_equity,omitempty"`
	// Shares Outstanding is the total number of a company's shares that are currently held by all its shareholders. It is used in the calculation of metrics like earnings per share and book value per share.
	SharesOutstanding float64 `protobuf:"fixed64,6,opt,name=shares_outstanding,json=sharesOutstanding,proto3" json:"shares_outstanding,omitempty"`
	// Sharpe Ratio measures the risk-adjusted return of the fund. It helps investors understand how much excess return they are receiving for the extra volatility they endure for holding a riskier asset compared to a risk-free asset
	SharpeRatio float64 `protobuf:"fixed64,7,opt,name=sharpe_ratio,json=sharpeRatio,proto3" json:"sharpe_ratio,omitempty"`
	// Tracking Error measures the degree to which the fund's performance deviates from the performance of its benchmark index
	TrackingErrorPercentage float64 `protobuf:"fixed64,8,opt,name=tracking_error_percentage,json=trackingErrorPercentage,proto3" json:"tracking_error_percentage,omitempty"`
	// Expense Ratio is the percentage value of the annual fee that the fund charges its investors
	ExpenseRatioPercentage float64 `protobuf:"fixed64,9,opt,name=expense_ratio_percentage,json=expenseRatioPercentage,proto3" json:"expense_ratio_percentage,omitempty"`
}

func (x *FundamentalParameters) Reset() {
	*x = FundamentalParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundamentalParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundamentalParameters) ProtoMessage() {}

func (x *FundamentalParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundamentalParameters.ProtoReflect.Descriptor instead.
func (*FundamentalParameters) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{7}
}

func (x *FundamentalParameters) GetBookValuePerShare() *money.Money {
	if x != nil {
		return x.BookValuePerShare
	}
	return nil
}

func (x *FundamentalParameters) GetPeRatio() float64 {
	if x != nil {
		return x.PeRatio
	}
	return 0
}

func (x *FundamentalParameters) GetPbRatio() float64 {
	if x != nil {
		return x.PbRatio
	}
	return 0
}

func (x *FundamentalParameters) GetDividendYield() float64 {
	if x != nil {
		return x.DividendYield
	}
	return 0
}

func (x *FundamentalParameters) GetReturnOnEquity() float64 {
	if x != nil {
		return x.ReturnOnEquity
	}
	return 0
}

func (x *FundamentalParameters) GetSharesOutstanding() float64 {
	if x != nil {
		return x.SharesOutstanding
	}
	return 0
}

func (x *FundamentalParameters) GetSharpeRatio() float64 {
	if x != nil {
		return x.SharpeRatio
	}
	return 0
}

func (x *FundamentalParameters) GetTrackingErrorPercentage() float64 {
	if x != nil {
		return x.TrackingErrorPercentage
	}
	return 0
}

func (x *FundamentalParameters) GetExpenseRatioPercentage() float64 {
	if x != nil {
		return x.ExpenseRatioPercentage
	}
	return 0
}

var File_api_securities_catalog_model_security_proto protoreflect.FileDescriptor

var file_api_securities_catalog_model_security_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xd5, 0x04, 0x0a, 0x08, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x49, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12,
	0x2c, 0x0a, 0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x52, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4c, 0x0a,
	0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x46,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb9, 0x01, 0x0a, 0x0f,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x4b, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0c,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x0c,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x46, 0x75, 0x6e, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x75, 0x6e, 0x64, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x96, 0x04, 0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x63,
	0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x55,
	0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x70, 0x6f, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x70, 0x6f,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x50, 0x0a, 0x10, 0x67, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x49, 0x43, 0x53, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0e, 0x67, 0x69, 0x63, 0x73, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x66, 0x0a, 0x18, 0x67, 0x69, 0x63, 0x73, 0x5f, 0x69, 0x6e, 0x64, 0x75,
	0x73, 0x74, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47,
	0x49, 0x43, 0x53, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x15, 0x67, 0x69, 0x63, 0x73, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74,
	0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x12, 0x67,
	0x69, 0x63, 0x73, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2e, 0x47, 0x49, 0x43, 0x53, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x10, 0x67, 0x69, 0x63, 0x73, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xc3, 0x03, 0x0a, 0x0b, 0x46, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x75, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65,
	0x53, 0x68, 0x6f, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x65, 0x6e,
	0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x30, 0x0a, 0x14, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x68, 0x6f,
	0x72, 0x74, 0x12, 0x43, 0x0a, 0x0c, 0x65, 0x74, 0x66, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0b, 0x65, 0x74, 0x66, 0x48,
	0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x62, 0x0a, 0x16, 0x65, 0x71, 0x75, 0x69, 0x74,
	0x79, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2e, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x48, 0x6f, 0x6c,
	0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x14, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x93, 0x01, 0x0a, 0x08, 0x48, 0x6f, 0x6c, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x4a, 0x0a, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x48,
	0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x1a,
	0x3b, 0x0a, 0x0d, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc0, 0x01, 0x0a,
	0x14, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x48, 0x6f, 0x6c,
	0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x66, 0x0a, 0x0e, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x5f,
	0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63,
	0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x45, 0x71, 0x75, 0x69,
	0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d,
	0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x1a, 0x40, 0x0a,
	0x12, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xaf, 0x01, 0x0a, 0x0d, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x43, 0x61, 0x70, 0x12, 0x6b, 0x0a, 0x1a, 0x74, 0x74, 0x6d, 0x5f, 0x66, 0x75, 0x6e, 0x64,
	0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x18, 0x74, 0x74, 0x6d, 0x46, 0x75, 0x6e, 0x64,
	0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x22, 0xab, 0x03, 0x0a, 0x15, 0x46, 0x75, 0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x43, 0x0a, 0x14, 0x62,
	0x6f, 0x6f, 0x6b, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x62,
	0x6f, 0x6f, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65, 0x72, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70,
	0x62, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x64, 0x5f, 0x79, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d,
	0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x64, 0x59, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x28, 0x0a,
	0x10, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x6f, 0x6e, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x74,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4f,
	0x6e, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x12, 0x2d, 0x0a, 0x12, 0x73, 0x68, 0x61, 0x72, 0x65,
	0x73, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x11, 0x73, 0x68, 0x61, 0x72, 0x65, 0x73, 0x4f, 0x75, 0x74, 0x73, 0x74,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x70, 0x65,
	0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x73, 0x68,
	0x61, 0x72, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x3a, 0x0a, 0x19, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65,
	0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65,
	0x52, 0x61, 0x74, 0x69, 0x6f, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x2a,
	0xbc, 0x03, 0x0a, 0x11, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54,
	0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x02, 0x12, 0x1c, 0x0a,
	0x18, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x2a, 0x0a, 0x26, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x49, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c,
	0x4f, 0x47, 0x4f, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x07, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x22,
	0x0a, 0x1e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x49,
	0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x0b, 0x42, 0x2f,
	0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_securities_catalog_model_security_proto_rawDescOnce sync.Once
	file_api_securities_catalog_model_security_proto_rawDescData = file_api_securities_catalog_model_security_proto_rawDesc
)

func file_api_securities_catalog_model_security_proto_rawDescGZIP() []byte {
	file_api_securities_catalog_model_security_proto_rawDescOnce.Do(func() {
		file_api_securities_catalog_model_security_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_securities_catalog_model_security_proto_rawDescData)
	})
	return file_api_securities_catalog_model_security_proto_rawDescData
}

var file_api_securities_catalog_model_security_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_securities_catalog_model_security_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_securities_catalog_model_security_proto_goTypes = []interface{}{
	(SecurityFieldMask)(0),        // 0: api.securities.catalog.SecurityFieldMask
	(*Security)(nil),              // 1: api.securities.catalog.Security
	(*SecurityDetails)(nil),       // 2: api.securities.catalog.SecurityDetails
	(*StockDetails)(nil),          // 3: api.securities.catalog.StockDetails
	(*FundDetails)(nil),           // 4: api.securities.catalog.FundDetails
	(*Holdings)(nil),              // 5: api.securities.catalog.Holdings
	(*EquitySectorHoldings)(nil),  // 6: api.securities.catalog.EquitySectorHoldings
	(*FinancialInfo)(nil),         // 7: api.securities.catalog.FinancialInfo
	(*FundamentalParameters)(nil), // 8: api.securities.catalog.FundamentalParameters
	nil,                           // 9: api.securities.catalog.Holdings.HoldingsEntry
	nil,                           // 10: api.securities.catalog.EquitySectorHoldings.EquitySectorsEntry
	(SecurityType)(0),             // 11: api.securities.catalog.SecurityType
	(vendorgateway.Vendor)(0),     // 12: vendorgateway.Vendor
	(*timestamppb.Timestamp)(nil), // 13: google.protobuf.Timestamp
	(GICSSectorType)(0),           // 14: api.securities.catalog.GICSSectorType
	(GICSIndustryGroupType)(0),    // 15: api.securities.catalog.GICSIndustryGroupType
	(GICSIndustryType)(0),         // 16: api.securities.catalog.GICSIndustryType
	(*money.Money)(nil),           // 17: google.type.Money
}
var file_api_securities_catalog_model_security_proto_depIdxs = []int32{
	11, // 0: api.securities.catalog.Security.security_type:type_name -> api.securities.catalog.SecurityType
	12, // 1: api.securities.catalog.Security.vendor:type_name -> vendorgateway.Vendor
	2,  // 2: api.securities.catalog.Security.security_details:type_name -> api.securities.catalog.SecurityDetails
	13, // 3: api.securities.catalog.Security.created_at:type_name -> google.protobuf.Timestamp
	13, // 4: api.securities.catalog.Security.updated_at:type_name -> google.protobuf.Timestamp
	13, // 5: api.securities.catalog.Security.deleted_at:type_name -> google.protobuf.Timestamp
	7,  // 6: api.securities.catalog.Security.financial_info:type_name -> api.securities.catalog.FinancialInfo
	3,  // 7: api.securities.catalog.SecurityDetails.stock_details:type_name -> api.securities.catalog.StockDetails
	4,  // 8: api.securities.catalog.SecurityDetails.fund_details:type_name -> api.securities.catalog.FundDetails
	14, // 9: api.securities.catalog.StockDetails.gics_sector_type:type_name -> api.securities.catalog.GICSSectorType
	15, // 10: api.securities.catalog.StockDetails.gics_industry_group_type:type_name -> api.securities.catalog.GICSIndustryGroupType
	16, // 11: api.securities.catalog.StockDetails.gics_industry_type:type_name -> api.securities.catalog.GICSIndustryType
	5,  // 12: api.securities.catalog.FundDetails.etf_holdings:type_name -> api.securities.catalog.Holdings
	6,  // 13: api.securities.catalog.FundDetails.equity_sector_holdings:type_name -> api.securities.catalog.EquitySectorHoldings
	9,  // 14: api.securities.catalog.Holdings.holdings:type_name -> api.securities.catalog.Holdings.HoldingsEntry
	10, // 15: api.securities.catalog.EquitySectorHoldings.equity_sectors:type_name -> api.securities.catalog.EquitySectorHoldings.EquitySectorsEntry
	17, // 16: api.securities.catalog.FinancialInfo.market_cap:type_name -> google.type.Money
	8,  // 17: api.securities.catalog.FinancialInfo.ttm_fundamental_parameters:type_name -> api.securities.catalog.FundamentalParameters
	17, // 18: api.securities.catalog.FundamentalParameters.book_value_per_share:type_name -> google.type.Money
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_api_securities_catalog_model_security_proto_init() }
func file_api_securities_catalog_model_security_proto_init() {
	if File_api_securities_catalog_model_security_proto != nil {
		return
	}
	file_api_securities_catalog_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_securities_catalog_model_security_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Security); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecurityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StockDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Holdings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquitySectorHoldings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinancialInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundamentalParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_securities_catalog_model_security_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*SecurityDetails_StockDetails)(nil),
		(*SecurityDetails_FundDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_securities_catalog_model_security_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_securities_catalog_model_security_proto_goTypes,
		DependencyIndexes: file_api_securities_catalog_model_security_proto_depIdxs,
		EnumInfos:         file_api_securities_catalog_model_security_proto_enumTypes,
		MessageInfos:      file_api_securities_catalog_model_security_proto_msgTypes,
	}.Build()
	File_api_securities_catalog_model_security_proto = out.File
	file_api_securities_catalog_model_security_proto_rawDesc = nil
	file_api_securities_catalog_model_security_proto_goTypes = nil
	file_api_securities_catalog_model_security_proto_depIdxs = nil
}
