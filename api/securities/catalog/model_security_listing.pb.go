//go:generate gen_sql -types=Exchange,ListingStatus,HistoricalPriceData,IntervalPriceData,IntervalDuration,PriceDataPoint

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/securities/catalog/model_security_listing.proto

package catalog

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SecurityListingFieldMask int32

const (
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_UNSPECIFIED        SecurityListingFieldMask = 0
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_INTERNAL_ID        SecurityListingFieldMask = 1
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID        SecurityListingFieldMask = 2
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SECURITY_ID        SecurityListingFieldMask = 3
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXCHANGE           SecurityListingFieldMask = 4
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SYMBOL             SecurityListingFieldMask = 5
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_IS_PRIMARY_LISTING SecurityListingFieldMask = 6
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_STATUS             SecurityListingFieldMask = 7
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN               SecurityListingFieldMask = 9
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR             SecurityListingFieldMask = 10
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID  SecurityListingFieldMask = 11
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_CREATED_AT         SecurityListingFieldMask = 12
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_UPDATED_AT         SecurityListingFieldMask = 13
	SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_DELETED_AT         SecurityListingFieldMask = 14
)

// Enum value maps for SecurityListingFieldMask.
var (
	SecurityListingFieldMask_name = map[int32]string{
		0:  "SECURITY_LISTING_FIELD_MASK_UNSPECIFIED",
		1:  "SECURITY_LISTING_FIELD_MASK_INTERNAL_ID",
		2:  "SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID",
		3:  "SECURITY_LISTING_FIELD_MASK_SECURITY_ID",
		4:  "SECURITY_LISTING_FIELD_MASK_EXCHANGE",
		5:  "SECURITY_LISTING_FIELD_MASK_SYMBOL",
		6:  "SECURITY_LISTING_FIELD_MASK_IS_PRIMARY_LISTING",
		7:  "SECURITY_LISTING_FIELD_MASK_STATUS",
		9:  "SECURITY_LISTING_FIELD_MASK_ISIN",
		10: "SECURITY_LISTING_FIELD_MASK_VENDOR",
		11: "SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID",
		12: "SECURITY_LISTING_FIELD_MASK_CREATED_AT",
		13: "SECURITY_LISTING_FIELD_MASK_UPDATED_AT",
		14: "SECURITY_LISTING_FIELD_MASK_DELETED_AT",
	}
	SecurityListingFieldMask_value = map[string]int32{
		"SECURITY_LISTING_FIELD_MASK_UNSPECIFIED":        0,
		"SECURITY_LISTING_FIELD_MASK_INTERNAL_ID":        1,
		"SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID":        2,
		"SECURITY_LISTING_FIELD_MASK_SECURITY_ID":        3,
		"SECURITY_LISTING_FIELD_MASK_EXCHANGE":           4,
		"SECURITY_LISTING_FIELD_MASK_SYMBOL":             5,
		"SECURITY_LISTING_FIELD_MASK_IS_PRIMARY_LISTING": 6,
		"SECURITY_LISTING_FIELD_MASK_STATUS":             7,
		"SECURITY_LISTING_FIELD_MASK_ISIN":               9,
		"SECURITY_LISTING_FIELD_MASK_VENDOR":             10,
		"SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID":  11,
		"SECURITY_LISTING_FIELD_MASK_CREATED_AT":         12,
		"SECURITY_LISTING_FIELD_MASK_UPDATED_AT":         13,
		"SECURITY_LISTING_FIELD_MASK_DELETED_AT":         14,
	}
)

func (x SecurityListingFieldMask) Enum() *SecurityListingFieldMask {
	p := new(SecurityListingFieldMask)
	*p = x
	return p
}

func (x SecurityListingFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecurityListingFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_securities_catalog_model_security_listing_proto_enumTypes[0].Descriptor()
}

func (SecurityListingFieldMask) Type() protoreflect.EnumType {
	return &file_api_securities_catalog_model_security_listing_proto_enumTypes[0]
}

func (x SecurityListingFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecurityListingFieldMask.Descriptor instead.
func (SecurityListingFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_listing_proto_rawDescGZIP(), []int{0}
}

// SecurityListing represents a listing of a security (such as a stock) on a specific exchange.
// It contains identifiers, exchange and symbol information, vendor details, and status fields.
// This message is designed to uniquely identify and describe a security's presence on a market.
type SecurityListing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for this security listing (e.g., "SL" + random string)
	InternalId string `protobuf:"bytes,1,opt,name=internal_id,json=internalId,proto3" json:"internal_id,omitempty"`
	// External id is the unique identifier in the usstocks table (format: USS/INS + idGen.RandAlphaNumericString(5)).
	// This field can be used to fetch the listing by its external reference.
	ExternalId string `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// Foreign key referencing the Security object this listing belongs to
	SecurityId string `protobuf:"bytes,3,opt,name=security_id,json=securityId,proto3" json:"security_id,omitempty"`
	// The exchange where this security is listed (e.g., NSE, NYSE, NASDAQ)
	Exchange Exchange `protobuf:"varint,4,opt,name=exchange,proto3,enum=api.securities.catalog.Exchange" json:"exchange,omitempty"`
	// The trading symbol/ticker for this listing on the exchange
	Symbol string `protobuf:"bytes,5,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// True if this is the primary listing for the security
	IsPrimaryListing bool `protobuf:"varint,6,opt,name=is_primary_listing,json=isPrimaryListing,proto3" json:"is_primary_listing,omitempty"`
	// The current status of this listing (active, inactive, etc.)
	Status ListingStatus `protobuf:"varint,7,opt,name=status,proto3,enum=api.securities.catalog.ListingStatus" json:"status,omitempty"`
	// International Securities Identification Number (ISIN) for this listing
	Isin string `protobuf:"bytes,8,opt,name=isin,proto3" json:"isin,omitempty"`
	// Vendor information (e.g., data provider or source)
	Vendor vendorgateway.Vendor `protobuf:"varint,9,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// The vendor's unique identifier for this listing
	VendorListingId string                 `protobuf:"bytes,10,opt,name=vendor_listing_id,json=vendorListingId,proto3" json:"vendor_listing_id,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt       *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *SecurityListing) Reset() {
	*x = SecurityListing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_listing_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityListing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityListing) ProtoMessage() {}

func (x *SecurityListing) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_listing_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityListing.ProtoReflect.Descriptor instead.
func (*SecurityListing) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_listing_proto_rawDescGZIP(), []int{0}
}

func (x *SecurityListing) GetInternalId() string {
	if x != nil {
		return x.InternalId
	}
	return ""
}

func (x *SecurityListing) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *SecurityListing) GetSecurityId() string {
	if x != nil {
		return x.SecurityId
	}
	return ""
}

func (x *SecurityListing) GetExchange() Exchange {
	if x != nil {
		return x.Exchange
	}
	return Exchange_EXCHANGE_UNSPECIFIED
}

func (x *SecurityListing) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SecurityListing) GetIsPrimaryListing() bool {
	if x != nil {
		return x.IsPrimaryListing
	}
	return false
}

func (x *SecurityListing) GetStatus() ListingStatus {
	if x != nil {
		return x.Status
	}
	return ListingStatus_LISTING_STATUS_UNSPECIFIED
}

func (x *SecurityListing) GetIsin() string {
	if x != nil {
		return x.Isin
	}
	return ""
}

func (x *SecurityListing) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *SecurityListing) GetVendorListingId() string {
	if x != nil {
		return x.VendorListingId
	}
	return ""
}

func (x *SecurityListing) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SecurityListing) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SecurityListing) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_securities_catalog_model_security_listing_proto protoreflect.FileDescriptor

var file_api_securities_catalog_model_security_listing_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x1a, 0x22, 0x61,
	0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xd7, 0x04, 0x0a, 0x0f, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x08, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x08, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12,
	0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3d, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63,
	0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x73, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x73, 0x69, 0x6e,
	0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12,
	0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x81, 0x05, 0x0a,
	0x18, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x49,
	0x44, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x44, 0x10, 0x02,
	0x12, 0x2b, 0x0a, 0x27, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53,
	0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x28, 0x0a,
	0x24, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e,
	0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x43,
	0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x59, 0x4d, 0x42, 0x4f, 0x4c, 0x10, 0x05, 0x12,
	0x32, 0x0a, 0x2e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49,
	0x53, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e,
	0x47, 0x10, 0x06, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x07, 0x12, 0x24, 0x0a, 0x20, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x53, 0x49, 0x4e, 0x10,
	0x09, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49,
	0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x0a, 0x12, 0x31, 0x0a, 0x2d, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x44, 0x10, 0x0b, 0x12, 0x2a, 0x0a, 0x26,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0c, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x0d, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59,
	0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0e,
	0x42, 0x2f, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_securities_catalog_model_security_listing_proto_rawDescOnce sync.Once
	file_api_securities_catalog_model_security_listing_proto_rawDescData = file_api_securities_catalog_model_security_listing_proto_rawDesc
)

func file_api_securities_catalog_model_security_listing_proto_rawDescGZIP() []byte {
	file_api_securities_catalog_model_security_listing_proto_rawDescOnce.Do(func() {
		file_api_securities_catalog_model_security_listing_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_securities_catalog_model_security_listing_proto_rawDescData)
	})
	return file_api_securities_catalog_model_security_listing_proto_rawDescData
}

var file_api_securities_catalog_model_security_listing_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_securities_catalog_model_security_listing_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_securities_catalog_model_security_listing_proto_goTypes = []interface{}{
	(SecurityListingFieldMask)(0), // 0: api.securities.catalog.SecurityListingFieldMask
	(*SecurityListing)(nil),       // 1: api.securities.catalog.SecurityListing
	(Exchange)(0),                 // 2: api.securities.catalog.Exchange
	(ListingStatus)(0),            // 3: api.securities.catalog.ListingStatus
	(vendorgateway.Vendor)(0),     // 4: vendorgateway.Vendor
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_api_securities_catalog_model_security_listing_proto_depIdxs = []int32{
	2, // 0: api.securities.catalog.SecurityListing.exchange:type_name -> api.securities.catalog.Exchange
	3, // 1: api.securities.catalog.SecurityListing.status:type_name -> api.securities.catalog.ListingStatus
	4, // 2: api.securities.catalog.SecurityListing.vendor:type_name -> vendorgateway.Vendor
	5, // 3: api.securities.catalog.SecurityListing.created_at:type_name -> google.protobuf.Timestamp
	5, // 4: api.securities.catalog.SecurityListing.updated_at:type_name -> google.protobuf.Timestamp
	5, // 5: api.securities.catalog.SecurityListing.deleted_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_securities_catalog_model_security_listing_proto_init() }
func file_api_securities_catalog_model_security_listing_proto_init() {
	if File_api_securities_catalog_model_security_listing_proto != nil {
		return
	}
	file_api_securities_catalog_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_securities_catalog_model_security_listing_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecurityListing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_securities_catalog_model_security_listing_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_securities_catalog_model_security_listing_proto_goTypes,
		DependencyIndexes: file_api_securities_catalog_model_security_listing_proto_depIdxs,
		EnumInfos:         file_api_securities_catalog_model_security_listing_proto_enumTypes,
		MessageInfos:      file_api_securities_catalog_model_security_listing_proto_msgTypes,
	}.Build()
	File_api_securities_catalog_model_security_listing_proto = out.File
	file_api_securities_catalog_model_security_listing_proto_rawDesc = nil
	file_api_securities_catalog_model_security_listing_proto_goTypes = nil
	file_api_securities_catalog_model_security_listing_proto_depIdxs = nil
}
