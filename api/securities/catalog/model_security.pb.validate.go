// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/securities/catalog/model_security.proto

package catalog

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on Security with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Security) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Security with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SecurityMultiError, or nil
// if none found.
func (m *Security) ValidateAll() error {
	return m.validate(true)
}

func (m *Security) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for SecurityType

	// no validation rules for SecurityName

	// no validation rules for Vendor

	// no validation rules for VendorSecurityId

	// no validation rules for LogoUrl

	if all {
		switch v := interface{}(m.GetSecurityDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "SecurityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "SecurityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecurityDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "SecurityDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinancialInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "FinancialInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "FinancialInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinancialInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "FinancialInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SecurityMultiError(errors)
	}

	return nil
}

// SecurityMultiError is an error wrapping multiple validation errors returned
// by Security.ValidateAll() if the designated constraints aren't met.
type SecurityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecurityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecurityMultiError) AllErrors() []error { return m }

// SecurityValidationError is the validation error returned by
// Security.Validate if the designated constraints aren't met.
type SecurityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecurityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecurityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecurityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecurityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecurityValidationError) ErrorName() string { return "SecurityValidationError" }

// Error satisfies the builtin error interface
func (e SecurityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecurity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecurityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecurityValidationError{}

// Validate checks the field values on SecurityDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SecurityDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecurityDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecurityDetailsMultiError, or nil if none found.
func (m *SecurityDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SecurityDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.SecurityType.(type) {
	case *SecurityDetails_StockDetails:
		if v == nil {
			err := SecurityDetailsValidationError{
				field:  "SecurityType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStockDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecurityDetailsValidationError{
						field:  "StockDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecurityDetailsValidationError{
						field:  "StockDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStockDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecurityDetailsValidationError{
					field:  "StockDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecurityDetails_FundDetails:
		if v == nil {
			err := SecurityDetailsValidationError{
				field:  "SecurityType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFundDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecurityDetailsValidationError{
						field:  "FundDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecurityDetailsValidationError{
						field:  "FundDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFundDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecurityDetailsValidationError{
					field:  "FundDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecurityDetailsMultiError(errors)
	}

	return nil
}

// SecurityDetailsMultiError is an error wrapping multiple validation errors
// returned by SecurityDetails.ValidateAll() if the designated constraints
// aren't met.
type SecurityDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecurityDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecurityDetailsMultiError) AllErrors() []error { return m }

// SecurityDetailsValidationError is the validation error returned by
// SecurityDetails.Validate if the designated constraints aren't met.
type SecurityDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecurityDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecurityDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecurityDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecurityDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecurityDetailsValidationError) ErrorName() string { return "SecurityDetailsValidationError" }

// Error satisfies the builtin error interface
func (e SecurityDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecurityDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecurityDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecurityDetailsValidationError{}

// Validate checks the field values on StockDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StockDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StockDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StockDetailsMultiError, or
// nil if none found.
func (m *StockDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *StockDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StockName

	// no validation rules for StockShortName

	// no validation rules for WebsiteUrl

	// no validation rules for RegionName

	// no validation rules for IncorporationCountryName

	// no validation rules for GicsSectorType

	// no validation rules for GicsIndustryGroupType

	// no validation rules for GicsIndustryType

	// no validation rules for StockDescription

	if len(errors) > 0 {
		return StockDetailsMultiError(errors)
	}

	return nil
}

// StockDetailsMultiError is an error wrapping multiple validation errors
// returned by StockDetails.ValidateAll() if the designated constraints aren't met.
type StockDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StockDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StockDetailsMultiError) AllErrors() []error { return m }

// StockDetailsValidationError is the validation error returned by
// StockDetails.Validate if the designated constraints aren't met.
type StockDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StockDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StockDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StockDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StockDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StockDetailsValidationError) ErrorName() string { return "StockDetailsValidationError" }

// Error satisfies the builtin error interface
func (e StockDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStockDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StockDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StockDetailsValidationError{}

// Validate checks the field values on FundDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FundDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FundDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FundDetailsMultiError, or
// nil if none found.
func (m *FundDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FundDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FundName

	// no validation rules for FundNameShort

	// no validation rules for RegionName

	// no validation rules for CountryName

	// no validation rules for BenchmarkName

	// no validation rules for BenchmarkNameShort

	if all {
		switch v := interface{}(m.GetEtfHoldings()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundDetailsValidationError{
					field:  "EtfHoldings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundDetailsValidationError{
					field:  "EtfHoldings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEtfHoldings()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundDetailsValidationError{
				field:  "EtfHoldings",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEquitySectorHoldings()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundDetailsValidationError{
					field:  "EquitySectorHoldings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundDetailsValidationError{
					field:  "EquitySectorHoldings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEquitySectorHoldings()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundDetailsValidationError{
				field:  "EquitySectorHoldings",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FundDescription

	if len(errors) > 0 {
		return FundDetailsMultiError(errors)
	}

	return nil
}

// FundDetailsMultiError is an error wrapping multiple validation errors
// returned by FundDetails.ValidateAll() if the designated constraints aren't met.
type FundDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FundDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FundDetailsMultiError) AllErrors() []error { return m }

// FundDetailsValidationError is the validation error returned by
// FundDetails.Validate if the designated constraints aren't met.
type FundDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FundDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FundDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FundDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FundDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FundDetailsValidationError) ErrorName() string { return "FundDetailsValidationError" }

// Error satisfies the builtin error interface
func (e FundDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFundDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FundDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FundDetailsValidationError{}

// Validate checks the field values on Holdings with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Holdings) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Holdings with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HoldingsMultiError, or nil
// if none found.
func (m *Holdings) ValidateAll() error {
	return m.validate(true)
}

func (m *Holdings) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Holdings

	if len(errors) > 0 {
		return HoldingsMultiError(errors)
	}

	return nil
}

// HoldingsMultiError is an error wrapping multiple validation errors returned
// by Holdings.ValidateAll() if the designated constraints aren't met.
type HoldingsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HoldingsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HoldingsMultiError) AllErrors() []error { return m }

// HoldingsValidationError is the validation error returned by
// Holdings.Validate if the designated constraints aren't met.
type HoldingsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HoldingsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HoldingsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HoldingsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HoldingsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HoldingsValidationError) ErrorName() string { return "HoldingsValidationError" }

// Error satisfies the builtin error interface
func (e HoldingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHoldings.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HoldingsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HoldingsValidationError{}

// Validate checks the field values on EquitySectorHoldings with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EquitySectorHoldings) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EquitySectorHoldings with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EquitySectorHoldingsMultiError, or nil if none found.
func (m *EquitySectorHoldings) ValidateAll() error {
	return m.validate(true)
}

func (m *EquitySectorHoldings) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EquitySectors

	if len(errors) > 0 {
		return EquitySectorHoldingsMultiError(errors)
	}

	return nil
}

// EquitySectorHoldingsMultiError is an error wrapping multiple validation
// errors returned by EquitySectorHoldings.ValidateAll() if the designated
// constraints aren't met.
type EquitySectorHoldingsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EquitySectorHoldingsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EquitySectorHoldingsMultiError) AllErrors() []error { return m }

// EquitySectorHoldingsValidationError is the validation error returned by
// EquitySectorHoldings.Validate if the designated constraints aren't met.
type EquitySectorHoldingsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EquitySectorHoldingsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EquitySectorHoldingsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EquitySectorHoldingsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EquitySectorHoldingsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EquitySectorHoldingsValidationError) ErrorName() string {
	return "EquitySectorHoldingsValidationError"
}

// Error satisfies the builtin error interface
func (e EquitySectorHoldingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEquitySectorHoldings.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EquitySectorHoldingsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EquitySectorHoldingsValidationError{}

// Validate checks the field values on FinancialInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FinancialInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinancialInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FinancialInfoMultiError, or
// nil if none found.
func (m *FinancialInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FinancialInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMarketCap()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "MarketCap",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "MarketCap",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMarketCap()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialInfoValidationError{
				field:  "MarketCap",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTtmFundamentalParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "TtmFundamentalParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "TtmFundamentalParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTtmFundamentalParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialInfoValidationError{
				field:  "TtmFundamentalParameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FinancialInfoMultiError(errors)
	}

	return nil
}

// FinancialInfoMultiError is an error wrapping multiple validation errors
// returned by FinancialInfo.ValidateAll() if the designated constraints
// aren't met.
type FinancialInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinancialInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinancialInfoMultiError) AllErrors() []error { return m }

// FinancialInfoValidationError is the validation error returned by
// FinancialInfo.Validate if the designated constraints aren't met.
type FinancialInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinancialInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinancialInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinancialInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinancialInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinancialInfoValidationError) ErrorName() string { return "FinancialInfoValidationError" }

// Error satisfies the builtin error interface
func (e FinancialInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinancialInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinancialInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinancialInfoValidationError{}

// Validate checks the field values on FundamentalParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FundamentalParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FundamentalParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FundamentalParametersMultiError, or nil if none found.
func (m *FundamentalParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *FundamentalParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBookValuePerShare()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundamentalParametersValidationError{
					field:  "BookValuePerShare",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundamentalParametersValidationError{
					field:  "BookValuePerShare",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBookValuePerShare()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundamentalParametersValidationError{
				field:  "BookValuePerShare",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PeRatio

	// no validation rules for PbRatio

	// no validation rules for DividendYield

	// no validation rules for ReturnOnEquity

	// no validation rules for SharesOutstanding

	// no validation rules for SharpeRatio

	// no validation rules for TrackingErrorPercentage

	// no validation rules for ExpenseRatioPercentage

	if len(errors) > 0 {
		return FundamentalParametersMultiError(errors)
	}

	return nil
}

// FundamentalParametersMultiError is an error wrapping multiple validation
// errors returned by FundamentalParameters.ValidateAll() if the designated
// constraints aren't met.
type FundamentalParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FundamentalParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FundamentalParametersMultiError) AllErrors() []error { return m }

// FundamentalParametersValidationError is the validation error returned by
// FundamentalParameters.Validate if the designated constraints aren't met.
type FundamentalParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FundamentalParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FundamentalParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FundamentalParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FundamentalParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FundamentalParametersValidationError) ErrorName() string {
	return "FundamentalParametersValidationError"
}

// Error satisfies the builtin error interface
func (e FundamentalParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFundamentalParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FundamentalParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FundamentalParametersValidationError{}
