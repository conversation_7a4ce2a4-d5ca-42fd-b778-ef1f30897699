// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/pay/signal/fund_transfer.proto

package signal

import (
	payment "github.com/epifi/gamma/api/order/payment"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Signal payload to be used by fund transfer workflow to notify it's client workflows
type FundTransferStatusSignal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionStatus payment.TransactionStatus `protobuf:"varint,1,opt,name=transaction_status,json=transactionStatus,proto3,enum=order.payment.TransactionStatus" json:"transaction_status,omitempty"`
	TransactionId     string                    `protobuf:"bytes,2,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	OrderId           string                    `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *FundTransferStatusSignal) Reset() {
	*x = FundTransferStatusSignal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_signal_fund_transfer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundTransferStatusSignal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundTransferStatusSignal) ProtoMessage() {}

func (x *FundTransferStatusSignal) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_signal_fund_transfer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundTransferStatusSignal.ProtoReflect.Descriptor instead.
func (*FundTransferStatusSignal) Descriptor() ([]byte, []int) {
	return file_api_pay_signal_fund_transfer_proto_rawDescGZIP(), []int{0}
}

func (x *FundTransferStatusSignal) GetTransactionStatus() payment.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return payment.TransactionStatus(0)
}

func (x *FundTransferStatusSignal) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *FundTransferStatusSignal) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

var File_api_pay_signal_fund_transfer_proto protoreflect.FileDescriptor

var file_api_pay_signal_fund_transfer_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c,
	0x2f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c,
	0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xad, 0x01, 0x0a, 0x18, 0x46, 0x75, 0x6e, 0x64, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x6c, 0x12, 0x4f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x5a, 0x25,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x2f, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_pay_signal_fund_transfer_proto_rawDescOnce sync.Once
	file_api_pay_signal_fund_transfer_proto_rawDescData = file_api_pay_signal_fund_transfer_proto_rawDesc
)

func file_api_pay_signal_fund_transfer_proto_rawDescGZIP() []byte {
	file_api_pay_signal_fund_transfer_proto_rawDescOnce.Do(func() {
		file_api_pay_signal_fund_transfer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_pay_signal_fund_transfer_proto_rawDescData)
	})
	return file_api_pay_signal_fund_transfer_proto_rawDescData
}

var file_api_pay_signal_fund_transfer_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_pay_signal_fund_transfer_proto_goTypes = []interface{}{
	(*FundTransferStatusSignal)(nil), // 0: pay.signal.FundTransferStatusSignal
	(payment.TransactionStatus)(0),   // 1: order.payment.TransactionStatus
}
var file_api_pay_signal_fund_transfer_proto_depIdxs = []int32{
	1, // 0: pay.signal.FundTransferStatusSignal.transaction_status:type_name -> order.payment.TransactionStatus
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_pay_signal_fund_transfer_proto_init() }
func file_api_pay_signal_fund_transfer_proto_init() {
	if File_api_pay_signal_fund_transfer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_pay_signal_fund_transfer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundTransferStatusSignal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_pay_signal_fund_transfer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_pay_signal_fund_transfer_proto_goTypes,
		DependencyIndexes: file_api_pay_signal_fund_transfer_proto_depIdxs,
		MessageInfos:      file_api_pay_signal_fund_transfer_proto_msgTypes,
	}.Build()
	File_api_pay_signal_fund_transfer_proto = out.File
	file_api_pay_signal_fund_transfer_proto_rawDesc = nil
	file_api_pay_signal_fund_transfer_proto_goTypes = nil
	file_api_pay_signal_fund_transfer_proto_depIdxs = nil
}
