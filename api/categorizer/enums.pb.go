// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/categorizer/enums.proto

package categorizer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// bank name of the user
//
//go:generate gen_sql -types=DisplayCategoryType
type BankName int32

const (
	BankName_BANK_NAME_UNSPECIFIED BankName = 0
	BankName_FI                    BankName = 1
	BankName_ICICI                 BankName = 2
	BankName_HDFC                  BankName = 3
)

// Enum value maps for BankName.
var (
	BankName_name = map[int32]string{
		0: "BANK_NAME_UNSPECIFIED",
		1: "FI",
		2: "ICICI",
		3: "HDFC",
	}
	BankName_value = map[string]int32{
		"BANK_NAME_UNSPECIFIED": 0,
		"FI":                    1,
		"ICICI":                 2,
		"HDFC":                  3,
	}
)

func (x BankName) Enum() *BankName {
	p := new(BankName)
	*p = x
	return p
}

func (x BankName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[0].Descriptor()
}

func (BankName) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[0]
}

func (x BankName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankName.Descriptor instead.
func (BankName) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{0}
}

// model name which predicts the category
type CategorisationSource int32

const (
	CategorisationSource_CATEGORISATION_SOURCE_UNSPECIFIED CategorisationSource = 0
	CategorisationSource_MERCHANT_RESOLUTION_SERVICE       CategorisationSource = 1
	CategorisationSource_MERCHANT_VPA_MCC                  CategorisationSource = 2
	CategorisationSource_MERCHANT_VPA_SUBCODE              CategorisationSource = 3
	CategorisationSource_MERCHANT_CARD_MCC                 CategorisationSource = 4
	CategorisationSource_RULE_ENGINE                       CategorisationSource = 5
	// Google Place APIs @ https://developers.google.com/maps/documentation/places/web-service/overview
	// They help to figure out the place details such as category, address
	CategorisationSource_GOOGLE_PLACE_API CategorisationSource = 6
	// Peer_engine will mostly categorize P2P txns. For such cases, Peers_engine will be given as categorisation source
	CategorisationSource_PEERS_ENGINE                                 CategorisationSource = 7
	CategorisationSource_ACCOUNT_AGGREGATOR_ENGINE                    CategorisationSource = 8
	CategorisationSource_REMARKS_ENGINE                               CategorisationSource = 9
	CategorisationSource_CATEGORIZATION_SOURCE_USER_FUTURE_PREFERENCE CategorisationSource = 10
	CategorisationSource_CROWD_AGGREGATION                            CategorisationSource = 11
	CategorisationSource_CATEGORIZATION_SOURCE_USER_SIMILAR_RECAT     CategorisationSource = 12
	// CC Txn contains a category populated by the vendor. If this category is used for txn categorisation then categorisation source will be CATEGORIZATION_SOURCE_CC_TXN_CATEGORY.
	CategorisationSource_CATEGORIZATION_SOURCE_CC_TXN_CATEGORY CategorisationSource = 13
)

// Enum value maps for CategorisationSource.
var (
	CategorisationSource_name = map[int32]string{
		0:  "CATEGORISATION_SOURCE_UNSPECIFIED",
		1:  "MERCHANT_RESOLUTION_SERVICE",
		2:  "MERCHANT_VPA_MCC",
		3:  "MERCHANT_VPA_SUBCODE",
		4:  "MERCHANT_CARD_MCC",
		5:  "RULE_ENGINE",
		6:  "GOOGLE_PLACE_API",
		7:  "PEERS_ENGINE",
		8:  "ACCOUNT_AGGREGATOR_ENGINE",
		9:  "REMARKS_ENGINE",
		10: "CATEGORIZATION_SOURCE_USER_FUTURE_PREFERENCE",
		11: "CROWD_AGGREGATION",
		12: "CATEGORIZATION_SOURCE_USER_SIMILAR_RECAT",
		13: "CATEGORIZATION_SOURCE_CC_TXN_CATEGORY",
	}
	CategorisationSource_value = map[string]int32{
		"CATEGORISATION_SOURCE_UNSPECIFIED":            0,
		"MERCHANT_RESOLUTION_SERVICE":                  1,
		"MERCHANT_VPA_MCC":                             2,
		"MERCHANT_VPA_SUBCODE":                         3,
		"MERCHANT_CARD_MCC":                            4,
		"RULE_ENGINE":                                  5,
		"GOOGLE_PLACE_API":                             6,
		"PEERS_ENGINE":                                 7,
		"ACCOUNT_AGGREGATOR_ENGINE":                    8,
		"REMARKS_ENGINE":                               9,
		"CATEGORIZATION_SOURCE_USER_FUTURE_PREFERENCE": 10,
		"CROWD_AGGREGATION":                            11,
		"CATEGORIZATION_SOURCE_USER_SIMILAR_RECAT":     12,
		"CATEGORIZATION_SOURCE_CC_TXN_CATEGORY":        13,
	}
)

func (x CategorisationSource) Enum() *CategorisationSource {
	p := new(CategorisationSource)
	*p = x
	return p
}

func (x CategorisationSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CategorisationSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[1].Descriptor()
}

func (CategorisationSource) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[1]
}

func (x CategorisationSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CategorisationSource.Descriptor instead.
func (CategorisationSource) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{1}
}

type DisplayCategoryType int32

const (
	DisplayCategoryType_DISPLAY_CATEGORY_TYPE_UNSPECIFIED DisplayCategoryType = 0
	DisplayCategoryType_CREDIT                            DisplayCategoryType = 1
	DisplayCategoryType_DEBIT                             DisplayCategoryType = 2
)

// Enum value maps for DisplayCategoryType.
var (
	DisplayCategoryType_name = map[int32]string{
		0: "DISPLAY_CATEGORY_TYPE_UNSPECIFIED",
		1: "CREDIT",
		2: "DEBIT",
	}
	DisplayCategoryType_value = map[string]int32{
		"DISPLAY_CATEGORY_TYPE_UNSPECIFIED": 0,
		"CREDIT":                            1,
		"DEBIT":                             2,
	}
)

func (x DisplayCategoryType) Enum() *DisplayCategoryType {
	p := new(DisplayCategoryType)
	*p = x
	return p
}

func (x DisplayCategoryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisplayCategoryType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[2].Descriptor()
}

func (DisplayCategoryType) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[2]
}

func (x DisplayCategoryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisplayCategoryType.Descriptor instead.
func (DisplayCategoryType) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{2}
}

// source from where category is generated
type Provenance int32

const (
	Provenance_PROVENANCE_UNSPECIFIED Provenance = 0
	// transaction category was added based on DS categoriser model output and associated meta data for a transaction
	Provenance_DS Provenance = 1
	// transaction category was attached based on user's input on re-categorisation from the app.
	Provenance_USER Provenance = 2
)

// Enum value maps for Provenance.
var (
	Provenance_name = map[int32]string{
		0: "PROVENANCE_UNSPECIFIED",
		1: "DS",
		2: "USER",
	}
	Provenance_value = map[string]int32{
		"PROVENANCE_UNSPECIFIED": 0,
		"DS":                     1,
		"USER":                   2,
	}
)

func (x Provenance) Enum() *Provenance {
	p := new(Provenance)
	*p = x
	return p
}

func (x Provenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Provenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[3].Descriptor()
}

func (Provenance) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[3]
}

func (x Provenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Provenance.Descriptor instead.
func (Provenance) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{3}
}

// enum values of human readable display category associated with an ontology
type DisplayCategory int32

const (
	DisplayCategory_DISPLAY_CATEGORY_UNSPECIFIED DisplayCategory = 0
	DisplayCategory_SALARY                       DisplayCategory = 1
	DisplayCategory_BONUS                        DisplayCategory = 2
	DisplayCategory_INCOME                       DisplayCategory = 3
	DisplayCategory_REDEMPTION                   DisplayCategory = 4
	DisplayCategory_INTEREST_DIVIDENDS           DisplayCategory = 5
	DisplayCategory_REFUNDS                      DisplayCategory = 6
	DisplayCategory_CASHBACKS                    DisplayCategory = 7
	DisplayCategory_SELF_TRANSFER                DisplayCategory = 9
	DisplayCategory_DEPOSITS                     DisplayCategory = 10
	DisplayCategory_STOCKS_MUTUAL_FUNDS          DisplayCategory = 11
	DisplayCategory_SECURITIES                   DisplayCategory = 12
	DisplayCategory_INVESTMENTS                  DisplayCategory = 13
	DisplayCategory_CRYPTO                       DisplayCategory = 14
	DisplayCategory_EMI_PAYMENT                  DisplayCategory = 15
	DisplayCategory_LOAN_PREPAYMENT              DisplayCategory = 16
	DisplayCategory_CREDIT_CARD                  DisplayCategory = 18
	DisplayCategory_CREDIT_REPAYMENT             DisplayCategory = 19
	DisplayCategory_INSURANCE                    DisplayCategory = 20
	DisplayCategory_HOUSE_DEPOSIT                DisplayCategory = 21
	DisplayCategory_UTILITY_BILLS                DisplayCategory = 22
	DisplayCategory_MOBILE_DATA_RECHARGE         DisplayCategory = 23
	DisplayCategory_MAINTENANCE_REPAIR           DisplayCategory = 25
	DisplayCategory_FURNITURE_APPLIANCES         DisplayCategory = 26
	DisplayCategory_HOUSING                      DisplayCategory = 27
	DisplayCategory_CHILDCARE                    DisplayCategory = 28
	DisplayCategory_DOMESTIC_HELP                DisplayCategory = 29
	DisplayCategory_EDUCATION                    DisplayCategory = 30
	DisplayCategory_PETS                         DisplayCategory = 31
	DisplayCategory_FAMILY                       DisplayCategory = 32
	DisplayCategory_ONLINE_COURSES               DisplayCategory = 33
	DisplayCategory_EATING_OUT                   DisplayCategory = 34
	DisplayCategory_ENTERTAINMENT                DisplayCategory = 35
	DisplayCategory_BOOKS_PUBLICATIONS           DisplayCategory = 36
	DisplayCategory_SPORTS_GAMES                 DisplayCategory = 37
	DisplayCategory_ONLINE_GAMING                DisplayCategory = 38
	DisplayCategory_MOVIES                       DisplayCategory = 41
	DisplayCategory_CONCERTS                     DisplayCategory = 42
	DisplayCategory_MUSIC                        DisplayCategory = 43
	DisplayCategory_ENTERTAINMENT_SUBSCRIPTIONS  DisplayCategory = 44
	DisplayCategory_CLOTHING_ACCESSORIES         DisplayCategory = 46
	DisplayCategory_GADGETS                      DisplayCategory = 47
	DisplayCategory_APPLIANCES                   DisplayCategory = 48
	DisplayCategory_ONLINE_SHOPPING              DisplayCategory = 49
	DisplayCategory_SHOPPING                     DisplayCategory = 51
	DisplayCategory_JEWELLERY                    DisplayCategory = 52
	DisplayCategory_GROCERIES_ESSENTIALS         DisplayCategory = 55
	DisplayCategory_PERSONAL_CARE                DisplayCategory = 56
	DisplayCategory_HEALTH_WELNESS               DisplayCategory = 57
	DisplayCategory_FLIGHTS                      DisplayCategory = 59
	DisplayCategory_TRAINS                       DisplayCategory = 60
	DisplayCategory_ROAD_TRAVEL                  DisplayCategory = 61
	DisplayCategory_COMMUTE                      DisplayCategory = 62
	DisplayCategory_FUEL                         DisplayCategory = 64
	DisplayCategory_VEHICLE_SERVICE              DisplayCategory = 66
	DisplayCategory_SHIPPING_LOGISTICS           DisplayCategory = 67
	DisplayCategory_TRANSPORTATION               DisplayCategory = 68
	DisplayCategory_MISCELLANEOUS                DisplayCategory = 69
	DisplayCategory_BUSINESS_SPENDS              DisplayCategory = 70
	DisplayCategory_DONATIONS                    DisplayCategory = 71
	DisplayCategory_SOFTWARE                     DisplayCategory = 72
	DisplayCategory_BANK_FEES                    DisplayCategory = 73
	DisplayCategory_FINANCIAL_SERVICES           DisplayCategory = 74
	DisplayCategory_GIFTS                        DisplayCategory = 75
	DisplayCategory_TAXES                        DisplayCategory = 76
	DisplayCategory_TRAVEL                       DisplayCategory = 77
	DisplayCategory_CASH_WITHDRAWALS             DisplayCategory = 78
	DisplayCategory_WALLET_RECHARGE              DisplayCategory = 79
	DisplayCategory_SPENDS                       DisplayCategory = 81
	DisplayCategory_RENT                         DisplayCategory = 82
	DisplayCategory_EVENTS_ACTIVITIES            DisplayCategory = 84
	DisplayCategory_CREDIT_CARD_LOAN             DisplayCategory = 85
	DisplayCategory_FAMILY_CHILDCARE             DisplayCategory = 86
	DisplayCategory_FOOD_DRINKS_GROCERIES        DisplayCategory = 87
	DisplayCategory_GIFTS_CHARITY                DisplayCategory = 88
	DisplayCategory_HOUSING_UTILITY              DisplayCategory = 89
	DisplayCategory_PERSONAL_CARE_HEALTH         DisplayCategory = 90
	DisplayCategory_REFUNDS_CASHBACKS            DisplayCategory = 91
	DisplayCategory_SELF_CREDIT                  DisplayCategory = 92
	DisplayCategory_SELF_DEBIT                   DisplayCategory = 93
	DisplayCategory_SUBSCRIPTIONS                DisplayCategory = 94
	DisplayCategory_ELECTRONICS_APPLIANCES       DisplayCategory = 95
	DisplayCategory_TRAVEL_TRANSPORTATION        DisplayCategory = 96
	DisplayCategory_WALLET_PAYMENT_GATEWAY       DisplayCategory = 97
	DisplayCategory_FASHION                      DisplayCategory = 98
	DisplayCategory_STOCKS_MF                    DisplayCategory = 99
	DisplayCategory_REFUND_REWARD                DisplayCategory = 100
	DisplayCategory_FAMILY_CARE                  DisplayCategory = 101
	DisplayCategory_CASH                         DisplayCategory = 102
	DisplayCategory_GIFT                         DisplayCategory = 103
	DisplayCategory_TAX                          DisplayCategory = 104
	DisplayCategory_GROCERY                      DisplayCategory = 105
	DisplayCategory_DINING                       DisplayCategory = 106
	DisplayCategory_BOOKS                        DisplayCategory = 107
	DisplayCategory_WALLET_PAYMENT               DisplayCategory = 108
	DisplayCategory_TRAVEL_COMMUTE               DisplayCategory = 109
	DisplayCategory_MONEY_TRANSFER               DisplayCategory = 110
	DisplayCategory_DIVIDEND                     DisplayCategory = 111
	DisplayCategory_INTEREST                     DisplayCategory = 112
	DisplayCategory_UNKNOWN                      DisplayCategory = 113
	DisplayCategory_GADGET                       DisplayCategory = 114
	DisplayCategory_BORROWED                     DisplayCategory = 115
	DisplayCategory_INVESTMENT_SALE              DisplayCategory = 116
	DisplayCategory_SELF_TRANSFER_CREDIT         DisplayCategory = 117
	DisplayCategory_SELF_TRANSFER_DEBIT          DisplayCategory = 118
	DisplayCategory_MONEY_TRANSFER_CREDIT        DisplayCategory = 119
	DisplayCategory_MONEY_TRANSFER_DEBIT         DisplayCategory = 120
	DisplayCategory_GROCERIES                    DisplayCategory = 121
	DisplayCategory_FOOD_DRINKS                  DisplayCategory = 122
	DisplayCategory_TRAVEL_VACATION              DisplayCategory = 123
	DisplayCategory_INVESTMENT_WITHDRAWAL        DisplayCategory = 124
	DisplayCategory_HOUSING_BILLS                DisplayCategory = 125
	DisplayCategory_SETTLEMENT                   DisplayCategory = 127
	DisplayCategory_FAMILY_TRANSFER_CREDIT       DisplayCategory = 128
	DisplayCategory_FAMILY_TRANSFER_DEBIT        DisplayCategory = 129
	DisplayCategory_FEES_CHARGES                 DisplayCategory = 130
)

// Enum value maps for DisplayCategory.
var (
	DisplayCategory_name = map[int32]string{
		0:   "DISPLAY_CATEGORY_UNSPECIFIED",
		1:   "SALARY",
		2:   "BONUS",
		3:   "INCOME",
		4:   "REDEMPTION",
		5:   "INTEREST_DIVIDENDS",
		6:   "REFUNDS",
		7:   "CASHBACKS",
		9:   "SELF_TRANSFER",
		10:  "DEPOSITS",
		11:  "STOCKS_MUTUAL_FUNDS",
		12:  "SECURITIES",
		13:  "INVESTMENTS",
		14:  "CRYPTO",
		15:  "EMI_PAYMENT",
		16:  "LOAN_PREPAYMENT",
		18:  "CREDIT_CARD",
		19:  "CREDIT_REPAYMENT",
		20:  "INSURANCE",
		21:  "HOUSE_DEPOSIT",
		22:  "UTILITY_BILLS",
		23:  "MOBILE_DATA_RECHARGE",
		25:  "MAINTENANCE_REPAIR",
		26:  "FURNITURE_APPLIANCES",
		27:  "HOUSING",
		28:  "CHILDCARE",
		29:  "DOMESTIC_HELP",
		30:  "EDUCATION",
		31:  "PETS",
		32:  "FAMILY",
		33:  "ONLINE_COURSES",
		34:  "EATING_OUT",
		35:  "ENTERTAINMENT",
		36:  "BOOKS_PUBLICATIONS",
		37:  "SPORTS_GAMES",
		38:  "ONLINE_GAMING",
		41:  "MOVIES",
		42:  "CONCERTS",
		43:  "MUSIC",
		44:  "ENTERTAINMENT_SUBSCRIPTIONS",
		46:  "CLOTHING_ACCESSORIES",
		47:  "GADGETS",
		48:  "APPLIANCES",
		49:  "ONLINE_SHOPPING",
		51:  "SHOPPING",
		52:  "JEWELLERY",
		55:  "GROCERIES_ESSENTIALS",
		56:  "PERSONAL_CARE",
		57:  "HEALTH_WELNESS",
		59:  "FLIGHTS",
		60:  "TRAINS",
		61:  "ROAD_TRAVEL",
		62:  "COMMUTE",
		64:  "FUEL",
		66:  "VEHICLE_SERVICE",
		67:  "SHIPPING_LOGISTICS",
		68:  "TRANSPORTATION",
		69:  "MISCELLANEOUS",
		70:  "BUSINESS_SPENDS",
		71:  "DONATIONS",
		72:  "SOFTWARE",
		73:  "BANK_FEES",
		74:  "FINANCIAL_SERVICES",
		75:  "GIFTS",
		76:  "TAXES",
		77:  "TRAVEL",
		78:  "CASH_WITHDRAWALS",
		79:  "WALLET_RECHARGE",
		81:  "SPENDS",
		82:  "RENT",
		84:  "EVENTS_ACTIVITIES",
		85:  "CREDIT_CARD_LOAN",
		86:  "FAMILY_CHILDCARE",
		87:  "FOOD_DRINKS_GROCERIES",
		88:  "GIFTS_CHARITY",
		89:  "HOUSING_UTILITY",
		90:  "PERSONAL_CARE_HEALTH",
		91:  "REFUNDS_CASHBACKS",
		92:  "SELF_CREDIT",
		93:  "SELF_DEBIT",
		94:  "SUBSCRIPTIONS",
		95:  "ELECTRONICS_APPLIANCES",
		96:  "TRAVEL_TRANSPORTATION",
		97:  "WALLET_PAYMENT_GATEWAY",
		98:  "FASHION",
		99:  "STOCKS_MF",
		100: "REFUND_REWARD",
		101: "FAMILY_CARE",
		102: "CASH",
		103: "GIFT",
		104: "TAX",
		105: "GROCERY",
		106: "DINING",
		107: "BOOKS",
		108: "WALLET_PAYMENT",
		109: "TRAVEL_COMMUTE",
		110: "MONEY_TRANSFER",
		111: "DIVIDEND",
		112: "INTEREST",
		113: "UNKNOWN",
		114: "GADGET",
		115: "BORROWED",
		116: "INVESTMENT_SALE",
		117: "SELF_TRANSFER_CREDIT",
		118: "SELF_TRANSFER_DEBIT",
		119: "MONEY_TRANSFER_CREDIT",
		120: "MONEY_TRANSFER_DEBIT",
		121: "GROCERIES",
		122: "FOOD_DRINKS",
		123: "TRAVEL_VACATION",
		124: "INVESTMENT_WITHDRAWAL",
		125: "HOUSING_BILLS",
		127: "SETTLEMENT",
		128: "FAMILY_TRANSFER_CREDIT",
		129: "FAMILY_TRANSFER_DEBIT",
		130: "FEES_CHARGES",
	}
	DisplayCategory_value = map[string]int32{
		"DISPLAY_CATEGORY_UNSPECIFIED": 0,
		"SALARY":                       1,
		"BONUS":                        2,
		"INCOME":                       3,
		"REDEMPTION":                   4,
		"INTEREST_DIVIDENDS":           5,
		"REFUNDS":                      6,
		"CASHBACKS":                    7,
		"SELF_TRANSFER":                9,
		"DEPOSITS":                     10,
		"STOCKS_MUTUAL_FUNDS":          11,
		"SECURITIES":                   12,
		"INVESTMENTS":                  13,
		"CRYPTO":                       14,
		"EMI_PAYMENT":                  15,
		"LOAN_PREPAYMENT":              16,
		"CREDIT_CARD":                  18,
		"CREDIT_REPAYMENT":             19,
		"INSURANCE":                    20,
		"HOUSE_DEPOSIT":                21,
		"UTILITY_BILLS":                22,
		"MOBILE_DATA_RECHARGE":         23,
		"MAINTENANCE_REPAIR":           25,
		"FURNITURE_APPLIANCES":         26,
		"HOUSING":                      27,
		"CHILDCARE":                    28,
		"DOMESTIC_HELP":                29,
		"EDUCATION":                    30,
		"PETS":                         31,
		"FAMILY":                       32,
		"ONLINE_COURSES":               33,
		"EATING_OUT":                   34,
		"ENTERTAINMENT":                35,
		"BOOKS_PUBLICATIONS":           36,
		"SPORTS_GAMES":                 37,
		"ONLINE_GAMING":                38,
		"MOVIES":                       41,
		"CONCERTS":                     42,
		"MUSIC":                        43,
		"ENTERTAINMENT_SUBSCRIPTIONS":  44,
		"CLOTHING_ACCESSORIES":         46,
		"GADGETS":                      47,
		"APPLIANCES":                   48,
		"ONLINE_SHOPPING":              49,
		"SHOPPING":                     51,
		"JEWELLERY":                    52,
		"GROCERIES_ESSENTIALS":         55,
		"PERSONAL_CARE":                56,
		"HEALTH_WELNESS":               57,
		"FLIGHTS":                      59,
		"TRAINS":                       60,
		"ROAD_TRAVEL":                  61,
		"COMMUTE":                      62,
		"FUEL":                         64,
		"VEHICLE_SERVICE":              66,
		"SHIPPING_LOGISTICS":           67,
		"TRANSPORTATION":               68,
		"MISCELLANEOUS":                69,
		"BUSINESS_SPENDS":              70,
		"DONATIONS":                    71,
		"SOFTWARE":                     72,
		"BANK_FEES":                    73,
		"FINANCIAL_SERVICES":           74,
		"GIFTS":                        75,
		"TAXES":                        76,
		"TRAVEL":                       77,
		"CASH_WITHDRAWALS":             78,
		"WALLET_RECHARGE":              79,
		"SPENDS":                       81,
		"RENT":                         82,
		"EVENTS_ACTIVITIES":            84,
		"CREDIT_CARD_LOAN":             85,
		"FAMILY_CHILDCARE":             86,
		"FOOD_DRINKS_GROCERIES":        87,
		"GIFTS_CHARITY":                88,
		"HOUSING_UTILITY":              89,
		"PERSONAL_CARE_HEALTH":         90,
		"REFUNDS_CASHBACKS":            91,
		"SELF_CREDIT":                  92,
		"SELF_DEBIT":                   93,
		"SUBSCRIPTIONS":                94,
		"ELECTRONICS_APPLIANCES":       95,
		"TRAVEL_TRANSPORTATION":        96,
		"WALLET_PAYMENT_GATEWAY":       97,
		"FASHION":                      98,
		"STOCKS_MF":                    99,
		"REFUND_REWARD":                100,
		"FAMILY_CARE":                  101,
		"CASH":                         102,
		"GIFT":                         103,
		"TAX":                          104,
		"GROCERY":                      105,
		"DINING":                       106,
		"BOOKS":                        107,
		"WALLET_PAYMENT":               108,
		"TRAVEL_COMMUTE":               109,
		"MONEY_TRANSFER":               110,
		"DIVIDEND":                     111,
		"INTEREST":                     112,
		"UNKNOWN":                      113,
		"GADGET":                       114,
		"BORROWED":                     115,
		"INVESTMENT_SALE":              116,
		"SELF_TRANSFER_CREDIT":         117,
		"SELF_TRANSFER_DEBIT":          118,
		"MONEY_TRANSFER_CREDIT":        119,
		"MONEY_TRANSFER_DEBIT":         120,
		"GROCERIES":                    121,
		"FOOD_DRINKS":                  122,
		"TRAVEL_VACATION":              123,
		"INVESTMENT_WITHDRAWAL":        124,
		"HOUSING_BILLS":                125,
		"SETTLEMENT":                   127,
		"FAMILY_TRANSFER_CREDIT":       128,
		"FAMILY_TRANSFER_DEBIT":        129,
		"FEES_CHARGES":                 130,
	}
)

func (x DisplayCategory) Enum() *DisplayCategory {
	p := new(DisplayCategory)
	*p = x
	return p
}

func (x DisplayCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisplayCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[4].Descriptor()
}

func (DisplayCategory) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[4]
}

func (x DisplayCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisplayCategory.Descriptor instead.
func (DisplayCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{4}
}

// enum value of first level of ontology
type L0 int32

const (
	L0_L0_UNSPECIFIED     L0 = 0
	L0_L0_INCOME          L0 = 1
	L0_L0_INVESTMENTS     L0 = 2
	L0_L0_SPEND           L0 = 3
	L0_L0_UNKNOWN         L0 = 4
	L0_L0_LOAN            L0 = 5
	L0_L0_DEBT_SETTLEMENT L0 = 7
)

// Enum value maps for L0.
var (
	L0_name = map[int32]string{
		0: "L0_UNSPECIFIED",
		1: "L0_INCOME",
		2: "L0_INVESTMENTS",
		3: "L0_SPEND",
		4: "L0_UNKNOWN",
		5: "L0_LOAN",
		7: "L0_DEBT_SETTLEMENT",
	}
	L0_value = map[string]int32{
		"L0_UNSPECIFIED":     0,
		"L0_INCOME":          1,
		"L0_INVESTMENTS":     2,
		"L0_SPEND":           3,
		"L0_UNKNOWN":         4,
		"L0_LOAN":            5,
		"L0_DEBT_SETTLEMENT": 7,
	}
)

func (x L0) Enum() *L0 {
	p := new(L0)
	*p = x
	return p
}

func (x L0) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (L0) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[5].Descriptor()
}

func (L0) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[5]
}

func (x L0) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use L0.Descriptor instead.
func (L0) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{5}
}

// enum value of second level of ontology
type L1 int32

const (
	L1_L1_UNSPECIFIED                  L1 = 0
	L1_L1_INCOME                       L1 = 1
	L1_L1_INVESTMENTS                  L1 = 2
	L1_L1_CREDIT_REPAYMENT             L1 = 3
	L1_L1_INSURANCE                    L1 = 4
	L1_L1_HOUSING                      L1 = 5
	L1_L1_HOME_AND_FAMILY              L1 = 6
	L1_L1_ENTERTAINMENT                L1 = 7
	L1_L1_SHOPPING                     L1 = 8
	L1_L1_FOOD_DRINKS_AND_GROCERIES    L1 = 9
	L1_L1_PERSONAL_CARE_HEALTH_MEDICAL L1 = 10
	L1_L1_TRANSPORT                    L1 = 11
	L1_L1_MISC                         L1 = 12
	L1_L1_BANKING                      L1 = 13
	L1_L1_TAXES                        L1 = 14
	L1_L1_TRAVEL_TRANSPORTATION        L1 = 15
	L1_L1_LOAN_DISBURSEMENT            L1 = 16
	L1_L1_UNKNOWN                      L1 = 17
	L1_L1_LOAN                         L1 = 18
	L1_L1_CREDIT_CARD                  L1 = 19
)

// Enum value maps for L1.
var (
	L1_name = map[int32]string{
		0:  "L1_UNSPECIFIED",
		1:  "L1_INCOME",
		2:  "L1_INVESTMENTS",
		3:  "L1_CREDIT_REPAYMENT",
		4:  "L1_INSURANCE",
		5:  "L1_HOUSING",
		6:  "L1_HOME_AND_FAMILY",
		7:  "L1_ENTERTAINMENT",
		8:  "L1_SHOPPING",
		9:  "L1_FOOD_DRINKS_AND_GROCERIES",
		10: "L1_PERSONAL_CARE_HEALTH_MEDICAL",
		11: "L1_TRANSPORT",
		12: "L1_MISC",
		13: "L1_BANKING",
		14: "L1_TAXES",
		15: "L1_TRAVEL_TRANSPORTATION",
		16: "L1_LOAN_DISBURSEMENT",
		17: "L1_UNKNOWN",
		18: "L1_LOAN",
		19: "L1_CREDIT_CARD",
	}
	L1_value = map[string]int32{
		"L1_UNSPECIFIED":                  0,
		"L1_INCOME":                       1,
		"L1_INVESTMENTS":                  2,
		"L1_CREDIT_REPAYMENT":             3,
		"L1_INSURANCE":                    4,
		"L1_HOUSING":                      5,
		"L1_HOME_AND_FAMILY":              6,
		"L1_ENTERTAINMENT":                7,
		"L1_SHOPPING":                     8,
		"L1_FOOD_DRINKS_AND_GROCERIES":    9,
		"L1_PERSONAL_CARE_HEALTH_MEDICAL": 10,
		"L1_TRANSPORT":                    11,
		"L1_MISC":                         12,
		"L1_BANKING":                      13,
		"L1_TAXES":                        14,
		"L1_TRAVEL_TRANSPORTATION":        15,
		"L1_LOAN_DISBURSEMENT":            16,
		"L1_UNKNOWN":                      17,
		"L1_LOAN":                         18,
		"L1_CREDIT_CARD":                  19,
	}
)

func (x L1) Enum() *L1 {
	p := new(L1)
	*p = x
	return p
}

func (x L1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (L1) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[6].Descriptor()
}

func (L1) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[6]
}

func (x L1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use L1.Descriptor instead.
func (L1) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{6}
}

// enum value of third level of ontology
type L2 int32

const (
	L2_L2_UNSPECIFIED                 L2 = 0
	L2_L2_INCOME_FROM_EMPLOYMENT      L2 = 1
	L2_L2_INCOME_FROM_RENT            L2 = 2
	L2_L2_REFUNDS                     L2 = 3
	L2_L2_FROM_SELF                   L2 = 5
	L2_L2_RECURRING_DEPOSITS          L2 = 7
	L2_L2_SMART_DEPOSITS              L2 = 8
	L2_L2_SIDS                        L2 = 9
	L2_L2_MUTUAL_FUNDS_AMCS           L2 = 10
	L2_L2_TRANSFER_TO_TRADING_AC      L2 = 11
	L2_L2_MF_STOCKS_BONDS_COMMODITIES L2 = 12
	L2_L2_PPF                         L2 = 13
	L2_L2_PENSION_POLICY              L2 = 14
	L2_L2_OTHER                       L2 = 15
	L2_L2_AUTO_LOAN                   L2 = 16
	L2_L2_EDUCATION_LOAN              L2 = 17
	L2_L2_HOME_LOAN                   L2 = 18
	L2_L2_PERSONAL_LOAN               L2 = 19
	L2_L2_OTHER_LOAN                  L2 = 20
	L2_L2_CREDIT_CARD_PAYMENT         L2 = 21
	L2_L2_AUTO                        L2 = 22
	L2_L2_GENERAL                     L2 = 23
	L2_L2_LIFE                        L2 = 24
	L2_L2_MEDICAL                     L2 = 25
	L2_L2_TRAVEL                      L2 = 26
	L2_L2_PROPERTY                    L2 = 27
	L2_L2_MONTHLY_RENT                L2 = 28
	L2_L2_RENTAL_DEPOSIT              L2 = 29
	L2_L2_UTILITIES                   L2 = 30
	L2_L2_HOME_IMPROVEMENT            L2 = 31
	L2_L2_MONTHLY_MAINTENANCE_CHARGES L2 = 32
	L2_L2_APPLIANCES                  L2 = 33
	L2_L2_BABIES_CHILDCARE            L2 = 35
	L2_L2_DOMESTIC_HELP               L2 = 36
	L2_L2_EDUCATION                   L2 = 37
	L2_L2_EXTRA_CURRICULAR            L2 = 38
	L2_L2_PETS                        L2 = 39
	L2_L2_POCKET_MONEY                L2 = 40
	L2_L2_SELF_IMPROVEMENT            L2 = 41
	L2_L2_ALCOHOL_BARS                L2 = 43
	L2_L2_ARTS                        L2 = 44
	L2_L2_BOOKS                       L2 = 45
	L2_L2_GAMES                       L2 = 46
	L2_L2_GAMING_APPS                 L2 = 47
	L2_L2_GAMING_SOFTWARE             L2 = 48
	L2_L2_HOBBIES                     L2 = 49
	L2_L2_SPORTING_GOODS              L2 = 50
	L2_L2_SPORTS                      L2 = 51
	L2_L2_MOVIES                      L2 = 52
	L2_L2_CONCERTS                    L2 = 53
	L2_L2_MUSIC                       L2 = 54
	L2_L2_STREAMING_SERVICES          L2 = 55
	L2_L2_TV_CABLE                    L2 = 56
	L2_L2_CLOTHING_FOOTWEAR           L2 = 58
	L2_L2_WEARABLE_ACCESSORIES        L2 = 59
	L2_L2_ELECTRONIC_GADGETS          L2 = 60
	L2_L2_ELECTRICAL_APPLIANCES       L2 = 61
	L2_L2_E_COMMERCE                  L2 = 62
	L2_L2_FURNITURE_FURNISHINGS       L2 = 63
	L2_L2_DECORATION                  L2 = 64
	L2_L2_JEWELLERY                   L2 = 65
	L2_L2_CAFE_COFFEE_SHOP            L2 = 67
	L2_L2_RESTAURANTS                 L2 = 68
	L2_L2_FOOD_DELIVERY               L2 = 69
	L2_L2_GROCERIES                   L2 = 70
	L2_L2_AESTHETIC_TREATMENTS        L2 = 72
	L2_L2_BARBER_HAIR_STYLIST_SALON   L2 = 73
	L2_L2_SPA_MASSAGE                 L2 = 74
	L2_L2_COSMETICS_TOILETRIES        L2 = 75
	L2_L2_GYM                         L2 = 76
	L2_L2_MENTAL_HEALTH_WELLNESS      L2 = 77
	L2_L2_DENTIST                     L2 = 78
	L2_L2_DOCTOR                      L2 = 79
	L2_L2_EYE_CARE                    L2 = 80
	L2_L2_HOSPITAL_EXPENSES           L2 = 81
	L2_L2_LAUNDRY_DRY_CLEANING        L2 = 82
	L2_L2_PHARMACY                    L2 = 83
	L2_L2_AIR_TRAVEL                  L2 = 85
	L2_L2_TRAIN_TRAVEL                L2 = 86
	L2_L2_BUS                         L2 = 87
	L2_L2_RENTAL_CAR                  L2 = 88
	L2_L2_CAB_RIDE                    L2 = 89
	L2_L2_RIDE_HAILING                L2 = 90
	L2_L2_TOLLS                       L2 = 91
	L2_L2_FUEL                        L2 = 92
	L2_L2_PARKING                     L2 = 93
	L2_L2_SERVICE_AUTO_PARTS          L2 = 94
	L2_L2_LOGISTICS                   L2 = 95
	L2_L2_MULTI_SPECIALITY            L2 = 97
	L2_L2_BUSINESS_EXPENSES           L2 = 98
	L2_L2_CHARITY                     L2 = 99
	L2_L2_ELECTRONICS_SOFTWARE        L2 = 100
	L2_L2_FEES_INTEREST               L2 = 101
	L2_L2_GIFTING_SERVICES_PRODUCTS   L2 = 102
	L2_L2_TAXES                       L2 = 103
	L2_L2_HOTEL                       L2 = 104
	L2_L2_TRAVEL_AGENCY               L2 = 105
	L2_L2_CASH_WITHDRAWALS            L2 = 106
	L2_L2_WALLET                      L2 = 108
	L2_L2_CRYPTO                      L2 = 109
	L2_L2_INCOME_FROM_INVESTMENTS     L2 = 110
	L2_L2_FIXED_DEPOSITS              L2 = 111
	L2_L2_BOOKS_PUBLICATIONS          L2 = 112
	L2_L2_EVENTS_ACTIVITIES           L2 = 113
	L2_L2_CREDIT_CARD                 L2 = 114
	L2_L2_FIXED_INCOME_INVESTMENTS    L2 = 115
	L2_L2_SPORTS_GAMES                L2 = 116
	L2_L2_FAMILY_CHILDCARE            L2 = 117
	L2_L2_FOOD_DRINKS                 L2 = 118
	L2_L2_GIFTS_CHARITY               L2 = 119
	L2_L2_GROCERIES_ESSENTIALS        L2 = 120
	L2_L2_UTILITY                     L2 = 121
	L2_L2_REFUNDS_CASHBACKS           L2 = 122
	L2_L2_TO_SELF                     L2 = 123
	L2_L2_SUBSCRIPTIONS               L2 = 124
	L2_L2_SOFTWARE                    L2 = 125
	L2_L2_CLOTHING_ACCESSORIES        L2 = 126
	L2_L2_ELECTRONICS_APPLIANCES      L2 = 127
	L2_L2_STOCKS_MUTUAL_FUNDS         L2 = 128
	L2_L2_TAX_PREPARATION_SERVICE     L2 = 129
	L2_L2_INCOME_TAX_PAYMENT          L2 = 130
	L2_L2_TDS_AT_SOURCE               L2 = 131
	L2_L2_FROM_OTHERS                 L2 = 132
	L2_L2_TO_OTHERS                   L2 = 133
	L2_L2_TRAVEL_VACATION             L2 = 134
	L2_L2_COMMUTE                     L2 = 135
	L2_L2_LOAN_DISBURSEMENT           L2 = 136
	L2_L2_BILLS                       L2 = 137
)

// Enum value maps for L2.
var (
	L2_name = map[int32]string{
		0:   "L2_UNSPECIFIED",
		1:   "L2_INCOME_FROM_EMPLOYMENT",
		2:   "L2_INCOME_FROM_RENT",
		3:   "L2_REFUNDS",
		5:   "L2_FROM_SELF",
		7:   "L2_RECURRING_DEPOSITS",
		8:   "L2_SMART_DEPOSITS",
		9:   "L2_SIDS",
		10:  "L2_MUTUAL_FUNDS_AMCS",
		11:  "L2_TRANSFER_TO_TRADING_AC",
		12:  "L2_MF_STOCKS_BONDS_COMMODITIES",
		13:  "L2_PPF",
		14:  "L2_PENSION_POLICY",
		15:  "L2_OTHER",
		16:  "L2_AUTO_LOAN",
		17:  "L2_EDUCATION_LOAN",
		18:  "L2_HOME_LOAN",
		19:  "L2_PERSONAL_LOAN",
		20:  "L2_OTHER_LOAN",
		21:  "L2_CREDIT_CARD_PAYMENT",
		22:  "L2_AUTO",
		23:  "L2_GENERAL",
		24:  "L2_LIFE",
		25:  "L2_MEDICAL",
		26:  "L2_TRAVEL",
		27:  "L2_PROPERTY",
		28:  "L2_MONTHLY_RENT",
		29:  "L2_RENTAL_DEPOSIT",
		30:  "L2_UTILITIES",
		31:  "L2_HOME_IMPROVEMENT",
		32:  "L2_MONTHLY_MAINTENANCE_CHARGES",
		33:  "L2_APPLIANCES",
		35:  "L2_BABIES_CHILDCARE",
		36:  "L2_DOMESTIC_HELP",
		37:  "L2_EDUCATION",
		38:  "L2_EXTRA_CURRICULAR",
		39:  "L2_PETS",
		40:  "L2_POCKET_MONEY",
		41:  "L2_SELF_IMPROVEMENT",
		43:  "L2_ALCOHOL_BARS",
		44:  "L2_ARTS",
		45:  "L2_BOOKS",
		46:  "L2_GAMES",
		47:  "L2_GAMING_APPS",
		48:  "L2_GAMING_SOFTWARE",
		49:  "L2_HOBBIES",
		50:  "L2_SPORTING_GOODS",
		51:  "L2_SPORTS",
		52:  "L2_MOVIES",
		53:  "L2_CONCERTS",
		54:  "L2_MUSIC",
		55:  "L2_STREAMING_SERVICES",
		56:  "L2_TV_CABLE",
		58:  "L2_CLOTHING_FOOTWEAR",
		59:  "L2_WEARABLE_ACCESSORIES",
		60:  "L2_ELECTRONIC_GADGETS",
		61:  "L2_ELECTRICAL_APPLIANCES",
		62:  "L2_E_COMMERCE",
		63:  "L2_FURNITURE_FURNISHINGS",
		64:  "L2_DECORATION",
		65:  "L2_JEWELLERY",
		67:  "L2_CAFE_COFFEE_SHOP",
		68:  "L2_RESTAURANTS",
		69:  "L2_FOOD_DELIVERY",
		70:  "L2_GROCERIES",
		72:  "L2_AESTHETIC_TREATMENTS",
		73:  "L2_BARBER_HAIR_STYLIST_SALON",
		74:  "L2_SPA_MASSAGE",
		75:  "L2_COSMETICS_TOILETRIES",
		76:  "L2_GYM",
		77:  "L2_MENTAL_HEALTH_WELLNESS",
		78:  "L2_DENTIST",
		79:  "L2_DOCTOR",
		80:  "L2_EYE_CARE",
		81:  "L2_HOSPITAL_EXPENSES",
		82:  "L2_LAUNDRY_DRY_CLEANING",
		83:  "L2_PHARMACY",
		85:  "L2_AIR_TRAVEL",
		86:  "L2_TRAIN_TRAVEL",
		87:  "L2_BUS",
		88:  "L2_RENTAL_CAR",
		89:  "L2_CAB_RIDE",
		90:  "L2_RIDE_HAILING",
		91:  "L2_TOLLS",
		92:  "L2_FUEL",
		93:  "L2_PARKING",
		94:  "L2_SERVICE_AUTO_PARTS",
		95:  "L2_LOGISTICS",
		97:  "L2_MULTI_SPECIALITY",
		98:  "L2_BUSINESS_EXPENSES",
		99:  "L2_CHARITY",
		100: "L2_ELECTRONICS_SOFTWARE",
		101: "L2_FEES_INTEREST",
		102: "L2_GIFTING_SERVICES_PRODUCTS",
		103: "L2_TAXES",
		104: "L2_HOTEL",
		105: "L2_TRAVEL_AGENCY",
		106: "L2_CASH_WITHDRAWALS",
		108: "L2_WALLET",
		109: "L2_CRYPTO",
		110: "L2_INCOME_FROM_INVESTMENTS",
		111: "L2_FIXED_DEPOSITS",
		112: "L2_BOOKS_PUBLICATIONS",
		113: "L2_EVENTS_ACTIVITIES",
		114: "L2_CREDIT_CARD",
		115: "L2_FIXED_INCOME_INVESTMENTS",
		116: "L2_SPORTS_GAMES",
		117: "L2_FAMILY_CHILDCARE",
		118: "L2_FOOD_DRINKS",
		119: "L2_GIFTS_CHARITY",
		120: "L2_GROCERIES_ESSENTIALS",
		121: "L2_UTILITY",
		122: "L2_REFUNDS_CASHBACKS",
		123: "L2_TO_SELF",
		124: "L2_SUBSCRIPTIONS",
		125: "L2_SOFTWARE",
		126: "L2_CLOTHING_ACCESSORIES",
		127: "L2_ELECTRONICS_APPLIANCES",
		128: "L2_STOCKS_MUTUAL_FUNDS",
		129: "L2_TAX_PREPARATION_SERVICE",
		130: "L2_INCOME_TAX_PAYMENT",
		131: "L2_TDS_AT_SOURCE",
		132: "L2_FROM_OTHERS",
		133: "L2_TO_OTHERS",
		134: "L2_TRAVEL_VACATION",
		135: "L2_COMMUTE",
		136: "L2_LOAN_DISBURSEMENT",
		137: "L2_BILLS",
	}
	L2_value = map[string]int32{
		"L2_UNSPECIFIED":                 0,
		"L2_INCOME_FROM_EMPLOYMENT":      1,
		"L2_INCOME_FROM_RENT":            2,
		"L2_REFUNDS":                     3,
		"L2_FROM_SELF":                   5,
		"L2_RECURRING_DEPOSITS":          7,
		"L2_SMART_DEPOSITS":              8,
		"L2_SIDS":                        9,
		"L2_MUTUAL_FUNDS_AMCS":           10,
		"L2_TRANSFER_TO_TRADING_AC":      11,
		"L2_MF_STOCKS_BONDS_COMMODITIES": 12,
		"L2_PPF":                         13,
		"L2_PENSION_POLICY":              14,
		"L2_OTHER":                       15,
		"L2_AUTO_LOAN":                   16,
		"L2_EDUCATION_LOAN":              17,
		"L2_HOME_LOAN":                   18,
		"L2_PERSONAL_LOAN":               19,
		"L2_OTHER_LOAN":                  20,
		"L2_CREDIT_CARD_PAYMENT":         21,
		"L2_AUTO":                        22,
		"L2_GENERAL":                     23,
		"L2_LIFE":                        24,
		"L2_MEDICAL":                     25,
		"L2_TRAVEL":                      26,
		"L2_PROPERTY":                    27,
		"L2_MONTHLY_RENT":                28,
		"L2_RENTAL_DEPOSIT":              29,
		"L2_UTILITIES":                   30,
		"L2_HOME_IMPROVEMENT":            31,
		"L2_MONTHLY_MAINTENANCE_CHARGES": 32,
		"L2_APPLIANCES":                  33,
		"L2_BABIES_CHILDCARE":            35,
		"L2_DOMESTIC_HELP":               36,
		"L2_EDUCATION":                   37,
		"L2_EXTRA_CURRICULAR":            38,
		"L2_PETS":                        39,
		"L2_POCKET_MONEY":                40,
		"L2_SELF_IMPROVEMENT":            41,
		"L2_ALCOHOL_BARS":                43,
		"L2_ARTS":                        44,
		"L2_BOOKS":                       45,
		"L2_GAMES":                       46,
		"L2_GAMING_APPS":                 47,
		"L2_GAMING_SOFTWARE":             48,
		"L2_HOBBIES":                     49,
		"L2_SPORTING_GOODS":              50,
		"L2_SPORTS":                      51,
		"L2_MOVIES":                      52,
		"L2_CONCERTS":                    53,
		"L2_MUSIC":                       54,
		"L2_STREAMING_SERVICES":          55,
		"L2_TV_CABLE":                    56,
		"L2_CLOTHING_FOOTWEAR":           58,
		"L2_WEARABLE_ACCESSORIES":        59,
		"L2_ELECTRONIC_GADGETS":          60,
		"L2_ELECTRICAL_APPLIANCES":       61,
		"L2_E_COMMERCE":                  62,
		"L2_FURNITURE_FURNISHINGS":       63,
		"L2_DECORATION":                  64,
		"L2_JEWELLERY":                   65,
		"L2_CAFE_COFFEE_SHOP":            67,
		"L2_RESTAURANTS":                 68,
		"L2_FOOD_DELIVERY":               69,
		"L2_GROCERIES":                   70,
		"L2_AESTHETIC_TREATMENTS":        72,
		"L2_BARBER_HAIR_STYLIST_SALON":   73,
		"L2_SPA_MASSAGE":                 74,
		"L2_COSMETICS_TOILETRIES":        75,
		"L2_GYM":                         76,
		"L2_MENTAL_HEALTH_WELLNESS":      77,
		"L2_DENTIST":                     78,
		"L2_DOCTOR":                      79,
		"L2_EYE_CARE":                    80,
		"L2_HOSPITAL_EXPENSES":           81,
		"L2_LAUNDRY_DRY_CLEANING":        82,
		"L2_PHARMACY":                    83,
		"L2_AIR_TRAVEL":                  85,
		"L2_TRAIN_TRAVEL":                86,
		"L2_BUS":                         87,
		"L2_RENTAL_CAR":                  88,
		"L2_CAB_RIDE":                    89,
		"L2_RIDE_HAILING":                90,
		"L2_TOLLS":                       91,
		"L2_FUEL":                        92,
		"L2_PARKING":                     93,
		"L2_SERVICE_AUTO_PARTS":          94,
		"L2_LOGISTICS":                   95,
		"L2_MULTI_SPECIALITY":            97,
		"L2_BUSINESS_EXPENSES":           98,
		"L2_CHARITY":                     99,
		"L2_ELECTRONICS_SOFTWARE":        100,
		"L2_FEES_INTEREST":               101,
		"L2_GIFTING_SERVICES_PRODUCTS":   102,
		"L2_TAXES":                       103,
		"L2_HOTEL":                       104,
		"L2_TRAVEL_AGENCY":               105,
		"L2_CASH_WITHDRAWALS":            106,
		"L2_WALLET":                      108,
		"L2_CRYPTO":                      109,
		"L2_INCOME_FROM_INVESTMENTS":     110,
		"L2_FIXED_DEPOSITS":              111,
		"L2_BOOKS_PUBLICATIONS":          112,
		"L2_EVENTS_ACTIVITIES":           113,
		"L2_CREDIT_CARD":                 114,
		"L2_FIXED_INCOME_INVESTMENTS":    115,
		"L2_SPORTS_GAMES":                116,
		"L2_FAMILY_CHILDCARE":            117,
		"L2_FOOD_DRINKS":                 118,
		"L2_GIFTS_CHARITY":               119,
		"L2_GROCERIES_ESSENTIALS":        120,
		"L2_UTILITY":                     121,
		"L2_REFUNDS_CASHBACKS":           122,
		"L2_TO_SELF":                     123,
		"L2_SUBSCRIPTIONS":               124,
		"L2_SOFTWARE":                    125,
		"L2_CLOTHING_ACCESSORIES":        126,
		"L2_ELECTRONICS_APPLIANCES":      127,
		"L2_STOCKS_MUTUAL_FUNDS":         128,
		"L2_TAX_PREPARATION_SERVICE":     129,
		"L2_INCOME_TAX_PAYMENT":          130,
		"L2_TDS_AT_SOURCE":               131,
		"L2_FROM_OTHERS":                 132,
		"L2_TO_OTHERS":                   133,
		"L2_TRAVEL_VACATION":             134,
		"L2_COMMUTE":                     135,
		"L2_LOAN_DISBURSEMENT":           136,
		"L2_BILLS":                       137,
	}
)

func (x L2) Enum() *L2 {
	p := new(L2)
	*p = x
	return p
}

func (x L2) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (L2) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[7].Descriptor()
}

func (L2) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[7]
}

func (x L2) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use L2.Descriptor instead.
func (L2) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{7}
}

// enum value of fourth level of ontology
type L3 int32

const (
	L3_L3_UNSPECIFIED                               L3 = 0
	L3_L3_INCOME_FROM_SALARY                        L3 = 1
	L3_L3_BONUS                                     L3 = 2
	L3_L3_FD_REDEMPTION_PRINCIPAL_AND_INTEREST      L3 = 4
	L3_L3_INTEREST_FROM_FDS                         L3 = 5
	L3_L3_INTEREST_FROM_SAVINGS_ACCOUNTS            L3 = 6
	L3_L3_SID_REDEMPTION_PRINCIPAL_AND_INTEREST     L3 = 7
	L3_L3_DIVIDENDS                                 L3 = 8
	L3_L3_LIQUIDATED_INVESTMENTS                    L3 = 9
	L3_L3_OTHER_INCOME                              L3 = 10
	L3_L3_PURCHASE_CANCELLATIONS                    L3 = 11
	L3_L3_PURCHASE_REFUNDS                          L3 = 12
	L3_L3_TAX_REFUNDS                               L3 = 13
	L3_L3_CASHBACKS                                 L3 = 14
	L3_L3_CREDIT_INTEREST_CAPITALISED               L3 = 15
	L3_L3_OTHER                                     L3 = 16
	L3_L3_MARKET_LINKED_MFS                         L3 = 17
	L3_L3_FIXED_INCOME_MFS                          L3 = 18
	L3_L3_AUTO_LOAN_EMI                             L3 = 19
	L3_L3_AUTO_LOAN_PREPAYMENT                      L3 = 20
	L3_L3_EDUCATION_LOAN_EMI                        L3 = 21
	L3_L3_EDUCATION_LOAN_PREPAYMENT                 L3 = 22
	L3_L3_HOME_LOAN_EMI                             L3 = 23
	L3_L3_HOME_LOAN_PREPAYMENT                      L3 = 24
	L3_L3_PERSONAL_LOAN_EMI                         L3 = 25
	L3_L3_PERSONAL_LOAN_PREPAYMENT                  L3 = 26
	L3_L3_OTHER_LOANS_EMI                           L3 = 27
	L3_L3_OTHER_LOANS_PREPAYMENT                    L3 = 28
	L3_L3_ELECTRICITY                               L3 = 29
	L3_L3_GAS_EG_LPG_                               L3 = 30
	L3_L3_WATER                                     L3 = 31
	L3_L3_INTERNET                                  L3 = 32
	L3_L3_PHONE                                     L3 = 33
	L3_L3_MASONRY                                   L3 = 35
	L3_L3_CARPENTRY                                 L3 = 36
	L3_L3_ELECTRICAL                                L3 = 37
	L3_L3_GARDENING                                 L3 = 38
	L3_L3_PLUMBING                                  L3 = 39
	L3_L3_REPAIRS                                   L3 = 40
	L3_L3_BABY_SUPPLIES                             L3 = 42
	L3_L3_CHILDCARE_FEES                            L3 = 43
	L3_L3_COOK                                      L3 = 44
	L3_L3_DRIVER                                    L3 = 45
	L3_L3_MAID                                      L3 = 46
	L3_L3_SCHOOL_SUPPLIES                           L3 = 47
	L3_L3_COLLEGE_FEES                              L3 = 48
	L3_L3_SCHOOL_FEES                               L3 = 49
	L3_L3_TUITION_FEES                              L3 = 50
	L3_L3_FOOD                                      L3 = 51
	L3_L3_VET                                       L3 = 52
	L3_L3_MISC                                      L3 = 53
	L3_L3_MOOCS                                     L3 = 54
	L3_L3_PAPERBACKS                                L3 = 55
	L3_L3_E_BOOKS                                   L3 = 56
	L3_L3_AUDIOBOOKS                                L3 = 57
	L3_L3_MAGAZINES_AND_NEWSPAPERS                  L3 = 58
	L3_L3_APP_DOWNLOAD                              L3 = 60
	L3_L3_IN_APP_PURCHASES                          L3 = 61
	L3_L3_ADVERTISING                               L3 = 62
	L3_L3_BUSINESS_SOFTWARE                         L3 = 63
	L3_L3_COMMISSIONS                               L3 = 64
	L3_L3_LEGAL                                     L3 = 65
	L3_L3_OFFICE_SUPPLIES                           L3 = 66
	L3_L3_PRINTING                                  L3 = 67
	L3_L3_SHIPPING                                  L3 = 68
	L3_L3_MOBILE_APPS                               L3 = 70
	L3_L3_ANNUAL_FEES                               L3 = 72
	L3_L3_BELOW_MAB_FEES                            L3 = 73
	L3_L3_CARD_FEES                                 L3 = 74
	L3_L3_CHEQUE_BOUNCE_FEES                        L3 = 75
	L3_L3_INTEREST_PAID                             L3 = 76
	L3_L3_LATE_FEES                                 L3 = 77
	L3_L3_OVERDRAFT_FEES                            L3 = 78
	L3_L3_TAX_PREPARATION_SERVICE                   L3 = 80
	L3_L3_INCOME_TAX_PAYMENT                        L3 = 81
	L3_L3_TDS_AT_SOURCE                             L3 = 82
	L3_L3_ATM                                       L3 = 83
	L3_L3_BRANCH                                    L3 = 84
	L3_L3_SD_REDEMPTION_PRINCIPAL_AND_INTEREST      L3 = 85
	L3_L3_RD_REDEMPTION_PRINCIPAL_AND_INTEREST      L3 = 86
	L3_L3_ARTS                                      L3 = 87
	L3_L3_HOBBIES                                   L3 = 88
	L3_L3_MOVIES                                    L3 = 89
	L3_L3_CONCERTS                                  L3 = 90
	L3_L3_CREDIT_CARD_PAYMENT                       L3 = 91
	L3_L3_CRYPTO                                    L3 = 92
	L3_L3_FIXED_DEPOSITS                            L3 = 93
	L3_L3_RECURRING_DEPOSITS                        L3 = 94
	L3_L3_SMART_DEPOSITS                            L3 = 95
	L3_L3_PPF                                       L3 = 96
	L3_L3_PENSION_POLICY                            L3 = 97
	L3_L3_EXTRA_CURRICULAR                          L3 = 98
	L3_L3_GAMES                                     L3 = 99
	L3_L3_GAMING_SOFTWARE                           L3 = 100
	L3_L3_SPORTING_GOODS                            L3 = 101
	L3_L3_SPORTS                                    L3 = 102
	L3_L3_POCKET_MONEY                              L3 = 103
	L3_L3_ALCOHOL_CIGARETTES                        L3 = 104
	L3_L3_CAFE_COFFEE_SHOP                          L3 = 105
	L3_L3_RESTAURANTS                               L3 = 106
	L3_L3_FOOD_DELIVERY                             L3 = 107
	L3_L3_CHARITY                                   L3 = 108
	L3_L3_GIFTING_SERVICES_PRODUCTS                 L3 = 109
	L3_L3_GROCERIES                                 L3 = 110
	L3_L3_PROPERTY                                  L3 = 111
	L3_L3_MONTHLY_RENT                              L3 = 112
	L3_L3_RENTAL_DEPOSIT                            L3 = 113
	L3_L3_GAS                                       L3 = 114
	L3_L3_FURNITURE_FURNISHINGS                     L3 = 115
	L3_L3_DOMESTIC_HELP                             L3 = 116
	L3_L3_INCOME_FROM_RENT                          L3 = 117
	L3_L3_MONTHLY_MAINTENANCE_CHARGES               L3 = 118
	L3_L3_MUSIC                                     L3 = 119
	L3_L3_STREAMING_SERVICES                        L3 = 120
	L3_L3_TV_CABLE                                  L3 = 121
	L3_L3_CLOTHING_FOOTWEAR                         L3 = 122
	L3_L3_WEARABLE_ACCESSORIES                      L3 = 123
	L3_L3_JEWELLERY                                 L3 = 124
	L3_L3_ELECTRONIC_GADGETS                        L3 = 125
	L3_L3_OTHER_MUTUAL_FUNDS_AMCS                   L3 = 126
	L3_L3_TRANSFER_TO_TRADING_AC                    L3 = 127
	L3_L3_MF_STOCKS_BONDS_COMMODITIES               L3 = 128
	L3_L3_APPLIANCES                                L3 = 129
	L3_L3_NA                                        L3 = 130
	L3_L3_HOTEL                                     L3 = 132
	L3_L3_TRAVEL_AGENCY                             L3 = 133
	L3_L3_AIR_TRAVEL                                L3 = 134
	L3_L3_TRAIN_TRAVEL                              L3 = 135
	L3_L3_BUS                                       L3 = 136
	L3_L3_RENTAL_CAR                                L3 = 137
	L3_L3_CAB_RIDE                                  L3 = 138
	L3_L3_RIDE_HAILING                              L3 = 139
	L3_L3_TOLLS                                     L3 = 140
	L3_L3_FUEL                                      L3 = 141
	L3_L3_PARKING                                   L3 = 142
	L3_L3_SERVICE_AUTO_PARTS                        L3 = 143
	L3_L3_LOGISTICS                                 L3 = 144
	L3_L3_INTERNATIONAL_MF_STOCKS_BONDS_COMMODITIES L3 = 145
	L3_L3_ECS_BOUNCE_FEES                           L3 = 146
	L3_L3_EMI_BOUNCE_FEES                           L3 = 147
	L3_L3_OTHER_BOUNCE_FEES                         L3 = 148
	L3_L3_ACH_BOUNCE_FEES                           L3 = 149
	L3_L3_TRANSACTION_BOUNCE_FEES                   L3 = 150
	L3_L3_INSUFFICIENT_BALANCE_BOUNCE_FEES          L3 = 151
)

// Enum value maps for L3.
var (
	L3_name = map[int32]string{
		0:   "L3_UNSPECIFIED",
		1:   "L3_INCOME_FROM_SALARY",
		2:   "L3_BONUS",
		4:   "L3_FD_REDEMPTION_PRINCIPAL_AND_INTEREST",
		5:   "L3_INTEREST_FROM_FDS",
		6:   "L3_INTEREST_FROM_SAVINGS_ACCOUNTS",
		7:   "L3_SID_REDEMPTION_PRINCIPAL_AND_INTEREST",
		8:   "L3_DIVIDENDS",
		9:   "L3_LIQUIDATED_INVESTMENTS",
		10:  "L3_OTHER_INCOME",
		11:  "L3_PURCHASE_CANCELLATIONS",
		12:  "L3_PURCHASE_REFUNDS",
		13:  "L3_TAX_REFUNDS",
		14:  "L3_CASHBACKS",
		15:  "L3_CREDIT_INTEREST_CAPITALISED",
		16:  "L3_OTHER",
		17:  "L3_MARKET_LINKED_MFS",
		18:  "L3_FIXED_INCOME_MFS",
		19:  "L3_AUTO_LOAN_EMI",
		20:  "L3_AUTO_LOAN_PREPAYMENT",
		21:  "L3_EDUCATION_LOAN_EMI",
		22:  "L3_EDUCATION_LOAN_PREPAYMENT",
		23:  "L3_HOME_LOAN_EMI",
		24:  "L3_HOME_LOAN_PREPAYMENT",
		25:  "L3_PERSONAL_LOAN_EMI",
		26:  "L3_PERSONAL_LOAN_PREPAYMENT",
		27:  "L3_OTHER_LOANS_EMI",
		28:  "L3_OTHER_LOANS_PREPAYMENT",
		29:  "L3_ELECTRICITY",
		30:  "L3_GAS_EG_LPG_",
		31:  "L3_WATER",
		32:  "L3_INTERNET",
		33:  "L3_PHONE",
		35:  "L3_MASONRY",
		36:  "L3_CARPENTRY",
		37:  "L3_ELECTRICAL",
		38:  "L3_GARDENING",
		39:  "L3_PLUMBING",
		40:  "L3_REPAIRS",
		42:  "L3_BABY_SUPPLIES",
		43:  "L3_CHILDCARE_FEES",
		44:  "L3_COOK",
		45:  "L3_DRIVER",
		46:  "L3_MAID",
		47:  "L3_SCHOOL_SUPPLIES",
		48:  "L3_COLLEGE_FEES",
		49:  "L3_SCHOOL_FEES",
		50:  "L3_TUITION_FEES",
		51:  "L3_FOOD",
		52:  "L3_VET",
		53:  "L3_MISC",
		54:  "L3_MOOCS",
		55:  "L3_PAPERBACKS",
		56:  "L3_E_BOOKS",
		57:  "L3_AUDIOBOOKS",
		58:  "L3_MAGAZINES_AND_NEWSPAPERS",
		60:  "L3_APP_DOWNLOAD",
		61:  "L3_IN_APP_PURCHASES",
		62:  "L3_ADVERTISING",
		63:  "L3_BUSINESS_SOFTWARE",
		64:  "L3_COMMISSIONS",
		65:  "L3_LEGAL",
		66:  "L3_OFFICE_SUPPLIES",
		67:  "L3_PRINTING",
		68:  "L3_SHIPPING",
		70:  "L3_MOBILE_APPS",
		72:  "L3_ANNUAL_FEES",
		73:  "L3_BELOW_MAB_FEES",
		74:  "L3_CARD_FEES",
		75:  "L3_CHEQUE_BOUNCE_FEES",
		76:  "L3_INTEREST_PAID",
		77:  "L3_LATE_FEES",
		78:  "L3_OVERDRAFT_FEES",
		80:  "L3_TAX_PREPARATION_SERVICE",
		81:  "L3_INCOME_TAX_PAYMENT",
		82:  "L3_TDS_AT_SOURCE",
		83:  "L3_ATM",
		84:  "L3_BRANCH",
		85:  "L3_SD_REDEMPTION_PRINCIPAL_AND_INTEREST",
		86:  "L3_RD_REDEMPTION_PRINCIPAL_AND_INTEREST",
		87:  "L3_ARTS",
		88:  "L3_HOBBIES",
		89:  "L3_MOVIES",
		90:  "L3_CONCERTS",
		91:  "L3_CREDIT_CARD_PAYMENT",
		92:  "L3_CRYPTO",
		93:  "L3_FIXED_DEPOSITS",
		94:  "L3_RECURRING_DEPOSITS",
		95:  "L3_SMART_DEPOSITS",
		96:  "L3_PPF",
		97:  "L3_PENSION_POLICY",
		98:  "L3_EXTRA_CURRICULAR",
		99:  "L3_GAMES",
		100: "L3_GAMING_SOFTWARE",
		101: "L3_SPORTING_GOODS",
		102: "L3_SPORTS",
		103: "L3_POCKET_MONEY",
		104: "L3_ALCOHOL_CIGARETTES",
		105: "L3_CAFE_COFFEE_SHOP",
		106: "L3_RESTAURANTS",
		107: "L3_FOOD_DELIVERY",
		108: "L3_CHARITY",
		109: "L3_GIFTING_SERVICES_PRODUCTS",
		110: "L3_GROCERIES",
		111: "L3_PROPERTY",
		112: "L3_MONTHLY_RENT",
		113: "L3_RENTAL_DEPOSIT",
		114: "L3_GAS",
		115: "L3_FURNITURE_FURNISHINGS",
		116: "L3_DOMESTIC_HELP",
		117: "L3_INCOME_FROM_RENT",
		118: "L3_MONTHLY_MAINTENANCE_CHARGES",
		119: "L3_MUSIC",
		120: "L3_STREAMING_SERVICES",
		121: "L3_TV_CABLE",
		122: "L3_CLOTHING_FOOTWEAR",
		123: "L3_WEARABLE_ACCESSORIES",
		124: "L3_JEWELLERY",
		125: "L3_ELECTRONIC_GADGETS",
		126: "L3_OTHER_MUTUAL_FUNDS_AMCS",
		127: "L3_TRANSFER_TO_TRADING_AC",
		128: "L3_MF_STOCKS_BONDS_COMMODITIES",
		129: "L3_APPLIANCES",
		130: "L3_NA",
		132: "L3_HOTEL",
		133: "L3_TRAVEL_AGENCY",
		134: "L3_AIR_TRAVEL",
		135: "L3_TRAIN_TRAVEL",
		136: "L3_BUS",
		137: "L3_RENTAL_CAR",
		138: "L3_CAB_RIDE",
		139: "L3_RIDE_HAILING",
		140: "L3_TOLLS",
		141: "L3_FUEL",
		142: "L3_PARKING",
		143: "L3_SERVICE_AUTO_PARTS",
		144: "L3_LOGISTICS",
		145: "L3_INTERNATIONAL_MF_STOCKS_BONDS_COMMODITIES",
		146: "L3_ECS_BOUNCE_FEES",
		147: "L3_EMI_BOUNCE_FEES",
		148: "L3_OTHER_BOUNCE_FEES",
		149: "L3_ACH_BOUNCE_FEES",
		150: "L3_TRANSACTION_BOUNCE_FEES",
		151: "L3_INSUFFICIENT_BALANCE_BOUNCE_FEES",
	}
	L3_value = map[string]int32{
		"L3_UNSPECIFIED":        0,
		"L3_INCOME_FROM_SALARY": 1,
		"L3_BONUS":              2,
		"L3_FD_REDEMPTION_PRINCIPAL_AND_INTEREST":  4,
		"L3_INTEREST_FROM_FDS":                     5,
		"L3_INTEREST_FROM_SAVINGS_ACCOUNTS":        6,
		"L3_SID_REDEMPTION_PRINCIPAL_AND_INTEREST": 7,
		"L3_DIVIDENDS":                             8,
		"L3_LIQUIDATED_INVESTMENTS":                9,
		"L3_OTHER_INCOME":                          10,
		"L3_PURCHASE_CANCELLATIONS":                11,
		"L3_PURCHASE_REFUNDS":                      12,
		"L3_TAX_REFUNDS":                           13,
		"L3_CASHBACKS":                             14,
		"L3_CREDIT_INTEREST_CAPITALISED":           15,
		"L3_OTHER":                                 16,
		"L3_MARKET_LINKED_MFS":                     17,
		"L3_FIXED_INCOME_MFS":                      18,
		"L3_AUTO_LOAN_EMI":                         19,
		"L3_AUTO_LOAN_PREPAYMENT":                  20,
		"L3_EDUCATION_LOAN_EMI":                    21,
		"L3_EDUCATION_LOAN_PREPAYMENT":             22,
		"L3_HOME_LOAN_EMI":                         23,
		"L3_HOME_LOAN_PREPAYMENT":                  24,
		"L3_PERSONAL_LOAN_EMI":                     25,
		"L3_PERSONAL_LOAN_PREPAYMENT":              26,
		"L3_OTHER_LOANS_EMI":                       27,
		"L3_OTHER_LOANS_PREPAYMENT":                28,
		"L3_ELECTRICITY":                           29,
		"L3_GAS_EG_LPG_":                           30,
		"L3_WATER":                                 31,
		"L3_INTERNET":                              32,
		"L3_PHONE":                                 33,
		"L3_MASONRY":                               35,
		"L3_CARPENTRY":                             36,
		"L3_ELECTRICAL":                            37,
		"L3_GARDENING":                             38,
		"L3_PLUMBING":                              39,
		"L3_REPAIRS":                               40,
		"L3_BABY_SUPPLIES":                         42,
		"L3_CHILDCARE_FEES":                        43,
		"L3_COOK":                                  44,
		"L3_DRIVER":                                45,
		"L3_MAID":                                  46,
		"L3_SCHOOL_SUPPLIES":                       47,
		"L3_COLLEGE_FEES":                          48,
		"L3_SCHOOL_FEES":                           49,
		"L3_TUITION_FEES":                          50,
		"L3_FOOD":                                  51,
		"L3_VET":                                   52,
		"L3_MISC":                                  53,
		"L3_MOOCS":                                 54,
		"L3_PAPERBACKS":                            55,
		"L3_E_BOOKS":                               56,
		"L3_AUDIOBOOKS":                            57,
		"L3_MAGAZINES_AND_NEWSPAPERS":              58,
		"L3_APP_DOWNLOAD":                          60,
		"L3_IN_APP_PURCHASES":                      61,
		"L3_ADVERTISING":                           62,
		"L3_BUSINESS_SOFTWARE":                     63,
		"L3_COMMISSIONS":                           64,
		"L3_LEGAL":                                 65,
		"L3_OFFICE_SUPPLIES":                       66,
		"L3_PRINTING":                              67,
		"L3_SHIPPING":                              68,
		"L3_MOBILE_APPS":                           70,
		"L3_ANNUAL_FEES":                           72,
		"L3_BELOW_MAB_FEES":                        73,
		"L3_CARD_FEES":                             74,
		"L3_CHEQUE_BOUNCE_FEES":                    75,
		"L3_INTEREST_PAID":                         76,
		"L3_LATE_FEES":                             77,
		"L3_OVERDRAFT_FEES":                        78,
		"L3_TAX_PREPARATION_SERVICE":               80,
		"L3_INCOME_TAX_PAYMENT":                    81,
		"L3_TDS_AT_SOURCE":                         82,
		"L3_ATM":                                   83,
		"L3_BRANCH":                                84,
		"L3_SD_REDEMPTION_PRINCIPAL_AND_INTEREST":  85,
		"L3_RD_REDEMPTION_PRINCIPAL_AND_INTEREST":  86,
		"L3_ARTS":                                  87,
		"L3_HOBBIES":                               88,
		"L3_MOVIES":                                89,
		"L3_CONCERTS":                              90,
		"L3_CREDIT_CARD_PAYMENT":                   91,
		"L3_CRYPTO":                                92,
		"L3_FIXED_DEPOSITS":                        93,
		"L3_RECURRING_DEPOSITS":                    94,
		"L3_SMART_DEPOSITS":                        95,
		"L3_PPF":                                   96,
		"L3_PENSION_POLICY":                        97,
		"L3_EXTRA_CURRICULAR":                      98,
		"L3_GAMES":                                 99,
		"L3_GAMING_SOFTWARE":                       100,
		"L3_SPORTING_GOODS":                        101,
		"L3_SPORTS":                                102,
		"L3_POCKET_MONEY":                          103,
		"L3_ALCOHOL_CIGARETTES":                    104,
		"L3_CAFE_COFFEE_SHOP":                      105,
		"L3_RESTAURANTS":                           106,
		"L3_FOOD_DELIVERY":                         107,
		"L3_CHARITY":                               108,
		"L3_GIFTING_SERVICES_PRODUCTS":             109,
		"L3_GROCERIES":                             110,
		"L3_PROPERTY":                              111,
		"L3_MONTHLY_RENT":                          112,
		"L3_RENTAL_DEPOSIT":                        113,
		"L3_GAS":                                   114,
		"L3_FURNITURE_FURNISHINGS":                 115,
		"L3_DOMESTIC_HELP":                         116,
		"L3_INCOME_FROM_RENT":                      117,
		"L3_MONTHLY_MAINTENANCE_CHARGES":           118,
		"L3_MUSIC":                                 119,
		"L3_STREAMING_SERVICES":                    120,
		"L3_TV_CABLE":                              121,
		"L3_CLOTHING_FOOTWEAR":                     122,
		"L3_WEARABLE_ACCESSORIES":                  123,
		"L3_JEWELLERY":                             124,
		"L3_ELECTRONIC_GADGETS":                    125,
		"L3_OTHER_MUTUAL_FUNDS_AMCS":               126,
		"L3_TRANSFER_TO_TRADING_AC":                127,
		"L3_MF_STOCKS_BONDS_COMMODITIES":           128,
		"L3_APPLIANCES":                            129,
		"L3_NA":                                    130,
		"L3_HOTEL":                                 132,
		"L3_TRAVEL_AGENCY":                         133,
		"L3_AIR_TRAVEL":                            134,
		"L3_TRAIN_TRAVEL":                          135,
		"L3_BUS":                                   136,
		"L3_RENTAL_CAR":                            137,
		"L3_CAB_RIDE":                              138,
		"L3_RIDE_HAILING":                          139,
		"L3_TOLLS":                                 140,
		"L3_FUEL":                                  141,
		"L3_PARKING":                               142,
		"L3_SERVICE_AUTO_PARTS":                    143,
		"L3_LOGISTICS":                             144,
		"L3_INTERNATIONAL_MF_STOCKS_BONDS_COMMODITIES": 145,
		"L3_ECS_BOUNCE_FEES":                           146,
		"L3_EMI_BOUNCE_FEES":                           147,
		"L3_OTHER_BOUNCE_FEES":                         148,
		"L3_ACH_BOUNCE_FEES":                           149,
		"L3_TRANSACTION_BOUNCE_FEES":                   150,
		"L3_INSUFFICIENT_BALANCE_BOUNCE_FEES":          151,
	}
)

func (x L3) Enum() *L3 {
	p := new(L3)
	*p = x
	return p
}

func (x L3) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (L3) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[8].Descriptor()
}

func (L3) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[8]
}

func (x L3) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use L3.Descriptor instead.
func (L3) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{8}
}

type TxnCategoryFieldMask int32

const (
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_UNSPECIFIED            TxnCategoryFieldMask = 0
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ID                     TxnCategoryFieldMask = 1
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_TXN_ID                 TxnCategoryFieldMask = 2
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ACTOR_ID               TxnCategoryFieldMask = 3
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID            TxnCategoryFieldMask = 4
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_CONFIDENCE_SCORE       TxnCategoryFieldMask = 5
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_PROVENANCE             TxnCategoryFieldMask = 6
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_CREATED_AT             TxnCategoryFieldMask = 7
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_UPDATED_AT             TxnCategoryFieldMask = 8
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_DELETED_AT             TxnCategoryFieldMask = 9
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_DS_CATEGORISATION_TIME TxnCategoryFieldMask = 10
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_MODEL_VERSION          TxnCategoryFieldMask = 11
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_IS_DISPLAY_ENABLED     TxnCategoryFieldMask = 12
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_DATA_CHANNEL           TxnCategoryFieldMask = 13
	TxnCategoryFieldMask_TXN_CATEGORY_FIELD_MASK_CATEGORISATION_SOURCE  TxnCategoryFieldMask = 14
)

// Enum value maps for TxnCategoryFieldMask.
var (
	TxnCategoryFieldMask_name = map[int32]string{
		0:  "TXN_CATEGORY_FIELD_MASK_UNSPECIFIED",
		1:  "TXN_CATEGORY_FIELD_MASK_ID",
		2:  "TXN_CATEGORY_FIELD_MASK_TXN_ID",
		3:  "TXN_CATEGORY_FIELD_MASK_ACTOR_ID",
		4:  "TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID",
		5:  "TXN_CATEGORY_FIELD_MASK_CONFIDENCE_SCORE",
		6:  "TXN_CATEGORY_FIELD_MASK_PROVENANCE",
		7:  "TXN_CATEGORY_FIELD_MASK_CREATED_AT",
		8:  "TXN_CATEGORY_FIELD_MASK_UPDATED_AT",
		9:  "TXN_CATEGORY_FIELD_MASK_DELETED_AT",
		10: "TXN_CATEGORY_FIELD_MASK_DS_CATEGORISATION_TIME",
		11: "TXN_CATEGORY_FIELD_MASK_MODEL_VERSION",
		12: "TXN_CATEGORY_FIELD_MASK_IS_DISPLAY_ENABLED",
		13: "TXN_CATEGORY_FIELD_MASK_DATA_CHANNEL",
		14: "TXN_CATEGORY_FIELD_MASK_CATEGORISATION_SOURCE",
	}
	TxnCategoryFieldMask_value = map[string]int32{
		"TXN_CATEGORY_FIELD_MASK_UNSPECIFIED":            0,
		"TXN_CATEGORY_FIELD_MASK_ID":                     1,
		"TXN_CATEGORY_FIELD_MASK_TXN_ID":                 2,
		"TXN_CATEGORY_FIELD_MASK_ACTOR_ID":               3,
		"TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID":            4,
		"TXN_CATEGORY_FIELD_MASK_CONFIDENCE_SCORE":       5,
		"TXN_CATEGORY_FIELD_MASK_PROVENANCE":             6,
		"TXN_CATEGORY_FIELD_MASK_CREATED_AT":             7,
		"TXN_CATEGORY_FIELD_MASK_UPDATED_AT":             8,
		"TXN_CATEGORY_FIELD_MASK_DELETED_AT":             9,
		"TXN_CATEGORY_FIELD_MASK_DS_CATEGORISATION_TIME": 10,
		"TXN_CATEGORY_FIELD_MASK_MODEL_VERSION":          11,
		"TXN_CATEGORY_FIELD_MASK_IS_DISPLAY_ENABLED":     12,
		"TXN_CATEGORY_FIELD_MASK_DATA_CHANNEL":           13,
		"TXN_CATEGORY_FIELD_MASK_CATEGORISATION_SOURCE":  14,
	}
)

func (x TxnCategoryFieldMask) Enum() *TxnCategoryFieldMask {
	p := new(TxnCategoryFieldMask)
	*p = x
	return p
}

func (x TxnCategoryFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TxnCategoryFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[9].Descriptor()
}

func (TxnCategoryFieldMask) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[9]
}

func (x TxnCategoryFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TxnCategoryFieldMask.Descriptor instead.
func (TxnCategoryFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{9}
}

type TxnCategoryOntologyFieldMask int32

const (
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_UNSPECIFIED      TxnCategoryOntologyFieldMask = 0
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_ONTOLOGY_ID      TxnCategoryOntologyFieldMask = 1
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L0               TxnCategoryOntologyFieldMask = 2
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L1               TxnCategoryOntologyFieldMask = 3
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L2               TxnCategoryOntologyFieldMask = 4
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L3               TxnCategoryOntologyFieldMask = 5
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_DISPLAY_CATEGORY TxnCategoryOntologyFieldMask = 6
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_CREATED_AT       TxnCategoryOntologyFieldMask = 7
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_UPDATED_AT       TxnCategoryOntologyFieldMask = 8
	TxnCategoryOntologyFieldMask_TXN_CATEGORY_ONTOLOGY_FIELD_MASK_DELETED_AT       TxnCategoryOntologyFieldMask = 9
)

// Enum value maps for TxnCategoryOntologyFieldMask.
var (
	TxnCategoryOntologyFieldMask_name = map[int32]string{
		0: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_UNSPECIFIED",
		1: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_ONTOLOGY_ID",
		2: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L0",
		3: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L1",
		4: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L2",
		5: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L3",
		6: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_DISPLAY_CATEGORY",
		7: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_CREATED_AT",
		8: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_UPDATED_AT",
		9: "TXN_CATEGORY_ONTOLOGY_FIELD_MASK_DELETED_AT",
	}
	TxnCategoryOntologyFieldMask_value = map[string]int32{
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_UNSPECIFIED":      0,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_ONTOLOGY_ID":      1,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L0":               2,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L1":               3,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L2":               4,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L3":               5,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_DISPLAY_CATEGORY": 6,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_CREATED_AT":       7,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_UPDATED_AT":       8,
		"TXN_CATEGORY_ONTOLOGY_FIELD_MASK_DELETED_AT":       9,
	}
)

func (x TxnCategoryOntologyFieldMask) Enum() *TxnCategoryOntologyFieldMask {
	p := new(TxnCategoryOntologyFieldMask)
	*p = x
	return p
}

func (x TxnCategoryOntologyFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TxnCategoryOntologyFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[10].Descriptor()
}

func (TxnCategoryOntologyFieldMask) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[10]
}

func (x TxnCategoryOntologyFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TxnCategoryOntologyFieldMask.Descriptor instead.
func (TxnCategoryOntologyFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{10}
}

type UserDisplayCategoryFieldMask int32

const (
	UserDisplayCategoryFieldMask_USER_DISPLAY_CATEGORY_FIELD_MASK_UNSPECIFIED      UserDisplayCategoryFieldMask = 0
	UserDisplayCategoryFieldMask_USER_DISPLAY_CATEGORY_FIELD_MASK_DISPLAY_CATEGORY UserDisplayCategoryFieldMask = 1
	UserDisplayCategoryFieldMask_USER_DISPLAY_CATEGORY_FIELD_MASK_ONTOLOGY_ID      UserDisplayCategoryFieldMask = 2
	UserDisplayCategoryFieldMask_USER_DISPLAY_CATEGORY_FIELD_MASK_CATREGORY_TYPE   UserDisplayCategoryFieldMask = 3
	UserDisplayCategoryFieldMask_USER_DISPLAY_CATEGORY_FIELD_MASK_CREATED_AT       UserDisplayCategoryFieldMask = 4
	UserDisplayCategoryFieldMask_USER_DISPLAY_CATEGORY_FIELD_MASK_UPDATED_AT       UserDisplayCategoryFieldMask = 5
	UserDisplayCategoryFieldMask_USER_DISPLAY_CATEGORY_FIELD_MASK_DELETED_AT       UserDisplayCategoryFieldMask = 6
)

// Enum value maps for UserDisplayCategoryFieldMask.
var (
	UserDisplayCategoryFieldMask_name = map[int32]string{
		0: "USER_DISPLAY_CATEGORY_FIELD_MASK_UNSPECIFIED",
		1: "USER_DISPLAY_CATEGORY_FIELD_MASK_DISPLAY_CATEGORY",
		2: "USER_DISPLAY_CATEGORY_FIELD_MASK_ONTOLOGY_ID",
		3: "USER_DISPLAY_CATEGORY_FIELD_MASK_CATREGORY_TYPE",
		4: "USER_DISPLAY_CATEGORY_FIELD_MASK_CREATED_AT",
		5: "USER_DISPLAY_CATEGORY_FIELD_MASK_UPDATED_AT",
		6: "USER_DISPLAY_CATEGORY_FIELD_MASK_DELETED_AT",
	}
	UserDisplayCategoryFieldMask_value = map[string]int32{
		"USER_DISPLAY_CATEGORY_FIELD_MASK_UNSPECIFIED":      0,
		"USER_DISPLAY_CATEGORY_FIELD_MASK_DISPLAY_CATEGORY": 1,
		"USER_DISPLAY_CATEGORY_FIELD_MASK_ONTOLOGY_ID":      2,
		"USER_DISPLAY_CATEGORY_FIELD_MASK_CATREGORY_TYPE":   3,
		"USER_DISPLAY_CATEGORY_FIELD_MASK_CREATED_AT":       4,
		"USER_DISPLAY_CATEGORY_FIELD_MASK_UPDATED_AT":       5,
		"USER_DISPLAY_CATEGORY_FIELD_MASK_DELETED_AT":       6,
	}
)

func (x UserDisplayCategoryFieldMask) Enum() *UserDisplayCategoryFieldMask {
	p := new(UserDisplayCategoryFieldMask)
	*p = x
	return p
}

func (x UserDisplayCategoryFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserDisplayCategoryFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[11].Descriptor()
}

func (UserDisplayCategoryFieldMask) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[11]
}

func (x UserDisplayCategoryFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserDisplayCategoryFieldMask.Descriptor instead.
func (UserDisplayCategoryFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{11}
}

// DataChannel defines the channel of data
// e.g. data channel can be fi, aa, gmail, sms, etc
type DataChannel int32

const (
	DataChannel_DATA_CHANNEL_UNSPECIFIED DataChannel = 0
	DataChannel_DATA_CHANNEL_FI          DataChannel = 1
	DataChannel_DATA_CHANNEL_AA          DataChannel = 2
	DataChannel_DATA_CHANNEL_FI_CARD     DataChannel = 3
)

// Enum value maps for DataChannel.
var (
	DataChannel_name = map[int32]string{
		0: "DATA_CHANNEL_UNSPECIFIED",
		1: "DATA_CHANNEL_FI",
		2: "DATA_CHANNEL_AA",
		3: "DATA_CHANNEL_FI_CARD",
	}
	DataChannel_value = map[string]int32{
		"DATA_CHANNEL_UNSPECIFIED": 0,
		"DATA_CHANNEL_FI":          1,
		"DATA_CHANNEL_AA":          2,
		"DATA_CHANNEL_FI_CARD":     3,
	}
)

func (x DataChannel) Enum() *DataChannel {
	p := new(DataChannel)
	*p = x
	return p
}

func (x DataChannel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataChannel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[12].Descriptor()
}

func (DataChannel) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[12]
}

func (x DataChannel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataChannel.Descriptor instead.
func (DataChannel) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{12}
}

type TransactionActorType int32

const (
	TransactionActorType_ACTOR_TYPE_UNSPECIFIED TransactionActorType = 0
	TransactionActorType_FROM_ACTOR             TransactionActorType = 1
	TransactionActorType_TO_ACTOR               TransactionActorType = 2
)

// Enum value maps for TransactionActorType.
var (
	TransactionActorType_name = map[int32]string{
		0: "ACTOR_TYPE_UNSPECIFIED",
		1: "FROM_ACTOR",
		2: "TO_ACTOR",
	}
	TransactionActorType_value = map[string]int32{
		"ACTOR_TYPE_UNSPECIFIED": 0,
		"FROM_ACTOR":             1,
		"TO_ACTOR":               2,
	}
)

func (x TransactionActorType) Enum() *TransactionActorType {
	p := new(TransactionActorType)
	*p = x
	return p
}

func (x TransactionActorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionActorType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[13].Descriptor()
}

func (TransactionActorType) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[13]
}

func (x TransactionActorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionActorType.Descriptor instead.
func (TransactionActorType) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{13}
}

type OrderTag int32

const (
	OrderTag_ORDER_TAG_UNSPECIFIED              OrderTag = 0
	OrderTag_ORDER_TAG_MUTUAL_FUND              OrderTag = 1
	OrderTag_ORDER_TAG_INTEREST_FD              OrderTag = 3
	OrderTag_ORDER_TAG_INTEREST_SAVINGS_ACCOUNT OrderTag = 4
	OrderTag_ORDER_TAG_JUMP_P2P_INVESTMENT      OrderTag = 5
	// scheduled emi payment of a loan
	OrderTag_ORDER_TAG_LOAN_EMI_PAYMENT OrderTag = 6
	// loan prepayment is lump sum payment of part or full loan amount
	OrderTag_ORDER_TAG_LOAN_PREPAYMENT    OrderTag = 7
	OrderTag_ORDER_TAG_DEBIT_CARD_CHARGES OrderTag = 8
)

// Enum value maps for OrderTag.
var (
	OrderTag_name = map[int32]string{
		0: "ORDER_TAG_UNSPECIFIED",
		1: "ORDER_TAG_MUTUAL_FUND",
		3: "ORDER_TAG_INTEREST_FD",
		4: "ORDER_TAG_INTEREST_SAVINGS_ACCOUNT",
		5: "ORDER_TAG_JUMP_P2P_INVESTMENT",
		6: "ORDER_TAG_LOAN_EMI_PAYMENT",
		7: "ORDER_TAG_LOAN_PREPAYMENT",
		8: "ORDER_TAG_DEBIT_CARD_CHARGES",
	}
	OrderTag_value = map[string]int32{
		"ORDER_TAG_UNSPECIFIED":              0,
		"ORDER_TAG_MUTUAL_FUND":              1,
		"ORDER_TAG_INTEREST_FD":              3,
		"ORDER_TAG_INTEREST_SAVINGS_ACCOUNT": 4,
		"ORDER_TAG_JUMP_P2P_INVESTMENT":      5,
		"ORDER_TAG_LOAN_EMI_PAYMENT":         6,
		"ORDER_TAG_LOAN_PREPAYMENT":          7,
		"ORDER_TAG_DEBIT_CARD_CHARGES":       8,
	}
)

func (x OrderTag) Enum() *OrderTag {
	p := new(OrderTag)
	*p = x
	return p
}

func (x OrderTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderTag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[14].Descriptor()
}

func (OrderTag) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[14]
}

func (x OrderTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderTag.Descriptor instead.
func (OrderTag) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{14}
}

// Display state enum refers to the state of a display category for a txn. Disabled means that the category is not shown for that txn.
// Enabled means the categories which are shown for the txn. Any includes both the types of categories, enabled and disabled.
type DisplayState int32

const (
	DisplayState_DISPLAY_STATE_UNSPECIFIED DisplayState = 0
	DisplayState_DISPLAY_STATE_ENABLED     DisplayState = 1
	DisplayState_DISPLAY_STATE_DISABLED    DisplayState = 2
	DisplayState_DISPLAY_STATE_ANY         DisplayState = 3
)

// Enum value maps for DisplayState.
var (
	DisplayState_name = map[int32]string{
		0: "DISPLAY_STATE_UNSPECIFIED",
		1: "DISPLAY_STATE_ENABLED",
		2: "DISPLAY_STATE_DISABLED",
		3: "DISPLAY_STATE_ANY",
	}
	DisplayState_value = map[string]int32{
		"DISPLAY_STATE_UNSPECIFIED": 0,
		"DISPLAY_STATE_ENABLED":     1,
		"DISPLAY_STATE_DISABLED":    2,
		"DISPLAY_STATE_ANY":         3,
	}
)

func (x DisplayState) Enum() *DisplayState {
	p := new(DisplayState)
	*p = x
	return p
}

func (x DisplayState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisplayState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_categorizer_enums_proto_enumTypes[15].Descriptor()
}

func (DisplayState) Type() protoreflect.EnumType {
	return &file_api_categorizer_enums_proto_enumTypes[15]
}

func (x DisplayState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisplayState.Descriptor instead.
func (DisplayState) EnumDescriptor() ([]byte, []int) {
	return file_api_categorizer_enums_proto_rawDescGZIP(), []int{15}
}

var File_api_categorizer_enums_proto protoreflect.FileDescriptor

var file_api_categorizer_enums_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65,
	0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2a, 0x42, 0x0a, 0x08, 0x42, 0x61,
	0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x06, 0x0a, 0x02, 0x46, 0x49, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x43, 0x49,
	0x43, 0x49, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x44, 0x46, 0x43, 0x10, 0x03, 0x2a, 0xb3,
	0x03, 0x0a, 0x14, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x49, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f,
	0x0a, 0x1b, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c,
	0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12,
	0x14, 0x0a, 0x10, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x56, 0x50, 0x41, 0x5f,
	0x4d, 0x43, 0x43, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e,
	0x54, 0x5f, 0x56, 0x50, 0x41, 0x5f, 0x53, 0x55, 0x42, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x03, 0x12,
	0x15, 0x0a, 0x11, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x4d, 0x43, 0x43, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x45,
	0x4e, 0x47, 0x49, 0x4e, 0x45, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x4f, 0x4f, 0x47, 0x4c,
	0x45, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x5f, 0x41, 0x50, 0x49, 0x10, 0x06, 0x12, 0x10, 0x0a,
	0x0c, 0x50, 0x45, 0x45, 0x52, 0x53, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x10, 0x07, 0x12,
	0x1d, 0x0a, 0x19, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45,
	0x47, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x10, 0x08, 0x12, 0x12,
	0x0a, 0x0e, 0x52, 0x45, 0x4d, 0x41, 0x52, 0x4b, 0x53, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45,
	0x10, 0x09, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x49, 0x5a, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x46, 0x55, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e,
	0x43, 0x45, 0x10, 0x0a, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x52, 0x4f, 0x57, 0x44, 0x5f, 0x41, 0x47,
	0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x2c, 0x0a, 0x28, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x4d, 0x49, 0x4c, 0x41,
	0x52, 0x5f, 0x52, 0x45, 0x43, 0x41, 0x54, 0x10, 0x0c, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x10, 0x0d, 0x2a, 0x53, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x44,
	0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x01, 0x12, 0x09,
	0x0a, 0x05, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x02, 0x2a, 0x3a, 0x0a, 0x0a, 0x50, 0x72, 0x6f,
	0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x56, 0x45,
	0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x44, 0x53, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x55,
	0x53, 0x45, 0x52, 0x10, 0x02, 0x2a, 0xd8, 0x10, 0x0a, 0x0f, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x49, 0x53,
	0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4f, 0x4e, 0x55, 0x53,
	0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x0e,
	0x0a, 0x0a, 0x52, 0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x16,
	0x0a, 0x12, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x44, 0x49, 0x56, 0x49, 0x44,
	0x45, 0x4e, 0x44, 0x53, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44,
	0x53, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x53,
	0x10, 0x07, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x46, 0x45, 0x52, 0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54,
	0x53, 0x10, 0x0a, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4d, 0x55,
	0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x0b, 0x12, 0x0e, 0x0a, 0x0a,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b,
	0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x0d, 0x12, 0x0a, 0x0a,
	0x06, 0x43, 0x52, 0x59, 0x50, 0x54, 0x4f, 0x10, 0x0e, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x4d, 0x49,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0f, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x10, 0x12,
	0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x12,
	0x12, 0x14, 0x0a, 0x10, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x13, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x14, 0x12, 0x11, 0x0a, 0x0d, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x15, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x54, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x53, 0x10, 0x16, 0x12, 0x18, 0x0a, 0x14, 0x4d,
	0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x43, 0x48, 0x41,
	0x52, 0x47, 0x45, 0x10, 0x17, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x4e,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x49, 0x52, 0x10, 0x19, 0x12, 0x18, 0x0a,
	0x14, 0x46, 0x55, 0x52, 0x4e, 0x49, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x1a, 0x12, 0x0b, 0x0a, 0x07, 0x48, 0x4f, 0x55, 0x53, 0x49,
	0x4e, 0x47, 0x10, 0x1b, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x48, 0x49, 0x4c, 0x44, 0x43, 0x41, 0x52,
	0x45, 0x10, 0x1c, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x4f, 0x4d, 0x45, 0x53, 0x54, 0x49, 0x43, 0x5f,
	0x48, 0x45, 0x4c, 0x50, 0x10, 0x1d, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x44, 0x55, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x1e, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x45, 0x54, 0x53, 0x10, 0x1f, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x41, 0x4d, 0x49, 0x4c, 0x59, 0x10, 0x20, 0x12, 0x12, 0x0a, 0x0e, 0x4f,
	0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x43, 0x4f, 0x55, 0x52, 0x53, 0x45, 0x53, 0x10, 0x21, 0x12,
	0x0e, 0x0a, 0x0a, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0x22, 0x12,
	0x11, 0x0a, 0x0d, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x54, 0x41, 0x49, 0x4e, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x23, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x50, 0x55, 0x42, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x24, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x50,
	0x4f, 0x52, 0x54, 0x53, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x53, 0x10, 0x25, 0x12, 0x11, 0x0a, 0x0d,
	0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x47, 0x41, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x26, 0x12,
	0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x56, 0x49, 0x45, 0x53, 0x10, 0x29, 0x12, 0x0c, 0x0a, 0x08, 0x43,
	0x4f, 0x4e, 0x43, 0x45, 0x52, 0x54, 0x53, 0x10, 0x2a, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x55, 0x53,
	0x49, 0x43, 0x10, 0x2b, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x54, 0x41, 0x49,
	0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x53, 0x10, 0x2c, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x49, 0x4e,
	0x47, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10, 0x2e, 0x12,
	0x0b, 0x0a, 0x07, 0x47, 0x41, 0x44, 0x47, 0x45, 0x54, 0x53, 0x10, 0x2f, 0x12, 0x0e, 0x0a, 0x0a,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x30, 0x12, 0x13, 0x0a, 0x0f,
	0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10,
	0x31, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x48, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x33, 0x12,
	0x0d, 0x0a, 0x09, 0x4a, 0x45, 0x57, 0x45, 0x4c, 0x4c, 0x45, 0x52, 0x59, 0x10, 0x34, 0x12, 0x18,
	0x0a, 0x14, 0x47, 0x52, 0x4f, 0x43, 0x45, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x53, 0x53, 0x45,
	0x4e, 0x54, 0x49, 0x41, 0x4c, 0x53, 0x10, 0x37, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x45, 0x52, 0x53,
	0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x45, 0x10, 0x38, 0x12, 0x12, 0x0a, 0x0e, 0x48,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x57, 0x45, 0x4c, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x39, 0x12,
	0x0b, 0x0a, 0x07, 0x46, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x10, 0x3b, 0x12, 0x0a, 0x0a, 0x06,
	0x54, 0x52, 0x41, 0x49, 0x4e, 0x53, 0x10, 0x3c, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x4f, 0x41, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x10, 0x3d, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4d,
	0x4d, 0x55, 0x54, 0x45, 0x10, 0x3e, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x55, 0x45, 0x4c, 0x10, 0x40,
	0x12, 0x13, 0x0a, 0x0f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x10, 0x42, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x48, 0x49, 0x50, 0x50, 0x49, 0x4e,
	0x47, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x53, 0x10, 0x43, 0x12, 0x12, 0x0a,
	0x0e, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x44, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x49, 0x53, 0x43, 0x45, 0x4c, 0x4c, 0x41, 0x4e, 0x45, 0x4f,
	0x55, 0x53, 0x10, 0x45, 0x12, 0x13, 0x0a, 0x0f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x53, 0x10, 0x46, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x4f, 0x4e,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x47, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x4f, 0x46, 0x54,
	0x57, 0x41, 0x52, 0x45, 0x10, 0x48, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x46,
	0x45, 0x45, 0x53, 0x10, 0x49, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49,
	0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x4a, 0x12, 0x09, 0x0a,
	0x05, 0x47, 0x49, 0x46, 0x54, 0x53, 0x10, 0x4b, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x41, 0x58, 0x45,
	0x53, 0x10, 0x4c, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x10, 0x4d, 0x12,
	0x14, 0x0a, 0x10, 0x43, 0x41, 0x53, 0x48, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57,
	0x41, 0x4c, 0x53, 0x10, 0x4e, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f,
	0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x4f, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x50,
	0x45, 0x4e, 0x44, 0x53, 0x10, 0x51, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x52,
	0x12, 0x15, 0x0a, 0x11, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x54, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x55, 0x12, 0x14, 0x0a,
	0x10, 0x46, 0x41, 0x4d, 0x49, 0x4c, 0x59, 0x5f, 0x43, 0x48, 0x49, 0x4c, 0x44, 0x43, 0x41, 0x52,
	0x45, 0x10, 0x56, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x44, 0x52, 0x49, 0x4e,
	0x4b, 0x53, 0x5f, 0x47, 0x52, 0x4f, 0x43, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x57, 0x12, 0x11,
	0x0a, 0x0d, 0x47, 0x49, 0x46, 0x54, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x49, 0x54, 0x59, 0x10,
	0x58, 0x12, 0x13, 0x0a, 0x0f, 0x48, 0x4f, 0x55, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x54, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x10, 0x59, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x45, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x10, 0x5a,
	0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x43, 0x41, 0x53, 0x48,
	0x42, 0x41, 0x43, 0x4b, 0x53, 0x10, 0x5b, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x45, 0x4c, 0x46, 0x5f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x5c, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x45, 0x4c, 0x46,
	0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x5d, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x55, 0x42, 0x53,
	0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x5e, 0x12, 0x1a, 0x0a, 0x16, 0x45,
	0x4c, 0x45, 0x43, 0x54, 0x52, 0x4f, 0x4e, 0x49, 0x43, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x5f, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x52, 0x41, 0x56, 0x45,
	0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x60, 0x12, 0x1a, 0x0a, 0x16, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x41, 0x54, 0x45, 0x57, 0x41, 0x59, 0x10, 0x61, 0x12, 0x0b,
	0x0a, 0x07, 0x46, 0x41, 0x53, 0x48, 0x49, 0x4f, 0x4e, 0x10, 0x62, 0x12, 0x0d, 0x0a, 0x09, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4d, 0x46, 0x10, 0x63, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x64, 0x12, 0x0f, 0x0a,
	0x0b, 0x46, 0x41, 0x4d, 0x49, 0x4c, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x45, 0x10, 0x65, 0x12, 0x08,
	0x0a, 0x04, 0x43, 0x41, 0x53, 0x48, 0x10, 0x66, 0x12, 0x08, 0x0a, 0x04, 0x47, 0x49, 0x46, 0x54,
	0x10, 0x67, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x41, 0x58, 0x10, 0x68, 0x12, 0x0b, 0x0a, 0x07, 0x47,
	0x52, 0x4f, 0x43, 0x45, 0x52, 0x59, 0x10, 0x69, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x49, 0x4e, 0x49,
	0x4e, 0x47, 0x10, 0x6a, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x10, 0x6b, 0x12,
	0x12, 0x0a, 0x0e, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x6c, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x55, 0x54, 0x45, 0x10, 0x6d, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x4f, 0x4e, 0x45, 0x59,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0x6e, 0x12, 0x0c, 0x0a, 0x08, 0x44,
	0x49, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x44, 0x10, 0x6f, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x45, 0x53, 0x54, 0x10, 0x70, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x71, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x41, 0x44, 0x47, 0x45, 0x54, 0x10, 0x72,
	0x12, 0x0c, 0x0a, 0x08, 0x42, 0x4f, 0x52, 0x52, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x73, 0x12, 0x13,
	0x0a, 0x0f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x41, 0x4c,
	0x45, 0x10, 0x74, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x46, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x75, 0x12, 0x17, 0x0a,
	0x13, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x44,
	0x45, 0x42, 0x49, 0x54, 0x10, 0x76, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10,
	0x77, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x46, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x78, 0x12, 0x0d, 0x0a, 0x09, 0x47,
	0x52, 0x4f, 0x43, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x79, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f,
	0x4f, 0x44, 0x5f, 0x44, 0x52, 0x49, 0x4e, 0x4b, 0x53, 0x10, 0x7a, 0x12, 0x13, 0x0a, 0x0f, 0x54,
	0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x56, 0x41, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x7b,
	0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x57,
	0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x10, 0x7c, 0x12, 0x11, 0x0a, 0x0d, 0x48,
	0x4f, 0x55, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x53, 0x10, 0x7d, 0x12, 0x0e,
	0x0a, 0x0a, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x7f, 0x12, 0x1b,
	0x0a, 0x16, 0x46, 0x41, 0x4d, 0x49, 0x4c, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45,
	0x52, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x80, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x46,
	0x41, 0x4d, 0x49, 0x4c, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x44,
	0x45, 0x42, 0x49, 0x54, 0x10, 0x81, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x46, 0x45, 0x45, 0x53, 0x5f,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x10, 0x82, 0x01, 0x22, 0x04, 0x08, 0x53, 0x10, 0x53,
	0x2a, 0x7e, 0x0a, 0x02, 0x4c, 0x30, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x30, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x30,
	0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x30, 0x5f,
	0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x02, 0x12, 0x0c, 0x0a,
	0x08, 0x4c, 0x30, 0x5f, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x4c,
	0x30, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x4c,
	0x30, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x30, 0x5f, 0x44,
	0x45, 0x42, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x07,
	0x2a, 0xa2, 0x03, 0x0a, 0x02, 0x4c, 0x31, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x31, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4c,
	0x31, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x31,
	0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x02, 0x12, 0x17,
	0x0a, 0x13, 0x4c, 0x31, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x31, 0x5f, 0x49, 0x4e,
	0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x31, 0x5f,
	0x48, 0x4f, 0x55, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x31, 0x5f,
	0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x46, 0x41, 0x4d, 0x49, 0x4c, 0x59, 0x10,
	0x06, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x31, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x54, 0x41, 0x49,
	0x4e, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x31, 0x5f, 0x53, 0x48,
	0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x08, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x31, 0x5f, 0x46,
	0x4f, 0x4f, 0x44, 0x5f, 0x44, 0x52, 0x49, 0x4e, 0x4b, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x47,
	0x52, 0x4f, 0x43, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x09, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x31,
	0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x45, 0x5f, 0x48,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x0a, 0x12,
	0x10, 0x0a, 0x0c, 0x4c, 0x31, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x10,
	0x0b, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x31, 0x5f, 0x4d, 0x49, 0x53, 0x43, 0x10, 0x0c, 0x12, 0x0e,
	0x0a, 0x0a, 0x4c, 0x31, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x0d, 0x12, 0x0c,
	0x0a, 0x08, 0x4c, 0x31, 0x5f, 0x54, 0x41, 0x58, 0x45, 0x53, 0x10, 0x0e, 0x12, 0x1c, 0x0a, 0x18,
	0x4c, 0x31, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50,
	0x4f, 0x52, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x31,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x45, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x10, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x31, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x11, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x31, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10,
	0x12, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x31, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x10, 0x13, 0x2a, 0xca, 0x15, 0x0a, 0x02, 0x4c, 0x32, 0x12, 0x12, 0x0a, 0x0e,
	0x4c, 0x32, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x32, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x46, 0x52,
	0x4f, 0x4d, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12,
	0x17, 0x0a, 0x13, 0x4c, 0x32, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x52,
	0x45, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x32, 0x5f, 0x46,
	0x52, 0x4f, 0x4d, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x32,
	0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x53, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x32, 0x5f, 0x53, 0x4d, 0x41, 0x52,
	0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07,
	0x4c, 0x32, 0x5f, 0x53, 0x49, 0x44, 0x53, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x32, 0x5f,
	0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x41, 0x4d, 0x43,
	0x53, 0x10, 0x0a, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x32, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46,
	0x45, 0x52, 0x5f, 0x54, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43,
	0x10, 0x0b, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x32, 0x5f, 0x4d, 0x46, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x53, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x49, 0x45, 0x53, 0x10, 0x0c, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x32, 0x5f, 0x50, 0x50, 0x46,
	0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x32, 0x5f, 0x50, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10, 0x0e, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x32, 0x5f,
	0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x0f, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x32, 0x5f, 0x41, 0x55,
	0x54, 0x4f, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x10, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x32, 0x5f,
	0x45, 0x44, 0x55, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x11,
	0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x32, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e,
	0x10, 0x12, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x32, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x13, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x32, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x14, 0x12, 0x1a, 0x0a, 0x16, 0x4c,
	0x32, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x15, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x32, 0x5f, 0x41, 0x55,
	0x54, 0x4f, 0x10, 0x16, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52,
	0x41, 0x4c, 0x10, 0x17, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x32, 0x5f, 0x4c, 0x49, 0x46, 0x45, 0x10,
	0x18, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x4c, 0x10,
	0x19, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x32, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x10, 0x1a,
	0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x32, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x10,
	0x1b, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x32, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f,
	0x52, 0x45, 0x4e, 0x54, 0x10, 0x1c, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x32, 0x5f, 0x52, 0x45, 0x4e,
	0x54, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x1d, 0x12, 0x10, 0x0a,
	0x0c, 0x4c, 0x32, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x1e, 0x12,
	0x17, 0x0a, 0x13, 0x4c, 0x32, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x49, 0x4d, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1f, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x32, 0x5f, 0x4d,
	0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x10, 0x20, 0x12, 0x11, 0x0a, 0x0d,
	0x4c, 0x32, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x21, 0x12,
	0x17, 0x0a, 0x13, 0x4c, 0x32, 0x5f, 0x42, 0x41, 0x42, 0x49, 0x45, 0x53, 0x5f, 0x43, 0x48, 0x49,
	0x4c, 0x44, 0x43, 0x41, 0x52, 0x45, 0x10, 0x23, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x32, 0x5f, 0x44,
	0x4f, 0x4d, 0x45, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x48, 0x45, 0x4c, 0x50, 0x10, 0x24, 0x12, 0x10,
	0x0a, 0x0c, 0x4c, 0x32, 0x5f, 0x45, 0x44, 0x55, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x25,
	0x12, 0x17, 0x0a, 0x13, 0x4c, 0x32, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x5f, 0x43, 0x55, 0x52,
	0x52, 0x49, 0x43, 0x55, 0x4c, 0x41, 0x52, 0x10, 0x26, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x32, 0x5f,
	0x50, 0x45, 0x54, 0x53, 0x10, 0x27, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x32, 0x5f, 0x50, 0x4f, 0x43,
	0x4b, 0x45, 0x54, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0x28, 0x12, 0x17, 0x0a, 0x13, 0x4c,
	0x32, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x49, 0x4d, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x29, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x32, 0x5f, 0x41, 0x4c, 0x43, 0x4f, 0x48,
	0x4f, 0x4c, 0x5f, 0x42, 0x41, 0x52, 0x53, 0x10, 0x2b, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x32, 0x5f,
	0x41, 0x52, 0x54, 0x53, 0x10, 0x2c, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x32, 0x5f, 0x42, 0x4f, 0x4f,
	0x4b, 0x53, 0x10, 0x2d, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x32, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x53,
	0x10, 0x2e, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x32, 0x5f, 0x47, 0x41, 0x4d, 0x49, 0x4e, 0x47, 0x5f,
	0x41, 0x50, 0x50, 0x53, 0x10, 0x2f, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x32, 0x5f, 0x47, 0x41, 0x4d,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45, 0x10, 0x30, 0x12, 0x0e,
	0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x48, 0x4f, 0x42, 0x42, 0x49, 0x45, 0x53, 0x10, 0x31, 0x12, 0x15,
	0x0a, 0x11, 0x4c, 0x32, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x4f,
	0x4f, 0x44, 0x53, 0x10, 0x32, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x32, 0x5f, 0x53, 0x50, 0x4f, 0x52,
	0x54, 0x53, 0x10, 0x33, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x32, 0x5f, 0x4d, 0x4f, 0x56, 0x49, 0x45,
	0x53, 0x10, 0x34, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x32, 0x5f, 0x43, 0x4f, 0x4e, 0x43, 0x45, 0x52,
	0x54, 0x53, 0x10, 0x35, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x32, 0x5f, 0x4d, 0x55, 0x53, 0x49, 0x43,
	0x10, 0x36, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x32, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x37, 0x12, 0x0f, 0x0a,
	0x0b, 0x4c, 0x32, 0x5f, 0x54, 0x56, 0x5f, 0x43, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x38, 0x12, 0x18,
	0x0a, 0x14, 0x4c, 0x32, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4f,
	0x4f, 0x54, 0x57, 0x45, 0x41, 0x52, 0x10, 0x3a, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x32, 0x5f, 0x57,
	0x45, 0x41, 0x52, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52,
	0x49, 0x45, 0x53, 0x10, 0x3b, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x32, 0x5f, 0x45, 0x4c, 0x45, 0x43,
	0x54, 0x52, 0x4f, 0x4e, 0x49, 0x43, 0x5f, 0x47, 0x41, 0x44, 0x47, 0x45, 0x54, 0x53, 0x10, 0x3c,
	0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x32, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x49, 0x43, 0x41,
	0x4c, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x3d, 0x12, 0x11,
	0x0a, 0x0d, 0x4c, 0x32, 0x5f, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x52, 0x43, 0x45, 0x10,
	0x3e, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x32, 0x5f, 0x46, 0x55, 0x52, 0x4e, 0x49, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x46, 0x55, 0x52, 0x4e, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x3f, 0x12,
	0x11, 0x0a, 0x0d, 0x4c, 0x32, 0x5f, 0x44, 0x45, 0x43, 0x4f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x40, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x32, 0x5f, 0x4a, 0x45, 0x57, 0x45, 0x4c, 0x4c, 0x45,
	0x52, 0x59, 0x10, 0x41, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x32, 0x5f, 0x43, 0x41, 0x46, 0x45, 0x5f,
	0x43, 0x4f, 0x46, 0x46, 0x45, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x50, 0x10, 0x43, 0x12, 0x12, 0x0a,
	0x0e, 0x4c, 0x32, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x41, 0x55, 0x52, 0x41, 0x4e, 0x54, 0x53, 0x10,
	0x44, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x32, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x44, 0x45, 0x4c,
	0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x32, 0x5f, 0x47, 0x52,
	0x4f, 0x43, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x46, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x32, 0x5f,
	0x41, 0x45, 0x53, 0x54, 0x48, 0x45, 0x54, 0x49, 0x43, 0x5f, 0x54, 0x52, 0x45, 0x41, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x53, 0x10, 0x48, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x32, 0x5f, 0x42, 0x41, 0x52,
	0x42, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x49, 0x52, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x49, 0x53, 0x54,
	0x5f, 0x53, 0x41, 0x4c, 0x4f, 0x4e, 0x10, 0x49, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x32, 0x5f, 0x53,
	0x50, 0x41, 0x5f, 0x4d, 0x41, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x4a, 0x12, 0x1b, 0x0a, 0x17,
	0x4c, 0x32, 0x5f, 0x43, 0x4f, 0x53, 0x4d, 0x45, 0x54, 0x49, 0x43, 0x53, 0x5f, 0x54, 0x4f, 0x49,
	0x4c, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10, 0x4b, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x32, 0x5f,
	0x47, 0x59, 0x4d, 0x10, 0x4c, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x32, 0x5f, 0x4d, 0x45, 0x4e, 0x54,
	0x41, 0x4c, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x57, 0x45, 0x4c, 0x4c, 0x4e, 0x45,
	0x53, 0x53, 0x10, 0x4d, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x53, 0x54, 0x10, 0x4e, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x32, 0x5f, 0x44, 0x4f, 0x43, 0x54, 0x4f,
	0x52, 0x10, 0x4f, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x32, 0x5f, 0x45, 0x59, 0x45, 0x5f, 0x43, 0x41,
	0x52, 0x45, 0x10, 0x50, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x32, 0x5f, 0x48, 0x4f, 0x53, 0x50, 0x49,
	0x54, 0x41, 0x4c, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x4e, 0x53, 0x45, 0x53, 0x10, 0x51, 0x12, 0x1b,
	0x0a, 0x17, 0x4c, 0x32, 0x5f, 0x4c, 0x41, 0x55, 0x4e, 0x44, 0x52, 0x59, 0x5f, 0x44, 0x52, 0x59,
	0x5f, 0x43, 0x4c, 0x45, 0x41, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x52, 0x12, 0x0f, 0x0a, 0x0b, 0x4c,
	0x32, 0x5f, 0x50, 0x48, 0x41, 0x52, 0x4d, 0x41, 0x43, 0x59, 0x10, 0x53, 0x12, 0x11, 0x0a, 0x0d,
	0x4c, 0x32, 0x5f, 0x41, 0x49, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x10, 0x55, 0x12,
	0x13, 0x0a, 0x0f, 0x4c, 0x32, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x56,
	0x45, 0x4c, 0x10, 0x56, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x32, 0x5f, 0x42, 0x55, 0x53, 0x10, 0x57,
	0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x32, 0x5f, 0x52, 0x45, 0x4e, 0x54, 0x41, 0x4c, 0x5f, 0x43, 0x41,
	0x52, 0x10, 0x58, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x32, 0x5f, 0x43, 0x41, 0x42, 0x5f, 0x52, 0x49,
	0x44, 0x45, 0x10, 0x59, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x32, 0x5f, 0x52, 0x49, 0x44, 0x45, 0x5f,
	0x48, 0x41, 0x49, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x5a, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x32, 0x5f,
	0x54, 0x4f, 0x4c, 0x4c, 0x53, 0x10, 0x5b, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x32, 0x5f, 0x46, 0x55,
	0x45, 0x4c, 0x10, 0x5c, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x50, 0x41, 0x52, 0x4b, 0x49,
	0x4e, 0x47, 0x10, 0x5d, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x32, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x10, 0x5e, 0x12,
	0x10, 0x0a, 0x0c, 0x4c, 0x32, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x53, 0x10,
	0x5f, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x32, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x61, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x32,
	0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x4e, 0x53,
	0x45, 0x53, 0x10, 0x62, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x49,
	0x54, 0x59, 0x10, 0x63, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x32, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x52, 0x4f, 0x4e, 0x49, 0x43, 0x53, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45, 0x10,
	0x64, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x32, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x45, 0x53, 0x54, 0x10, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x32, 0x5f, 0x47, 0x49,
	0x46, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x66, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x32, 0x5f,
	0x54, 0x41, 0x58, 0x45, 0x53, 0x10, 0x67, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x32, 0x5f, 0x48, 0x4f,
	0x54, 0x45, 0x4c, 0x10, 0x68, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x32, 0x5f, 0x54, 0x52, 0x41, 0x56,
	0x45, 0x4c, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x69, 0x12, 0x17, 0x0a, 0x13, 0x4c,
	0x32, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41,
	0x4c, 0x53, 0x10, 0x6a, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x32, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45,
	0x54, 0x10, 0x6c, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x32, 0x5f, 0x43, 0x52, 0x59, 0x50, 0x54, 0x4f,
	0x10, 0x6d, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x32, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f,
	0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x53,
	0x10, 0x6e, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x32, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x10, 0x6f, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x32, 0x5f,
	0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x70, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x32, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54,
	0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x71, 0x12, 0x12,
	0x0a, 0x0e, 0x4c, 0x32, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x72, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x32, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x49,
	0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x53, 0x10, 0x73, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x32, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x53,
	0x5f, 0x47, 0x41, 0x4d, 0x45, 0x53, 0x10, 0x74, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x32, 0x5f, 0x46,
	0x41, 0x4d, 0x49, 0x4c, 0x59, 0x5f, 0x43, 0x48, 0x49, 0x4c, 0x44, 0x43, 0x41, 0x52, 0x45, 0x10,
	0x75, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x32, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x44, 0x52, 0x49,
	0x4e, 0x4b, 0x53, 0x10, 0x76, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x32, 0x5f, 0x47, 0x49, 0x46, 0x54,
	0x53, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x49, 0x54, 0x59, 0x10, 0x77, 0x12, 0x1b, 0x0a, 0x17, 0x4c,
	0x32, 0x5f, 0x47, 0x52, 0x4f, 0x43, 0x45, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x53, 0x53, 0x45,
	0x4e, 0x54, 0x49, 0x41, 0x4c, 0x53, 0x10, 0x78, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x55,
	0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x32, 0x5f, 0x52,
	0x45, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42, 0x41, 0x43, 0x4b, 0x53,
	0x10, 0x7a, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x54, 0x4f, 0x5f, 0x53, 0x45, 0x4c, 0x46,
	0x10, 0x7b, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x32, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x7c, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x32, 0x5f, 0x53,
	0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45, 0x10, 0x7d, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x32, 0x5f,
	0x43, 0x4c, 0x4f, 0x54, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f,
	0x52, 0x49, 0x45, 0x53, 0x10, 0x7e, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x32, 0x5f, 0x45, 0x4c, 0x45,
	0x43, 0x54, 0x52, 0x4f, 0x4e, 0x49, 0x43, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x41, 0x4e,
	0x43, 0x45, 0x53, 0x10, 0x7f, 0x12, 0x1b, 0x0a, 0x16, 0x4c, 0x32, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x53, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10,
	0x80, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x4c, 0x32, 0x5f, 0x54, 0x41, 0x58, 0x5f, 0x50, 0x52, 0x45,
	0x50, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x10, 0x81, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x4c, 0x32, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45,
	0x5f, 0x54, 0x41, 0x58, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x82, 0x01, 0x12,
	0x15, 0x0a, 0x10, 0x4c, 0x32, 0x5f, 0x54, 0x44, 0x53, 0x5f, 0x41, 0x54, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x10, 0x83, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x4c, 0x32, 0x5f, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x84, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x4c,
	0x32, 0x5f, 0x54, 0x4f, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x85, 0x01, 0x12, 0x17,
	0x0a, 0x12, 0x4c, 0x32, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x56, 0x41, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x86, 0x01, 0x12, 0x0f, 0x0a, 0x0a, 0x4c, 0x32, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x55, 0x54, 0x45, 0x10, 0x87, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4c, 0x32, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x88, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x4c, 0x32, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x53, 0x10,
	0x89, 0x01, 0x2a, 0xbe, 0x19, 0x0a, 0x02, 0x4c, 0x33, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a,
	0x15, 0x4c, 0x33, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x33, 0x5f, 0x42,
	0x4f, 0x4e, 0x55, 0x53, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x33, 0x5f, 0x46, 0x44, 0x5f,
	0x52, 0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x43,
	0x49, 0x50, 0x41, 0x4c, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53,
	0x54, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x33, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45,
	0x53, 0x54, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x46, 0x44, 0x53, 0x10, 0x05, 0x12, 0x25, 0x0a,
	0x21, 0x4c, 0x33, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x53, 0x10, 0x06, 0x12, 0x2c, 0x0a, 0x28, 0x4c, 0x33, 0x5f, 0x53, 0x49, 0x44, 0x5f, 0x52,
	0x45, 0x44, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x43, 0x49,
	0x50, 0x41, 0x4c, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54,
	0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x33, 0x5f, 0x44, 0x49, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x44, 0x53, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x33, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49,
	0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x53, 0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x33, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f,
	0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x10, 0x0a, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x33, 0x5f, 0x50,
	0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x33, 0x5f, 0x50, 0x55,
	0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x0c,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f, 0x54, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x53, 0x10, 0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x33, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x42,
	0x41, 0x43, 0x4b, 0x53, 0x10, 0x0e, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x33, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x50,
	0x49, 0x54, 0x41, 0x4c, 0x49, 0x53, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x33,
	0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x10, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x33, 0x5f, 0x4d,
	0x41, 0x52, 0x4b, 0x45, 0x54, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x4d, 0x46, 0x53,
	0x10, 0x11, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x33, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x49,
	0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x4d, 0x46, 0x53, 0x10, 0x12, 0x12, 0x14, 0x0a, 0x10, 0x4c,
	0x33, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x4d, 0x49, 0x10,
	0x13, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x33, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x14, 0x12, 0x19,
	0x0a, 0x15, 0x4c, 0x33, 0x5f, 0x45, 0x44, 0x55, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x4d, 0x49, 0x10, 0x15, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x33, 0x5f,
	0x45, 0x44, 0x55, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x16, 0x12, 0x14, 0x0a, 0x10, 0x4c,
	0x33, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x4d, 0x49, 0x10,
	0x17, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x33, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x18, 0x12, 0x18,
	0x0a, 0x14, 0x4c, 0x33, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x45, 0x4d, 0x49, 0x10, 0x19, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x33, 0x5f, 0x50,
	0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x45,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1a, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x33, 0x5f,
	0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x45, 0x4d, 0x49, 0x10,
	0x1b, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x33, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1c,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x49, 0x43, 0x49,
	0x54, 0x59, 0x10, 0x1d, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f, 0x47, 0x41, 0x53, 0x5f, 0x45,
	0x47, 0x5f, 0x4c, 0x50, 0x47, 0x5f, 0x10, 0x1e, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x33, 0x5f, 0x57,
	0x41, 0x54, 0x45, 0x52, 0x10, 0x1f, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x33, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x45, 0x54, 0x10, 0x20, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x33, 0x5f, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x10, 0x21, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x33, 0x5f, 0x4d, 0x41, 0x53, 0x4f,
	0x4e, 0x52, 0x59, 0x10, 0x23, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x33, 0x5f, 0x43, 0x41, 0x52, 0x50,
	0x45, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x24, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x33, 0x5f, 0x45, 0x4c,
	0x45, 0x43, 0x54, 0x52, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x25, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x33,
	0x5f, 0x47, 0x41, 0x52, 0x44, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x26, 0x12, 0x0f, 0x0a, 0x0b,
	0x4c, 0x33, 0x5f, 0x50, 0x4c, 0x55, 0x4d, 0x42, 0x49, 0x4e, 0x47, 0x10, 0x27, 0x12, 0x0e, 0x0a,
	0x0a, 0x4c, 0x33, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x49, 0x52, 0x53, 0x10, 0x28, 0x12, 0x14, 0x0a,
	0x10, 0x4c, 0x33, 0x5f, 0x42, 0x41, 0x42, 0x59, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45,
	0x53, 0x10, 0x2a, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x33, 0x5f, 0x43, 0x48, 0x49, 0x4c, 0x44, 0x43,
	0x41, 0x52, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x2b, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x33,
	0x5f, 0x43, 0x4f, 0x4f, 0x4b, 0x10, 0x2c, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x33, 0x5f, 0x44, 0x52,
	0x49, 0x56, 0x45, 0x52, 0x10, 0x2d, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x33, 0x5f, 0x4d, 0x41, 0x49,
	0x44, 0x10, 0x2e, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x33, 0x5f, 0x53, 0x43, 0x48, 0x4f, 0x4f, 0x4c,
	0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x10, 0x2f, 0x12, 0x13, 0x0a, 0x0f, 0x4c,
	0x33, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x47, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x30,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f, 0x53, 0x43, 0x48, 0x4f, 0x4f, 0x4c, 0x5f, 0x46, 0x45,
	0x45, 0x53, 0x10, 0x31, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x33, 0x5f, 0x54, 0x55, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x32, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x33, 0x5f,
	0x46, 0x4f, 0x4f, 0x44, 0x10, 0x33, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x33, 0x5f, 0x56, 0x45, 0x54,
	0x10, 0x34, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x33, 0x5f, 0x4d, 0x49, 0x53, 0x43, 0x10, 0x35, 0x12,
	0x0c, 0x0a, 0x08, 0x4c, 0x33, 0x5f, 0x4d, 0x4f, 0x4f, 0x43, 0x53, 0x10, 0x36, 0x12, 0x11, 0x0a,
	0x0d, 0x4c, 0x33, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x42, 0x41, 0x43, 0x4b, 0x53, 0x10, 0x37,
	0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x33, 0x5f, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x10, 0x38,
	0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x33, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x42, 0x4f, 0x4f, 0x4b,
	0x53, 0x10, 0x39, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x33, 0x5f, 0x4d, 0x41, 0x47, 0x41, 0x5a, 0x49,
	0x4e, 0x45, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4e, 0x45, 0x57, 0x53, 0x50, 0x41, 0x50, 0x45,
	0x52, 0x53, 0x10, 0x3a, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x33, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x44,
	0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x3c, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x33, 0x5f,
	0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x53,
	0x10, 0x3d, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f, 0x41, 0x44, 0x56, 0x45, 0x52, 0x54, 0x49,
	0x53, 0x49, 0x4e, 0x47, 0x10, 0x3e, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x33, 0x5f, 0x42, 0x55, 0x53,
	0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45, 0x10, 0x3f,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x40, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x33, 0x5f, 0x4c, 0x45, 0x47, 0x41, 0x4c,
	0x10, 0x41, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x33, 0x5f, 0x4f, 0x46, 0x46, 0x49, 0x43, 0x45, 0x5f,
	0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x10, 0x42, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x33,
	0x5f, 0x50, 0x52, 0x49, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x43, 0x12, 0x0f, 0x0a, 0x0b, 0x4c,
	0x33, 0x5f, 0x53, 0x48, 0x49, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x44, 0x12, 0x12, 0x0a, 0x0e,
	0x4c, 0x33, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x53, 0x10, 0x46,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f, 0x41, 0x4e, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x45,
	0x45, 0x53, 0x10, 0x48, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x33, 0x5f, 0x42, 0x45, 0x4c, 0x4f, 0x57,
	0x5f, 0x4d, 0x41, 0x42, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x49, 0x12, 0x10, 0x0a, 0x0c, 0x4c,
	0x33, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x4a, 0x12, 0x19, 0x0a,
	0x15, 0x4c, 0x33, 0x5f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x43,
	0x45, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x4b, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x33, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x4c, 0x12, 0x10,
	0x0a, 0x0c, 0x4c, 0x33, 0x5f, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x4d,
	0x12, 0x15, 0x0a, 0x11, 0x4c, 0x33, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x44, 0x52, 0x41, 0x46, 0x54,
	0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x4e, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x33, 0x5f, 0x54, 0x41,
	0x58, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x50, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x33, 0x5f, 0x49, 0x4e,
	0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x54, 0x41, 0x58, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x51, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x33, 0x5f, 0x54, 0x44, 0x53, 0x5f, 0x41, 0x54, 0x5f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x52, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x33, 0x5f, 0x41,
	0x54, 0x4d, 0x10, 0x53, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x33, 0x5f, 0x42, 0x52, 0x41, 0x4e, 0x43,
	0x48, 0x10, 0x54, 0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x33, 0x5f, 0x53, 0x44, 0x5f, 0x52, 0x45, 0x44,
	0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x43, 0x49, 0x50, 0x41,
	0x4c, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x10, 0x55,
	0x12, 0x2b, 0x0a, 0x27, 0x4c, 0x33, 0x5f, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x4d, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x43, 0x49, 0x50, 0x41, 0x4c, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x10, 0x56, 0x12, 0x0b, 0x0a,
	0x07, 0x4c, 0x33, 0x5f, 0x41, 0x52, 0x54, 0x53, 0x10, 0x57, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x33,
	0x5f, 0x48, 0x4f, 0x42, 0x42, 0x49, 0x45, 0x53, 0x10, 0x58, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x33,
	0x5f, 0x4d, 0x4f, 0x56, 0x49, 0x45, 0x53, 0x10, 0x59, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x33, 0x5f,
	0x43, 0x4f, 0x4e, 0x43, 0x45, 0x52, 0x54, 0x53, 0x10, 0x5a, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x33,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x5b, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x33, 0x5f, 0x43, 0x52, 0x59,
	0x50, 0x54, 0x4f, 0x10, 0x5c, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x33, 0x5f, 0x46, 0x49, 0x58, 0x45,
	0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x10, 0x5d, 0x12, 0x19, 0x0a, 0x15,
	0x4c, 0x33, 0x5f, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x50,
	0x4f, 0x53, 0x49, 0x54, 0x53, 0x10, 0x5e, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x33, 0x5f, 0x53, 0x4d,
	0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x10, 0x5f, 0x12, 0x0a,
	0x0a, 0x06, 0x4c, 0x33, 0x5f, 0x50, 0x50, 0x46, 0x10, 0x60, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x33,
	0x5f, 0x50, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10,
	0x61, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x33, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x5f, 0x43, 0x55,
	0x52, 0x52, 0x49, 0x43, 0x55, 0x4c, 0x41, 0x52, 0x10, 0x62, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x33,
	0x5f, 0x47, 0x41, 0x4d, 0x45, 0x53, 0x10, 0x63, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x33, 0x5f, 0x47,
	0x41, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45, 0x10, 0x64,
	0x12, 0x15, 0x0a, 0x11, 0x4c, 0x33, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x33, 0x5f, 0x53, 0x50,
	0x4f, 0x52, 0x54, 0x53, 0x10, 0x66, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x33, 0x5f, 0x50, 0x4f, 0x43,
	0x4b, 0x45, 0x54, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0x67, 0x12, 0x19, 0x0a, 0x15, 0x4c,
	0x33, 0x5f, 0x41, 0x4c, 0x43, 0x4f, 0x48, 0x4f, 0x4c, 0x5f, 0x43, 0x49, 0x47, 0x41, 0x52, 0x45,
	0x54, 0x54, 0x45, 0x53, 0x10, 0x68, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x33, 0x5f, 0x43, 0x41, 0x46,
	0x45, 0x5f, 0x43, 0x4f, 0x46, 0x46, 0x45, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x50, 0x10, 0x69, 0x12,
	0x12, 0x0a, 0x0e, 0x4c, 0x33, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x41, 0x55, 0x52, 0x41, 0x4e, 0x54,
	0x53, 0x10, 0x6a, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x33, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x44,
	0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x6b, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x33, 0x5f,
	0x43, 0x48, 0x41, 0x52, 0x49, 0x54, 0x59, 0x10, 0x6c, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x33, 0x5f,
	0x47, 0x49, 0x46, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53,
	0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x6d, 0x12, 0x10, 0x0a, 0x0c, 0x4c,
	0x33, 0x5f, 0x47, 0x52, 0x4f, 0x43, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x6e, 0x12, 0x0f, 0x0a,
	0x0b, 0x4c, 0x33, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x45, 0x52, 0x54, 0x59, 0x10, 0x6f, 0x12, 0x13,
	0x0a, 0x0f, 0x4c, 0x33, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x4e,
	0x54, 0x10, 0x70, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x33, 0x5f, 0x52, 0x45, 0x4e, 0x54, 0x41, 0x4c,
	0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x71, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x33,
	0x5f, 0x47, 0x41, 0x53, 0x10, 0x72, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x33, 0x5f, 0x46, 0x55, 0x52,
	0x4e, 0x49, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x46, 0x55, 0x52, 0x4e, 0x49, 0x53, 0x48, 0x49, 0x4e,
	0x47, 0x53, 0x10, 0x73, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x33, 0x5f, 0x44, 0x4f, 0x4d, 0x45, 0x53,
	0x54, 0x49, 0x43, 0x5f, 0x48, 0x45, 0x4c, 0x50, 0x10, 0x74, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x33,
	0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x52, 0x45, 0x4e,
	0x54, 0x10, 0x75, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x33, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c,
	0x59, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x53, 0x10, 0x76, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x33, 0x5f, 0x4d, 0x55,
	0x53, 0x49, 0x43, 0x10, 0x77, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x33, 0x5f, 0x53, 0x54, 0x52, 0x45,
	0x41, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x78,
	0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x33, 0x5f, 0x54, 0x56, 0x5f, 0x43, 0x41, 0x42, 0x4c, 0x45, 0x10,
	0x79, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x33, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x49, 0x4e, 0x47,
	0x5f, 0x46, 0x4f, 0x4f, 0x54, 0x57, 0x45, 0x41, 0x52, 0x10, 0x7a, 0x12, 0x1b, 0x0a, 0x17, 0x4c,
	0x33, 0x5f, 0x57, 0x45, 0x41, 0x52, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10, 0x7b, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x33, 0x5f, 0x4a,
	0x45, 0x57, 0x45, 0x4c, 0x4c, 0x45, 0x52, 0x59, 0x10, 0x7c, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x33,
	0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x4f, 0x4e, 0x49, 0x43, 0x5f, 0x47, 0x41, 0x44, 0x47,
	0x45, 0x54, 0x53, 0x10, 0x7d, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x33, 0x5f, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x41,
	0x4d, 0x43, 0x53, 0x10, 0x7e, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x33, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x41, 0x43, 0x10, 0x7f, 0x12, 0x23, 0x0a, 0x1e, 0x4c, 0x33, 0x5f, 0x4d, 0x46, 0x5f, 0x53, 0x54,
	0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x80, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x4c, 0x33, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x81, 0x01, 0x12, 0x0a, 0x0a,
	0x05, 0x4c, 0x33, 0x5f, 0x4e, 0x41, 0x10, 0x82, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x4c, 0x33, 0x5f,
	0x48, 0x4f, 0x54, 0x45, 0x4c, 0x10, 0x84, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x4c, 0x33, 0x5f, 0x54,
	0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x85, 0x01, 0x12,
	0x12, 0x0a, 0x0d, 0x4c, 0x33, 0x5f, 0x41, 0x49, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c,
	0x10, 0x86, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x4c, 0x33, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x5f,
	0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x10, 0x87, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x4c, 0x33, 0x5f,
	0x42, 0x55, 0x53, 0x10, 0x88, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x4c, 0x33, 0x5f, 0x52, 0x45, 0x4e,
	0x54, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x10, 0x89, 0x01, 0x12, 0x10, 0x0a, 0x0b, 0x4c, 0x33,
	0x5f, 0x43, 0x41, 0x42, 0x5f, 0x52, 0x49, 0x44, 0x45, 0x10, 0x8a, 0x01, 0x12, 0x14, 0x0a, 0x0f,
	0x4c, 0x33, 0x5f, 0x52, 0x49, 0x44, 0x45, 0x5f, 0x48, 0x41, 0x49, 0x4c, 0x49, 0x4e, 0x47, 0x10,
	0x8b, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x4c, 0x33, 0x5f, 0x54, 0x4f, 0x4c, 0x4c, 0x53, 0x10, 0x8c,
	0x01, 0x12, 0x0c, 0x0a, 0x07, 0x4c, 0x33, 0x5f, 0x46, 0x55, 0x45, 0x4c, 0x10, 0x8d, 0x01, 0x12,
	0x0f, 0x0a, 0x0a, 0x4c, 0x33, 0x5f, 0x50, 0x41, 0x52, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x8e, 0x01,
	0x12, 0x1a, 0x0a, 0x15, 0x4c, 0x33, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x10, 0x8f, 0x01, 0x12, 0x11, 0x0a, 0x0c,
	0x4c, 0x33, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x53, 0x10, 0x90, 0x01, 0x12,
	0x31, 0x0a, 0x2c, 0x4c, 0x33, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x41, 0x4c, 0x5f, 0x4d, 0x46, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x42, 0x4f,
	0x4e, 0x44, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10,
	0x91, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4c, 0x33, 0x5f, 0x45, 0x43, 0x53, 0x5f, 0x42, 0x4f, 0x55,
	0x4e, 0x43, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x92, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4c,
	0x33, 0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x45, 0x45,
	0x53, 0x10, 0x93, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x4c, 0x33, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52,
	0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x94, 0x01, 0x12,
	0x17, 0x0a, 0x12, 0x4c, 0x33, 0x5f, 0x41, 0x43, 0x48, 0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x43, 0x45,
	0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x95, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x4c, 0x33, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x43,
	0x45, 0x5f, 0x46, 0x45, 0x45, 0x53, 0x10, 0x96, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x4c, 0x33, 0x5f,
	0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x41, 0x4c,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x53,
	0x10, 0x97, 0x01, 0x2a, 0x8c, 0x05, 0x0a, 0x14, 0x54, 0x78, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x23,
	0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x54, 0x58, 0x4e,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12,
	0x27, 0x0a, 0x23, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c,
	0x4f, 0x47, 0x59, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28, 0x54, 0x58, 0x4e, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x10, 0x05, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x06, 0x12, 0x26,
	0x0a, 0x22, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x26,
	0x0a, 0x22, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x32, 0x0a, 0x2e, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x44, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x49, 0x53, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x0a, 0x12, 0x29, 0x0a, 0x25, 0x54, 0x58,
	0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x56, 0x45, 0x52, 0x53,
	0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x2e, 0x0a, 0x2a, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x49, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x28, 0x0a, 0x24, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x10, 0x0d, 0x12,
	0x31, 0x0a, 0x2d, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x49, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x10, 0x0e, 0x2a, 0xf0, 0x03, 0x0a, 0x1c, 0x54, 0x78, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x4f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x12, 0x30, 0x0a, 0x2c, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f,
	0x47, 0x59, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x54, 0x58, 0x4e, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x30, 0x10, 0x02,
	0x12, 0x27, 0x0a, 0x23, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x31, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x54, 0x58, 0x4e,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f,
	0x47, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x32,
	0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x33, 0x10, 0x05, 0x12, 0x35, 0x0a, 0x31, 0x54,
	0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f,
	0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x10, 0x06, 0x12, 0x2f, 0x0a, 0x2b, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x07, 0x12, 0x2f, 0x0a, 0x2b, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x10, 0x08, 0x12, 0x2f, 0x0a, 0x2b, 0x54, 0x58, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x54, 0x10, 0x09, 0x2a, 0x81, 0x03, 0x0a, 0x1c, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x30, 0x0a, 0x2c, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x35, 0x0a, 0x31, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x49, 0x53,
	0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x01, 0x12,
	0x30, 0x0a, 0x2c, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x4e, 0x54, 0x4f, 0x4c, 0x4f, 0x47, 0x59, 0x5f, 0x49, 0x44, 0x10,
	0x02, 0x12, 0x33, 0x0a, 0x2f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x52, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12, 0x2f, 0x0a, 0x2b, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x04, 0x12, 0x2f, 0x0a, 0x2b, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x2f, 0x0a, 0x2b, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x2a, 0x6f, 0x0a, 0x0b, 0x44, 0x61, 0x74,
	0x61, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43,
	0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x46, 0x49, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x41, 0x41, 0x10, 0x02,
	0x12, 0x18, 0x0a, 0x14, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c,
	0x5f, 0x46, 0x49, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x03, 0x2a, 0x50, 0x0a, 0x14, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e,
	0x0a, 0x0a, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x54, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x02, 0x2a, 0x8d, 0x02, 0x0a,
	0x08, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x54, 0x41, 0x47, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x41,
	0x47, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x41, 0x47, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x44, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x54, 0x41, 0x47, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x41, 0x47, 0x5f,
	0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54,
	0x41, 0x47, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x4d, 0x49, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54,
	0x41, 0x47, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x41,
	0x47, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x48, 0x41,
	0x52, 0x47, 0x45, 0x53, 0x10, 0x08, 0x22, 0x04, 0x08, 0x02, 0x10, 0x02, 0x2a, 0x7b, 0x0a, 0x0c,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x19,
	0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x44,
	0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x41,
	0x42, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41,
	0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x41, 0x4e, 0x59, 0x10, 0x03, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x7a, 0x65, 0x72, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_categorizer_enums_proto_rawDescOnce sync.Once
	file_api_categorizer_enums_proto_rawDescData = file_api_categorizer_enums_proto_rawDesc
)

func file_api_categorizer_enums_proto_rawDescGZIP() []byte {
	file_api_categorizer_enums_proto_rawDescOnce.Do(func() {
		file_api_categorizer_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_categorizer_enums_proto_rawDescData)
	})
	return file_api_categorizer_enums_proto_rawDescData
}

var file_api_categorizer_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 16)
var file_api_categorizer_enums_proto_goTypes = []interface{}{
	(BankName)(0),                     // 0: categorizer.BankName
	(CategorisationSource)(0),         // 1: categorizer.CategorisationSource
	(DisplayCategoryType)(0),          // 2: categorizer.DisplayCategoryType
	(Provenance)(0),                   // 3: categorizer.Provenance
	(DisplayCategory)(0),              // 4: categorizer.DisplayCategory
	(L0)(0),                           // 5: categorizer.L0
	(L1)(0),                           // 6: categorizer.L1
	(L2)(0),                           // 7: categorizer.L2
	(L3)(0),                           // 8: categorizer.L3
	(TxnCategoryFieldMask)(0),         // 9: categorizer.TxnCategoryFieldMask
	(TxnCategoryOntologyFieldMask)(0), // 10: categorizer.TxnCategoryOntologyFieldMask
	(UserDisplayCategoryFieldMask)(0), // 11: categorizer.UserDisplayCategoryFieldMask
	(DataChannel)(0),                  // 12: categorizer.DataChannel
	(TransactionActorType)(0),         // 13: categorizer.TransactionActorType
	(OrderTag)(0),                     // 14: categorizer.OrderTag
	(DisplayState)(0),                 // 15: categorizer.DisplayState
}
var file_api_categorizer_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_categorizer_enums_proto_init() }
func file_api_categorizer_enums_proto_init() {
	if File_api_categorizer_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_categorizer_enums_proto_rawDesc,
			NumEnums:      16,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_categorizer_enums_proto_goTypes,
		DependencyIndexes: file_api_categorizer_enums_proto_depIdxs,
		EnumInfos:         file_api_categorizer_enums_proto_enumTypes,
	}.Build()
	File_api_categorizer_enums_proto = out.File
	file_api_categorizer_enums_proto_rawDesc = nil
	file_api_categorizer_enums_proto_goTypes = nil
	file_api_categorizer_enums_proto_depIdxs = nil
}
