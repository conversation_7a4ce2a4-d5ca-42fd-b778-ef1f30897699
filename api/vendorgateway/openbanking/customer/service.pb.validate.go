// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/openbanking/customer/service.proto

package customer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	dedupe "github.com/epifi/gamma/api/vendorgateway/openbanking/customer/dedupe"

	employment "github.com/epifi/gamma/api/employment"

	kyc "github.com/epifi/gamma/api/kyc"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = dedupe.Flow(0)

	_ = employment.EmploymentType(0)

	_ = kyc.IdProofType(0)

	_ = typesv2.Gender(0)
)

// Validate checks the field values on CreateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerRequestMultiError, or nil if none found.
func (m *CreateCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "CurrentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "CurrentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "CurrentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanNumber

	// no validation rules for Email

	// no validation rules for FatherName

	// no validation rules for MotherName

	if all {
		switch v := interface{}(m.GetIdentityProof()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "IdentityProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "IdentityProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentityProof()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "IdentityProof",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddressProof()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "AddressProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "AddressProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressProof()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "AddressProof",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UidNo

	// no validation rules for SignImage

	// no validation rules for Type

	// no validation rules for AnnualIncome

	// no validation rules for CustomerCreationFlow

	// no validation rules for OccupationType

	// no validation rules for SolId

	// no validation rules for DisabilityType

	// no validation rules for Category

	// no validation rules for PepCategory

	// no validation rules for Qualification

	if len(errors) > 0 {
		return CreateCustomerRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerRequestMultiError) AllErrors() []error { return m }

// CreateCustomerRequestValidationError is the validation error returned by
// CreateCustomerRequest.Validate if the designated constraints aren't met.
type CreateCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerRequestValidationError) ErrorName() string {
	return "CreateCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerRequestValidationError{}

// Validate checks the field values on ProofDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProofDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProofDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProofDetailsMultiError, or
// nil if none found.
func (m *ProofDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ProofDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for IdNumber

	if all {
		switch v := interface{}(m.GetIdIssueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProofDetailsValidationError{
					field:  "IdIssueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProofDetailsValidationError{
					field:  "IdIssueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdIssueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProofDetailsValidationError{
				field:  "IdIssueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIdExpiryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProofDetailsValidationError{
					field:  "IdExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProofDetailsValidationError{
					field:  "IdExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdExpiryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProofDetailsValidationError{
				field:  "IdExpiryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProofDetailsMultiError(errors)
	}

	return nil
}

// ProofDetailsMultiError is an error wrapping multiple validation errors
// returned by ProofDetails.ValidateAll() if the designated constraints aren't met.
type ProofDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProofDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProofDetailsMultiError) AllErrors() []error { return m }

// ProofDetailsValidationError is the validation error returned by
// ProofDetails.Validate if the designated constraints aren't met.
type ProofDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProofDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProofDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProofDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProofDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProofDetailsValidationError) ErrorName() string { return "ProofDetailsValidationError" }

// Error satisfies the builtin error interface
func (e ProofDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProofDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProofDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProofDetailsValidationError{}

// Validate checks the field values on CreateCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerResponseMultiError, or nil if none found.
func (m *CreateCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCustomerResponseMultiError(errors)
	}

	return nil
}

// CreateCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by CreateCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerResponseMultiError) AllErrors() []error { return m }

// CreateCustomerResponseValidationError is the validation error returned by
// CreateCustomerResponse.Validate if the designated constraints aren't met.
type CreateCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerResponseValidationError) ErrorName() string {
	return "CreateCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerResponseValidationError{}

// Validate checks the field values on CreateCustomerForNonResidentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateCustomerForNonResidentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerForNonResidentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateCustomerForNonResidentRequestMultiError, or nil if none found.
func (m *CreateCustomerForNonResidentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerForNonResidentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentRequestValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentRequestValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "CurrentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "CurrentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentRequestValidationError{
				field:  "CurrentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanNumber

	// no validation rules for Email

	// no validation rules for FatherName

	// no validation rules for MotherName

	// no validation rules for EmploymentType

	// no validation rules for CustomerCreationFlow

	// no validation rules for OccupationType

	// no validation rules for SolId

	if all {
		switch v := interface{}(m.GetPassportData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "PassportData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "PassportData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassportData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentRequestValidationError{
				field:  "PassportData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnnualIncomeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "AnnualIncomeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentRequestValidationError{
					field:  "AnnualIncomeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualIncomeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentRequestValidationError{
				field:  "AnnualIncomeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SignImage

	// no validation rules for MaritalStatus

	// no validation rules for Form_60OptedFlag

	if len(errors) > 0 {
		return CreateCustomerForNonResidentRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerForNonResidentRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateCustomerForNonResidentRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerForNonResidentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerForNonResidentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerForNonResidentRequestMultiError) AllErrors() []error { return m }

// CreateCustomerForNonResidentRequestValidationError is the validation error
// returned by CreateCustomerForNonResidentRequest.Validate if the designated
// constraints aren't met.
type CreateCustomerForNonResidentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerForNonResidentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerForNonResidentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerForNonResidentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerForNonResidentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerForNonResidentRequestValidationError) ErrorName() string {
	return "CreateCustomerForNonResidentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerForNonResidentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerForNonResidentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerForNonResidentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerForNonResidentRequestValidationError{}

// Validate checks the field values on CreateCustomerForNonResidentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateCustomerForNonResidentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerForNonResidentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateCustomerForNonResidentResponseMultiError, or nil if none found.
func (m *CreateCustomerForNonResidentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerForNonResidentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerForNonResidentResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerForNonResidentResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCustomerForNonResidentResponseMultiError(errors)
	}

	return nil
}

// CreateCustomerForNonResidentResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateCustomerForNonResidentResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerForNonResidentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerForNonResidentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerForNonResidentResponseMultiError) AllErrors() []error { return m }

// CreateCustomerForNonResidentResponseValidationError is the validation error
// returned by CreateCustomerForNonResidentResponse.Validate if the designated
// constraints aren't met.
type CreateCustomerForNonResidentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerForNonResidentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerForNonResidentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerForNonResidentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerForNonResidentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerForNonResidentResponseValidationError) ErrorName() string {
	return "CreateCustomerForNonResidentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerForNonResidentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerForNonResidentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerForNonResidentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerForNonResidentResponseValidationError{}

// Validate checks the field values on CheckCustomerStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckCustomerStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckCustomerStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckCustomerStatusRequestMultiError, or nil if none found.
func (m *CheckCustomerStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCustomerStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCustomerStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCustomerStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCustomerStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OriginalRequestId

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCustomerStatusRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCustomerStatusRequestValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCustomerStatusRequestValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MobileNumber

	if len(errors) > 0 {
		return CheckCustomerStatusRequestMultiError(errors)
	}

	return nil
}

// CheckCustomerStatusRequestMultiError is an error wrapping multiple
// validation errors returned by CheckCustomerStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckCustomerStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCustomerStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCustomerStatusRequestMultiError) AllErrors() []error { return m }

// CheckCustomerStatusRequestValidationError is the validation error returned
// by CheckCustomerStatusRequest.Validate if the designated constraints aren't met.
type CheckCustomerStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCustomerStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCustomerStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCustomerStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCustomerStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCustomerStatusRequestValidationError) ErrorName() string {
	return "CheckCustomerStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCustomerStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCustomerStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCustomerStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCustomerStatusRequestValidationError{}

// Validate checks the field values on CheckCustomerStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckCustomerStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckCustomerStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckCustomerStatusResponseMultiError, or nil if none found.
func (m *CheckCustomerStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCustomerStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BankCustomerId

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCustomerStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCustomerStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCustomerStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCustomerStatusResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCustomerStatusResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCustomerStatusResponseValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCustomerStatusResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCustomerStatusResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCustomerStatusResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckCustomerStatusResponseMultiError(errors)
	}

	return nil
}

// CheckCustomerStatusResponseMultiError is an error wrapping multiple
// validation errors returned by CheckCustomerStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckCustomerStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCustomerStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCustomerStatusResponseMultiError) AllErrors() []error { return m }

// CheckCustomerStatusResponseValidationError is the validation error returned
// by CheckCustomerStatusResponse.Validate if the designated constraints
// aren't met.
type CheckCustomerStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCustomerStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCustomerStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCustomerStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCustomerStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCustomerStatusResponseValidationError) ErrorName() string {
	return "CheckCustomerStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCustomerStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCustomerStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCustomerStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCustomerStatusResponseValidationError{}

// Validate checks the field values on CheckCustomerStatusForNonResidentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckCustomerStatusForNonResidentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckCustomerStatusForNonResidentRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckCustomerStatusForNonResidentRequestMultiError, or nil if none found.
func (m *CheckCustomerStatusForNonResidentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCustomerStatusForNonResidentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCustomerStatusForNonResidentRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCustomerStatusForNonResidentRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCustomerStatusForNonResidentRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OriginalRequestId

	if len(errors) > 0 {
		return CheckCustomerStatusForNonResidentRequestMultiError(errors)
	}

	return nil
}

// CheckCustomerStatusForNonResidentRequestMultiError is an error wrapping
// multiple validation errors returned by
// CheckCustomerStatusForNonResidentRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckCustomerStatusForNonResidentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCustomerStatusForNonResidentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCustomerStatusForNonResidentRequestMultiError) AllErrors() []error { return m }

// CheckCustomerStatusForNonResidentRequestValidationError is the validation
// error returned by CheckCustomerStatusForNonResidentRequest.Validate if the
// designated constraints aren't met.
type CheckCustomerStatusForNonResidentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCustomerStatusForNonResidentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCustomerStatusForNonResidentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCustomerStatusForNonResidentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCustomerStatusForNonResidentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCustomerStatusForNonResidentRequestValidationError) ErrorName() string {
	return "CheckCustomerStatusForNonResidentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCustomerStatusForNonResidentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCustomerStatusForNonResidentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCustomerStatusForNonResidentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCustomerStatusForNonResidentRequestValidationError{}

// Validate checks the field values on
// CheckCustomerStatusForNonResidentResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckCustomerStatusForNonResidentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckCustomerStatusForNonResidentResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckCustomerStatusForNonResidentResponseMultiError, or nil if none found.
func (m *CheckCustomerStatusForNonResidentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCustomerStatusForNonResidentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCustomerStatusForNonResidentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCustomerStatusForNonResidentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCustomerStatusForNonResidentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BankCustomerId

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCustomerStatusForNonResidentResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCustomerStatusForNonResidentResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCustomerStatusForNonResidentResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckCustomerStatusForNonResidentResponseMultiError(errors)
	}

	return nil
}

// CheckCustomerStatusForNonResidentResponseMultiError is an error wrapping
// multiple validation errors returned by
// CheckCustomerStatusForNonResidentResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckCustomerStatusForNonResidentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCustomerStatusForNonResidentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCustomerStatusForNonResidentResponseMultiError) AllErrors() []error { return m }

// CheckCustomerStatusForNonResidentResponseValidationError is the validation
// error returned by CheckCustomerStatusForNonResidentResponse.Validate if the
// designated constraints aren't met.
type CheckCustomerStatusForNonResidentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCustomerStatusForNonResidentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCustomerStatusForNonResidentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCustomerStatusForNonResidentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCustomerStatusForNonResidentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCustomerStatusForNonResidentResponseValidationError) ErrorName() string {
	return "CheckCustomerStatusForNonResidentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCustomerStatusForNonResidentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCustomerStatusForNonResidentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCustomerStatusForNonResidentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCustomerStatusForNonResidentResponseValidationError{}

// Validate checks the field values on DedupeCheckRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DedupeCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DedupeCheckRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DedupeCheckRequestMultiError, or nil if none found.
func (m *DedupeCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DedupeCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DedupeCheckRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DedupeCheckRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DedupeCheckRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanNumber

	// no validation rules for AadhaarNumber

	// no validation rules for PassportNumber

	// no validation rules for DrivingLicense

	// no validation rules for VoterId

	// no validation rules for MobileNum

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DedupeCheckRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DedupeCheckRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DedupeCheckRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserId

	// no validation rules for UidReferenceKey

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DedupeCheckRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DedupeCheckRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DedupeCheckRequestValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for EmailId

	// no validation rules for Flow

	if len(errors) > 0 {
		return DedupeCheckRequestMultiError(errors)
	}

	return nil
}

// DedupeCheckRequestMultiError is an error wrapping multiple validation errors
// returned by DedupeCheckRequest.ValidateAll() if the designated constraints
// aren't met.
type DedupeCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DedupeCheckRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DedupeCheckRequestMultiError) AllErrors() []error { return m }

// DedupeCheckRequestValidationError is the validation error returned by
// DedupeCheckRequest.Validate if the designated constraints aren't met.
type DedupeCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DedupeCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DedupeCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DedupeCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DedupeCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DedupeCheckRequestValidationError) ErrorName() string {
	return "DedupeCheckRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DedupeCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDedupeCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DedupeCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DedupeCheckRequestValidationError{}

// Validate checks the field values on DedupeCheckResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DedupeCheckResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DedupeCheckResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DedupeCheckResponseMultiError, or nil if none found.
func (m *DedupeCheckResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DedupeCheckResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DobFlag

	// no validation rules for MobileFlag

	// no validation rules for CustomerId

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DedupeCheckResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DedupeCheckResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DedupeCheckResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerName

	// no validation rules for DedupeStatus

	// no validation rules for CustomersLinkedWithPhoneNumber

	// no validation rules for RawResponse

	// no validation rules for KYCFlag

	// no validation rules for CreditCardFlag

	// no validation rules for CustomersLinkedWithEmailId

	// no validation rules for PanFlag

	// no validation rules for AadhaarFlag

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DedupeCheckResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DedupeCheckResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DedupeCheckResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DedupeCheckResponseMultiError(errors)
	}

	return nil
}

// DedupeCheckResponseMultiError is an error wrapping multiple validation
// errors returned by DedupeCheckResponse.ValidateAll() if the designated
// constraints aren't met.
type DedupeCheckResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DedupeCheckResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DedupeCheckResponseMultiError) AllErrors() []error { return m }

// DedupeCheckResponseValidationError is the validation error returned by
// DedupeCheckResponse.Validate if the designated constraints aren't met.
type DedupeCheckResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DedupeCheckResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DedupeCheckResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DedupeCheckResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DedupeCheckResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DedupeCheckResponseValidationError) ErrorName() string {
	return "DedupeCheckResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DedupeCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDedupeCheckResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DedupeCheckResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DedupeCheckResponseValidationError{}

// Validate checks the field values on FetchCustomerDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCustomerDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCustomerDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCustomerDetailsRequestMultiError, or nil if none found.
func (m *FetchCustomerDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCustomerDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ChannelType

	if len(errors) > 0 {
		return FetchCustomerDetailsRequestMultiError(errors)
	}

	return nil
}

// FetchCustomerDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by FetchCustomerDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type FetchCustomerDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCustomerDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCustomerDetailsRequestMultiError) AllErrors() []error { return m }

// FetchCustomerDetailsRequestValidationError is the validation error returned
// by FetchCustomerDetailsRequest.Validate if the designated constraints
// aren't met.
type FetchCustomerDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCustomerDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCustomerDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCustomerDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCustomerDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCustomerDetailsRequestValidationError) ErrorName() string {
	return "FetchCustomerDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCustomerDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCustomerDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCustomerDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCustomerDetailsRequestValidationError{}

// Validate checks the field values on FetchCustomerDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchCustomerDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchCustomerDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchCustomerDetailsResponseMultiError, or nil if none found.
func (m *FetchCustomerDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchCustomerDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCustomerName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "CustomerName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "CustomerName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "CustomerName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetFatherOrHusbandName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "FatherOrHusbandName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "FatherOrHusbandName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherOrHusbandName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "FatherOrHusbandName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MotherMaidenName

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmailId

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaritalStatus

	// no validation rules for Nre

	// no validation rules for Occupation

	if all {
		switch v := interface{}(m.GetCommunicationAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "CommunicationAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "CommunicationAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommunicationAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "CommunicationAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShippingAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "ShippingAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "ShippingAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShippingAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "ShippingAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Salutation

	for idx, item := range m.GetAccountList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchCustomerDetailsResponseValidationError{
						field:  fmt.Sprintf("AccountList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchCustomerDetailsResponseValidationError{
						field:  fmt.Sprintf("AccountList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchCustomerDetailsResponseValidationError{
					field:  fmt.Sprintf("AccountList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for OccupationType

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchCustomerDetailsResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchCustomerDetailsResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchCustomerDetailsResponseMultiError(errors)
	}

	return nil
}

// FetchCustomerDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by FetchCustomerDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type FetchCustomerDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchCustomerDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchCustomerDetailsResponseMultiError) AllErrors() []error { return m }

// FetchCustomerDetailsResponseValidationError is the validation error returned
// by FetchCustomerDetailsResponse.Validate if the designated constraints
// aren't met.
type FetchCustomerDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchCustomerDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchCustomerDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchCustomerDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchCustomerDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchCustomerDetailsResponseValidationError) ErrorName() string {
	return "FetchCustomerDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchCustomerDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchCustomerDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchCustomerDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchCustomerDetailsResponseValidationError{}

// Validate checks the field values on VendorAddress with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VendorAddress) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorAddress with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VendorAddressMultiError, or
// nil if none found.
func (m *VendorAddress) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorAddress) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AddressLine1

	// no validation rules for AddressLine2

	// no validation rules for CityCode

	// no validation rules for StateCode

	// no validation rules for CountryCode

	// no validation rules for PinCode

	if len(errors) > 0 {
		return VendorAddressMultiError(errors)
	}

	return nil
}

// VendorAddressMultiError is an error wrapping multiple validation errors
// returned by VendorAddress.ValidateAll() if the designated constraints
// aren't met.
type VendorAddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorAddressMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorAddressMultiError) AllErrors() []error { return m }

// VendorAddressValidationError is the validation error returned by
// VendorAddress.Validate if the designated constraints aren't met.
type VendorAddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorAddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorAddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorAddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorAddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorAddressValidationError) ErrorName() string { return "VendorAddressValidationError" }

// Error satisfies the builtin error interface
func (e VendorAddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorAddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorAddressValidationError{}

// Validate checks the field values on Account with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Account with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AccountMultiError, or nil if none found.
func (m *Account) ValidateAll() error {
	return m.validate(true)
}

func (m *Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for AccountType

	// no validation rules for SchemeCode

	if len(errors) > 0 {
		return AccountMultiError(errors)
	}

	return nil
}

// AccountMultiError is an error wrapping multiple validation errors returned
// by Account.ValidateAll() if the designated constraints aren't met.
type AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountMultiError) AllErrors() []error { return m }

// AccountValidationError is the validation error returned by Account.Validate
// if the designated constraints aren't met.
type AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountValidationError) ErrorName() string { return "AccountValidationError" }

// Error satisfies the builtin error interface
func (e AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountValidationError{}

// Validate checks the field values on CheckProductEligibilityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckProductEligibilityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckProductEligibilityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckProductEligibilityRequestMultiError, or nil if none found.
func (m *CheckProductEligibilityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckProductEligibilityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckProductEligibilityRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckProductEligibilityRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckProductEligibilityRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetMobileNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckProductEligibilityRequestValidationError{
					field:  "MobileNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckProductEligibilityRequestValidationError{
					field:  "MobileNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobileNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckProductEligibilityRequestValidationError{
				field:  "MobileNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	// no validation rules for DeviceId

	// no validation rules for ProductType

	if len(errors) > 0 {
		return CheckProductEligibilityRequestMultiError(errors)
	}

	return nil
}

// CheckProductEligibilityRequestMultiError is an error wrapping multiple
// validation errors returned by CheckProductEligibilityRequest.ValidateAll()
// if the designated constraints aren't met.
type CheckProductEligibilityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckProductEligibilityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckProductEligibilityRequestMultiError) AllErrors() []error { return m }

// CheckProductEligibilityRequestValidationError is the validation error
// returned by CheckProductEligibilityRequest.Validate if the designated
// constraints aren't met.
type CheckProductEligibilityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckProductEligibilityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckProductEligibilityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckProductEligibilityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckProductEligibilityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckProductEligibilityRequestValidationError) ErrorName() string {
	return "CheckProductEligibilityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckProductEligibilityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckProductEligibilityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckProductEligibilityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckProductEligibilityRequestValidationError{}

// Validate checks the field values on CheckProductEligibilityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckProductEligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckProductEligibilityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckProductEligibilityResponseMultiError, or nil if none found.
func (m *CheckProductEligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckProductEligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckProductEligibilityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckProductEligibilityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckProductEligibilityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationReferenceId

	// no validation rules for DedupeStatus

	// no validation rules for IsEligible

	// no validation rules for EkycToken

	// no validation rules for EkycUrl

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckProductEligibilityResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckProductEligibilityResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckProductEligibilityResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckProductEligibilityResponseMultiError(errors)
	}

	return nil
}

// CheckProductEligibilityResponseMultiError is an error wrapping multiple
// validation errors returned by CheckProductEligibilityResponse.ValidateAll()
// if the designated constraints aren't met.
type CheckProductEligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckProductEligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckProductEligibilityResponseMultiError) AllErrors() []error { return m }

// CheckProductEligibilityResponseValidationError is the validation error
// returned by CheckProductEligibilityResponse.Validate if the designated
// constraints aren't met.
type CheckProductEligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckProductEligibilityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckProductEligibilityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckProductEligibilityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckProductEligibilityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckProductEligibilityResponseValidationError) ErrorName() string {
	return "CheckProductEligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckProductEligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckProductEligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckProductEligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckProductEligibilityResponseValidationError{}

// Validate checks the field values on EnquireVKYCStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnquireVKYCStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireVKYCStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnquireVKYCStatusRequestMultiError, or nil if none found.
func (m *EnquireVKYCStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireVKYCStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireVKYCStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireVKYCStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireVKYCStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EkycRrn

	if len(errors) > 0 {
		return EnquireVKYCStatusRequestMultiError(errors)
	}

	return nil
}

// EnquireVKYCStatusRequestMultiError is an error wrapping multiple validation
// errors returned by EnquireVKYCStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type EnquireVKYCStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireVKYCStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireVKYCStatusRequestMultiError) AllErrors() []error { return m }

// EnquireVKYCStatusRequestValidationError is the validation error returned by
// EnquireVKYCStatusRequest.Validate if the designated constraints aren't met.
type EnquireVKYCStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireVKYCStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireVKYCStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireVKYCStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireVKYCStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireVKYCStatusRequestValidationError) ErrorName() string {
	return "EnquireVKYCStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireVKYCStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireVKYCStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireVKYCStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireVKYCStatusRequestValidationError{}

// Validate checks the field values on EnquireVKYCStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnquireVKYCStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireVKYCStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnquireVKYCStatusResponseMultiError, or nil if none found.
func (m *EnquireVKYCStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireVKYCStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireVKYCStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireVKYCStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireVKYCStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VkycStatus

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireVKYCStatusResponseValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireVKYCStatusResponseValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireVKYCStatusResponseValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnquireVKYCStatusResponseMultiError(errors)
	}

	return nil
}

// EnquireVKYCStatusResponseMultiError is an error wrapping multiple validation
// errors returned by EnquireVKYCStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type EnquireVKYCStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireVKYCStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireVKYCStatusResponseMultiError) AllErrors() []error { return m }

// EnquireVKYCStatusResponseValidationError is the validation error returned by
// EnquireVKYCStatusResponse.Validate if the designated constraints aren't met.
type EnquireVKYCStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireVKYCStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireVKYCStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireVKYCStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireVKYCStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireVKYCStatusResponseValidationError) ErrorName() string {
	return "EnquireVKYCStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireVKYCStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireVKYCStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireVKYCStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireVKYCStatusResponseValidationError{}

// Validate checks the field values on UpgradeKYCLevelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpgradeKYCLevelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeKYCLevelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeKYCLevelRequestMultiError, or nil if none found.
func (m *UpgradeKYCLevelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeKYCLevelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeKYCLevelRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeKYCLevelRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeKYCLevelRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorCustomerId

	// no validation rules for EkycRrn

	// no validation rules for SignImage

	if len(errors) > 0 {
		return UpgradeKYCLevelRequestMultiError(errors)
	}

	return nil
}

// UpgradeKYCLevelRequestMultiError is an error wrapping multiple validation
// errors returned by UpgradeKYCLevelRequest.ValidateAll() if the designated
// constraints aren't met.
type UpgradeKYCLevelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeKYCLevelRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeKYCLevelRequestMultiError) AllErrors() []error { return m }

// UpgradeKYCLevelRequestValidationError is the validation error returned by
// UpgradeKYCLevelRequest.Validate if the designated constraints aren't met.
type UpgradeKYCLevelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeKYCLevelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeKYCLevelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeKYCLevelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeKYCLevelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeKYCLevelRequestValidationError) ErrorName() string {
	return "UpgradeKYCLevelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpgradeKYCLevelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeKYCLevelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeKYCLevelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeKYCLevelRequestValidationError{}

// Validate checks the field values on UpgradeKYCLevelResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpgradeKYCLevelResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeKYCLevelResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeKYCLevelResponseMultiError, or nil if none found.
func (m *UpgradeKYCLevelResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeKYCLevelResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeKYCLevelResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeKYCLevelResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeKYCLevelResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeKYCLevelResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeKYCLevelResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeKYCLevelResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpgradeKYCLevelResponseMultiError(errors)
	}

	return nil
}

// UpgradeKYCLevelResponseMultiError is an error wrapping multiple validation
// errors returned by UpgradeKYCLevelResponse.ValidateAll() if the designated
// constraints aren't met.
type UpgradeKYCLevelResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeKYCLevelResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeKYCLevelResponseMultiError) AllErrors() []error { return m }

// UpgradeKYCLevelResponseValidationError is the validation error returned by
// UpgradeKYCLevelResponse.Validate if the designated constraints aren't met.
type UpgradeKYCLevelResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeKYCLevelResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeKYCLevelResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeKYCLevelResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeKYCLevelResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeKYCLevelResponseValidationError) ErrorName() string {
	return "UpgradeKYCLevelResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpgradeKYCLevelResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeKYCLevelResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeKYCLevelResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeKYCLevelResponseValidationError{}

// Validate checks the field values on CreateLoanCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanCustomerRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLoanCustomerRequestMultiError, or nil if none found.
func (m *CreateLoanCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for SolId

	// no validation rules for BreRefNumber

	// no validation rules for UidNo

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FatherName

	// no validation rules for MotherName

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerRequestValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	// no validation rules for MaritalStatus

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCommunicationAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "CommunicationAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "CommunicationAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommunicationAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerRequestValidationError{
				field:  "CommunicationAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerRequestValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIdentityProof()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "IdentityProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "IdentityProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentityProof()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerRequestValidationError{
				field:  "IdentityProof",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddressProof()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "AddressProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerRequestValidationError{
					field:  "AddressProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressProof()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerRequestValidationError{
				field:  "AddressProof",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanNumber

	// no validation rules for SignImage

	// no validation rules for EmploymentType

	// no validation rules for AnnualIncome

	// no validation rules for OccupationType

	// no validation rules for Community

	// no validation rules for Qualification

	// no validation rules for Designation

	// no validation rules for DisabilityType

	// no validation rules for Category

	if len(errors) > 0 {
		return CreateLoanCustomerRequestMultiError(errors)
	}

	return nil
}

// CreateLoanCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by CreateLoanCustomerRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateLoanCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanCustomerRequestMultiError) AllErrors() []error { return m }

// CreateLoanCustomerRequestValidationError is the validation error returned by
// CreateLoanCustomerRequest.Validate if the designated constraints aren't met.
type CreateLoanCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanCustomerRequestValidationError) ErrorName() string {
	return "CreateLoanCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanCustomerRequestValidationError{}

// Validate checks the field values on CreateLoanCustomerResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLoanCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLoanCustomerResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLoanCustomerResponseMultiError, or nil if none found.
func (m *CreateLoanCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLoanCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLoanCustomerResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLoanCustomerResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLoanCustomerResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateLoanCustomerResponseMultiError(errors)
	}

	return nil
}

// CreateLoanCustomerResponseMultiError is an error wrapping multiple
// validation errors returned by CreateLoanCustomerResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateLoanCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLoanCustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLoanCustomerResponseMultiError) AllErrors() []error { return m }

// CreateLoanCustomerResponseValidationError is the validation error returned
// by CreateLoanCustomerResponse.Validate if the designated constraints aren't met.
type CreateLoanCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLoanCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLoanCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLoanCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLoanCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLoanCustomerResponseValidationError) ErrorName() string {
	return "CreateLoanCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLoanCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLoanCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLoanCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLoanCustomerResponseValidationError{}

// Validate checks the field values on LoanCustomerCreationStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LoanCustomerCreationStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanCustomerCreationStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanCustomerCreationStatusRequestMultiError, or nil if none found.
func (m *LoanCustomerCreationStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanCustomerCreationStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanCustomerCreationStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanCustomerCreationStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanCustomerCreationStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for CifRequestId

	if len(errors) > 0 {
		return LoanCustomerCreationStatusRequestMultiError(errors)
	}

	return nil
}

// LoanCustomerCreationStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// LoanCustomerCreationStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type LoanCustomerCreationStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanCustomerCreationStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanCustomerCreationStatusRequestMultiError) AllErrors() []error { return m }

// LoanCustomerCreationStatusRequestValidationError is the validation error
// returned by LoanCustomerCreationStatusRequest.Validate if the designated
// constraints aren't met.
type LoanCustomerCreationStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanCustomerCreationStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanCustomerCreationStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanCustomerCreationStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanCustomerCreationStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanCustomerCreationStatusRequestValidationError) ErrorName() string {
	return "LoanCustomerCreationStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LoanCustomerCreationStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanCustomerCreationStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanCustomerCreationStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanCustomerCreationStatusRequestValidationError{}

// Validate checks the field values on LoanCustomerCreationStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LoanCustomerCreationStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanCustomerCreationStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LoanCustomerCreationStatusResponseMultiError, or nil if none found.
func (m *LoanCustomerCreationStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanCustomerCreationStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for CustomerId

	// no validation rules for CustomerName

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanCustomerCreationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanCustomerCreationStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanCustomerCreationStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanCustomerCreationStatusResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanCustomerCreationStatusResponseValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanCustomerCreationStatusResponseValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCifCreatedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanCustomerCreationStatusResponseValidationError{
					field:  "CifCreatedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanCustomerCreationStatusResponseValidationError{
					field:  "CifCreatedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCifCreatedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanCustomerCreationStatusResponseValidationError{
				field:  "CifCreatedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanCustomerCreationStatusResponseMultiError(errors)
	}

	return nil
}

// LoanCustomerCreationStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// LoanCustomerCreationStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type LoanCustomerCreationStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanCustomerCreationStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanCustomerCreationStatusResponseMultiError) AllErrors() []error { return m }

// LoanCustomerCreationStatusResponseValidationError is the validation error
// returned by LoanCustomerCreationStatusResponse.Validate if the designated
// constraints aren't met.
type LoanCustomerCreationStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanCustomerCreationStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanCustomerCreationStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanCustomerCreationStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanCustomerCreationStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanCustomerCreationStatusResponseValidationError) ErrorName() string {
	return "LoanCustomerCreationStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LoanCustomerCreationStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanCustomerCreationStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanCustomerCreationStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanCustomerCreationStatusResponseValidationError{}
