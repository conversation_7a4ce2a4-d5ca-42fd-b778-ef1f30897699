//nolint:dupl
package connected_account

import (
	"fmt"

	"github.com/pkg/errors"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/money"

	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
)

type Parser interface {
	ParseToExternalTransactionEvent(aaTxn *AaTransaction, txnEventType caEnumPb.TransactionEventTypeInitiatedBy) (*caExtPb.TransactionEvent, error)
}

func (d *AaDepositTransaction) ParseToExternalTransactionEvent(aaTxn *AaTransaction, txnEventTypeInitiatedBy caEnumPb.TransactionEventTypeInitiatedBy) (*caExtPb.TransactionEvent, error) {
	amount, amtErr := money.ParseString(aaTxn.GetAmount(), "INR")
	if amtErr != nil {
		return nil, errors.New(fmt.Sprintf("error in parsing amount string for id: %v amt: %v",
			aaTxn.GetId(), aaTxn.GetAmount()))
	}
	curBalance, amtErr := money.ParseString(d.GetDepositTxnMeta().GetCurrentBalance(), "INR")
	if amtErr != nil {
		return nil, errors.New(fmt.Sprintf("error in parsing cur balance string for id: %v bal: %v",
			aaTxn.GetId(), d.GetDepositTxnMeta().GetCurrentBalance()))
	}
	publishEvent := &caExtPb.TransactionEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
		Transaction: &caExtPb.TransactionEvent_DepositTransaction{DepositTransaction: &caExtPb.DepositTransaction{
			TransactionId:         aaTxn.GetId(),
			AccountId:             aaTxn.GetAccountId(),
			ActorId:               aaTxn.GetActorId(),
			ExternalTransactionId: aaTxn.GetTxnId(),
			Amount:                amount,
			Narration:             aaTxn.GetNarration(),
			TransactionTimestamp:  aaTxn.GetTransactionDate(),
			Type:                  aaTxn.GetType(),
			Mode:                  aaTxn.GetMode(),
			Reference:             d.GetDepositTxnMeta().GetReference(),
			AccInstrumentType:     caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
			CurrentBalance:        curBalance,
			ValueDate:             d.GetDepositTxnMeta().GetValueDate(),
		}},
		AccountId:               aaTxn.GetAccountId(),
		EventTimestamp:          timestampPb.Now(),
		TxnEventTypeInitiatedBy: txnEventTypeInitiatedBy,
	}
	return publishEvent, nil
}

func (d *AaRecurringDepositTransaction) ParseToExternalTransactionEvent(aaTxn *AaTransaction, txnEventTypeInitiatedBy caEnumPb.TransactionEventTypeInitiatedBy) (*caExtPb.TransactionEvent, error) {
	amount, amtErr := money.ParseString(aaTxn.GetAmount(), "INR")
	if amtErr != nil {
		return nil, errors.New(fmt.Sprintf("error in parsing amount string for id: %v amt: %v",
			aaTxn.GetId(), aaTxn.GetAmount()))
	}
	bal, amtErr := money.ParseString(d.GetRecurringDepositTxnMeta().GetBalance(), "INR")
	if amtErr != nil {
		return nil, errors.New(fmt.Sprintf("error in parsing cur balance string for id: %v bal: %v",
			aaTxn.GetId(), d.GetRecurringDepositTxnMeta().GetBalance()))
	}
	publishEvent := &caExtPb.TransactionEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
		Transaction: &caExtPb.TransactionEvent_RecurringDepositTransaction{
			RecurringDepositTransaction: &caExtPb.RecurringDepositTransaction{
				TransactionId:         aaTxn.GetId(),
				AccountId:             aaTxn.GetAccountId(),
				ActorId:               aaTxn.GetActorId(),
				ExternalTransactionId: aaTxn.GetTxnId(),
				Amount:                amount,
				Narration:             aaTxn.GetNarration(),
				TransactionTimestamp:  aaTxn.GetTransactionDate(),
				Type:                  aaTxn.GetType(),
				Mode:                  aaTxn.GetMode(),
				Reference:             d.GetRecurringDepositTxnMeta().GetReference(),
				AccInstrumentType:     caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
				CurrentBalance:        bal,
				ValueDate:             d.GetRecurringDepositTxnMeta().GetValueDate(),
			},
		},
		AccountId:               aaTxn.GetAccountId(),
		EventTimestamp:          timestampPb.Now(),
		TxnEventTypeInitiatedBy: txnEventTypeInitiatedBy,
	}
	return publishEvent, nil
}

func (d *AaTermDepositTransaction) ParseToExternalTransactionEvent(aaTxn *AaTransaction, txnEventTypeInitiatedBy caEnumPb.TransactionEventTypeInitiatedBy) (*caExtPb.TransactionEvent, error) {
	amount, amtErr := money.ParseString(aaTxn.GetAmount(), "INR")
	if amtErr != nil {
		return nil, errors.New(fmt.Sprintf("error in parsing amount string for id: %v amt: %v",
			aaTxn.GetId(), aaTxn.GetAmount()))
	}
	bal, amtErr := money.ParseString(d.GetTermDepositTxnMeta().GetBalance(), "INR")
	if amtErr != nil {
		return nil, errors.New(fmt.Sprintf("error in parsing cur balance string for id: %v bal: %v",
			aaTxn.GetId(), d.GetTermDepositTxnMeta().GetBalance()))
	}
	publishEvent := &caExtPb.TransactionEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
		Transaction: &caExtPb.TransactionEvent_RecurringDepositTransaction{
			RecurringDepositTransaction: &caExtPb.RecurringDepositTransaction{
				TransactionId:         aaTxn.GetId(),
				AccountId:             aaTxn.GetAccountId(),
				ActorId:               aaTxn.GetActorId(),
				ExternalTransactionId: aaTxn.GetTxnId(),
				Amount:                amount,
				Narration:             aaTxn.GetNarration(),
				TransactionTimestamp:  aaTxn.GetTransactionDate(),
				Type:                  aaTxn.GetType(),
				Mode:                  aaTxn.GetMode(),
				Reference:             d.GetTermDepositTxnMeta().GetReference(),
				AccInstrumentType:     caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
				CurrentBalance:        bal,
				ValueDate:             d.GetTermDepositTxnMeta().GetValueDate(),
			},
		},
		AccountId:               aaTxn.GetAccountId(),
		EventTimestamp:          timestampPb.Now(),
		TxnEventTypeInitiatedBy: txnEventTypeInitiatedBy,
	}
	return publishEvent, nil
}

func (e *AaEquityTransaction) ParseToExternalTransactionEvent(aaTxn *AaTransaction, txnEventTypeInitiatedBy caEnumPb.TransactionEventTypeInitiatedBy) (*caExtPb.TransactionEvent, error) {

	extTxn, err := e.ConvertToExternalTransaction(aaTxn)
	if err != nil {
		return nil, fmt.Errorf("failed to convert to external equity transaction: %w", err)
	}

	publishEvent := &caExtPb.TransactionEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
		Transaction: &caExtPb.TransactionEvent_EquityTransaction{
			EquityTransaction: extTxn,
		},
		AccountId:               aaTxn.GetAccountId(),
		EventTimestamp:          timestampPb.Now(),
		TxnEventTypeInitiatedBy: txnEventTypeInitiatedBy,
	}
	return publishEvent, nil
}

func (e *AaEquityTransaction) ConvertToExternalTransaction(aaTxn *AaTransaction) (*caExtPb.EquityTransaction, error) {
	var rate *moneyPb.Money
	if e.GetRate() != "" {
		var rateErr error
		rate, rateErr = money.ParseString(e.GetRate(), "INR")
		if rateErr != nil {
			return nil, errors.New(fmt.Sprintf("error in parsing rate string for id: %v rate: %v",
				aaTxn.GetId(), e.GetRate()))
		}
	}
	return &caExtPb.EquityTransaction{
		TransactionId:             aaTxn.GetId(),
		AccountId:                 aaTxn.GetAccountId(),
		ActorId:                   aaTxn.GetActorId(),
		ExternalTransactionId:     aaTxn.GetTxnId(),
		Rate:                      rate,
		Narration:                 aaTxn.GetNarration(),
		TransactionTimestamp:      aaTxn.GetTransactionDate(),
		Type:                      e.GetType(),
		EquityTransactionCategory: e.GetMetaData().GetEquityTransactionCategory(),
		Units:                     e.GetUnits(),
		Isin:                      e.GetIsin(),
		IsinDescription:           e.GetMetaData().GetIsinDescription(),
		OrderId:                   e.GetMetaData().GetOrderId(),
		CompanyName:               e.GetMetaData().GetCompanyName(),
		StockExchange:             e.GetMetaData().GetEquityStockExchange(),
	}, nil
}

func (e *AaEtfTransaction) ConvertToExternalEtfTransaction(aaTxn *AaTransaction) (*caExtPb.EtfTransaction, error) {
	return &caExtPb.EtfTransaction{
		TransactionId:         aaTxn.GetId(),
		AccountId:             aaTxn.GetAccountId(),
		ActorId:               aaTxn.GetActorId(),
		ExternalTransactionId: aaTxn.GetTxnId(),
		Nav:                   e.GetNav(),
		Narration:             aaTxn.GetNarration(),
		TransactionTimestamp:  aaTxn.GetTransactionDate(),
		Type:                  e.GetType(),
		BrokerCode:            e.GetBrokerCode(),
		Units:                 e.GetUnits(),
		Isin:                  e.GetIsin(),
		IsinDescription:       e.GetMetaData().GetIsinDescription(),
		Amount:                e.GetAmount(),
	}, nil
}

func (e *AaEtfTransaction) ParseToExternalTransactionEvent(aaTxn *AaTransaction, txnEventTypeInitiatedBy caEnumPb.TransactionEventTypeInitiatedBy) (*caExtPb.TransactionEvent, error) {

	publishEvent := &caExtPb.TransactionEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF,
		Transaction: &caExtPb.TransactionEvent_EtfTransaction{
			EtfTransaction: &caExtPb.EtfTransaction{
				TransactionId:         aaTxn.GetId(),
				AccountId:             aaTxn.GetAccountId(),
				ActorId:               aaTxn.GetActorId(),
				ExternalTransactionId: aaTxn.GetTxnId(),
				Nav:                   e.GetNav(),
				Narration:             aaTxn.GetNarration(),
				TransactionTimestamp:  aaTxn.GetTransactionDate(),
				Type:                  e.GetType(),
				Units:                 e.GetUnits(),
				Isin:                  e.GetIsin(),
				IsinDescription:       e.GetMetaData().GetIsinDescription(),
				Amount:                e.GetAmount(),
				BrokerCode:            e.GetBrokerCode(),
			},
		},
		AccountId:               aaTxn.GetAccountId(),
		EventTimestamp:          timestampPb.Now(),
		TxnEventTypeInitiatedBy: txnEventTypeInitiatedBy,
	}
	return publishEvent, nil
}

func (a *AaAccount) ConvertToExternalAccountDetail(conf *genconf.Config) *caExtPb.AccountDetails {
	if a == nil {
		return nil
	}
	return &caExtPb.AccountDetails{
		FipId:               a.GetFipId(),
		AccountId:           a.GetId(),
		LinkedAccountRef:    a.GetLinkedAccountRef(),
		ActorId:             a.GetActorId(),
		MaskedAccountNumber: a.GetMaskedAccountNumber(),
		AccInstrumentType:   a.GetAccInstrumentType(),
		AccountStatus:       a.GetAccountStatus(),
		LastSyncedAt:        a.GetLastSyncedAt(),
		AccountSubStatus:    a.GetAccountSubStatus(),
		ConnectedAt:         a.GetCreatedAt(),
		AaEntity:            a.GetAaEntity(),
		AccountType:         a.GetAccountTypeFromAccount(),
		AccountControlsMap:  conf.GetAccountControlsForFip(a.GetFipId()),
	}
}

func (acc *AaAccount) GetAccountTypeFromAccount() *caExtPb.AccountTypeConfig {
	switch acc.GetAccInstrumentType() {
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT:
		return &caExtPb.AccountTypeConfig{AccType: &caExtPb.AccountTypeConfig_DepositAccountType{
			DepositAccountType: acc.GetAccountSubType().GetDepositAccountType()}}
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT:
		return &caExtPb.AccountTypeConfig{AccType: &caExtPb.AccountTypeConfig_RecurringDepositAccountType{
			RecurringDepositAccountType: acc.GetAccountSubType().GetRecurringDepositAccountType()}}
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT:
		return &caExtPb.AccountTypeConfig{AccType: &caExtPb.AccountTypeConfig_TermDepositAccountType{
			TermDepositAccountType: acc.GetAccountSubType().GetTermDepositAccountType()}}
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES:
		return &caExtPb.AccountTypeConfig{AccType: &caExtPb.AccountTypeConfig_EquityAccountType{
			EquityAccountType: caEnumPb.EquityAccountType_EQUITY_ACCOUNT_TYPE_DEFAULT_TYPE}}
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF:
		return &caExtPb.AccountTypeConfig{AccType: &caExtPb.AccountTypeConfig_EtfAccountType{
			EtfAccountType: caEnumPb.ETFAccountType_ETF_ACCOUNT_TYPE_DEFAULT_TYPE}}
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT:
		return &caExtPb.AccountTypeConfig{AccType: &caExtPb.AccountTypeConfig_ReitAccountType{
			ReitAccountType: caEnumPb.ReitAccountType_REIT_ACCOUNT_TYPE_DEFAULT_TYPE}}
	case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT:
		return &caExtPb.AccountTypeConfig{AccType: &caExtPb.AccountTypeConfig_InvitAccountType{
			InvitAccountType: caEnumPb.InvitAccountType_INVIT_ACCOUNT_TYPE_DEFAULT_TYPE}}
	default:
		return nil
	}
}

func (c *Consent) ConvertToExternalConsentDetails(cr *ConsentRequest) *caExtPb.ConsentDetails {
	if c == nil {
		return nil
	}
	return &caExtPb.ConsentDetails{
		ConsentHandle: cr.GetConsentHandle(),
		ConsentId:     c.GetConsentId(),
		ConsentStatus: c.GetConsentStatus(),
		ActorId:       c.GetActorId(),
	}
}

func (d *DataFetchAttempt) ConvertToExternalDfaDetail() *caExtPb.DataFetchAttemptDetails {
	if d == nil {
		return nil
	}
	return &caExtPb.DataFetchAttemptDetails{
		Id:                 d.GetId(),
		ActorId:            d.GetActorId(),
		ConsentReferenceId: d.GetConsentReferenceId(),
		FetchStatus:        d.GetFetchStatus(),
		DataRangeFrom:      d.GetDataRangeFrom(),
		DataRangeTo:        d.GetDataRangeTo(),
		CreatedAt:          d.GetCreatedAt(),
	}
}

func (accDetails *AccountProfileSummaryDetails) GetDematIdFromEquityAccountDetails() (string, error) {
	equityProfileDetails := accDetails.GetEquityProfile().GetHoldersDetails()
	if len(equityProfileDetails) == 0 {
		return "", fmt.Errorf("no holder details in equity profile : %s", accDetails.GetAccountDetails().GetAccountId())
	}
	dematId := equityProfileDetails[0].GetDematId()
	return dematId, nil
}

func (accDetails *AccountProfileSummaryDetails) GetDematIdFromEtfAccountDetails() (string, error) {
	etfProfileDetails := accDetails.GetEtfProfile().GetHoldersDetails()
	if len(etfProfileDetails) == 0 {
		return "", fmt.Errorf("no holder details in etf profile : %s", accDetails.GetAccountDetails().GetAccountId())
	}
	dematId := etfProfileDetails[0].GetDematId()
	return dematId, nil
}

func (accDetails *AccountProfileSummaryDetails) GetDematIdFromInvitAccountDetails() (string, error) {
	invitProfileDetails := accDetails.GetInvitProfile().GetInvitHoldersDetails()
	if len(invitProfileDetails) == 0 {
		return "", fmt.Errorf("no holder details in invit profile : %s", accDetails.GetAccountDetails().GetAccountId())
	}
	dematId := invitProfileDetails[0].GetDematId()
	return dematId, nil
}

func (accDetails *AccountProfileSummaryDetails) GetDematIdFromReitAccountDetails() (string, error) {
	reitProfileDetails := accDetails.GetReitProfile().GetReitHoldersDetails()
	if len(reitProfileDetails) == 0 {
		return "", fmt.Errorf("no holder details in reit profile : %s", accDetails.GetAccountDetails().GetAccountId())
	}
	dematId := reitProfileDetails[0].GetDematId()
	return dematId, nil
}

func (e *AaReitTransaction) ConvertToExternalReitTransaction(aaTxn *AaTransaction) (*caExtPb.ReitTransaction, error) {
	return &caExtPb.ReitTransaction{
		TransactionId:          aaTxn.GetId(),
		AccountId:              aaTxn.GetAccountId(),
		ActorId:                aaTxn.GetActorId(),
		ExternalTransactionId:  aaTxn.GetTxnId(),
		Isin:                   e.GetIsin(),
		IssuerName:             e.GetMetadata().GetIssuerName(),
		IsinDescription:        e.GetMetadata().GetIsinDescription(),
		Exchange:               e.GetExchange(),
		TransactionDescription: e.GetMetadata().GetTransactionDescription(),
		TransactionTime:        e.GetTransactionDateTime(),
		Units:                  e.GetUnits(),
		Narration:              aaTxn.GetNarration(),
	}, nil
}

func (e *AaReitTransaction) ParseToExternalTransactionEvent(aaTxn *AaTransaction, txnEventTypeInitiatedBy caEnumPb.TransactionEventTypeInitiatedBy) (*caExtPb.TransactionEvent, error) {
	publishEvent := &caExtPb.TransactionEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT,
		Transaction: &caExtPb.TransactionEvent_ReitTransaction{
			ReitTransaction: &caExtPb.ReitTransaction{
				TransactionId:          aaTxn.GetId(),
				AccountId:              aaTxn.GetAccountId(),
				ActorId:                aaTxn.GetActorId(),
				ExternalTransactionId:  aaTxn.GetTxnId(),
				Isin:                   e.GetIsin(),
				IssuerName:             e.GetMetadata().GetIssuerName(),
				IsinDescription:        e.GetMetadata().GetIsinDescription(),
				Exchange:               e.GetExchange(),
				TransactionDescription: e.GetMetadata().GetTransactionDescription(),
				TransactionTime:        e.GetTransactionDateTime(),
				Units:                  e.GetUnits(),
				Narration:              aaTxn.GetNarration(),
			},
		},
		AccountId:               aaTxn.GetAccountId(),
		EventTimestamp:          timestampPb.Now(),
		TxnEventTypeInitiatedBy: txnEventTypeInitiatedBy,
	}
	return publishEvent, nil
}

func (e *AaInvitTransaction) ConvertToExternalInvitTransaction(aaTxn *AaTransaction) (*caExtPb.InvitTransaction, error) {
	return &caExtPb.InvitTransaction{
		TransactionId:          aaTxn.GetId(),
		AccountId:              aaTxn.GetAccountId(),
		ActorId:                aaTxn.GetActorId(),
		ExternalTransactionId:  aaTxn.GetTxnId(),
		Isin:                   e.GetIsin(),
		IssuerName:             e.GetMetadata().GetIssuerName(),
		IsinDescription:        e.GetMetadata().GetIsinDescription(),
		TransactionDescription: e.GetMetadata().GetTransactionDescription(),
		TransactionDateTime:    aaTxn.GetTransactionDate(),
		Units:                  e.GetUnits(),
	}, nil
}

func (e *AaInvitTransaction) ParseToExternalTransactionEvent(aaTxn *AaTransaction, txnEventTypeInitiatedBy caEnumPb.TransactionEventTypeInitiatedBy) (*caExtPb.TransactionEvent, error) {

	publishEvent := &caExtPb.TransactionEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT,
		Transaction: &caExtPb.TransactionEvent_InvitTransaction{
			InvitTransaction: &caExtPb.InvitTransaction{
				TransactionId:          aaTxn.GetId(),
				AccountId:              aaTxn.GetAccountId(),
				ActorId:                aaTxn.GetActorId(),
				ExternalTransactionId:  aaTxn.GetTxnId(),
				Isin:                   e.GetIsin(),
				IssuerName:             e.GetMetadata().GetIssuerName(),
				IsinDescription:        e.GetMetadata().GetIsinDescription(),
				TransactionDescription: e.GetMetadata().GetTransactionDescription(),
				TransactionDateTime:    aaTxn.GetTransactionDate(),
				Units:                  e.GetUnits(),
			},
		},
		AccountId:               aaTxn.GetAccountId(),
		EventTimestamp:          timestampPb.Now(),
		TxnEventTypeInitiatedBy: txnEventTypeInitiatedBy,
	}
	return publishEvent, nil
}
