syntax = "proto3";

package frontend.rewards;

import "api/frontend/rewards/display_components.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/money.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards";
option java_package = "com.github.epifi.gamma.api.frontend.rewards";

// Status of rewards
enum RewardStatus {
  UNSPECIFIED_REWARD_STATUS = 0;
  LOCKED = 1;
  EXPIRED = 2;
  UNLOCKED = 3;
  // denotes that the reward is already claimed but will get processed with a delay
  ARRIVING = 4;
  PROCESSING = 5;
  PROCESSED = 6;
  PROCESSING_FAILED = 7;
  REVERSED = 8;
  // denotes that reward will get unlocked and auto-processed at the same time in future
  DELAYED_UNLOCK_WITH_AUTOCLAIM = 9;
}

// defines the theme for displaying reward tile on APP.
// Based on theme, the APP decides the images to show on reward tile and corresponding animation to
// play while claiming the reward. If RewardTileThemeType is THEME_TYPE_UNSPECIFIED, the APP fallbacks
// to a default theme.
enum RewardTileThemeType {
  REWARD_TILE_THEME_UNSPECIFIED = 0;
  PLANT_THEME_CHRYSANTHEMUM = 1;
  PLANT_THEME_ORCHID = 2;
}

// proto for reward. Will be used to communicate between backend and client
message Reward {
  string id = 1;
  string ref_id = 2;
  RewardStatus status = 3;
  // status background colour shown over tile
  string status_bg_color = 4 [deprecated = true];
  string processing_sub_status = 5;
  google.protobuf.Timestamp created_at = 6;
  RewardOptions reward_options = 7;
  RewardOption claimed_reward = 8;
  string action_desc = 9;
  // whether tile is clickable or not
  bool is_tile_clickable = 10;
  // text to be shown on top of the tile
  string tile_text = 11 [deprecated = true];
  // external_id is equivalent to reward id, but this external_id can be displayed
  // on the app while id shouldn't be due to security reasons.
  string external_id = 12;
  // background image for reward tile.
  string tile_bg_image = 13;
  // Based on tile_theme_type, the APP decides the images to show on reward tile and corresponding animation to
  // play while claiming the reward. If tile_theme_type is THEME_TYPE_UNSPECIFIED, the APP fallbacks
  // to a default theme.
  RewardTileThemeType tile_theme_type = 14;
  // status_desc text like "This will be added to your coin balance on 12 May 2021"
  string status_desc = 15;
  // CTA to skip flower animation for reward. Will be `nil` if we don't want user to be able to skip animation.
  // note: ignore this field if `SkipAnimation` is set to true
  SkipAnimationCta skip_animation_cta = 16;
  // flag to decide whether we'd want to skip the whole animation altogether.
  // use case: in case booster is applied, we'd want to skip animation to give time for further frames.
  // note: ignore `SkipAnimationCta` if this field is set to true
  bool skip_animation = 19;
  // icon to be shown on top-left corner of the tile.
  // Note: Can be nil as well.
  api.typesv2.common.Image tile_top_left_icon = 17;
  // icon to be shown on top-right corner of the tile.
  // Note: Can be nil as well.
  api.typesv2.common.Image tile_top_right_icon = 20;
  // bottom-sheet info to show details after tapping on the reward tile.
  // If present, should be prioritised over opening reward details.
  // deprecated in favour of RewardUnlockDetails
  BottomSheetInfo inoperable_info = 18 [deprecated = true];
  // border color for the reward tile.
  // note: can be empty as well
  string border_color = 21;
  // tag to be shown at the bottom-centre of the reward-tile.
  // note: can be empty as well
  api.typesv2.common.Text bottom_tag = 22;
  // details on how to unlock a locked reward, will only be populated if reward is in LOCKED state
  RewardUnlockDetails reward_unlock_details = 23;
  // Determines how fast the money plant animation should run
  // note: The expected default value is 1.5, 1.0 for normal speed, 1.5 for faster, 0.5 for slower and so on
  float animation_speed = 24;

  message SkipAnimationCta {
    // text to be shown on the CTA like "Skip to Reward"
    api.typesv2.common.Text cta_text = 1;
    // URL of icon to be displayed along-side text
    string icon_url = 2;
  }
}

// for Cash reward
message Cash {
  api.typesv2.Money amount = 1;
}

// for Fi Coins reward
message FiCoins {
  uint32 points = 1;
  google.protobuf.Timestamp expires_at = 2;
}

// for smart deposit reward
message SmartDeposit {
  api.typesv2.Money amount = 1;
}

// for lucky draw reward
message LuckyDraw {
  string luckyDrawId = 1;
}

message GiftHamper {
  // name of product like "Monsoon Harvest Hamper"
  string product_name = 1;
  // address where gift hamper is (or to be) shipped.
  api.typesv2.PostalAddress shipping_address = 2;
}

// for NO_Reward rewardType
message NoReward {
}

// A Reward option message, agnostic of the type of the underlying reward. Can be used to display any kind of reward
// offering as an option in the Rewards claim options screen:
// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=5953-65408&mode=design&t=k2Siz82TJF56Xup5-4
message RewardOptionText {
  // Displays the value of the Reward option, e.g. 25,000, 45 etc.
  api.typesv2.common.Text reward_value = 1;
  // Displays some more textual context of the reward type, e.g Fi coins, Us stocks, Cash INR
  api.typesv2.common.Text reward_description = 2;
  // loading screen shown post selecting the option
  // https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=7451%3A23207&t=fOLy0ahv7PwxGHOy-1
  PostOptionSelectionLoader post_option_selection_loader = 3;

  // https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=7451%3A23207&t=fOLy0ahv7PwxGHOy-1
  message PostOptionSelectionLoader {
    api.typesv2.common.VisualElement icon = 1;
    // title, ex- ₹50
    api.typesv2.common.Text title = 2;
    // subtitle, ex- In a smart deposit
    api.typesv2.common.Text subtitle = 3;
    // bottom banner text, ex- Processing
    api.typesv2.common.Text bottom_banner_text = 4;
    // bottom banner background color
    api.typesv2.common.ui.widget.BackgroundColour bottom_banner_bg_colour = 5;
  }
}

message RewardOption {
  message DisplayDetails {
    // one liner description like "Fi-Coins" "Fi-Coins 6 days later"
    string desc = 1;
    string icon = 2;
    string bg_color = 3;
    // html formatted details
    // e.g for a Gift Hamper Reward, a detail entry can look like
    // {
    //  header : "Description"
    //  body :"<b>Gift Box Contains :</b> <br>
    //  1 Toasted Millet Muesli <br>
    //  1 Choco chip oat clusters and Ragi flakes with banana <br>
    //  1 Box of 12 Crunchy granola bars - dark chocolate & espresso."
    // }
    message HtmlFormattedDetail {
      string header = 1;
      string body = 2;
    }
    repeated HtmlFormattedDetail html_formatted_details = 4;
    // banner text displayed on top of the reward option card at the time of choosing the option e.g. "10% EXTRA REWARD"
    // Design : https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=5953%3A65408
    // can be empty if banner text does not needs to be displayed.
    string banner_text = 7;
    // tag to be shown below the final-credited reward-units, i.e. "FI PLUS"
    api.typesv2.common.Text reward_details_tag = 8;
  }
  DisplayDetails display_details = 1;
  oneof option {
    Cash cash = 2;
    FiCoins fi_coins = 3;
    SmartDeposit sd = 4;
    LuckyDraw luckydraw = 5;
    GiftHamper gift_hamper = 6;
    NoReward no_reward = 7;
  }
  google.protobuf.Timestamp processing_date = 10;
  string id = 11;

  // stores the breakdown, i.e. individual entries leading up to the final reward-value calculated
  RewardUnitsCalculationInfo reward_units_calculation_info = 12;
  // A generic Text type driven field to display the Reward options text and values. This will be preferred by clients
  // over the 'option' field in order to render Reward claim options in a generic manner.
  RewardOptionText option_text = 13;
}

message RewardUnitsCalculationInfo {
  // title of the section, for e.g. "Reward boosters added"
  api.typesv2.common.Text title = 1;
  repeated RewardUnitsCalculationEntry reward_units_calculation_entries = 2;

  message RewardUnitsCalculationEntry {
    // intermediate reward-units, i.e. reward value till this calculation
    uint32 units = 1;
    // delta with the previous value, i.e. units above the previous value
    uint32 delta = 2;
    DisplayDetails display_details = 3;

    message DisplayDetails {
      // icon url, for e.g. fi-coins icon
      string icon_url = 1;
      // background color
      string bg_color = 2;

      // title for the entry, i.e. "2x boost applied for Fi Plus accounts"
      api.typesv2.common.Text title = 3;

      // color for units
      string units_color = 4;
      // color for the delta units
      string delta_color = 5;
    }
  }
}

message RewardOptions {
  uint32 default_decide_time_in_secs = 1;
  google.protobuf.Timestamp unlock_date = 2;
  repeated RewardOption options = 3;
  string action_details = 4;
}

// details on how to unlock a locked reward
message RewardUnlockDetails {
  // icon to be shown on the details section
  api.typesv2.common.VisualElement icon = 1;
  // heading of the section
  api.typesv2.common.Text heading = 2;
  // description of the section
  api.typesv2.common.Text desc = 3;
  // list of CTAs that are to be shown along with the unlock details
  repeated CtaV1 ctas = 4;
}
