// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/rewards/redeemed_offer.proto

package rewards

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RedeemedOfferProcessingStatus int32

const (
	RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_UNSPECIFIED    RedeemedOfferProcessingStatus = 0
	RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_IN_PROGRESS    RedeemedOfferProcessingStatus = 10
	RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_PENDING_UPDATE RedeemedOfferProcessingStatus = 20
	RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_FAILED         RedeemedOfferProcessingStatus = 50
	RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS        RedeemedOfferProcessingStatus = 100
)

// Enum value maps for RedeemedOfferProcessingStatus.
var (
	RedeemedOfferProcessingStatus_name = map[int32]string{
		0:   "RedeemedOfferProcessingStatus_UNSPECIFIED",
		10:  "RedeemedOfferProcessingStatus_IN_PROGRESS",
		20:  "RedeemedOfferProcessingStatus_PENDING_UPDATE",
		50:  "RedeemedOfferProcessingStatus_FAILED",
		100: "RedeemedOfferProcessingStatus_SUCCESS",
	}
	RedeemedOfferProcessingStatus_value = map[string]int32{
		"RedeemedOfferProcessingStatus_UNSPECIFIED":    0,
		"RedeemedOfferProcessingStatus_IN_PROGRESS":    10,
		"RedeemedOfferProcessingStatus_PENDING_UPDATE": 20,
		"RedeemedOfferProcessingStatus_FAILED":         50,
		"RedeemedOfferProcessingStatus_SUCCESS":        100,
	}
)

func (x RedeemedOfferProcessingStatus) Enum() *RedeemedOfferProcessingStatus {
	p := new(RedeemedOfferProcessingStatus)
	*p = x
	return p
}

func (x RedeemedOfferProcessingStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedeemedOfferProcessingStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_rewards_redeemed_offer_proto_enumTypes[0].Descriptor()
}

func (RedeemedOfferProcessingStatus) Type() protoreflect.EnumType {
	return &file_api_frontend_rewards_redeemed_offer_proto_enumTypes[0]
}

func (x RedeemedOfferProcessingStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedeemedOfferProcessingStatus.Descriptor instead.
func (RedeemedOfferProcessingStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{0}
}

type RedeemedOffer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id of redeemed offer
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// offer that was redeemed
	Offer *Offer `protobuf:"bytes,4,opt,name=offer,proto3" json:"offer,omitempty"`
	// processing status of redeemed offer
	ProcessingStatus RedeemedOfferProcessingStatus `protobuf:"varint,5,opt,name=processing_status,json=processingStatus,proto3,enum=frontend.rewards.RedeemedOfferProcessingStatus" json:"processing_status,omitempty"`
	// details of the redeemed offer, like coupon code, pin etc.
	RedeemedOfferDetails *RedeemedOffer_RedeemedOfferDetails `protobuf:"bytes,7,opt,name=redeemed_offer_details,json=redeemedOfferDetails,proto3" json:"redeemed_offer_details,omitempty"`
	// creation date of redeemed offer
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// external_id is equivalent to redeemed offer id, but this external_id can be displayed
	// on the app while Id shouldn't be due to security reasons.
	ExternalId string `protobuf:"bytes,9,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// price at which redemption was made
	// deprecated in favour of redemption_value
	//
	// Deprecated: Marked as deprecated in api/frontend/rewards/redeemed_offer.proto.
	RedemptionPrice uint32 `protobuf:"varint,10,opt,name=redemption_price,json=redemptionPrice,proto3" json:"redemption_price,omitempty"`
	// used to show the sections on offer details page, multiple sections can be used to show data related to different topics, eg. delivery details card, how to redeem section etc.
	// eg.  delivery details card can contain details about delivery address, delivery partner etc.
	RedeemedOfferDetailsSections []*OfferDetailsSection `protobuf:"bytes,11,rep,name=redeemed_offer_details_sections,json=redeemedOfferDetailsSections,proto3" json:"redeemed_offer_details_sections,omitempty"`
	// tags displayed on the redeemed offer tile e.g "CREDIT CARD REWARD"
	// design : https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=14751-85442&t=vRsbSIR6i2QmpzyV-0
	DisplayTags []*common.Text `protobuf:"bytes,12,rep,name=display_tags,json=displayTags,proto3" json:"display_tags,omitempty"`
	// flag to decide whether or not to display redemption price,
	// if set to true then redemption price shouldn't be displayed on redeemed offer tile.
	// deprecated in favour of not sending redemption_value
	//
	// Deprecated: Marked as deprecated in api/frontend/rewards/redeemed_offer.proto.
	ShouldNotDisplayRedemptionPrice bool `protobuf:"varint,13,opt,name=should_not_display_redemption_price,json=shouldNotDisplayRedemptionPrice,proto3" json:"should_not_display_redemption_price,omitempty"`
	// redemption value of the redeemed offer
	RedemptionValue *ui.VerticalKeyValuePair `protobuf:"bytes,15,opt,name=redemption_value,json=redemptionValue,proto3" json:"redemption_value,omitempty"`
}

func (x *RedeemedOffer) Reset() {
	*x = RedeemedOffer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeemedOffer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemedOffer) ProtoMessage() {}

func (x *RedeemedOffer) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemedOffer.ProtoReflect.Descriptor instead.
func (*RedeemedOffer) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{0}
}

func (x *RedeemedOffer) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RedeemedOffer) GetOffer() *Offer {
	if x != nil {
		return x.Offer
	}
	return nil
}

func (x *RedeemedOffer) GetProcessingStatus() RedeemedOfferProcessingStatus {
	if x != nil {
		return x.ProcessingStatus
	}
	return RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_UNSPECIFIED
}

func (x *RedeemedOffer) GetRedeemedOfferDetails() *RedeemedOffer_RedeemedOfferDetails {
	if x != nil {
		return x.RedeemedOfferDetails
	}
	return nil
}

func (x *RedeemedOffer) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RedeemedOffer) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/rewards/redeemed_offer.proto.
func (x *RedeemedOffer) GetRedemptionPrice() uint32 {
	if x != nil {
		return x.RedemptionPrice
	}
	return 0
}

func (x *RedeemedOffer) GetRedeemedOfferDetailsSections() []*OfferDetailsSection {
	if x != nil {
		return x.RedeemedOfferDetailsSections
	}
	return nil
}

func (x *RedeemedOffer) GetDisplayTags() []*common.Text {
	if x != nil {
		return x.DisplayTags
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/rewards/redeemed_offer.proto.
func (x *RedeemedOffer) GetShouldNotDisplayRedemptionPrice() bool {
	if x != nil {
		return x.ShouldNotDisplayRedemptionPrice
	}
	return false
}

func (x *RedeemedOffer) GetRedemptionValue() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.RedemptionValue
	}
	return nil
}

// egv details like code, pin etc
type EGiftCardDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardNo         string                 `protobuf:"bytes,1,opt,name=card_no,json=cardNo,proto3" json:"card_no,omitempty"`
	Pin            string                 `protobuf:"bytes,2,opt,name=pin,proto3" json:"pin,omitempty"`
	ActivationCode string                 `protobuf:"bytes,3,opt,name=activation_code,json=activationCode,proto3" json:"activation_code,omitempty"`
	ActivationUrl  string                 `protobuf:"bytes,4,opt,name=activation_url,json=activationUrl,proto3" json:"activation_url,omitempty"`
	ExpiryTime     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=expiry_time,json=expiryTime,proto3" json:"expiry_time,omitempty"`
}

func (x *EGiftCardDetails) Reset() {
	*x = EGiftCardDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EGiftCardDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EGiftCardDetails) ProtoMessage() {}

func (x *EGiftCardDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EGiftCardDetails.ProtoReflect.Descriptor instead.
func (*EGiftCardDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{1}
}

func (x *EGiftCardDetails) GetCardNo() string {
	if x != nil {
		return x.CardNo
	}
	return ""
}

func (x *EGiftCardDetails) GetPin() string {
	if x != nil {
		return x.Pin
	}
	return ""
}

func (x *EGiftCardDetails) GetActivationCode() string {
	if x != nil {
		return x.ActivationCode
	}
	return ""
}

func (x *EGiftCardDetails) GetActivationUrl() string {
	if x != nil {
		return x.ActivationUrl
	}
	return ""
}

func (x *EGiftCardDetails) GetExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTime
	}
	return nil
}

// charity details
type CharityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CharityDetails) Reset() {
	*x = CharityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CharityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CharityDetails) ProtoMessage() {}

func (x *CharityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CharityDetails.ProtoReflect.Descriptor instead.
func (*CharityDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{2}
}

// physical merchandise details
type PhysicalMerchandiseDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address where merchandise is (to be) shipped
	ShippingAddress *typesv2.PostalAddress `protobuf:"bytes,1,opt,name=shipping_address,json=shippingAddress,proto3" json:"shipping_address,omitempty"`
}

func (x *PhysicalMerchandiseDetails) Reset() {
	*x = PhysicalMerchandiseDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalMerchandiseDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalMerchandiseDetails) ProtoMessage() {}

func (x *PhysicalMerchandiseDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalMerchandiseDetails.ProtoReflect.Descriptor instead.
func (*PhysicalMerchandiseDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{3}
}

func (x *PhysicalMerchandiseDetails) GetShippingAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.ShippingAddress
	}
	return nil
}

// power-up details
// currently no power-up specific information is needed hence this is empty.
// we have to keep this despite being empty as client expects to receive something as part of one-off field, inside RedeemedOfferDetails message.
type PowerUpDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PowerUpDetails) Reset() {
	*x = PowerUpDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerUpDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerUpDetails) ProtoMessage() {}

func (x *PowerUpDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerUpDetails.ProtoReflect.Descriptor instead.
func (*PowerUpDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{4}
}

type ThriweBenefitsPackageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the expiry time of the benefits package.
	ExpiryTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=expiry_time,json=expiryTime,proto3" json:"expiry_time,omitempty"`
}

func (x *ThriweBenefitsPackageDetails) Reset() {
	*x = ThriweBenefitsPackageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThriweBenefitsPackageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThriweBenefitsPackageDetails) ProtoMessage() {}

func (x *ThriweBenefitsPackageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThriweBenefitsPackageDetails.ProtoReflect.Descriptor instead.
func (*ThriweBenefitsPackageDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{5}
}

func (x *ThriweBenefitsPackageDetails) GetExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTime
	}
	return nil
}

type VistaraAirMilesDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the url where the user can view the redeemed club vistara points
	ActivationUrl string `protobuf:"bytes,1,opt,name=activation_url,json=activationUrl,proto3" json:"activation_url,omitempty"`
	// denotes the expiry of redeemed club vistara points
	ExpiryTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expiry_time,json=expiryTime,proto3" json:"expiry_time,omitempty"`
	// denotes number of club vistara points user will get on successful redemption
	CvPoints int32 `protobuf:"varint,3,opt,name=cv_points,json=cvPoints,proto3" json:"cv_points,omitempty"`
}

func (x *VistaraAirMilesDetails) Reset() {
	*x = VistaraAirMilesDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VistaraAirMilesDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VistaraAirMilesDetails) ProtoMessage() {}

func (x *VistaraAirMilesDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VistaraAirMilesDetails.ProtoReflect.Descriptor instead.
func (*VistaraAirMilesDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{6}
}

func (x *VistaraAirMilesDetails) GetActivationUrl() string {
	if x != nil {
		return x.ActivationUrl
	}
	return ""
}

func (x *VistaraAirMilesDetails) GetExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTime
	}
	return nil
}

func (x *VistaraAirMilesDetails) GetCvPoints() int32 {
	if x != nil {
		return x.CvPoints
	}
	return 0
}

type RedeemedOffer_RedeemedOfferDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to OfferTypeSpecificDetails:
	//
	//	*RedeemedOffer_RedeemedOfferDetails_EgiftCardDetails
	//	*RedeemedOffer_RedeemedOfferDetails_CharityDetails
	//	*RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails
	//	*RedeemedOffer_RedeemedOfferDetails_PowerUpDetails
	//	*RedeemedOffer_RedeemedOfferDetails_ThriweBenefitsPackageDetails
	//	*RedeemedOffer_RedeemedOfferDetails_VistaraAirMilesDetails
	OfferTypeSpecificDetails isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails `protobuf_oneof:"offer_type_specific_details"`
	DynamicFields            []*RedeemedOffer_RedeemedOfferDetails_DynamicField            `protobuf:"bytes,10,rep,name=dynamic_fields,json=dynamicFields,proto3" json:"dynamic_fields,omitempty"`
}

func (x *RedeemedOffer_RedeemedOfferDetails) Reset() {
	*x = RedeemedOffer_RedeemedOfferDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeemedOffer_RedeemedOfferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemedOffer_RedeemedOfferDetails) ProtoMessage() {}

func (x *RedeemedOffer_RedeemedOfferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemedOffer_RedeemedOfferDetails.ProtoReflect.Descriptor instead.
func (*RedeemedOffer_RedeemedOfferDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{0, 0}
}

func (m *RedeemedOffer_RedeemedOfferDetails) GetOfferTypeSpecificDetails() isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails {
	if m != nil {
		return m.OfferTypeSpecificDetails
	}
	return nil
}

func (x *RedeemedOffer_RedeemedOfferDetails) GetEgiftCardDetails() *EGiftCardDetails {
	if x, ok := x.GetOfferTypeSpecificDetails().(*RedeemedOffer_RedeemedOfferDetails_EgiftCardDetails); ok {
		return x.EgiftCardDetails
	}
	return nil
}

func (x *RedeemedOffer_RedeemedOfferDetails) GetCharityDetails() *CharityDetails {
	if x, ok := x.GetOfferTypeSpecificDetails().(*RedeemedOffer_RedeemedOfferDetails_CharityDetails); ok {
		return x.CharityDetails
	}
	return nil
}

func (x *RedeemedOffer_RedeemedOfferDetails) GetPhysicalMerchandiseDetails() *PhysicalMerchandiseDetails {
	if x, ok := x.GetOfferTypeSpecificDetails().(*RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails); ok {
		return x.PhysicalMerchandiseDetails
	}
	return nil
}

func (x *RedeemedOffer_RedeemedOfferDetails) GetPowerUpDetails() *PowerUpDetails {
	if x, ok := x.GetOfferTypeSpecificDetails().(*RedeemedOffer_RedeemedOfferDetails_PowerUpDetails); ok {
		return x.PowerUpDetails
	}
	return nil
}

func (x *RedeemedOffer_RedeemedOfferDetails) GetThriweBenefitsPackageDetails() *ThriweBenefitsPackageDetails {
	if x, ok := x.GetOfferTypeSpecificDetails().(*RedeemedOffer_RedeemedOfferDetails_ThriweBenefitsPackageDetails); ok {
		return x.ThriweBenefitsPackageDetails
	}
	return nil
}

func (x *RedeemedOffer_RedeemedOfferDetails) GetVistaraAirMilesDetails() *VistaraAirMilesDetails {
	if x, ok := x.GetOfferTypeSpecificDetails().(*RedeemedOffer_RedeemedOfferDetails_VistaraAirMilesDetails); ok {
		return x.VistaraAirMilesDetails
	}
	return nil
}

func (x *RedeemedOffer_RedeemedOfferDetails) GetDynamicFields() []*RedeemedOffer_RedeemedOfferDetails_DynamicField {
	if x != nil {
		return x.DynamicFields
	}
	return nil
}

type isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails interface {
	isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails()
}

type RedeemedOffer_RedeemedOfferDetails_EgiftCardDetails struct {
	EgiftCardDetails *EGiftCardDetails `protobuf:"bytes,1,opt,name=egift_card_details,json=egiftCardDetails,proto3,oneof"`
}

type RedeemedOffer_RedeemedOfferDetails_CharityDetails struct {
	CharityDetails *CharityDetails `protobuf:"bytes,2,opt,name=charity_details,json=charityDetails,proto3,oneof"`
}

type RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails struct {
	PhysicalMerchandiseDetails *PhysicalMerchandiseDetails `protobuf:"bytes,3,opt,name=physical_merchandise_details,json=physicalMerchandiseDetails,proto3,oneof"`
}

type RedeemedOffer_RedeemedOfferDetails_PowerUpDetails struct {
	PowerUpDetails *PowerUpDetails `protobuf:"bytes,4,opt,name=power_up_details,json=powerUpDetails,proto3,oneof"`
}

type RedeemedOffer_RedeemedOfferDetails_ThriweBenefitsPackageDetails struct {
	ThriweBenefitsPackageDetails *ThriweBenefitsPackageDetails `protobuf:"bytes,5,opt,name=thriwe_benefits_package_details,json=thriweBenefitsPackageDetails,proto3,oneof"`
}

type RedeemedOffer_RedeemedOfferDetails_VistaraAirMilesDetails struct {
	VistaraAirMilesDetails *VistaraAirMilesDetails `protobuf:"bytes,6,opt,name=vistara_air_miles_details,json=vistaraAirMilesDetails,proto3,oneof"`
}

func (*RedeemedOffer_RedeemedOfferDetails_EgiftCardDetails) isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails() {
}

func (*RedeemedOffer_RedeemedOfferDetails_CharityDetails) isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails() {
}

func (*RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails) isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails() {
}

func (*RedeemedOffer_RedeemedOfferDetails_PowerUpDetails) isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails() {
}

func (*RedeemedOffer_RedeemedOfferDetails_ThriweBenefitsPackageDetails) isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails() {
}

func (*RedeemedOffer_RedeemedOfferDetails_VistaraAirMilesDetails) isRedeemedOffer_RedeemedOfferDetails_OfferTypeSpecificDetails() {
}

// DynamicField contains data to show extra field for a redeemed offer on app.
// It helps in showing a new field on the APP for already supported offer types
// without requiring any client side change.
type RedeemedOffer_RedeemedOfferDetails_DynamicField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value      string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	IsSharable bool   `protobuf:"varint,3,opt,name=is_sharable,json=isSharable,proto3" json:"is_sharable,omitempty"`
}

func (x *RedeemedOffer_RedeemedOfferDetails_DynamicField) Reset() {
	*x = RedeemedOffer_RedeemedOfferDetails_DynamicField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeemedOffer_RedeemedOfferDetails_DynamicField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemedOffer_RedeemedOfferDetails_DynamicField) ProtoMessage() {}

func (x *RedeemedOffer_RedeemedOfferDetails_DynamicField) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_rewards_redeemed_offer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemedOffer_RedeemedOfferDetails_DynamicField.ProtoReflect.Descriptor instead.
func (*RedeemedOffer_RedeemedOfferDetails_DynamicField) Descriptor() ([]byte, []int) {
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *RedeemedOffer_RedeemedOfferDetails_DynamicField) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RedeemedOffer_RedeemedOfferDetails_DynamicField) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *RedeemedOffer_RedeemedOfferDetails_DynamicField) GetIsSharable() bool {
	if x != nil {
		return x.IsSharable
	}
	return false
}

var File_api_frontend_rewards_redeemed_offer_proto protoreflect.FileDescriptor

var file_api_frontend_rewards_redeemed_offer_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x1a, 0x2d, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65,
	0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61,
	0x6c, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaf, 0x0c, 0x0a, 0x0d, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x05, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x05, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x5c, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6a, 0x0a, 0x16, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x72, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x10, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x72, 0x65,
	0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x6c, 0x0a,
	0x1f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1c, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x54, 0x61, 0x67, 0x73, 0x12, 0x50, 0x0a, 0x23, 0x73, 0x68, 0x6f, 0x75,
	0x6c, 0x64, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x72,
	0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x1f, 0x73, 0x68, 0x6f, 0x75, 0x6c,
	0x64, 0x4e, 0x6f, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x64, 0x65, 0x6d,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x4f, 0x0a, 0x10, 0x72, 0x65,
	0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0xbb, 0x06, 0x0a, 0x14,
	0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x52, 0x0a, 0x12, 0x65, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x45, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x10, 0x65, 0x67, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4b, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x72,
	0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x70, 0x0a, 0x1c, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61,
	0x6c, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x64, 0x69, 0x73, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x50,
	0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x64, 0x69,
	0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x1a, 0x70, 0x68, 0x79,
	0x73, 0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x64, 0x69, 0x73, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x10, 0x70, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x75, 0x70, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x70, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x70, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x77, 0x0a, 0x1f, 0x74, 0x68, 0x72, 0x69, 0x77, 0x65, 0x5f,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x54, 0x68, 0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00,
	0x52, 0x1c, 0x74, 0x68, 0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x65,
	0x0a, 0x19, 0x76, 0x69, 0x73, 0x74, 0x61, 0x72, 0x61, 0x5f, 0x61, 0x69, 0x72, 0x5f, 0x6d, 0x69,
	0x6c, 0x65, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x74, 0x61, 0x72, 0x61, 0x41, 0x69, 0x72, 0x4d,
	0x69, 0x6c, 0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x16, 0x76,
	0x69, 0x73, 0x74, 0x61, 0x72, 0x61, 0x41, 0x69, 0x72, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x68, 0x0a, 0x0e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x52, 0x0d, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a,
	0x59, 0x0a, 0x0c, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x73, 0x68, 0x61, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x53, 0x68, 0x61, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x1d, 0x0a, 0x1b, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xca, 0x01, 0x0a, 0x10, 0x45, 0x47,
	0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x69, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x72, 0x69, 0x74,
	0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x63, 0x0a, 0x1a, 0x50, 0x68, 0x79, 0x73,
	0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x64, 0x69, 0x73, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0f, 0x73, 0x68,
	0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x10, 0x0a,
	0x0e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22,
	0x5b, 0x0a, 0x1c, 0x54, 0x68, 0x72, 0x69, 0x77, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x99, 0x01, 0x0a,
	0x16, 0x56, 0x69, 0x73, 0x74, 0x61, 0x72, 0x61, 0x41, 0x69, 0x72, 0x4d, 0x69, 0x6c, 0x65, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x3b,
	0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x76, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x63, 0x76, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x2a, 0x84, 0x02, 0x0a, 0x1d, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x0a, 0x12, 0x30, 0x0a, 0x2c, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x14, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x32, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x64, 0x42,
	0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5a, 0x2b,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_rewards_redeemed_offer_proto_rawDescOnce sync.Once
	file_api_frontend_rewards_redeemed_offer_proto_rawDescData = file_api_frontend_rewards_redeemed_offer_proto_rawDesc
)

func file_api_frontend_rewards_redeemed_offer_proto_rawDescGZIP() []byte {
	file_api_frontend_rewards_redeemed_offer_proto_rawDescOnce.Do(func() {
		file_api_frontend_rewards_redeemed_offer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_rewards_redeemed_offer_proto_rawDescData)
	})
	return file_api_frontend_rewards_redeemed_offer_proto_rawDescData
}

var file_api_frontend_rewards_redeemed_offer_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_frontend_rewards_redeemed_offer_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_frontend_rewards_redeemed_offer_proto_goTypes = []interface{}{
	(RedeemedOfferProcessingStatus)(0),                      // 0: frontend.rewards.RedeemedOfferProcessingStatus
	(*RedeemedOffer)(nil),                                   // 1: frontend.rewards.RedeemedOffer
	(*EGiftCardDetails)(nil),                                // 2: frontend.rewards.EGiftCardDetails
	(*CharityDetails)(nil),                                  // 3: frontend.rewards.CharityDetails
	(*PhysicalMerchandiseDetails)(nil),                      // 4: frontend.rewards.PhysicalMerchandiseDetails
	(*PowerUpDetails)(nil),                                  // 5: frontend.rewards.PowerUpDetails
	(*ThriweBenefitsPackageDetails)(nil),                    // 6: frontend.rewards.ThriweBenefitsPackageDetails
	(*VistaraAirMilesDetails)(nil),                          // 7: frontend.rewards.VistaraAirMilesDetails
	(*RedeemedOffer_RedeemedOfferDetails)(nil),              // 8: frontend.rewards.RedeemedOffer.RedeemedOfferDetails
	(*RedeemedOffer_RedeemedOfferDetails_DynamicField)(nil), // 9: frontend.rewards.RedeemedOffer.RedeemedOfferDetails.DynamicField
	(*Offer)(nil),                                           // 10: frontend.rewards.Offer
	(*timestamppb.Timestamp)(nil),                           // 11: google.protobuf.Timestamp
	(*OfferDetailsSection)(nil),                             // 12: frontend.rewards.OfferDetailsSection
	(*common.Text)(nil),                                     // 13: api.typesv2.common.Text
	(*ui.VerticalKeyValuePair)(nil),                         // 14: api.typesv2.ui.VerticalKeyValuePair
	(*typesv2.PostalAddress)(nil),                           // 15: api.typesv2.PostalAddress
}
var file_api_frontend_rewards_redeemed_offer_proto_depIdxs = []int32{
	10, // 0: frontend.rewards.RedeemedOffer.offer:type_name -> frontend.rewards.Offer
	0,  // 1: frontend.rewards.RedeemedOffer.processing_status:type_name -> frontend.rewards.RedeemedOfferProcessingStatus
	8,  // 2: frontend.rewards.RedeemedOffer.redeemed_offer_details:type_name -> frontend.rewards.RedeemedOffer.RedeemedOfferDetails
	11, // 3: frontend.rewards.RedeemedOffer.created_at:type_name -> google.protobuf.Timestamp
	12, // 4: frontend.rewards.RedeemedOffer.redeemed_offer_details_sections:type_name -> frontend.rewards.OfferDetailsSection
	13, // 5: frontend.rewards.RedeemedOffer.display_tags:type_name -> api.typesv2.common.Text
	14, // 6: frontend.rewards.RedeemedOffer.redemption_value:type_name -> api.typesv2.ui.VerticalKeyValuePair
	11, // 7: frontend.rewards.EGiftCardDetails.expiry_time:type_name -> google.protobuf.Timestamp
	15, // 8: frontend.rewards.PhysicalMerchandiseDetails.shipping_address:type_name -> api.typesv2.PostalAddress
	11, // 9: frontend.rewards.ThriweBenefitsPackageDetails.expiry_time:type_name -> google.protobuf.Timestamp
	11, // 10: frontend.rewards.VistaraAirMilesDetails.expiry_time:type_name -> google.protobuf.Timestamp
	2,  // 11: frontend.rewards.RedeemedOffer.RedeemedOfferDetails.egift_card_details:type_name -> frontend.rewards.EGiftCardDetails
	3,  // 12: frontend.rewards.RedeemedOffer.RedeemedOfferDetails.charity_details:type_name -> frontend.rewards.CharityDetails
	4,  // 13: frontend.rewards.RedeemedOffer.RedeemedOfferDetails.physical_merchandise_details:type_name -> frontend.rewards.PhysicalMerchandiseDetails
	5,  // 14: frontend.rewards.RedeemedOffer.RedeemedOfferDetails.power_up_details:type_name -> frontend.rewards.PowerUpDetails
	6,  // 15: frontend.rewards.RedeemedOffer.RedeemedOfferDetails.thriwe_benefits_package_details:type_name -> frontend.rewards.ThriweBenefitsPackageDetails
	7,  // 16: frontend.rewards.RedeemedOffer.RedeemedOfferDetails.vistara_air_miles_details:type_name -> frontend.rewards.VistaraAirMilesDetails
	9,  // 17: frontend.rewards.RedeemedOffer.RedeemedOfferDetails.dynamic_fields:type_name -> frontend.rewards.RedeemedOffer.RedeemedOfferDetails.DynamicField
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_frontend_rewards_redeemed_offer_proto_init() }
func file_api_frontend_rewards_redeemed_offer_proto_init() {
	if File_api_frontend_rewards_redeemed_offer_proto != nil {
		return
	}
	file_api_frontend_rewards_display_components_proto_init()
	file_api_frontend_rewards_offer_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedeemedOffer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EGiftCardDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CharityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalMerchandiseDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerUpDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThriweBenefitsPackageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VistaraAirMilesDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedeemedOffer_RedeemedOfferDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_rewards_redeemed_offer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedeemedOffer_RedeemedOfferDetails_DynamicField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_rewards_redeemed_offer_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*RedeemedOffer_RedeemedOfferDetails_EgiftCardDetails)(nil),
		(*RedeemedOffer_RedeemedOfferDetails_CharityDetails)(nil),
		(*RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails)(nil),
		(*RedeemedOffer_RedeemedOfferDetails_PowerUpDetails)(nil),
		(*RedeemedOffer_RedeemedOfferDetails_ThriweBenefitsPackageDetails)(nil),
		(*RedeemedOffer_RedeemedOfferDetails_VistaraAirMilesDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_rewards_redeemed_offer_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_rewards_redeemed_offer_proto_goTypes,
		DependencyIndexes: file_api_frontend_rewards_redeemed_offer_proto_depIdxs,
		EnumInfos:         file_api_frontend_rewards_redeemed_offer_proto_enumTypes,
		MessageInfos:      file_api_frontend_rewards_redeemed_offer_proto_msgTypes,
	}.Build()
	File_api_frontend_rewards_redeemed_offer_proto = out.File
	file_api_frontend_rewards_redeemed_offer_proto_rawDesc = nil
	file_api_frontend_rewards_redeemed_offer_proto_goTypes = nil
	file_api_frontend_rewards_redeemed_offer_proto_depIdxs = nil
}
