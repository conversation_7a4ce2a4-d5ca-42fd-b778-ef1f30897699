// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/rewards/redeemed_offer.proto

package rewards

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RedeemedOffer with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RedeemedOffer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedeemedOffer with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RedeemedOfferMultiError, or
// nil if none found.
func (m *RedeemedOffer) ValidateAll() error {
	return m.validate(true)
}

func (m *RedeemedOffer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedeemedOfferValidationError{
					field:  "Offer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedeemedOfferValidationError{
					field:  "Offer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedeemedOfferValidationError{
				field:  "Offer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessingStatus

	if all {
		switch v := interface{}(m.GetRedeemedOfferDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedeemedOfferValidationError{
					field:  "RedeemedOfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedeemedOfferValidationError{
					field:  "RedeemedOfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedeemedOfferDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedeemedOfferValidationError{
				field:  "RedeemedOfferDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedeemedOfferValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedeemedOfferValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedeemedOfferValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalId

	// no validation rules for RedemptionPrice

	for idx, item := range m.GetRedeemedOfferDetailsSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOfferValidationError{
						field:  fmt.Sprintf("RedeemedOfferDetailsSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOfferValidationError{
						field:  fmt.Sprintf("RedeemedOfferDetailsSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOfferValidationError{
					field:  fmt.Sprintf("RedeemedOfferDetailsSections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDisplayTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOfferValidationError{
						field:  fmt.Sprintf("DisplayTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOfferValidationError{
						field:  fmt.Sprintf("DisplayTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOfferValidationError{
					field:  fmt.Sprintf("DisplayTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ShouldNotDisplayRedemptionPrice

	if all {
		switch v := interface{}(m.GetRedemptionValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RedeemedOfferValidationError{
					field:  "RedemptionValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RedeemedOfferValidationError{
					field:  "RedemptionValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedemptionValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RedeemedOfferValidationError{
				field:  "RedemptionValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RedeemedOfferMultiError(errors)
	}

	return nil
}

// RedeemedOfferMultiError is an error wrapping multiple validation errors
// returned by RedeemedOffer.ValidateAll() if the designated constraints
// aren't met.
type RedeemedOfferMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedeemedOfferMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedeemedOfferMultiError) AllErrors() []error { return m }

// RedeemedOfferValidationError is the validation error returned by
// RedeemedOffer.Validate if the designated constraints aren't met.
type RedeemedOfferValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedeemedOfferValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedeemedOfferValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedeemedOfferValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedeemedOfferValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedeemedOfferValidationError) ErrorName() string { return "RedeemedOfferValidationError" }

// Error satisfies the builtin error interface
func (e RedeemedOfferValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedeemedOffer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedeemedOfferValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedeemedOfferValidationError{}

// Validate checks the field values on EGiftCardDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EGiftCardDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EGiftCardDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EGiftCardDetailsMultiError, or nil if none found.
func (m *EGiftCardDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *EGiftCardDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardNo

	// no validation rules for Pin

	// no validation rules for ActivationCode

	// no validation rules for ActivationUrl

	if all {
		switch v := interface{}(m.GetExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EGiftCardDetailsValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EGiftCardDetailsValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EGiftCardDetailsValidationError{
				field:  "ExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EGiftCardDetailsMultiError(errors)
	}

	return nil
}

// EGiftCardDetailsMultiError is an error wrapping multiple validation errors
// returned by EGiftCardDetails.ValidateAll() if the designated constraints
// aren't met.
type EGiftCardDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EGiftCardDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EGiftCardDetailsMultiError) AllErrors() []error { return m }

// EGiftCardDetailsValidationError is the validation error returned by
// EGiftCardDetails.Validate if the designated constraints aren't met.
type EGiftCardDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EGiftCardDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EGiftCardDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EGiftCardDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EGiftCardDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EGiftCardDetailsValidationError) ErrorName() string { return "EGiftCardDetailsValidationError" }

// Error satisfies the builtin error interface
func (e EGiftCardDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEGiftCardDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EGiftCardDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EGiftCardDetailsValidationError{}

// Validate checks the field values on CharityDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CharityDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CharityDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CharityDetailsMultiError,
// or nil if none found.
func (m *CharityDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CharityDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CharityDetailsMultiError(errors)
	}

	return nil
}

// CharityDetailsMultiError is an error wrapping multiple validation errors
// returned by CharityDetails.ValidateAll() if the designated constraints
// aren't met.
type CharityDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CharityDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CharityDetailsMultiError) AllErrors() []error { return m }

// CharityDetailsValidationError is the validation error returned by
// CharityDetails.Validate if the designated constraints aren't met.
type CharityDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CharityDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CharityDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CharityDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CharityDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CharityDetailsValidationError) ErrorName() string { return "CharityDetailsValidationError" }

// Error satisfies the builtin error interface
func (e CharityDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCharityDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CharityDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CharityDetailsValidationError{}

// Validate checks the field values on PhysicalMerchandiseDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhysicalMerchandiseDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhysicalMerchandiseDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhysicalMerchandiseDetailsMultiError, or nil if none found.
func (m *PhysicalMerchandiseDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalMerchandiseDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetShippingAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalMerchandiseDetailsValidationError{
					field:  "ShippingAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalMerchandiseDetailsValidationError{
					field:  "ShippingAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShippingAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalMerchandiseDetailsValidationError{
				field:  "ShippingAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PhysicalMerchandiseDetailsMultiError(errors)
	}

	return nil
}

// PhysicalMerchandiseDetailsMultiError is an error wrapping multiple
// validation errors returned by PhysicalMerchandiseDetails.ValidateAll() if
// the designated constraints aren't met.
type PhysicalMerchandiseDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalMerchandiseDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalMerchandiseDetailsMultiError) AllErrors() []error { return m }

// PhysicalMerchandiseDetailsValidationError is the validation error returned
// by PhysicalMerchandiseDetails.Validate if the designated constraints aren't met.
type PhysicalMerchandiseDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalMerchandiseDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhysicalMerchandiseDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhysicalMerchandiseDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhysicalMerchandiseDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhysicalMerchandiseDetailsValidationError) ErrorName() string {
	return "PhysicalMerchandiseDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalMerchandiseDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalMerchandiseDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalMerchandiseDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalMerchandiseDetailsValidationError{}

// Validate checks the field values on PowerUpDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PowerUpDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PowerUpDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PowerUpDetailsMultiError,
// or nil if none found.
func (m *PowerUpDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PowerUpDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PowerUpDetailsMultiError(errors)
	}

	return nil
}

// PowerUpDetailsMultiError is an error wrapping multiple validation errors
// returned by PowerUpDetails.ValidateAll() if the designated constraints
// aren't met.
type PowerUpDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PowerUpDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PowerUpDetailsMultiError) AllErrors() []error { return m }

// PowerUpDetailsValidationError is the validation error returned by
// PowerUpDetails.Validate if the designated constraints aren't met.
type PowerUpDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PowerUpDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PowerUpDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PowerUpDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PowerUpDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PowerUpDetailsValidationError) ErrorName() string { return "PowerUpDetailsValidationError" }

// Error satisfies the builtin error interface
func (e PowerUpDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPowerUpDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PowerUpDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PowerUpDetailsValidationError{}

// Validate checks the field values on ThriweBenefitsPackageDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThriweBenefitsPackageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThriweBenefitsPackageDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThriweBenefitsPackageDetailsMultiError, or nil if none found.
func (m *ThriweBenefitsPackageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ThriweBenefitsPackageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ThriweBenefitsPackageDetailsValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ThriweBenefitsPackageDetailsValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ThriweBenefitsPackageDetailsValidationError{
				field:  "ExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ThriweBenefitsPackageDetailsMultiError(errors)
	}

	return nil
}

// ThriweBenefitsPackageDetailsMultiError is an error wrapping multiple
// validation errors returned by ThriweBenefitsPackageDetails.ValidateAll() if
// the designated constraints aren't met.
type ThriweBenefitsPackageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThriweBenefitsPackageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThriweBenefitsPackageDetailsMultiError) AllErrors() []error { return m }

// ThriweBenefitsPackageDetailsValidationError is the validation error returned
// by ThriweBenefitsPackageDetails.Validate if the designated constraints
// aren't met.
type ThriweBenefitsPackageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThriweBenefitsPackageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThriweBenefitsPackageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThriweBenefitsPackageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThriweBenefitsPackageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThriweBenefitsPackageDetailsValidationError) ErrorName() string {
	return "ThriweBenefitsPackageDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ThriweBenefitsPackageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThriweBenefitsPackageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThriweBenefitsPackageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThriweBenefitsPackageDetailsValidationError{}

// Validate checks the field values on VistaraAirMilesDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VistaraAirMilesDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VistaraAirMilesDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VistaraAirMilesDetailsMultiError, or nil if none found.
func (m *VistaraAirMilesDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *VistaraAirMilesDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActivationUrl

	if all {
		switch v := interface{}(m.GetExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VistaraAirMilesDetailsValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VistaraAirMilesDetailsValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VistaraAirMilesDetailsValidationError{
				field:  "ExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CvPoints

	if len(errors) > 0 {
		return VistaraAirMilesDetailsMultiError(errors)
	}

	return nil
}

// VistaraAirMilesDetailsMultiError is an error wrapping multiple validation
// errors returned by VistaraAirMilesDetails.ValidateAll() if the designated
// constraints aren't met.
type VistaraAirMilesDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VistaraAirMilesDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VistaraAirMilesDetailsMultiError) AllErrors() []error { return m }

// VistaraAirMilesDetailsValidationError is the validation error returned by
// VistaraAirMilesDetails.Validate if the designated constraints aren't met.
type VistaraAirMilesDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VistaraAirMilesDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VistaraAirMilesDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VistaraAirMilesDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VistaraAirMilesDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VistaraAirMilesDetailsValidationError) ErrorName() string {
	return "VistaraAirMilesDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e VistaraAirMilesDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVistaraAirMilesDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VistaraAirMilesDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VistaraAirMilesDetailsValidationError{}

// Validate checks the field values on RedeemedOffer_RedeemedOfferDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RedeemedOffer_RedeemedOfferDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RedeemedOffer_RedeemedOfferDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RedeemedOffer_RedeemedOfferDetailsMultiError, or nil if none found.
func (m *RedeemedOffer_RedeemedOfferDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RedeemedOffer_RedeemedOfferDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDynamicFields() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  fmt.Sprintf("DynamicFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  fmt.Sprintf("DynamicFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOffer_RedeemedOfferDetailsValidationError{
					field:  fmt.Sprintf("DynamicFields[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	switch v := m.OfferTypeSpecificDetails.(type) {
	case *RedeemedOffer_RedeemedOfferDetails_EgiftCardDetails:
		if v == nil {
			err := RedeemedOffer_RedeemedOfferDetailsValidationError{
				field:  "OfferTypeSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEgiftCardDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "EgiftCardDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "EgiftCardDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEgiftCardDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOffer_RedeemedOfferDetailsValidationError{
					field:  "EgiftCardDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RedeemedOffer_RedeemedOfferDetails_CharityDetails:
		if v == nil {
			err := RedeemedOffer_RedeemedOfferDetailsValidationError{
				field:  "OfferTypeSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCharityDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "CharityDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "CharityDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCharityDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOffer_RedeemedOfferDetailsValidationError{
					field:  "CharityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails:
		if v == nil {
			err := RedeemedOffer_RedeemedOfferDetailsValidationError{
				field:  "OfferTypeSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhysicalMerchandiseDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "PhysicalMerchandiseDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "PhysicalMerchandiseDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhysicalMerchandiseDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOffer_RedeemedOfferDetailsValidationError{
					field:  "PhysicalMerchandiseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RedeemedOffer_RedeemedOfferDetails_PowerUpDetails:
		if v == nil {
			err := RedeemedOffer_RedeemedOfferDetailsValidationError{
				field:  "OfferTypeSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPowerUpDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "PowerUpDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "PowerUpDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPowerUpDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOffer_RedeemedOfferDetailsValidationError{
					field:  "PowerUpDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RedeemedOffer_RedeemedOfferDetails_ThriweBenefitsPackageDetails:
		if v == nil {
			err := RedeemedOffer_RedeemedOfferDetailsValidationError{
				field:  "OfferTypeSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetThriweBenefitsPackageDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "ThriweBenefitsPackageDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "ThriweBenefitsPackageDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetThriweBenefitsPackageDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOffer_RedeemedOfferDetailsValidationError{
					field:  "ThriweBenefitsPackageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RedeemedOffer_RedeemedOfferDetails_VistaraAirMilesDetails:
		if v == nil {
			err := RedeemedOffer_RedeemedOfferDetailsValidationError{
				field:  "OfferTypeSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVistaraAirMilesDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "VistaraAirMilesDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RedeemedOffer_RedeemedOfferDetailsValidationError{
						field:  "VistaraAirMilesDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVistaraAirMilesDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RedeemedOffer_RedeemedOfferDetailsValidationError{
					field:  "VistaraAirMilesDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RedeemedOffer_RedeemedOfferDetailsMultiError(errors)
	}

	return nil
}

// RedeemedOffer_RedeemedOfferDetailsMultiError is an error wrapping multiple
// validation errors returned by
// RedeemedOffer_RedeemedOfferDetails.ValidateAll() if the designated
// constraints aren't met.
type RedeemedOffer_RedeemedOfferDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedeemedOffer_RedeemedOfferDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedeemedOffer_RedeemedOfferDetailsMultiError) AllErrors() []error { return m }

// RedeemedOffer_RedeemedOfferDetailsValidationError is the validation error
// returned by RedeemedOffer_RedeemedOfferDetails.Validate if the designated
// constraints aren't met.
type RedeemedOffer_RedeemedOfferDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedeemedOffer_RedeemedOfferDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RedeemedOffer_RedeemedOfferDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RedeemedOffer_RedeemedOfferDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedeemedOffer_RedeemedOfferDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedeemedOffer_RedeemedOfferDetailsValidationError) ErrorName() string {
	return "RedeemedOffer_RedeemedOfferDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RedeemedOffer_RedeemedOfferDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedeemedOffer_RedeemedOfferDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedeemedOffer_RedeemedOfferDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedeemedOffer_RedeemedOfferDetailsValidationError{}

// Validate checks the field values on
// RedeemedOffer_RedeemedOfferDetails_DynamicField with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RedeemedOffer_RedeemedOfferDetails_DynamicField) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RedeemedOffer_RedeemedOfferDetails_DynamicField with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// RedeemedOffer_RedeemedOfferDetails_DynamicFieldMultiError, or nil if none found.
func (m *RedeemedOffer_RedeemedOfferDetails_DynamicField) ValidateAll() error {
	return m.validate(true)
}

func (m *RedeemedOffer_RedeemedOfferDetails_DynamicField) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	// no validation rules for IsSharable

	if len(errors) > 0 {
		return RedeemedOffer_RedeemedOfferDetails_DynamicFieldMultiError(errors)
	}

	return nil
}

// RedeemedOffer_RedeemedOfferDetails_DynamicFieldMultiError is an error
// wrapping multiple validation errors returned by
// RedeemedOffer_RedeemedOfferDetails_DynamicField.ValidateAll() if the
// designated constraints aren't met.
type RedeemedOffer_RedeemedOfferDetails_DynamicFieldMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RedeemedOffer_RedeemedOfferDetails_DynamicFieldMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RedeemedOffer_RedeemedOfferDetails_DynamicFieldMultiError) AllErrors() []error { return m }

// RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError is the
// validation error returned by
// RedeemedOffer_RedeemedOfferDetails_DynamicField.Validate if the designated
// constraints aren't met.
type RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError) ErrorName() string {
	return "RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError"
}

// Error satisfies the builtin error interface
func (e RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRedeemedOffer_RedeemedOfferDetails_DynamicField.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RedeemedOffer_RedeemedOfferDetails_DynamicFieldValidationError{}
