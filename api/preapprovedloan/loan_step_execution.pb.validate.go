// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/preapprovedloan/loan_step_execution.proto

package preapprovedloan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/preapprovedloan/enums"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.MandateType(0)

	_ = typesv2.EmploymentType(0)
)

// Validate checks the field values on LoanStepExecution with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LoanStepExecution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanStepExecution with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanStepExecutionMultiError, or nil if none found.
func (m *LoanStepExecution) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanStepExecution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for RefId

	// no validation rules for Flow

	// no validation rules for OrchId

	// no validation rules for StepName

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanStepExecutionValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for SubStatus

	if all {
		switch v := interface{}(m.GetStaledAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "StaledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "StaledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStaledAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanStepExecutionValidationError{
				field:  "StaledAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanStepExecutionValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanStepExecutionValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanStepExecutionValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanStepExecutionValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanStepExecutionValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GroupStage

	if len(errors) > 0 {
		return LoanStepExecutionMultiError(errors)
	}

	return nil
}

// LoanStepExecutionMultiError is an error wrapping multiple validation errors
// returned by LoanStepExecution.ValidateAll() if the designated constraints
// aren't met.
type LoanStepExecutionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanStepExecutionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanStepExecutionMultiError) AllErrors() []error { return m }

// LoanStepExecutionValidationError is the validation error returned by
// LoanStepExecution.Validate if the designated constraints aren't met.
type LoanStepExecutionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanStepExecutionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanStepExecutionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanStepExecutionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanStepExecutionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanStepExecutionValidationError) ErrorName() string {
	return "LoanStepExecutionValidationError"
}

// Error satisfies the builtin error interface
func (e LoanStepExecutionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanStepExecution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanStepExecutionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanStepExecutionValidationError{}

// Validate checks the field values on LivenessStepData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LivenessStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessStepDataMultiError, or nil if none found.
func (m *LivenessStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttemptId

	// no validation rules for Otp

	{
		sorted_keys := make([]string, len(m.GetNotificationTypeIdMap()))
		i := 0
		for key := range m.GetNotificationTypeIdMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetNotificationTypeIdMap()[key]
			_ = val

			// no validation rules for NotificationTypeIdMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, LivenessStepDataValidationError{
							field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, LivenessStepDataValidationError{
							field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return LivenessStepDataValidationError{
						field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return LivenessStepDataMultiError(errors)
	}

	return nil
}

// LivenessStepDataMultiError is an error wrapping multiple validation errors
// returned by LivenessStepData.ValidateAll() if the designated constraints
// aren't met.
type LivenessStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessStepDataMultiError) AllErrors() []error { return m }

// LivenessStepDataValidationError is the validation error returned by
// LivenessStepData.Validate if the designated constraints aren't met.
type LivenessStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessStepDataValidationError) ErrorName() string { return "LivenessStepDataValidationError" }

// Error satisfies the builtin error interface
func (e LivenessStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessStepDataValidationError{}

// Validate checks the field values on FaceMatchStepData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FaceMatchStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FaceMatchStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FaceMatchStepDataMultiError, or nil if none found.
func (m *FaceMatchStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *FaceMatchStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttemptId

	if len(errors) > 0 {
		return FaceMatchStepDataMultiError(errors)
	}

	return nil
}

// FaceMatchStepDataMultiError is an error wrapping multiple validation errors
// returned by FaceMatchStepData.ValidateAll() if the designated constraints
// aren't met.
type FaceMatchStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FaceMatchStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FaceMatchStepDataMultiError) AllErrors() []error { return m }

// FaceMatchStepDataValidationError is the validation error returned by
// FaceMatchStepData.Validate if the designated constraints aren't met.
type FaceMatchStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FaceMatchStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FaceMatchStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FaceMatchStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FaceMatchStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FaceMatchStepDataValidationError) ErrorName() string {
	return "FaceMatchStepDataValidationError"
}

// Error satisfies the builtin error interface
func (e FaceMatchStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFaceMatchStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FaceMatchStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FaceMatchStepDataValidationError{}

// Validate checks the field values on ESignStepData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ESignStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ESignStepData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ESignStepDataMultiError, or
// nil if none found.
func (m *ESignStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *ESignStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SignUrl

	if all {
		switch v := interface{}(m.GetExpiryAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ESignStepDataValidationError{
				field:  "ExpiryAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetNotificationTypeIdMap()))
		i := 0
		for key := range m.GetNotificationTypeIdMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetNotificationTypeIdMap()[key]
			_ = val

			// no validation rules for NotificationTypeIdMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ESignStepDataValidationError{
							field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ESignStepDataValidationError{
							field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ESignStepDataValidationError{
						field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetOtpInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "OtpInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "OtpInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtpInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ESignStepDataValidationError{
				field:  "OtpInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DocumentId

	// no validation rules for AwsDestinationPath

	if all {
		switch v := interface{}(m.GetKfsDocument()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "KfsDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "KfsDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKfsDocument()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ESignStepDataValidationError{
				field:  "KfsDocument",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanAgreementDocument()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "LoanAgreementDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "LoanAgreementDocument",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAgreementDocument()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ESignStepDataValidationError{
				field:  "LoanAgreementDocument",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRoiModificationDeadline()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "RoiModificationDeadline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ESignStepDataValidationError{
					field:  "RoiModificationDeadline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRoiModificationDeadline()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ESignStepDataValidationError{
				field:  "RoiModificationDeadline",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ESignStepDataMultiError(errors)
	}

	return nil
}

// ESignStepDataMultiError is an error wrapping multiple validation errors
// returned by ESignStepData.ValidateAll() if the designated constraints
// aren't met.
type ESignStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ESignStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ESignStepDataMultiError) AllErrors() []error { return m }

// ESignStepDataValidationError is the validation error returned by
// ESignStepData.Validate if the designated constraints aren't met.
type ESignStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ESignStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ESignStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ESignStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ESignStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ESignStepDataValidationError) ErrorName() string { return "ESignStepDataValidationError" }

// Error satisfies the builtin error interface
func (e ESignStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sESignStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ESignStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ESignStepDataValidationError{}

// Validate checks the field values on LoanDocument with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanDocument) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanDocument with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoanDocumentMultiError, or
// nil if none found.
func (m *LoanDocument) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanDocument) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SignUrl

	// no validation rules for AwsDestinationPath

	if all {
		switch v := interface{}(m.GetExpiryAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDocumentValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDocumentValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDocumentValidationError{
				field:  "ExpiryAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SignedDocS3Path

	if len(errors) > 0 {
		return LoanDocumentMultiError(errors)
	}

	return nil
}

// LoanDocumentMultiError is an error wrapping multiple validation errors
// returned by LoanDocument.ValidateAll() if the designated constraints aren't met.
type LoanDocumentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanDocumentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanDocumentMultiError) AllErrors() []error { return m }

// LoanDocumentValidationError is the validation error returned by
// LoanDocument.Validate if the designated constraints aren't met.
type LoanDocumentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanDocumentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanDocumentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanDocumentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanDocumentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanDocumentValidationError) ErrorName() string { return "LoanDocumentValidationError" }

// Error satisfies the builtin error interface
func (e LoanDocumentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanDocument.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanDocumentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanDocumentValidationError{}

// Validate checks the field values on OtpInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OtpInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtpInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OtpInfoMultiError, or nil if none found.
func (m *OtpInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OtpInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Otp

	// no validation rules for MaxAttempts

	// no validation rules for AttemptsCount

	// no validation rules for LastEnteredOtp

	// no validation rules for Token

	if len(errors) > 0 {
		return OtpInfoMultiError(errors)
	}

	return nil
}

// OtpInfoMultiError is an error wrapping multiple validation errors returned
// by OtpInfo.ValidateAll() if the designated constraints aren't met.
type OtpInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtpInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtpInfoMultiError) AllErrors() []error { return m }

// OtpInfoValidationError is the validation error returned by OtpInfo.Validate
// if the designated constraints aren't met.
type OtpInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtpInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtpInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtpInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtpInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtpInfoValidationError) ErrorName() string { return "OtpInfoValidationError" }

// Error satisfies the builtin error interface
func (e OtpInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtpInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtpInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtpInfoValidationError{}

// Validate checks the field values on VkycStepData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VkycStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VkycStepData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VkycStepDataMultiError, or
// nil if none found.
func (m *VkycStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *VkycStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NotificationId

	{
		sorted_keys := make([]string, len(m.GetNotificationTypeIdMap()))
		i := 0
		for key := range m.GetNotificationTypeIdMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetNotificationTypeIdMap()[key]
			_ = val

			// no validation rules for NotificationTypeIdMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, VkycStepDataValidationError{
							field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, VkycStepDataValidationError{
							field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return VkycStepDataValidationError{
						field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for ApplicationId

	// no validation rules for CallId

	if len(errors) > 0 {
		return VkycStepDataMultiError(errors)
	}

	return nil
}

// VkycStepDataMultiError is an error wrapping multiple validation errors
// returned by VkycStepData.ValidateAll() if the designated constraints aren't met.
type VkycStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VkycStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VkycStepDataMultiError) AllErrors() []error { return m }

// VkycStepDataValidationError is the validation error returned by
// VkycStepData.Validate if the designated constraints aren't met.
type VkycStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VkycStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VkycStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VkycStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VkycStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VkycStepDataValidationError) ErrorName() string { return "VkycStepDataValidationError" }

// Error satisfies the builtin error interface
func (e VkycStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVkycStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VkycStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VkycStepDataValidationError{}

// Validate checks the field values on KycStepData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KycStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycStepData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KycStepDataMultiError, or
// nil if none found.
func (m *KycStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *KycStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KycUrl

	// no validation rules for KycTrackingId

	if all {
		switch v := interface{}(m.GetUrlGeneratedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycStepDataValidationError{
					field:  "UrlGeneratedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycStepDataValidationError{
					field:  "UrlGeneratedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUrlGeneratedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycStepDataValidationError{
				field:  "UrlGeneratedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KycStepDataMultiError(errors)
	}

	return nil
}

// KycStepDataMultiError is an error wrapping multiple validation errors
// returned by KycStepData.ValidateAll() if the designated constraints aren't met.
type KycStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycStepDataMultiError) AllErrors() []error { return m }

// KycStepDataValidationError is the validation error returned by
// KycStepData.Validate if the designated constraints aren't met.
type KycStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycStepDataValidationError) ErrorName() string { return "KycStepDataValidationError" }

// Error satisfies the builtin error interface
func (e KycStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycStepDataValidationError{}

// Validate checks the field values on CkycStepData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CkycStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CkycStepData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CkycStepDataMultiError, or
// nil if none found.
func (m *CkycStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *CkycStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CkycImagePath

	// no validation rules for EmploymentType

	// no validation rules for CkycId

	// no validation rules for PositiveConfirmation

	// no validation rules for PAN

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycStepDataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycStepDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycStepDataValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCorrespondenceAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCorrespondenceAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycStepDataValidationError{
				field:  "CorrespondenceAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFathersName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "FathersName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "FathersName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFathersName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycStepDataValidationError{
				field:  "FathersName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMothersName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "MothersName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "MothersName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMothersName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycStepDataValidationError{
				field:  "MothersName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorKycId

	if all {
		switch v := interface{}(m.GetKycAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "KycAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycStepDataValidationError{
					field:  "KycAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycStepDataValidationError{
				field:  "KycAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.VendorSpecificDetails.(type) {
	case *CkycStepData_Idfc:
		if v == nil {
			err := CkycStepDataValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIdfc()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CkycStepDataValidationError{
						field:  "Idfc",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CkycStepDataValidationError{
						field:  "Idfc",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIdfc()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CkycStepDataValidationError{
					field:  "Idfc",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CkycStepData_Abfl:
		if v == nil {
			err := CkycStepDataValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAbfl()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CkycStepDataValidationError{
						field:  "Abfl",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CkycStepDataValidationError{
						field:  "Abfl",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAbfl()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CkycStepDataValidationError{
					field:  "Abfl",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CkycStepData_SgData:
		if v == nil {
			err := CkycStepDataValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSgData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CkycStepDataValidationError{
						field:  "SgData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CkycStepDataValidationError{
						field:  "SgData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSgData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CkycStepDataValidationError{
					field:  "SgData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CkycStepDataMultiError(errors)
	}

	return nil
}

// CkycStepDataMultiError is an error wrapping multiple validation errors
// returned by CkycStepData.ValidateAll() if the designated constraints aren't met.
type CkycStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CkycStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CkycStepDataMultiError) AllErrors() []error { return m }

// CkycStepDataValidationError is the validation error returned by
// CkycStepData.Validate if the designated constraints aren't met.
type CkycStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CkycStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CkycStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CkycStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CkycStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CkycStepDataValidationError) ErrorName() string { return "CkycStepDataValidationError" }

// Error satisfies the builtin error interface
func (e CkycStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCkycStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CkycStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CkycStepDataValidationError{}

// Validate checks the field values on AbflMandateData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AbflMandateData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AbflMandateData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AbflMandateDataMultiError, or nil if none found.
func (m *AbflMandateData) ValidateAll() error {
	return m.validate(true)
}

func (m *AbflMandateData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RpCustId

	// no validation rules for ShortUrl

	if len(errors) > 0 {
		return AbflMandateDataMultiError(errors)
	}

	return nil
}

// AbflMandateDataMultiError is an error wrapping multiple validation errors
// returned by AbflMandateData.ValidateAll() if the designated constraints
// aren't met.
type AbflMandateDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AbflMandateDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AbflMandateDataMultiError) AllErrors() []error { return m }

// AbflMandateDataValidationError is the validation error returned by
// AbflMandateData.Validate if the designated constraints aren't met.
type AbflMandateDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AbflMandateDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AbflMandateDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AbflMandateDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AbflMandateDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AbflMandateDataValidationError) ErrorName() string { return "AbflMandateDataValidationError" }

// Error satisfies the builtin error interface
func (e AbflMandateDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAbflMandateData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AbflMandateDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AbflMandateDataValidationError{}

// Validate checks the field values on SgMandateData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SgMandateData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SgMandateData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SgMandateDataMultiError, or
// nil if none found.
func (m *SgMandateData) ValidateAll() error {
	return m.validate(true)
}

func (m *SgMandateData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrchId

	if len(errors) > 0 {
		return SgMandateDataMultiError(errors)
	}

	return nil
}

// SgMandateDataMultiError is an error wrapping multiple validation errors
// returned by SgMandateData.ValidateAll() if the designated constraints
// aren't met.
type SgMandateDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SgMandateDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SgMandateDataMultiError) AllErrors() []error { return m }

// SgMandateDataValidationError is the validation error returned by
// SgMandateData.Validate if the designated constraints aren't met.
type SgMandateDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SgMandateDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SgMandateDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SgMandateDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SgMandateDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SgMandateDataValidationError) ErrorName() string { return "SgMandateDataValidationError" }

// Error satisfies the builtin error interface
func (e SgMandateDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSgMandateData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SgMandateDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SgMandateDataValidationError{}

// Validate checks the field values on FedMandateData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FedMandateData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FedMandateData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FedMandateDataMultiError,
// or nil if none found.
func (m *FedMandateData) ValidateAll() error {
	return m.validate(true)
}

func (m *FedMandateData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FedMandateDataMultiError(errors)
	}

	return nil
}

// FedMandateDataMultiError is an error wrapping multiple validation errors
// returned by FedMandateData.ValidateAll() if the designated constraints
// aren't met.
type FedMandateDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FedMandateDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FedMandateDataMultiError) AllErrors() []error { return m }

// FedMandateDataValidationError is the validation error returned by
// FedMandateData.Validate if the designated constraints aren't met.
type FedMandateDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FedMandateDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FedMandateDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FedMandateDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FedMandateDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FedMandateDataValidationError) ErrorName() string { return "FedMandateDataValidationError" }

// Error satisfies the builtin error interface
func (e FedMandateDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFedMandateData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FedMandateDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FedMandateDataValidationError{}

// Validate checks the field values on LLMandateData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LLMandateData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LLMandateData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LLMandateDataMultiError, or
// nil if none found.
func (m *LLMandateData) ValidateAll() error {
	return m.validate(true)
}

func (m *LLMandateData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MandateId

	// no validation rules for MandateAttempt

	if all {
		switch v := interface{}(m.GetLatestMandateAttemptTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LLMandateDataValidationError{
					field:  "LatestMandateAttemptTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LLMandateDataValidationError{
					field:  "LatestMandateAttemptTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestMandateAttemptTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LLMandateDataValidationError{
				field:  "LatestMandateAttemptTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LLMandateDataMultiError(errors)
	}

	return nil
}

// LLMandateDataMultiError is an error wrapping multiple validation errors
// returned by LLMandateData.ValidateAll() if the designated constraints
// aren't met.
type LLMandateDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LLMandateDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LLMandateDataMultiError) AllErrors() []error { return m }

// LLMandateDataValidationError is the validation error returned by
// LLMandateData.Validate if the designated constraints aren't met.
type LLMandateDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LLMandateDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LLMandateDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LLMandateDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LLMandateDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LLMandateDataValidationError) ErrorName() string { return "LLMandateDataValidationError" }

// Error satisfies the builtin error interface
func (e LLMandateDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLLMandateData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LLMandateDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LLMandateDataValidationError{}

// Validate checks the field values on IdfcCkycStepData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IdfcCkycStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IdfcCkycStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IdfcCkycStepDataMultiError, or nil if none found.
func (m *IdfcCkycStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *IdfcCkycStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AddressPinCodeType

	if len(errors) > 0 {
		return IdfcCkycStepDataMultiError(errors)
	}

	return nil
}

// IdfcCkycStepDataMultiError is an error wrapping multiple validation errors
// returned by IdfcCkycStepData.ValidateAll() if the designated constraints
// aren't met.
type IdfcCkycStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IdfcCkycStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IdfcCkycStepDataMultiError) AllErrors() []error { return m }

// IdfcCkycStepDataValidationError is the validation error returned by
// IdfcCkycStepData.Validate if the designated constraints aren't met.
type IdfcCkycStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IdfcCkycStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IdfcCkycStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IdfcCkycStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IdfcCkycStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IdfcCkycStepDataValidationError) ErrorName() string { return "IdfcCkycStepDataValidationError" }

// Error satisfies the builtin error interface
func (e IdfcCkycStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIdfcCkycStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IdfcCkycStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IdfcCkycStepDataValidationError{}

// Validate checks the field values on SgCkycStepData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SgCkycStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SgCkycStepData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SgCkycStepDataMultiError,
// or nil if none found.
func (m *SgCkycStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *SgCkycStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsKycAlreadyDone

	// no validation rules for KycType

	if len(errors) > 0 {
		return SgCkycStepDataMultiError(errors)
	}

	return nil
}

// SgCkycStepDataMultiError is an error wrapping multiple validation errors
// returned by SgCkycStepData.ValidateAll() if the designated constraints
// aren't met.
type SgCkycStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SgCkycStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SgCkycStepDataMultiError) AllErrors() []error { return m }

// SgCkycStepDataValidationError is the validation error returned by
// SgCkycStepData.Validate if the designated constraints aren't met.
type SgCkycStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SgCkycStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SgCkycStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SgCkycStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SgCkycStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SgCkycStepDataValidationError) ErrorName() string { return "SgCkycStepDataValidationError" }

// Error satisfies the builtin error interface
func (e SgCkycStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSgCkycStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SgCkycStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SgCkycStepDataValidationError{}

// Validate checks the field values on AbflCkycStepData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AbflCkycStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AbflCkycStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AbflCkycStepDataMultiError, or nil if none found.
func (m *AbflCkycStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *AbflCkycStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for AadhaarBase64Image

	// no validation rules for PhotographBase64Image

	// no validation rules for EkycAuthBase64Image

	// no validation rules for PanBase64Image

	// no validation rules for DrivingLicenceBase64Image

	// no validation rules for SignatureBase64Image

	// no validation rules for VoterIdBase64Image

	// no validation rules for PassportBase64Image

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflCkycStepDataValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflCkycStepDataValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflCkycStepDataValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCorrespondingAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflCkycStepDataValidationError{
					field:  "CorrespondingAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflCkycStepDataValidationError{
					field:  "CorrespondingAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCorrespondingAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflCkycStepDataValidationError{
				field:  "CorrespondingAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetFullName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflCkycStepDataValidationError{
					field:  "FullName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflCkycStepDataValidationError{
					field:  "FullName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFullName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflCkycStepDataValidationError{
				field:  "FullName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflCkycStepDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflCkycStepDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflCkycStepDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CkycId

	if len(errors) > 0 {
		return AbflCkycStepDataMultiError(errors)
	}

	return nil
}

// AbflCkycStepDataMultiError is an error wrapping multiple validation errors
// returned by AbflCkycStepData.ValidateAll() if the designated constraints
// aren't met.
type AbflCkycStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AbflCkycStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AbflCkycStepDataMultiError) AllErrors() []error { return m }

// AbflCkycStepDataValidationError is the validation error returned by
// AbflCkycStepData.Validate if the designated constraints aren't met.
type AbflCkycStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AbflCkycStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AbflCkycStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AbflCkycStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AbflCkycStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AbflCkycStepDataValidationError) ErrorName() string { return "AbflCkycStepDataValidationError" }

// Error satisfies the builtin error interface
func (e AbflCkycStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAbflCkycStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AbflCkycStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AbflCkycStepDataValidationError{}

// Validate checks the field values on ManualReviewStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManualReviewStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualReviewStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ManualReviewStepDataMultiError, or nil if none found.
func (m *ManualReviewStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualReviewStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NotificationId

	{
		sorted_keys := make([]string, len(m.GetNotificationTypeIdMap()))
		i := 0
		for key := range m.GetNotificationTypeIdMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetNotificationTypeIdMap()[key]
			_ = val

			// no validation rules for NotificationTypeIdMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ManualReviewStepDataValidationError{
							field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ManualReviewStepDataValidationError{
							field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ManualReviewStepDataValidationError{
						field:  fmt.Sprintf("NotificationTypeIdMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for Reason

	if all {
		switch v := interface{}(m.GetReviewerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualReviewStepDataValidationError{
					field:  "ReviewerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualReviewStepDataValidationError{
					field:  "ReviewerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualReviewStepDataValidationError{
				field:  "ReviewerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ManualReviewStepDataMultiError(errors)
	}

	return nil
}

// ManualReviewStepDataMultiError is an error wrapping multiple validation
// errors returned by ManualReviewStepData.ValidateAll() if the designated
// constraints aren't met.
type ManualReviewStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualReviewStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualReviewStepDataMultiError) AllErrors() []error { return m }

// ManualReviewStepDataValidationError is the validation error returned by
// ManualReviewStepData.Validate if the designated constraints aren't met.
type ManualReviewStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualReviewStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualReviewStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualReviewStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualReviewStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualReviewStepDataValidationError) ErrorName() string {
	return "ManualReviewStepDataValidationError"
}

// Error satisfies the builtin error interface
func (e ManualReviewStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualReviewStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualReviewStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualReviewStepDataValidationError{}

// Validate checks the field values on OnboardingData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OnboardingData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OnboardingDataMultiError,
// or nil if none found.
func (m *OnboardingData) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAddressDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDataValidationError{
				field:  "AddressDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmploymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDataValidationError{
				field:  "EmploymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBankingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "BankingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "BankingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDataValidationError{
				field:  "BankingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OnboardingDataMultiError(errors)
	}

	return nil
}

// OnboardingDataMultiError is an error wrapping multiple validation errors
// returned by OnboardingData.ValidateAll() if the designated constraints
// aren't met.
type OnboardingDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingDataMultiError) AllErrors() []error { return m }

// OnboardingDataValidationError is the validation error returned by
// OnboardingData.Validate if the designated constraints aren't met.
type OnboardingDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingDataValidationError) ErrorName() string { return "OnboardingDataValidationError" }

// Error satisfies the builtin error interface
func (e OnboardingDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingDataValidationError{}

// Validate checks the field values on MandateData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MandateData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MandateData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MandateDataMultiError, or
// nil if none found.
func (m *MandateData) ValidateAll() error {
	return m.validate(true)
}

func (m *MandateData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for RecurringPaymentId

	// no validation rules for MerchantTxnId

	if all {
		switch v := interface{}(m.GetMaxTxnAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateDataValidationError{
					field:  "MaxTxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateDataValidationError{
					field:  "MaxTxnAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxTxnAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateDataValidationError{
				field:  "MaxTxnAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMandateLinkExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateDataValidationError{
					field:  "MandateLinkExpiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateDataValidationError{
					field:  "MandateLinkExpiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMandateLinkExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateDataValidationError{
				field:  "MandateLinkExpiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBankingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateDataValidationError{
					field:  "BankingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateDataValidationError{
					field:  "BankingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateDataValidationError{
				field:  "BankingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MandateWithFiAccountNotAllowed

	// no validation rules for FiAccountIneligibleForMandateReason

	if all {
		switch v := interface{}(m.GetUrlGeneratedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateDataValidationError{
					field:  "UrlGeneratedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateDataValidationError{
					field:  "UrlGeneratedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUrlGeneratedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateDataValidationError{
				field:  "UrlGeneratedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MandateType

	switch v := m.VendorSpecificDetails.(type) {
	case *MandateData_Abfl:
		if v == nil {
			err := MandateDataValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAbfl()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MandateDataValidationError{
						field:  "Abfl",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MandateDataValidationError{
						field:  "Abfl",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAbfl()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MandateDataValidationError{
					field:  "Abfl",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MandateData_LlMandateData:
		if v == nil {
			err := MandateDataValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLlMandateData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MandateDataValidationError{
						field:  "LlMandateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MandateDataValidationError{
						field:  "LlMandateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLlMandateData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MandateDataValidationError{
					field:  "LlMandateData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MandateData_SgMandateData:
		if v == nil {
			err := MandateDataValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSgMandateData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MandateDataValidationError{
						field:  "SgMandateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MandateDataValidationError{
						field:  "SgMandateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSgMandateData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MandateDataValidationError{
					field:  "SgMandateData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MandateData_FedMandateData:
		if v == nil {
			err := MandateDataValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFedMandateData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MandateDataValidationError{
						field:  "FedMandateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MandateDataValidationError{
						field:  "FedMandateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFedMandateData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MandateDataValidationError{
					field:  "FedMandateData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return MandateDataMultiError(errors)
	}

	return nil
}

// MandateDataMultiError is an error wrapping multiple validation errors
// returned by MandateData.ValidateAll() if the designated constraints aren't met.
type MandateDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MandateDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MandateDataMultiError) AllErrors() []error { return m }

// MandateDataValidationError is the validation error returned by
// MandateData.Validate if the designated constraints aren't met.
type MandateDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MandateDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MandateDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MandateDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MandateDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MandateDataValidationError) ErrorName() string { return "MandateDataValidationError" }

// Error satisfies the builtin error interface
func (e MandateDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMandateData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MandateDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MandateDataValidationError{}

// Validate checks the field values on LoanStepExecutionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanStepExecutionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanStepExecutionDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanStepExecutionDetailsMultiError, or nil if none found.
func (m *LoanStepExecutionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanStepExecutionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorData

	switch v := m.Details.(type) {
	case *LoanStepExecutionDetails_LivenessStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLivenessStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "LivenessStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "LivenessStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLivenessStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "LivenessStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_FaceMatchStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFaceMatchStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "FaceMatchStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "FaceMatchStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFaceMatchStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "FaceMatchStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_ESignStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetESignStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ESignStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ESignStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetESignStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "ESignStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_VkycStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVkycStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "VkycStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "VkycStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVkycStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "VkycStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_ManualReviewStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetManualReviewStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ManualReviewStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ManualReviewStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetManualReviewStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "ManualReviewStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_OnboardingData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOnboardingData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "OnboardingData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "OnboardingData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOnboardingData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "OnboardingData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_MandateData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMandateData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "MandateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "MandateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMandateData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "MandateData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_HunterData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHunterData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "HunterData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "HunterData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHunterData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "HunterData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_CkycStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCkycStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "CkycStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "CkycStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCkycStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "CkycStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_CollectionData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCollectionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "CollectionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "CollectionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCollectionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "CollectionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_ApplicantData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetApplicantData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ApplicantData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ApplicantData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetApplicantData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "ApplicantData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_OtpVerificationData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOtpVerificationData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "OtpVerificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "OtpVerificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOtpVerificationData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "OtpVerificationData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_BreData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBreData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "BreData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "BreData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBreData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "BreData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_SelfieData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSelfieData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "SelfieData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "SelfieData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSelfieData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "SelfieData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_CreateLeadStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreateLeadStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "CreateLeadStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "CreateLeadStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateLeadStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "CreateLeadStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_VendorPwaStagesStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVendorPwaStagesStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "VendorPwaStagesStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "VendorPwaStagesStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVendorPwaStagesStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "VendorPwaStagesStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_KycStepData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetKycStepData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "KycStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "KycStepData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetKycStepData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "KycStepData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_IncomeEstimateData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIncomeEstimateData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "IncomeEstimateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "IncomeEstimateData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIncomeEstimateData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "IncomeEstimateData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_AadhaarData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAadhaarData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "AadhaarData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "AadhaarData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAadhaarData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "AadhaarData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_LoanDetailsVerificationData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoanDetailsVerificationData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "LoanDetailsVerificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "LoanDetailsVerificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoanDetailsVerificationData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "LoanDetailsVerificationData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_ResetVendorLoanApplicationData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetResetVendorLoanApplicationData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ResetVendorLoanApplicationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ResetVendorLoanApplicationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResetVendorLoanApplicationData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "ResetVendorLoanApplicationData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_ContactabilityDetailsData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetContactabilityDetailsData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ContactabilityDetailsData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "ContactabilityDetailsData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetContactabilityDetailsData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "ContactabilityDetailsData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_VendorIdentifiersData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVendorIdentifiersData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "VendorIdentifiersData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "VendorIdentifiersData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVendorIdentifiersData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "VendorIdentifiersData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_RoiModificationData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRoiModificationData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "RoiModificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "RoiModificationData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRoiModificationData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "RoiModificationData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanStepExecutionDetails_PreEligibilityData:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPreEligibilityData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "PreEligibilityData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "PreEligibilityData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPreEligibilityData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "PreEligibilityData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.VendorSpecificDetails.(type) {
	case *LoanStepExecutionDetails_Abfl:
		if v == nil {
			err := LoanStepExecutionDetailsValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAbfl()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "Abfl",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanStepExecutionDetailsValidationError{
						field:  "Abfl",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAbfl()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanStepExecutionDetailsValidationError{
					field:  "Abfl",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LoanStepExecutionDetailsMultiError(errors)
	}

	return nil
}

// LoanStepExecutionDetailsMultiError is an error wrapping multiple validation
// errors returned by LoanStepExecutionDetails.ValidateAll() if the designated
// constraints aren't met.
type LoanStepExecutionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanStepExecutionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanStepExecutionDetailsMultiError) AllErrors() []error { return m }

// LoanStepExecutionDetailsValidationError is the validation error returned by
// LoanStepExecutionDetails.Validate if the designated constraints aren't met.
type LoanStepExecutionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanStepExecutionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanStepExecutionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanStepExecutionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanStepExecutionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanStepExecutionDetailsValidationError) ErrorName() string {
	return "LoanStepExecutionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanStepExecutionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanStepExecutionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanStepExecutionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanStepExecutionDetailsValidationError{}

// Validate checks the field values on PreEligibilityData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PreEligibilityData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreEligibilityData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreEligibilityDataMultiError, or nil if none found.
func (m *PreEligibilityData) ValidateAll() error {
	return m.validate(true)
}

func (m *PreEligibilityData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CreditReportAttemptCount

	if len(errors) > 0 {
		return PreEligibilityDataMultiError(errors)
	}

	return nil
}

// PreEligibilityDataMultiError is an error wrapping multiple validation errors
// returned by PreEligibilityData.ValidateAll() if the designated constraints
// aren't met.
type PreEligibilityDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreEligibilityDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreEligibilityDataMultiError) AllErrors() []error { return m }

// PreEligibilityDataValidationError is the validation error returned by
// PreEligibilityData.Validate if the designated constraints aren't met.
type PreEligibilityDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreEligibilityDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreEligibilityDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreEligibilityDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreEligibilityDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreEligibilityDataValidationError) ErrorName() string {
	return "PreEligibilityDataValidationError"
}

// Error satisfies the builtin error interface
func (e PreEligibilityDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreEligibilityData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreEligibilityDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreEligibilityDataValidationError{}

// Validate checks the field values on AadhaarData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AadhaarData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AadhaarData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AadhaarDataMultiError, or
// nil if none found.
func (m *AadhaarData) ValidateAll() error {
	return m.validate(true)
}

func (m *AadhaarData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LastFourDigit

	switch v := m.VendorSpecificDetails.(type) {
	case *AadhaarData_Abfl:
		if v == nil {
			err := AadhaarDataValidationError{
				field:  "VendorSpecificDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAbfl()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AadhaarDataValidationError{
						field:  "Abfl",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AadhaarDataValidationError{
						field:  "Abfl",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAbfl()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AadhaarDataValidationError{
					field:  "Abfl",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AadhaarDataMultiError(errors)
	}

	return nil
}

// AadhaarDataMultiError is an error wrapping multiple validation errors
// returned by AadhaarData.ValidateAll() if the designated constraints aren't met.
type AadhaarDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AadhaarDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AadhaarDataMultiError) AllErrors() []error { return m }

// AadhaarDataValidationError is the validation error returned by
// AadhaarData.Validate if the designated constraints aren't met.
type AadhaarDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AadhaarDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AadhaarDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AadhaarDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AadhaarDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AadhaarDataValidationError) ErrorName() string { return "AadhaarDataValidationError" }

// Error satisfies the builtin error interface
func (e AadhaarDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAadhaarData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AadhaarDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AadhaarDataValidationError{}

// Validate checks the field values on AbflAadhaarData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AbflAadhaarData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AbflAadhaarData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AbflAadhaarDataMultiError, or nil if none found.
func (m *AbflAadhaarData) ValidateAll() error {
	return m.validate(true)
}

func (m *AbflAadhaarData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProfileId

	// no validation rules for PartnerReqId

	// no validation rules for CaptureLink

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflAadhaarDataValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflAadhaarDataValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflAadhaarDataValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Age

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflAadhaarDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflAadhaarDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflAadhaarDataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StreetAddress

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AbflAadhaarDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AbflAadhaarDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AbflAadhaarDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AbflAadhaarDataMultiError(errors)
	}

	return nil
}

// AbflAadhaarDataMultiError is an error wrapping multiple validation errors
// returned by AbflAadhaarData.ValidateAll() if the designated constraints
// aren't met.
type AbflAadhaarDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AbflAadhaarDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AbflAadhaarDataMultiError) AllErrors() []error { return m }

// AbflAadhaarDataValidationError is the validation error returned by
// AbflAadhaarData.Validate if the designated constraints aren't met.
type AbflAadhaarDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AbflAadhaarDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AbflAadhaarDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AbflAadhaarDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AbflAadhaarDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AbflAadhaarDataValidationError) ErrorName() string { return "AbflAadhaarDataValidationError" }

// Error satisfies the builtin error interface
func (e AbflAadhaarDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAbflAadhaarData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AbflAadhaarDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AbflAadhaarDataValidationError{}

// Validate checks the field values on IncomeEstimateData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IncomeEstimateData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IncomeEstimateData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IncomeEstimateDataMultiError, or nil if none found.
func (m *IncomeEstimateData) ValidateAll() error {
	return m.validate(true)
}

func (m *IncomeEstimateData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPredictedIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeEstimateDataValidationError{
					field:  "PredictedIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeEstimateDataValidationError{
					field:  "PredictedIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPredictedIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeEstimateDataValidationError{
				field:  "PredictedIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Confidence

	// no validation rules for RawResponse

	// no validation rules for IncomeDataSource

	if all {
		switch v := interface{}(m.GetIncomeEstimatorRespStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeEstimateDataValidationError{
					field:  "IncomeEstimatorRespStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeEstimateDataValidationError{
					field:  "IncomeEstimatorRespStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIncomeEstimatorRespStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeEstimateDataValidationError{
				field:  "IncomeEstimatorRespStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetItrIncomeInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeEstimateDataValidationError{
					field:  "ItrIncomeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeEstimateDataValidationError{
					field:  "ItrIncomeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetItrIncomeInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeEstimateDataValidationError{
				field:  "ItrIncomeInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IncomeEstimateDataMultiError(errors)
	}

	return nil
}

// IncomeEstimateDataMultiError is an error wrapping multiple validation errors
// returned by IncomeEstimateData.ValidateAll() if the designated constraints
// aren't met.
type IncomeEstimateDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IncomeEstimateDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IncomeEstimateDataMultiError) AllErrors() []error { return m }

// IncomeEstimateDataValidationError is the validation error returned by
// IncomeEstimateData.Validate if the designated constraints aren't met.
type IncomeEstimateDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IncomeEstimateDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IncomeEstimateDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IncomeEstimateDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IncomeEstimateDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IncomeEstimateDataValidationError) ErrorName() string {
	return "IncomeEstimateDataValidationError"
}

// Error satisfies the builtin error interface
func (e IncomeEstimateDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIncomeEstimateData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IncomeEstimateDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IncomeEstimateDataValidationError{}

// Validate checks the field values on ItrIncomeInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ItrIncomeInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ItrIncomeInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ItrIncomeInfoMultiError, or
// nil if none found.
func (m *ItrIncomeInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ItrIncomeInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItrAttemptData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ItrIncomeInfoValidationError{
						field:  fmt.Sprintf("ItrAttemptData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ItrIncomeInfoValidationError{
						field:  fmt.Sprintf("ItrAttemptData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ItrIncomeInfoValidationError{
					field:  fmt.Sprintf("ItrAttemptData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetItrIntimation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ItrIncomeInfoValidationError{
					field:  "ItrIntimation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ItrIncomeInfoValidationError{
					field:  "ItrIntimation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetItrIntimation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ItrIncomeInfoValidationError{
				field:  "ItrIntimation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ItrIncomeInfoMultiError(errors)
	}

	return nil
}

// ItrIncomeInfoMultiError is an error wrapping multiple validation errors
// returned by ItrIncomeInfo.ValidateAll() if the designated constraints
// aren't met.
type ItrIncomeInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ItrIncomeInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ItrIncomeInfoMultiError) AllErrors() []error { return m }

// ItrIncomeInfoValidationError is the validation error returned by
// ItrIncomeInfo.Validate if the designated constraints aren't met.
type ItrIncomeInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ItrIncomeInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ItrIncomeInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ItrIncomeInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ItrIncomeInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ItrIncomeInfoValidationError) ErrorName() string { return "ItrIncomeInfoValidationError" }

// Error satisfies the builtin error interface
func (e ItrIncomeInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sItrIncomeInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ItrIncomeInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ItrIncomeInfoValidationError{}

// Validate checks the field values on AbflLoanDisbursementData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AbflLoanDisbursementData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AbflLoanDisbursementData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AbflLoanDisbursementDataMultiError, or nil if none found.
func (m *AbflLoanDisbursementData) ValidateAll() error {
	return m.validate(true)
}

func (m *AbflLoanDisbursementData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanUniqueId

	// no validation rules for LoanNumber

	// no validation rules for DealNumber

	if len(errors) > 0 {
		return AbflLoanDisbursementDataMultiError(errors)
	}

	return nil
}

// AbflLoanDisbursementDataMultiError is an error wrapping multiple validation
// errors returned by AbflLoanDisbursementData.ValidateAll() if the designated
// constraints aren't met.
type AbflLoanDisbursementDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AbflLoanDisbursementDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AbflLoanDisbursementDataMultiError) AllErrors() []error { return m }

// AbflLoanDisbursementDataValidationError is the validation error returned by
// AbflLoanDisbursementData.Validate if the designated constraints aren't met.
type AbflLoanDisbursementDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AbflLoanDisbursementDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AbflLoanDisbursementDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AbflLoanDisbursementDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AbflLoanDisbursementDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AbflLoanDisbursementDataValidationError) ErrorName() string {
	return "AbflLoanDisbursementDataValidationError"
}

// Error satisfies the builtin error interface
func (e AbflLoanDisbursementDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAbflLoanDisbursementData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AbflLoanDisbursementDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AbflLoanDisbursementDataValidationError{}

// Validate checks the field values on ListOfString with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListOfString) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOfString with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListOfStringMultiError, or
// nil if none found.
func (m *ListOfString) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOfString) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListOfStringMultiError(errors)
	}

	return nil
}

// ListOfStringMultiError is an error wrapping multiple validation errors
// returned by ListOfString.ValidateAll() if the designated constraints aren't met.
type ListOfStringMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOfStringMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOfStringMultiError) AllErrors() []error { return m }

// ListOfStringValidationError is the validation error returned by
// ListOfString.Validate if the designated constraints aren't met.
type ListOfStringValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOfStringValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOfStringValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOfStringValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOfStringValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOfStringValidationError) ErrorName() string { return "ListOfStringValidationError" }

// Error satisfies the builtin error interface
func (e ListOfStringValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOfString.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOfStringValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOfStringValidationError{}

// Validate checks the field values on HunterData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HunterData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HunterData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HunterDataMultiError, or
// nil if none found.
func (m *HunterData) ValidateAll() error {
	return m.validate(true)
}

func (m *HunterData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Score

	// no validation rules for Rule

	if len(errors) > 0 {
		return HunterDataMultiError(errors)
	}

	return nil
}

// HunterDataMultiError is an error wrapping multiple validation errors
// returned by HunterData.ValidateAll() if the designated constraints aren't met.
type HunterDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HunterDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HunterDataMultiError) AllErrors() []error { return m }

// HunterDataValidationError is the validation error returned by
// HunterData.Validate if the designated constraints aren't met.
type HunterDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HunterDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HunterDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HunterDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HunterDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HunterDataValidationError) ErrorName() string { return "HunterDataValidationError" }

// Error satisfies the builtin error interface
func (e HunterDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHunterData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HunterDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HunterDataValidationError{}

// Validate checks the field values on CollectionData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CollectionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectionData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CollectionDataMultiError,
// or nil if none found.
func (m *CollectionData) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRepaymentBreakup() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectionDataValidationError{
						field:  fmt.Sprintf("RepaymentBreakup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectionDataValidationError{
						field:  fmt.Sprintf("RepaymentBreakup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectionDataValidationError{
					field:  fmt.Sprintf("RepaymentBreakup[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CollectionDataMultiError(errors)
	}

	return nil
}

// CollectionDataMultiError is an error wrapping multiple validation errors
// returned by CollectionData.ValidateAll() if the designated constraints
// aren't met.
type CollectionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectionDataMultiError) AllErrors() []error { return m }

// CollectionDataValidationError is the validation error returned by
// CollectionData.Validate if the designated constraints aren't met.
type CollectionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectionDataValidationError) ErrorName() string { return "CollectionDataValidationError" }

// Error satisfies the builtin error interface
func (e CollectionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectionDataValidationError{}

// Validate checks the field values on ApplicantData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ApplicantData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicantData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ApplicantDataMultiError, or
// nil if none found.
func (m *ApplicantData) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicantData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantDataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for EmploymentType

	// no validation rules for MaritalStatus

	// no validation rules for ResidenceType

	// no validation rules for FatherName

	// no validation rules for MotherName

	for idx, item := range m.GetReferences() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApplicantDataValidationError{
						field:  fmt.Sprintf("References[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApplicantDataValidationError{
						field:  fmt.Sprintf("References[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApplicantDataValidationError{
					field:  fmt.Sprintf("References[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PurposeOfLoan

	if all {
		switch v := interface{}(m.GetDesiredLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicantDataValidationError{
					field:  "DesiredLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicantDataValidationError{
					field:  "DesiredLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDesiredLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicantDataValidationError{
				field:  "DesiredLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplicantDataMultiError(errors)
	}

	return nil
}

// ApplicantDataMultiError is an error wrapping multiple validation errors
// returned by ApplicantData.ValidateAll() if the designated constraints
// aren't met.
type ApplicantDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicantDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicantDataMultiError) AllErrors() []error { return m }

// ApplicantDataValidationError is the validation error returned by
// ApplicantData.Validate if the designated constraints aren't met.
type ApplicantDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicantDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicantDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicantDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicantDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicantDataValidationError) ErrorName() string { return "ApplicantDataValidationError" }

// Error satisfies the builtin error interface
func (e ApplicantDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicantData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicantDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicantDataValidationError{}

// Validate checks the field values on OtpVerificationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OtpVerificationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtpVerificationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OtpVerificationDataMultiError, or nil if none found.
func (m *OtpVerificationData) ValidateAll() error {
	return m.validate(true)
}

func (m *OtpVerificationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOtpData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtpVerificationDataValidationError{
						field:  fmt.Sprintf("OtpData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtpVerificationDataValidationError{
						field:  fmt.Sprintf("OtpData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtpVerificationDataValidationError{
					field:  fmt.Sprintf("OtpData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SkipRemainingOtps

	// no validation rules for OtpVerificationInProgress

	if all {
		switch v := interface{}(m.GetOtpVerificationStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpVerificationDataValidationError{
					field:  "OtpVerificationStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpVerificationDataValidationError{
					field:  "OtpVerificationStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtpVerificationStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpVerificationDataValidationError{
				field:  "OtpVerificationStartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OtpVerificationDataMultiError(errors)
	}

	return nil
}

// OtpVerificationDataMultiError is an error wrapping multiple validation
// errors returned by OtpVerificationData.ValidateAll() if the designated
// constraints aren't met.
type OtpVerificationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtpVerificationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtpVerificationDataMultiError) AllErrors() []error { return m }

// OtpVerificationDataValidationError is the validation error returned by
// OtpVerificationData.Validate if the designated constraints aren't met.
type OtpVerificationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtpVerificationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtpVerificationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtpVerificationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtpVerificationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtpVerificationDataValidationError) ErrorName() string {
	return "OtpVerificationDataValidationError"
}

// Error satisfies the builtin error interface
func (e OtpVerificationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtpVerificationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtpVerificationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtpVerificationDataValidationError{}

// Validate checks the field values on Condition with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Condition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Condition with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConditionMultiError, or nil
// if none found.
func (m *Condition) ValidateAll() error {
	return m.validate(true)
}

func (m *Condition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Condition.(type) {
	case *Condition_OtpStatusCondition:
		if v == nil {
			err := ConditionValidationError{
				field:  "Condition",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOtpStatusCondition()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConditionValidationError{
						field:  "OtpStatusCondition",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConditionValidationError{
						field:  "OtpStatusCondition",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOtpStatusCondition()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConditionValidationError{
					field:  "OtpStatusCondition",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Condition_NotCondition:
		if v == nil {
			err := ConditionValidationError{
				field:  "Condition",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNotCondition()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConditionValidationError{
						field:  "NotCondition",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConditionValidationError{
						field:  "NotCondition",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNotCondition()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConditionValidationError{
					field:  "NotCondition",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ConditionMultiError(errors)
	}

	return nil
}

// ConditionMultiError is an error wrapping multiple validation errors returned
// by Condition.ValidateAll() if the designated constraints aren't met.
type ConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionMultiError) AllErrors() []error { return m }

// ConditionValidationError is the validation error returned by
// Condition.Validate if the designated constraints aren't met.
type ConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionValidationError) ErrorName() string { return "ConditionValidationError" }

// Error satisfies the builtin error interface
func (e ConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionValidationError{}

// Validate checks the field values on OtpStatusCondition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OtpStatusCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtpStatusCondition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OtpStatusConditionMultiError, or nil if none found.
func (m *OtpStatusCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *OtpStatusCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OtpId

	if len(errors) > 0 {
		return OtpStatusConditionMultiError(errors)
	}

	return nil
}

// OtpStatusConditionMultiError is an error wrapping multiple validation errors
// returned by OtpStatusCondition.ValidateAll() if the designated constraints
// aren't met.
type OtpStatusConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtpStatusConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtpStatusConditionMultiError) AllErrors() []error { return m }

// OtpStatusConditionValidationError is the validation error returned by
// OtpStatusCondition.Validate if the designated constraints aren't met.
type OtpStatusConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtpStatusConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtpStatusConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtpStatusConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtpStatusConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtpStatusConditionValidationError) ErrorName() string {
	return "OtpStatusConditionValidationError"
}

// Error satisfies the builtin error interface
func (e OtpStatusConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtpStatusCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtpStatusConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtpStatusConditionValidationError{}

// Validate checks the field values on NotCondition with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NotCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NotCondition with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NotConditionMultiError, or
// nil if none found.
func (m *NotCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *NotCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NotConditionValidationError{
					field:  "BaseCondition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NotConditionValidationError{
					field:  "BaseCondition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NotConditionValidationError{
				field:  "BaseCondition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NotConditionMultiError(errors)
	}

	return nil
}

// NotConditionMultiError is an error wrapping multiple validation errors
// returned by NotCondition.ValidateAll() if the designated constraints aren't met.
type NotConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotConditionMultiError) AllErrors() []error { return m }

// NotConditionValidationError is the validation error returned by
// NotCondition.Validate if the designated constraints aren't met.
type NotConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotConditionValidationError) ErrorName() string { return "NotConditionValidationError" }

// Error satisfies the builtin error interface
func (e NotConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotConditionValidationError{}

// Validate checks the field values on BreData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BreData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BreData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BreDataMultiError, or nil if none found.
func (m *BreData) ValidateAll() error {
	return m.validate(true)
}

func (m *BreData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsEpfoDataNeeded

	// no validation rules for Identifier

	if len(errors) > 0 {
		return BreDataMultiError(errors)
	}

	return nil
}

// BreDataMultiError is an error wrapping multiple validation errors returned
// by BreData.ValidateAll() if the designated constraints aren't met.
type BreDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BreDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BreDataMultiError) AllErrors() []error { return m }

// BreDataValidationError is the validation error returned by BreData.Validate
// if the designated constraints aren't met.
type BreDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BreDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BreDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BreDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BreDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BreDataValidationError) ErrorName() string { return "BreDataValidationError" }

// Error satisfies the builtin error interface
func (e BreDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBreData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BreDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BreDataValidationError{}

// Validate checks the field values on SelfieData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SelfieData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelfieData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SelfieDataMultiError, or
// nil if none found.
func (m *SelfieData) ValidateAll() error {
	return m.validate(true)
}

func (m *SelfieData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSelfieImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelfieDataValidationError{
					field:  "SelfieImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelfieDataValidationError{
					field:  "SelfieImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelfieImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelfieDataValidationError{
				field:  "SelfieImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SelfieDataMultiError(errors)
	}

	return nil
}

// SelfieDataMultiError is an error wrapping multiple validation errors
// returned by SelfieData.ValidateAll() if the designated constraints aren't met.
type SelfieDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelfieDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelfieDataMultiError) AllErrors() []error { return m }

// SelfieDataValidationError is the validation error returned by
// SelfieData.Validate if the designated constraints aren't met.
type SelfieDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelfieDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelfieDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelfieDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelfieDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelfieDataValidationError) ErrorName() string { return "SelfieDataValidationError" }

// Error satisfies the builtin error interface
func (e SelfieDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelfieData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelfieDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelfieDataValidationError{}

// Validate checks the field values on Reference with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Reference) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Reference with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReferenceMultiError, or nil
// if none found.
func (m *Reference) ValidateAll() error {
	return m.validate(true)
}

func (m *Reference) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReferenceValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReferenceValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReferenceValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReferenceValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReferenceValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReferenceValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReferenceMultiError(errors)
	}

	return nil
}

// ReferenceMultiError is an error wrapping multiple validation errors returned
// by Reference.ValidateAll() if the designated constraints aren't met.
type ReferenceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReferenceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReferenceMultiError) AllErrors() []error { return m }

// ReferenceValidationError is the validation error returned by
// Reference.Validate if the designated constraints aren't met.
type ReferenceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReferenceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReferenceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReferenceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReferenceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReferenceValidationError) ErrorName() string { return "ReferenceValidationError" }

// Error satisfies the builtin error interface
func (e ReferenceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReference.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReferenceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReferenceValidationError{}

// Validate checks the field values on CreateLeadStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLeadStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLeadStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLeadStepDataMultiError, or nil if none found.
func (m *CreateLeadStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLeadStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LeadId

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadStepDataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadStepDataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadStepDataValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmploymentType

	if all {
		switch v := interface{}(m.GetDeclaredIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "DeclaredIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "DeclaredIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeclaredIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadStepDataValidationError{
				field:  "DeclaredIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "CurrentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadStepDataValidationError{
					field:  "CurrentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadStepDataValidationError{
				field:  "CurrentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if len(errors) > 0 {
		return CreateLeadStepDataMultiError(errors)
	}

	return nil
}

// CreateLeadStepDataMultiError is an error wrapping multiple validation errors
// returned by CreateLeadStepData.ValidateAll() if the designated constraints
// aren't met.
type CreateLeadStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLeadStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLeadStepDataMultiError) AllErrors() []error { return m }

// CreateLeadStepDataValidationError is the validation error returned by
// CreateLeadStepData.Validate if the designated constraints aren't met.
type CreateLeadStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLeadStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLeadStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLeadStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLeadStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLeadStepDataValidationError) ErrorName() string {
	return "CreateLeadStepDataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLeadStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLeadStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLeadStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLeadStepDataValidationError{}

// Validate checks the field values on VendorPWAStagesStepData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VendorPWAStagesStepData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorPWAStagesStepData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VendorPWAStagesStepDataMultiError, or nil if none found.
func (m *VendorPWAStagesStepData) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorPWAStagesStepData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStageInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VendorPWAStagesStepDataValidationError{
						field:  fmt.Sprintf("StageInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VendorPWAStagesStepDataValidationError{
						field:  fmt.Sprintf("StageInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VendorPWAStagesStepDataValidationError{
					field:  fmt.Sprintf("StageInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PwaUrl

	if all {
		switch v := interface{}(m.GetPwaUrlExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorPWAStagesStepDataValidationError{
					field:  "PwaUrlExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorPWAStagesStepDataValidationError{
					field:  "PwaUrlExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPwaUrlExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorPWAStagesStepDataValidationError{
				field:  "PwaUrlExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VendorPWAStagesStepDataMultiError(errors)
	}

	return nil
}

// VendorPWAStagesStepDataMultiError is an error wrapping multiple validation
// errors returned by VendorPWAStagesStepData.ValidateAll() if the designated
// constraints aren't met.
type VendorPWAStagesStepDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorPWAStagesStepDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorPWAStagesStepDataMultiError) AllErrors() []error { return m }

// VendorPWAStagesStepDataValidationError is the validation error returned by
// VendorPWAStagesStepData.Validate if the designated constraints aren't met.
type VendorPWAStagesStepDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorPWAStagesStepDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorPWAStagesStepDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorPWAStagesStepDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorPWAStagesStepDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorPWAStagesStepDataValidationError) ErrorName() string {
	return "VendorPWAStagesStepDataValidationError"
}

// Error satisfies the builtin error interface
func (e VendorPWAStagesStepDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorPWAStagesStepData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorPWAStagesStepDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorPWAStagesStepDataValidationError{}

// Validate checks the field values on LoanDetailsVerificationData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanDetailsVerificationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanDetailsVerificationData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanDetailsVerificationDataMultiError, or nil if none found.
func (m *LoanDetailsVerificationData) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanDetailsVerificationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *LoanDetailsVerificationData_Lamf:
		if v == nil {
			err := LoanDetailsVerificationDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLamf()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanDetailsVerificationDataValidationError{
						field:  "Lamf",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanDetailsVerificationDataValidationError{
						field:  "Lamf",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLamf()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanDetailsVerificationDataValidationError{
					field:  "Lamf",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LoanDetailsVerificationDataMultiError(errors)
	}

	return nil
}

// LoanDetailsVerificationDataMultiError is an error wrapping multiple
// validation errors returned by LoanDetailsVerificationData.ValidateAll() if
// the designated constraints aren't met.
type LoanDetailsVerificationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanDetailsVerificationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanDetailsVerificationDataMultiError) AllErrors() []error { return m }

// LoanDetailsVerificationDataValidationError is the validation error returned
// by LoanDetailsVerificationData.Validate if the designated constraints
// aren't met.
type LoanDetailsVerificationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanDetailsVerificationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanDetailsVerificationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanDetailsVerificationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanDetailsVerificationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanDetailsVerificationDataValidationError) ErrorName() string {
	return "LoanDetailsVerificationDataValidationError"
}

// Error satisfies the builtin error interface
func (e LoanDetailsVerificationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanDetailsVerificationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanDetailsVerificationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanDetailsVerificationDataValidationError{}

// Validate checks the field values on LamfLoanDetailsVerificationData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LamfLoanDetailsVerificationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LamfLoanDetailsVerificationData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LamfLoanDetailsVerificationDataMultiError, or nil if none found.
func (m *LamfLoanDetailsVerificationData) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfLoanDetailsVerificationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserActionTaken

	for idx, item := range m.GetPfFetch_Data() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationDataValidationError{
						field:  fmt.Sprintf("PfFetch_Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationDataValidationError{
						field:  fmt.Sprintf("PfFetch_Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LamfLoanDetailsVerificationDataValidationError{
					field:  fmt.Sprintf("PfFetch_Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMobileLinkDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationDataValidationError{
					field:  "MobileLinkDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationDataValidationError{
					field:  "MobileLinkDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobileLinkDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfLoanDetailsVerificationDataValidationError{
				field:  "MobileLinkDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmailLinkDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationDataValidationError{
					field:  "EmailLinkDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationDataValidationError{
					field:  "EmailLinkDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmailLinkDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfLoanDetailsVerificationDataValidationError{
				field:  "EmailLinkDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationDataValidationError{
					field:  "UserAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationDataValidationError{
					field:  "UserAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfLoanDetailsVerificationDataValidationError{
				field:  "UserAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LamfLoanDetailsVerificationDataMultiError(errors)
	}

	return nil
}

// LamfLoanDetailsVerificationDataMultiError is an error wrapping multiple
// validation errors returned by LamfLoanDetailsVerificationData.ValidateAll()
// if the designated constraints aren't met.
type LamfLoanDetailsVerificationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfLoanDetailsVerificationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfLoanDetailsVerificationDataMultiError) AllErrors() []error { return m }

// LamfLoanDetailsVerificationDataValidationError is the validation error
// returned by LamfLoanDetailsVerificationData.Validate if the designated
// constraints aren't met.
type LamfLoanDetailsVerificationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfLoanDetailsVerificationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LamfLoanDetailsVerificationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LamfLoanDetailsVerificationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LamfLoanDetailsVerificationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfLoanDetailsVerificationDataValidationError) ErrorName() string {
	return "LamfLoanDetailsVerificationDataValidationError"
}

// Error satisfies the builtin error interface
func (e LamfLoanDetailsVerificationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfLoanDetailsVerificationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfLoanDetailsVerificationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfLoanDetailsVerificationDataValidationError{}

// Validate checks the field values on ResetVendorLoanApplicationData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetVendorLoanApplicationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetVendorLoanApplicationData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ResetVendorLoanApplicationDataMultiError, or nil if none found.
func (m *ResetVendorLoanApplicationData) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetVendorLoanApplicationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *ResetVendorLoanApplicationData_Lamf:
		if v == nil {
			err := ResetVendorLoanApplicationDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLamf()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetVendorLoanApplicationDataValidationError{
						field:  "Lamf",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetVendorLoanApplicationDataValidationError{
						field:  "Lamf",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLamf()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetVendorLoanApplicationDataValidationError{
					field:  "Lamf",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ResetVendorLoanApplicationDataMultiError(errors)
	}

	return nil
}

// ResetVendorLoanApplicationDataMultiError is an error wrapping multiple
// validation errors returned by ResetVendorLoanApplicationData.ValidateAll()
// if the designated constraints aren't met.
type ResetVendorLoanApplicationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetVendorLoanApplicationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetVendorLoanApplicationDataMultiError) AllErrors() []error { return m }

// ResetVendorLoanApplicationDataValidationError is the validation error
// returned by ResetVendorLoanApplicationData.Validate if the designated
// constraints aren't met.
type ResetVendorLoanApplicationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetVendorLoanApplicationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetVendorLoanApplicationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetVendorLoanApplicationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetVendorLoanApplicationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetVendorLoanApplicationDataValidationError) ErrorName() string {
	return "ResetVendorLoanApplicationDataValidationError"
}

// Error satisfies the builtin error interface
func (e ResetVendorLoanApplicationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetVendorLoanApplicationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetVendorLoanApplicationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetVendorLoanApplicationDataValidationError{}

// Validate checks the field values on LamfResetVendorLoanApplicationData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *LamfResetVendorLoanApplicationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LamfResetVendorLoanApplicationData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// LamfResetVendorLoanApplicationDataMultiError, or nil if none found.
func (m *LamfResetVendorLoanApplicationData) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfResetVendorLoanApplicationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserActionTaken

	if len(errors) > 0 {
		return LamfResetVendorLoanApplicationDataMultiError(errors)
	}

	return nil
}

// LamfResetVendorLoanApplicationDataMultiError is an error wrapping multiple
// validation errors returned by
// LamfResetVendorLoanApplicationData.ValidateAll() if the designated
// constraints aren't met.
type LamfResetVendorLoanApplicationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfResetVendorLoanApplicationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfResetVendorLoanApplicationDataMultiError) AllErrors() []error { return m }

// LamfResetVendorLoanApplicationDataValidationError is the validation error
// returned by LamfResetVendorLoanApplicationData.Validate if the designated
// constraints aren't met.
type LamfResetVendorLoanApplicationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfResetVendorLoanApplicationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LamfResetVendorLoanApplicationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LamfResetVendorLoanApplicationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LamfResetVendorLoanApplicationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfResetVendorLoanApplicationDataValidationError) ErrorName() string {
	return "LamfResetVendorLoanApplicationDataValidationError"
}

// Error satisfies the builtin error interface
func (e LamfResetVendorLoanApplicationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfResetVendorLoanApplicationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfResetVendorLoanApplicationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfResetVendorLoanApplicationDataValidationError{}

// Validate checks the field values on ContactabilityDetailsData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContactabilityDetailsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContactabilityDetailsData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContactabilityDetailsDataMultiError, or nil if none found.
func (m *ContactabilityDetailsData) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactabilityDetailsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactabilityDetailsDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactabilityDetailsDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactabilityDetailsDataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for MaxOtpAttempts

	// no validation rules for OtpAttemptCount

	// no validation rules for PhoneNumberAttemptCount

	// no validation rules for MaxPhoneAttemptAllowed

	if all {
		switch v := interface{}(m.GetLastAttemptTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactabilityDetailsDataValidationError{
					field:  "LastAttemptTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactabilityDetailsDataValidationError{
					field:  "LastAttemptTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastAttemptTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactabilityDetailsDataValidationError{
				field:  "LastAttemptTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ContactabilityDetailsDataMultiError(errors)
	}

	return nil
}

// ContactabilityDetailsDataMultiError is an error wrapping multiple validation
// errors returned by ContactabilityDetailsData.ValidateAll() if the
// designated constraints aren't met.
type ContactabilityDetailsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactabilityDetailsDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactabilityDetailsDataMultiError) AllErrors() []error { return m }

// ContactabilityDetailsDataValidationError is the validation error returned by
// ContactabilityDetailsData.Validate if the designated constraints aren't met.
type ContactabilityDetailsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactabilityDetailsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactabilityDetailsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactabilityDetailsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactabilityDetailsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactabilityDetailsDataValidationError) ErrorName() string {
	return "ContactabilityDetailsDataValidationError"
}

// Error satisfies the builtin error interface
func (e ContactabilityDetailsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactabilityDetailsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactabilityDetailsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactabilityDetailsDataValidationError{}

// Validate checks the field values on VendorIdentifiersData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VendorIdentifiersData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorIdentifiersData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VendorIdentifiersDataMultiError, or nil if none found.
func (m *VendorIdentifiersData) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorIdentifiersData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if len(errors) > 0 {
		return VendorIdentifiersDataMultiError(errors)
	}

	return nil
}

// VendorIdentifiersDataMultiError is an error wrapping multiple validation
// errors returned by VendorIdentifiersData.ValidateAll() if the designated
// constraints aren't met.
type VendorIdentifiersDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorIdentifiersDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorIdentifiersDataMultiError) AllErrors() []error { return m }

// VendorIdentifiersDataValidationError is the validation error returned by
// VendorIdentifiersData.Validate if the designated constraints aren't met.
type VendorIdentifiersDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorIdentifiersDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorIdentifiersDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorIdentifiersDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorIdentifiersDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorIdentifiersDataValidationError) ErrorName() string {
	return "VendorIdentifiersDataValidationError"
}

// Error satisfies the builtin error interface
func (e VendorIdentifiersDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorIdentifiersData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorIdentifiersDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorIdentifiersDataValidationError{}

// Validate checks the field values on ROIModificationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ROIModificationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ROIModificationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ROIModificationDataMultiError, or nil if none found.
func (m *ROIModificationData) ValidateAll() error {
	return m.validate(true)
}

func (m *ROIModificationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChosenRoi

	if all {
		switch v := interface{}(m.GetKfsDoc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ROIModificationDataValidationError{
					field:  "KfsDoc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ROIModificationDataValidationError{
					field:  "KfsDoc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKfsDoc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ROIModificationDataValidationError{
				field:  "KfsDoc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanAgreementDoc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ROIModificationDataValidationError{
					field:  "LoanAgreementDoc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ROIModificationDataValidationError{
					field:  "LoanAgreementDoc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAgreementDoc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ROIModificationDataValidationError{
				field:  "LoanAgreementDoc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInstallmentAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ROIModificationDataValidationError{
					field:  "InstallmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ROIModificationDataValidationError{
					field:  "InstallmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstallmentAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ROIModificationDataValidationError{
				field:  "InstallmentAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ROIModificationDataMultiError(errors)
	}

	return nil
}

// ROIModificationDataMultiError is an error wrapping multiple validation
// errors returned by ROIModificationData.ValidateAll() if the designated
// constraints aren't met.
type ROIModificationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ROIModificationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ROIModificationDataMultiError) AllErrors() []error { return m }

// ROIModificationDataValidationError is the validation error returned by
// ROIModificationData.Validate if the designated constraints aren't met.
type ROIModificationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ROIModificationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ROIModificationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ROIModificationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ROIModificationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ROIModificationDataValidationError) ErrorName() string {
	return "ROIModificationDataValidationError"
}

// Error satisfies the builtin error interface
func (e ROIModificationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sROIModificationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ROIModificationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ROIModificationDataValidationError{}

// Validate checks the field values on ManualReviewStepData_ReviewerDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ManualReviewStepData_ReviewerDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualReviewStepData_ReviewerDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ManualReviewStepData_ReviewerDetailsMultiError, or nil if none found.
func (m *ManualReviewStepData_ReviewerDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualReviewStepData_ReviewerDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetReviewedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualReviewStepData_ReviewerDetailsValidationError{
					field:  "ReviewedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualReviewStepData_ReviewerDetailsValidationError{
					field:  "ReviewedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualReviewStepData_ReviewerDetailsValidationError{
				field:  "ReviewedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ManualReviewStepData_ReviewerDetailsMultiError(errors)
	}

	return nil
}

// ManualReviewStepData_ReviewerDetailsMultiError is an error wrapping multiple
// validation errors returned by
// ManualReviewStepData_ReviewerDetails.ValidateAll() if the designated
// constraints aren't met.
type ManualReviewStepData_ReviewerDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualReviewStepData_ReviewerDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualReviewStepData_ReviewerDetailsMultiError) AllErrors() []error { return m }

// ManualReviewStepData_ReviewerDetailsValidationError is the validation error
// returned by ManualReviewStepData_ReviewerDetails.Validate if the designated
// constraints aren't met.
type ManualReviewStepData_ReviewerDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualReviewStepData_ReviewerDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualReviewStepData_ReviewerDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualReviewStepData_ReviewerDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualReviewStepData_ReviewerDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualReviewStepData_ReviewerDetailsValidationError) ErrorName() string {
	return "ManualReviewStepData_ReviewerDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ManualReviewStepData_ReviewerDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualReviewStepData_ReviewerDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualReviewStepData_ReviewerDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualReviewStepData_ReviewerDetailsValidationError{}

// Validate checks the field values on OnboardingData_AddressDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OnboardingData_AddressDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingData_AddressDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// OnboardingData_AddressDetailsMultiError, or nil if none found.
func (m *OnboardingData_AddressDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingData_AddressDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAddressDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingData_AddressDetailsValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingData_AddressDetailsValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingData_AddressDetailsValidationError{
				field:  "AddressDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AddressType

	// no validation rules for LocationToken

	// no validation rules for ResidenceType

	if all {
		switch v := interface{}(m.GetMonthlyRent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingData_AddressDetailsValidationError{
					field:  "MonthlyRent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingData_AddressDetailsValidationError{
					field:  "MonthlyRent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyRent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingData_AddressDetailsValidationError{
				field:  "MonthlyRent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OnboardingData_AddressDetailsMultiError(errors)
	}

	return nil
}

// OnboardingData_AddressDetailsMultiError is an error wrapping multiple
// validation errors returned by OnboardingData_AddressDetails.ValidateAll()
// if the designated constraints aren't met.
type OnboardingData_AddressDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingData_AddressDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingData_AddressDetailsMultiError) AllErrors() []error { return m }

// OnboardingData_AddressDetailsValidationError is the validation error
// returned by OnboardingData_AddressDetails.Validate if the designated
// constraints aren't met.
type OnboardingData_AddressDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingData_AddressDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingData_AddressDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingData_AddressDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingData_AddressDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingData_AddressDetailsValidationError) ErrorName() string {
	return "OnboardingData_AddressDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingData_AddressDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingData_AddressDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingData_AddressDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingData_AddressDetailsValidationError{}

// Validate checks the field values on OnboardingData_EmploymentDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *OnboardingData_EmploymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingData_EmploymentDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// OnboardingData_EmploymentDetailsMultiError, or nil if none found.
func (m *OnboardingData_EmploymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingData_EmploymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Occupation

	// no validation rules for OrganizationName

	if all {
		switch v := interface{}(m.GetMonthlyIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingData_EmploymentDetailsValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingData_EmploymentDetailsValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingData_EmploymentDetailsValidationError{
				field:  "MonthlyIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WorkEmail

	if all {
		switch v := interface{}(m.GetOfficeAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingData_EmploymentDetailsValidationError{
					field:  "OfficeAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingData_EmploymentDetailsValidationError{
					field:  "OfficeAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfficeAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingData_EmploymentDetailsValidationError{
				field:  "OfficeAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnnualRevenue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingData_EmploymentDetailsValidationError{
					field:  "AnnualRevenue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingData_EmploymentDetailsValidationError{
					field:  "AnnualRevenue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualRevenue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingData_EmploymentDetailsValidationError{
				field:  "AnnualRevenue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GSTIN

	if len(errors) > 0 {
		return OnboardingData_EmploymentDetailsMultiError(errors)
	}

	return nil
}

// OnboardingData_EmploymentDetailsMultiError is an error wrapping multiple
// validation errors returned by
// OnboardingData_EmploymentDetails.ValidateAll() if the designated
// constraints aren't met.
type OnboardingData_EmploymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingData_EmploymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingData_EmploymentDetailsMultiError) AllErrors() []error { return m }

// OnboardingData_EmploymentDetailsValidationError is the validation error
// returned by OnboardingData_EmploymentDetails.Validate if the designated
// constraints aren't met.
type OnboardingData_EmploymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingData_EmploymentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingData_EmploymentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingData_EmploymentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingData_EmploymentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingData_EmploymentDetailsValidationError) ErrorName() string {
	return "OnboardingData_EmploymentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingData_EmploymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingData_EmploymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingData_EmploymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingData_EmploymentDetailsValidationError{}

// Validate checks the field values on OnboardingData_BankingDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OnboardingData_BankingDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingData_BankingDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// OnboardingData_BankingDetailsMultiError, or nil if none found.
func (m *OnboardingData_BankingDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingData_BankingDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for AccountHolderName

	// no validation rules for IfscCode

	// no validation rules for BankName

	if len(errors) > 0 {
		return OnboardingData_BankingDetailsMultiError(errors)
	}

	return nil
}

// OnboardingData_BankingDetailsMultiError is an error wrapping multiple
// validation errors returned by OnboardingData_BankingDetails.ValidateAll()
// if the designated constraints aren't met.
type OnboardingData_BankingDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingData_BankingDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingData_BankingDetailsMultiError) AllErrors() []error { return m }

// OnboardingData_BankingDetailsValidationError is the validation error
// returned by OnboardingData_BankingDetails.Validate if the designated
// constraints aren't met.
type OnboardingData_BankingDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingData_BankingDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingData_BankingDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingData_BankingDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingData_BankingDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingData_BankingDetailsValidationError) ErrorName() string {
	return "OnboardingData_BankingDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingData_BankingDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingData_BankingDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingData_BankingDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingData_BankingDetailsValidationError{}

// Validate checks the field values on MandateData_BankingDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MandateData_BankingDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MandateData_BankingDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MandateData_BankingDetailsMultiError, or nil if none found.
func (m *MandateData_BankingDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MandateData_BankingDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetAlternateAccDetails()))
		i := 0
		for key := range m.GetAlternateAccDetails() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAlternateAccDetails()[key]
			_ = val

			// no validation rules for AlternateAccDetails[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, MandateData_BankingDetailsValidationError{
							field:  fmt.Sprintf("AlternateAccDetails[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, MandateData_BankingDetailsValidationError{
							field:  fmt.Sprintf("AlternateAccDetails[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return MandateData_BankingDetailsValidationError{
						field:  fmt.Sprintf("AlternateAccDetails[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetFinalAccDetailsUsed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateData_BankingDetailsValidationError{
					field:  "FinalAccDetailsUsed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateData_BankingDetailsValidationError{
					field:  "FinalAccDetailsUsed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinalAccDetailsUsed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateData_BankingDetailsValidationError{
				field:  "FinalAccDetailsUsed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MandateData_BankingDetailsMultiError(errors)
	}

	return nil
}

// MandateData_BankingDetailsMultiError is an error wrapping multiple
// validation errors returned by MandateData_BankingDetails.ValidateAll() if
// the designated constraints aren't met.
type MandateData_BankingDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MandateData_BankingDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MandateData_BankingDetailsMultiError) AllErrors() []error { return m }

// MandateData_BankingDetailsValidationError is the validation error returned
// by MandateData_BankingDetails.Validate if the designated constraints aren't met.
type MandateData_BankingDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MandateData_BankingDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MandateData_BankingDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MandateData_BankingDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MandateData_BankingDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MandateData_BankingDetailsValidationError) ErrorName() string {
	return "MandateData_BankingDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e MandateData_BankingDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMandateData_BankingDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MandateData_BankingDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MandateData_BankingDetailsValidationError{}

// Validate checks the field values on
// MandateData_BankingDetails_AccountDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MandateData_BankingDetails_AccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MandateData_BankingDetails_AccountDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// MandateData_BankingDetails_AccountDetailsMultiError, or nil if none found.
func (m *MandateData_BankingDetails_AccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MandateData_BankingDetails_AccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for AccountHolderName

	// no validation rules for IfscCode

	// no validation rules for BankName

	if len(errors) > 0 {
		return MandateData_BankingDetails_AccountDetailsMultiError(errors)
	}

	return nil
}

// MandateData_BankingDetails_AccountDetailsMultiError is an error wrapping
// multiple validation errors returned by
// MandateData_BankingDetails_AccountDetails.ValidateAll() if the designated
// constraints aren't met.
type MandateData_BankingDetails_AccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MandateData_BankingDetails_AccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MandateData_BankingDetails_AccountDetailsMultiError) AllErrors() []error { return m }

// MandateData_BankingDetails_AccountDetailsValidationError is the validation
// error returned by MandateData_BankingDetails_AccountDetails.Validate if the
// designated constraints aren't met.
type MandateData_BankingDetails_AccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MandateData_BankingDetails_AccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MandateData_BankingDetails_AccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MandateData_BankingDetails_AccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MandateData_BankingDetails_AccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MandateData_BankingDetails_AccountDetailsValidationError) ErrorName() string {
	return "MandateData_BankingDetails_AccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e MandateData_BankingDetails_AccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMandateData_BankingDetails_AccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MandateData_BankingDetails_AccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MandateData_BankingDetails_AccountDetailsValidationError{}

// Validate checks the field values on ItrIncomeInfo_ItrAttemptData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ItrIncomeInfo_ItrAttemptData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ItrIncomeInfo_ItrAttemptData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ItrIncomeInfo_ItrAttemptDataMultiError, or nil if none found.
func (m *ItrIncomeInfo_ItrAttemptData) ValidateAll() error {
	return m.validate(true)
}

func (m *ItrIncomeInfo_ItrAttemptData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttemptId

	// no validation rules for ItrStatus

	if len(errors) > 0 {
		return ItrIncomeInfo_ItrAttemptDataMultiError(errors)
	}

	return nil
}

// ItrIncomeInfo_ItrAttemptDataMultiError is an error wrapping multiple
// validation errors returned by ItrIncomeInfo_ItrAttemptData.ValidateAll() if
// the designated constraints aren't met.
type ItrIncomeInfo_ItrAttemptDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ItrIncomeInfo_ItrAttemptDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ItrIncomeInfo_ItrAttemptDataMultiError) AllErrors() []error { return m }

// ItrIncomeInfo_ItrAttemptDataValidationError is the validation error returned
// by ItrIncomeInfo_ItrAttemptData.Validate if the designated constraints
// aren't met.
type ItrIncomeInfo_ItrAttemptDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ItrIncomeInfo_ItrAttemptDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ItrIncomeInfo_ItrAttemptDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ItrIncomeInfo_ItrAttemptDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ItrIncomeInfo_ItrAttemptDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ItrIncomeInfo_ItrAttemptDataValidationError) ErrorName() string {
	return "ItrIncomeInfo_ItrAttemptDataValidationError"
}

// Error satisfies the builtin error interface
func (e ItrIncomeInfo_ItrAttemptDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sItrIncomeInfo_ItrAttemptData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ItrIncomeInfo_ItrAttemptDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ItrIncomeInfo_ItrAttemptDataValidationError{}

// Validate checks the field values on ItrIncomeInfo_ItrIntimationData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ItrIncomeInfo_ItrIntimationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ItrIncomeInfo_ItrIntimationData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ItrIncomeInfo_ItrIntimationDataMultiError, or nil if none found.
func (m *ItrIncomeInfo_ItrIntimationData) ValidateAll() error {
	return m.validate(true)
}

func (m *ItrIncomeInfo_ItrIntimationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ItrIncomeInfo_ItrIntimationDataValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ItrIncomeInfo_ItrIntimationDataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetGrossTotalIncomeProvided()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "GrossTotalIncomeProvided",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "GrossTotalIncomeProvided",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGrossTotalIncomeProvided()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ItrIncomeInfo_ItrIntimationDataValidationError{
				field:  "GrossTotalIncomeProvided",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGrossTotalIncomeComputed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "GrossTotalIncomeComputed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "GrossTotalIncomeComputed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGrossTotalIncomeComputed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ItrIncomeInfo_ItrIntimationDataValidationError{
				field:  "GrossTotalIncomeComputed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalIncomeProvided()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "TotalIncomeProvided",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "TotalIncomeProvided",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalIncomeProvided()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ItrIncomeInfo_ItrIntimationDataValidationError{
				field:  "TotalIncomeProvided",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalIncomeComputed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "TotalIncomeComputed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "TotalIncomeComputed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalIncomeComputed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ItrIncomeInfo_ItrIntimationDataValidationError{
				field:  "TotalIncomeComputed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDateOfFiling()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "DateOfFiling",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ItrIncomeInfo_ItrIntimationDataValidationError{
					field:  "DateOfFiling",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfFiling()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ItrIncomeInfo_ItrIntimationDataValidationError{
				field:  "DateOfFiling",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AssessmentYear

	// no validation rules for ItrFormType

	// no validation rules for TaxpayerStatus

	// no validation rules for ResidentialStatus

	if len(errors) > 0 {
		return ItrIncomeInfo_ItrIntimationDataMultiError(errors)
	}

	return nil
}

// ItrIncomeInfo_ItrIntimationDataMultiError is an error wrapping multiple
// validation errors returned by ItrIncomeInfo_ItrIntimationData.ValidateAll()
// if the designated constraints aren't met.
type ItrIncomeInfo_ItrIntimationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ItrIncomeInfo_ItrIntimationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ItrIncomeInfo_ItrIntimationDataMultiError) AllErrors() []error { return m }

// ItrIncomeInfo_ItrIntimationDataValidationError is the validation error
// returned by ItrIncomeInfo_ItrIntimationData.Validate if the designated
// constraints aren't met.
type ItrIncomeInfo_ItrIntimationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ItrIncomeInfo_ItrIntimationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ItrIncomeInfo_ItrIntimationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ItrIncomeInfo_ItrIntimationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ItrIncomeInfo_ItrIntimationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ItrIncomeInfo_ItrIntimationDataValidationError) ErrorName() string {
	return "ItrIncomeInfo_ItrIntimationDataValidationError"
}

// Error satisfies the builtin error interface
func (e ItrIncomeInfo_ItrIntimationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sItrIncomeInfo_ItrIntimationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ItrIncomeInfo_ItrIntimationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ItrIncomeInfo_ItrIntimationDataValidationError{}

// Validate checks the field values on OtpVerificationData_OtpData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OtpVerificationData_OtpData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtpVerificationData_OtpData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OtpVerificationData_OtpDataMultiError, or nil if none found.
func (m *OtpVerificationData_OtpData) ValidateAll() error {
	return m.validate(true)
}

func (m *OtpVerificationData_OtpData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for OtpType

	// no validation rules for OtpStatus

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpVerificationData_OtpDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpVerificationData_OtpDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpVerificationData_OtpDataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for VendorSessionId

	// no validation rules for AssetDetails

	if all {
		switch v := interface{}(m.GetOtpValidationCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OtpVerificationData_OtpDataValidationError{
					field:  "OtpValidationCondition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OtpVerificationData_OtpDataValidationError{
					field:  "OtpValidationCondition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtpValidationCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OtpVerificationData_OtpDataValidationError{
				field:  "OtpValidationCondition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OtpSerialNo

	if len(errors) > 0 {
		return OtpVerificationData_OtpDataMultiError(errors)
	}

	return nil
}

// OtpVerificationData_OtpDataMultiError is an error wrapping multiple
// validation errors returned by OtpVerificationData_OtpData.ValidateAll() if
// the designated constraints aren't met.
type OtpVerificationData_OtpDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtpVerificationData_OtpDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtpVerificationData_OtpDataMultiError) AllErrors() []error { return m }

// OtpVerificationData_OtpDataValidationError is the validation error returned
// by OtpVerificationData_OtpData.Validate if the designated constraints
// aren't met.
type OtpVerificationData_OtpDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtpVerificationData_OtpDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtpVerificationData_OtpDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtpVerificationData_OtpDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtpVerificationData_OtpDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtpVerificationData_OtpDataValidationError) ErrorName() string {
	return "OtpVerificationData_OtpDataValidationError"
}

// Error satisfies the builtin error interface
func (e OtpVerificationData_OtpDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtpVerificationData_OtpData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtpVerificationData_OtpDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtpVerificationData_OtpDataValidationError{}

// Validate checks the field values on VendorPWAStagesStepData_StageInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VendorPWAStagesStepData_StageInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorPWAStagesStepData_StageInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VendorPWAStagesStepData_StageInfoMultiError, or nil if none found.
func (m *VendorPWAStagesStepData_StageInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorPWAStagesStepData_StageInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StageName

	// no validation rules for StageStatus

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorPWAStagesStepData_StageInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorPWAStagesStepData_StageInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorPWAStagesStepData_StageInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorStageName

	if all {
		switch v := interface{}(m.GetVendorUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorPWAStagesStepData_StageInfoValidationError{
					field:  "VendorUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorPWAStagesStepData_StageInfoValidationError{
					field:  "VendorUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorPWAStagesStepData_StageInfoValidationError{
				field:  "VendorUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VendorPWAStagesStepData_StageInfoMultiError(errors)
	}

	return nil
}

// VendorPWAStagesStepData_StageInfoMultiError is an error wrapping multiple
// validation errors returned by
// VendorPWAStagesStepData_StageInfo.ValidateAll() if the designated
// constraints aren't met.
type VendorPWAStagesStepData_StageInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorPWAStagesStepData_StageInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorPWAStagesStepData_StageInfoMultiError) AllErrors() []error { return m }

// VendorPWAStagesStepData_StageInfoValidationError is the validation error
// returned by VendorPWAStagesStepData_StageInfo.Validate if the designated
// constraints aren't met.
type VendorPWAStagesStepData_StageInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorPWAStagesStepData_StageInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorPWAStagesStepData_StageInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorPWAStagesStepData_StageInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorPWAStagesStepData_StageInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorPWAStagesStepData_StageInfoValidationError) ErrorName() string {
	return "VendorPWAStagesStepData_StageInfoValidationError"
}

// Error satisfies the builtin error interface
func (e VendorPWAStagesStepData_StageInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorPWAStagesStepData_StageInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorPWAStagesStepData_StageInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorPWAStagesStepData_StageInfoValidationError{}

// Validate checks the field values on
// LamfLoanDetailsVerificationData_PfFetchData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LamfLoanDetailsVerificationData_PfFetchData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LamfLoanDetailsVerificationData_PfFetchData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// LamfLoanDetailsVerificationData_PfFetchDataMultiError, or nil if none found.
func (m *LamfLoanDetailsVerificationData_PfFetchData) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfLoanDetailsVerificationData_PfFetchData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReqId

	// no validation rules for FetchCompleted

	// no validation rules for IsFetchSuccess

	if all {
		switch v := interface{}(m.GetCompletionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationData_PfFetchDataValidationError{
					field:  "CompletionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationData_PfFetchDataValidationError{
					field:  "CompletionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfLoanDetailsVerificationData_PfFetchDataValidationError{
				field:  "CompletionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LamfLoanDetailsVerificationData_PfFetchDataMultiError(errors)
	}

	return nil
}

// LamfLoanDetailsVerificationData_PfFetchDataMultiError is an error wrapping
// multiple validation errors returned by
// LamfLoanDetailsVerificationData_PfFetchData.ValidateAll() if the designated
// constraints aren't met.
type LamfLoanDetailsVerificationData_PfFetchDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfLoanDetailsVerificationData_PfFetchDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfLoanDetailsVerificationData_PfFetchDataMultiError) AllErrors() []error { return m }

// LamfLoanDetailsVerificationData_PfFetchDataValidationError is the validation
// error returned by LamfLoanDetailsVerificationData_PfFetchData.Validate if
// the designated constraints aren't met.
type LamfLoanDetailsVerificationData_PfFetchDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfLoanDetailsVerificationData_PfFetchDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LamfLoanDetailsVerificationData_PfFetchDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LamfLoanDetailsVerificationData_PfFetchDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LamfLoanDetailsVerificationData_PfFetchDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfLoanDetailsVerificationData_PfFetchDataValidationError) ErrorName() string {
	return "LamfLoanDetailsVerificationData_PfFetchDataValidationError"
}

// Error satisfies the builtin error interface
func (e LamfLoanDetailsVerificationData_PfFetchDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfLoanDetailsVerificationData_PfFetchData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfLoanDetailsVerificationData_PfFetchDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfLoanDetailsVerificationData_PfFetchDataValidationError{}

// Validate checks the field values on
// LamfLoanDetailsVerificationData_MfMobileLinkDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LamfLoanDetailsVerificationData_MfMobileLinkDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LamfLoanDetailsVerificationData_MfMobileLinkDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LamfLoanDetailsVerificationData_MfMobileLinkDetailsMultiError, or nil if
// none found.
func (m *LamfLoanDetailsVerificationData_MfMobileLinkDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfLoanDetailsVerificationData_MfMobileLinkDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNewMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
					field:  "NewMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
					field:  "NewMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
				field:  "NewMobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LinkedEmail

	for idx, item := range m.GetFolios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
						field:  fmt.Sprintf("Folios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
						field:  fmt.Sprintf("Folios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
					field:  fmt.Sprintf("Folios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetNftReqDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
						field:  fmt.Sprintf("NftReqDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
						field:  fmt.Sprintf("NftReqDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{
					field:  fmt.Sprintf("NftReqDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LamfLoanDetailsVerificationData_MfMobileLinkDetailsMultiError(errors)
	}

	return nil
}

// LamfLoanDetailsVerificationData_MfMobileLinkDetailsMultiError is an error
// wrapping multiple validation errors returned by
// LamfLoanDetailsVerificationData_MfMobileLinkDetails.ValidateAll() if the
// designated constraints aren't met.
type LamfLoanDetailsVerificationData_MfMobileLinkDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfLoanDetailsVerificationData_MfMobileLinkDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfLoanDetailsVerificationData_MfMobileLinkDetailsMultiError) AllErrors() []error { return m }

// LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError is the
// validation error returned by
// LamfLoanDetailsVerificationData_MfMobileLinkDetails.Validate if the
// designated constraints aren't met.
type LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError) ErrorName() string {
	return "LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfLoanDetailsVerificationData_MfMobileLinkDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfLoanDetailsVerificationData_MfMobileLinkDetailsValidationError{}

// Validate checks the field values on
// LamfLoanDetailsVerificationData_MfEmailLinkDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LamfLoanDetailsVerificationData_MfEmailLinkDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LamfLoanDetailsVerificationData_MfEmailLinkDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LamfLoanDetailsVerificationData_MfEmailLinkDetailsMultiError, or nil if
// none found.
func (m *LamfLoanDetailsVerificationData_MfEmailLinkDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfLoanDetailsVerificationData_MfEmailLinkDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NewEmail

	if all {
		switch v := interface{}(m.GetLinkedMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
					field:  "LinkedMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
					field:  "LinkedMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLinkedMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
				field:  "LinkedMobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFolios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
						field:  fmt.Sprintf("Folios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
						field:  fmt.Sprintf("Folios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
					field:  fmt.Sprintf("Folios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetNftReqDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
						field:  fmt.Sprintf("NftReqDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
						field:  fmt.Sprintf("NftReqDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{
					field:  fmt.Sprintf("NftReqDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LamfLoanDetailsVerificationData_MfEmailLinkDetailsMultiError(errors)
	}

	return nil
}

// LamfLoanDetailsVerificationData_MfEmailLinkDetailsMultiError is an error
// wrapping multiple validation errors returned by
// LamfLoanDetailsVerificationData_MfEmailLinkDetails.ValidateAll() if the
// designated constraints aren't met.
type LamfLoanDetailsVerificationData_MfEmailLinkDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfLoanDetailsVerificationData_MfEmailLinkDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfLoanDetailsVerificationData_MfEmailLinkDetailsMultiError) AllErrors() []error { return m }

// LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError is the
// validation error returned by
// LamfLoanDetailsVerificationData_MfEmailLinkDetails.Validate if the
// designated constraints aren't met.
type LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError) ErrorName() string {
	return "LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfLoanDetailsVerificationData_MfEmailLinkDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfLoanDetailsVerificationData_MfEmailLinkDetailsValidationError{}

// Validate checks the field values on
// LamfLoanDetailsVerificationData_FolioData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LamfLoanDetailsVerificationData_FolioData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LamfLoanDetailsVerificationData_FolioData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// LamfLoanDetailsVerificationData_FolioDataMultiError, or nil if none found.
func (m *LamfLoanDetailsVerificationData_FolioData) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfLoanDetailsVerificationData_FolioData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FolioNumber

	// no validation rules for Isin

	// no validation rules for Quantity

	// no validation rules for Amc

	if len(errors) > 0 {
		return LamfLoanDetailsVerificationData_FolioDataMultiError(errors)
	}

	return nil
}

// LamfLoanDetailsVerificationData_FolioDataMultiError is an error wrapping
// multiple validation errors returned by
// LamfLoanDetailsVerificationData_FolioData.ValidateAll() if the designated
// constraints aren't met.
type LamfLoanDetailsVerificationData_FolioDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfLoanDetailsVerificationData_FolioDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfLoanDetailsVerificationData_FolioDataMultiError) AllErrors() []error { return m }

// LamfLoanDetailsVerificationData_FolioDataValidationError is the validation
// error returned by LamfLoanDetailsVerificationData_FolioData.Validate if the
// designated constraints aren't met.
type LamfLoanDetailsVerificationData_FolioDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfLoanDetailsVerificationData_FolioDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LamfLoanDetailsVerificationData_FolioDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LamfLoanDetailsVerificationData_FolioDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LamfLoanDetailsVerificationData_FolioDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfLoanDetailsVerificationData_FolioDataValidationError) ErrorName() string {
	return "LamfLoanDetailsVerificationData_FolioDataValidationError"
}

// Error satisfies the builtin error interface
func (e LamfLoanDetailsVerificationData_FolioDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfLoanDetailsVerificationData_FolioData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfLoanDetailsVerificationData_FolioDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfLoanDetailsVerificationData_FolioDataValidationError{}

// Validate checks the field values on
// LamfLoanDetailsVerificationData_NftRequestDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LamfLoanDetailsVerificationData_NftRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LamfLoanDetailsVerificationData_NftRequestDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LamfLoanDetailsVerificationData_NftRequestDetailsMultiError, or nil if none found.
func (m *LamfLoanDetailsVerificationData_NftRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfLoanDetailsVerificationData_NftRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationData_NftRequestDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LamfLoanDetailsVerificationData_NftRequestDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LamfLoanDetailsVerificationData_NftRequestDetailsValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LamfLoanDetailsVerificationData_NftRequestDetailsMultiError(errors)
	}

	return nil
}

// LamfLoanDetailsVerificationData_NftRequestDetailsMultiError is an error
// wrapping multiple validation errors returned by
// LamfLoanDetailsVerificationData_NftRequestDetails.ValidateAll() if the
// designated constraints aren't met.
type LamfLoanDetailsVerificationData_NftRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfLoanDetailsVerificationData_NftRequestDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfLoanDetailsVerificationData_NftRequestDetailsMultiError) AllErrors() []error { return m }

// LamfLoanDetailsVerificationData_NftRequestDetailsValidationError is the
// validation error returned by
// LamfLoanDetailsVerificationData_NftRequestDetails.Validate if the
// designated constraints aren't met.
type LamfLoanDetailsVerificationData_NftRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfLoanDetailsVerificationData_NftRequestDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LamfLoanDetailsVerificationData_NftRequestDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LamfLoanDetailsVerificationData_NftRequestDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LamfLoanDetailsVerificationData_NftRequestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfLoanDetailsVerificationData_NftRequestDetailsValidationError) ErrorName() string {
	return "LamfLoanDetailsVerificationData_NftRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LamfLoanDetailsVerificationData_NftRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfLoanDetailsVerificationData_NftRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfLoanDetailsVerificationData_NftRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfLoanDetailsVerificationData_NftRequestDetailsValidationError{}

// Validate checks the field values on
// LamfLoanDetailsVerificationData_UserAction with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LamfLoanDetailsVerificationData_UserAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LamfLoanDetailsVerificationData_UserAction with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// LamfLoanDetailsVerificationData_UserActionMultiError, or nil if none found.
func (m *LamfLoanDetailsVerificationData_UserAction) ValidateAll() error {
	return m.validate(true)
}

func (m *LamfLoanDetailsVerificationData_UserAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Identifier

	if len(errors) > 0 {
		return LamfLoanDetailsVerificationData_UserActionMultiError(errors)
	}

	return nil
}

// LamfLoanDetailsVerificationData_UserActionMultiError is an error wrapping
// multiple validation errors returned by
// LamfLoanDetailsVerificationData_UserAction.ValidateAll() if the designated
// constraints aren't met.
type LamfLoanDetailsVerificationData_UserActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LamfLoanDetailsVerificationData_UserActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LamfLoanDetailsVerificationData_UserActionMultiError) AllErrors() []error { return m }

// LamfLoanDetailsVerificationData_UserActionValidationError is the validation
// error returned by LamfLoanDetailsVerificationData_UserAction.Validate if
// the designated constraints aren't met.
type LamfLoanDetailsVerificationData_UserActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LamfLoanDetailsVerificationData_UserActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LamfLoanDetailsVerificationData_UserActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LamfLoanDetailsVerificationData_UserActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LamfLoanDetailsVerificationData_UserActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LamfLoanDetailsVerificationData_UserActionValidationError) ErrorName() string {
	return "LamfLoanDetailsVerificationData_UserActionValidationError"
}

// Error satisfies the builtin error interface
func (e LamfLoanDetailsVerificationData_UserActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLamfLoanDetailsVerificationData_UserAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LamfLoanDetailsVerificationData_UserActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LamfLoanDetailsVerificationData_UserActionValidationError{}
