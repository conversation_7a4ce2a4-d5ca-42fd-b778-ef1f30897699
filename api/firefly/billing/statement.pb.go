//go:generate gen_sql -types=StatementSummary

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/billing/statement.proto

package billing

import (
	enums "github.com/epifi/gamma/api/firefly/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Statement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserDetail *StatementUserDetail `protobuf:"bytes,1,opt,name=user_detail,json=userDetail,proto3" json:"user_detail,omitempty"`
	// date from which the statement has been fetched
	FromDate *date.Date `protobuf:"bytes,2,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	// date upto which the statement has been fetched
	ToDate       *date.Date              `protobuf:"bytes,3,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	Summary      *StatementSummary       `protobuf:"bytes,4,opt,name=summary,proto3" json:"summary,omitempty"`
	Transactions []*StatementTransaction `protobuf:"bytes,5,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *Statement) Reset() {
	*x = Statement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Statement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Statement) ProtoMessage() {}

func (x *Statement) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Statement.ProtoReflect.Descriptor instead.
func (*Statement) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{0}
}

func (x *Statement) GetUserDetail() *StatementUserDetail {
	if x != nil {
		return x.UserDetail
	}
	return nil
}

func (x *Statement) GetFromDate() *date.Date {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *Statement) GetToDate() *date.Date {
	if x != nil {
		return x.ToDate
	}
	return nil
}

func (x *Statement) GetSummary() *StatementSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *Statement) GetTransactions() []*StatementTransaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type StatementSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date on which the statement has been created
	StatementDate *date.Date `protobuf:"bytes,1,opt,name=statement_date,json=statementDate,proto3" json:"statement_date,omitempty"`
	// date on which the payment is due
	PaymentDueDate *date.Date `protobuf:"bytes,2,opt,name=payment_due_date,json=paymentDueDate,proto3" json:"payment_due_date,omitempty"`
	// amount of money available
	AvailableLimit *money.Money `protobuf:"bytes,3,opt,name=available_limit,json=availableLimit,proto3" json:"available_limit,omitempty"`
	// total amount due
	TotalAmountDue *money.Money `protobuf:"bytes,4,opt,name=total_amount_due,json=totalAmountDue,proto3" json:"total_amount_due,omitempty"`
	// minimum amount due
	MinAmountDue *money.Money `protobuf:"bytes,5,opt,name=min_amount_due,json=minAmountDue,proto3" json:"min_amount_due,omitempty"`
	// Opening balance on the date from which statement begins
	OpeningBalance *money.Money `protobuf:"bytes,6,opt,name=opening_balance,json=openingBalance,proto3" json:"opening_balance,omitempty"`
	// summation of all expense transactions
	Spends *money.Money `protobuf:"bytes,7,opt,name=spends,proto3" json:"spends,omitempty"`
	// interest accrued
	InterestCharges *money.Money `protobuf:"bytes,8,opt,name=interest_charges,json=interestCharges,proto3" json:"interest_charges,omitempty"`
	// total fees
	Fees *money.Money `protobuf:"bytes,9,opt,name=fees,proto3" json:"fees,omitempty"`
	// repayments and returns for the statement period
	RepaymentAndRefunds *money.Money `protobuf:"bytes,10,opt,name=repayment_and_refunds,json=repaymentAndRefunds,proto3" json:"repayment_and_refunds,omitempty"`
	// spent amount converted to emi
	SpendConvertedToEmi *money.Money `protobuf:"bytes,11,opt,name=spend_converted_to_emi,json=spendConvertedToEmi,proto3" json:"spend_converted_to_emi,omitempty"`
}

func (x *StatementSummary) Reset() {
	*x = StatementSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatementSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatementSummary) ProtoMessage() {}

func (x *StatementSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatementSummary.ProtoReflect.Descriptor instead.
func (*StatementSummary) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{1}
}

func (x *StatementSummary) GetStatementDate() *date.Date {
	if x != nil {
		return x.StatementDate
	}
	return nil
}

func (x *StatementSummary) GetPaymentDueDate() *date.Date {
	if x != nil {
		return x.PaymentDueDate
	}
	return nil
}

func (x *StatementSummary) GetAvailableLimit() *money.Money {
	if x != nil {
		return x.AvailableLimit
	}
	return nil
}

func (x *StatementSummary) GetTotalAmountDue() *money.Money {
	if x != nil {
		return x.TotalAmountDue
	}
	return nil
}

func (x *StatementSummary) GetMinAmountDue() *money.Money {
	if x != nil {
		return x.MinAmountDue
	}
	return nil
}

func (x *StatementSummary) GetOpeningBalance() *money.Money {
	if x != nil {
		return x.OpeningBalance
	}
	return nil
}

func (x *StatementSummary) GetSpends() *money.Money {
	if x != nil {
		return x.Spends
	}
	return nil
}

func (x *StatementSummary) GetInterestCharges() *money.Money {
	if x != nil {
		return x.InterestCharges
	}
	return nil
}

func (x *StatementSummary) GetFees() *money.Money {
	if x != nil {
		return x.Fees
	}
	return nil
}

func (x *StatementSummary) GetRepaymentAndRefunds() *money.Money {
	if x != nil {
		return x.RepaymentAndRefunds
	}
	return nil
}

func (x *StatementSummary) GetSpendConvertedToEmi() *money.Money {
	if x != nil {
		return x.SpendConvertedToEmi
	}
	return nil
}

type StatementUserDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the user
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// contact number of the user
	PhoneNumber uint64 `protobuf:"varint,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// email id of the user
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// permanent address of the user
	Address string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// masked credit card number of the user
	MaskedCreditCardNumber string `protobuf:"bytes,5,opt,name=masked_credit_card_number,json=maskedCreditCardNumber,proto3" json:"masked_credit_card_number,omitempty"`
}

func (x *StatementUserDetail) Reset() {
	*x = StatementUserDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatementUserDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatementUserDetail) ProtoMessage() {}

func (x *StatementUserDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatementUserDetail.ProtoReflect.Descriptor instead.
func (*StatementUserDetail) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{2}
}

func (x *StatementUserDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StatementUserDetail) GetPhoneNumber() uint64 {
	if x != nil {
		return x.PhoneNumber
	}
	return 0
}

func (x *StatementUserDetail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *StatementUserDetail) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *StatementUserDetail) GetMaskedCreditCardNumber() string {
	if x != nil {
		return x.MaskedCreditCardNumber
	}
	return ""
}

type StatementTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// amount of money transacted (in INR)
	Amount *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// date of transaction
	TransactionTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=transaction_timestamp,json=transactionTimestamp,proto3" json:"transaction_timestamp,omitempty"`
	MerchantName         string                 `protobuf:"bytes,3,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
	// location where the transaction happened
	Location          string                  `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	TransactionOrigin enums.TransactionOrigin `protobuf:"varint,5,opt,name=transaction_origin,json=transactionOrigin,proto3,enum=firefly.enums.TransactionOrigin" json:"transaction_origin,omitempty"`
	PaymentMethod     string                  `protobuf:"bytes,6,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	Category          string                  `protobuf:"bytes,7,opt,name=category,proto3" json:"category,omitempty"`
	// Type of transaction - debit or credit
	TransactionTransferType typesv2.TransactionTransferType `protobuf:"varint,8,opt,name=transaction_transfer_type,json=transactionTransferType,proto3,enum=api.typesv2.TransactionTransferType" json:"transaction_transfer_type,omitempty"`
	RewardCoins             int32                           `protobuf:"varint,9,opt,name=reward_coins,json=rewardCoins,proto3" json:"reward_coins,omitempty"`
	RewardPoints            int32                           `protobuf:"varint,10,opt,name=reward_points,json=rewardPoints,proto3" json:"reward_points,omitempty"`
}

func (x *StatementTransaction) Reset() {
	*x = StatementTransaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatementTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatementTransaction) ProtoMessage() {}

func (x *StatementTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatementTransaction.ProtoReflect.Descriptor instead.
func (*StatementTransaction) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{3}
}

func (x *StatementTransaction) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *StatementTransaction) GetTransactionTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionTimestamp
	}
	return nil
}

func (x *StatementTransaction) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

func (x *StatementTransaction) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *StatementTransaction) GetTransactionOrigin() enums.TransactionOrigin {
	if x != nil {
		return x.TransactionOrigin
	}
	return enums.TransactionOrigin(0)
}

func (x *StatementTransaction) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *StatementTransaction) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *StatementTransaction) GetTransactionTransferType() typesv2.TransactionTransferType {
	if x != nil {
		return x.TransactionTransferType
	}
	return typesv2.TransactionTransferType(0)
}

func (x *StatementTransaction) GetRewardCoins() int32 {
	if x != nil {
		return x.RewardCoins
	}
	return 0
}

func (x *StatementTransaction) GetRewardPoints() int32 {
	if x != nil {
		return x.RewardPoints
	}
	return 0
}

type FeeBreakDown struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                  string                    `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FeeBreakDownComponents []*FeeBreakDownComponents `protobuf:"bytes,2,rep,name=fee_break_down_components,json=feeBreakDownComponents,proto3" json:"fee_break_down_components,omitempty"`
}

func (x *FeeBreakDown) Reset() {
	*x = FeeBreakDown{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeeBreakDown) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeBreakDown) ProtoMessage() {}

func (x *FeeBreakDown) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeBreakDown.ProtoReflect.Descriptor instead.
func (*FeeBreakDown) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{4}
}

func (x *FeeBreakDown) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FeeBreakDown) GetFeeBreakDownComponents() []*FeeBreakDownComponents {
	if x != nil {
		return x.FeeBreakDownComponents
	}
	return nil
}

type FeeBreakDownComponents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeeType string       `protobuf:"bytes,1,opt,name=fee_type,json=feeType,proto3" json:"fee_type,omitempty"`
	Amount  *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// PLUS OR MINUS
	FeeAmountType string `protobuf:"bytes,3,opt,name=fee_amount_type,json=feeAmountType,proto3" json:"fee_amount_type,omitempty"`
}

func (x *FeeBreakDownComponents) Reset() {
	*x = FeeBreakDownComponents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeeBreakDownComponents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeBreakDownComponents) ProtoMessage() {}

func (x *FeeBreakDownComponents) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeBreakDownComponents.ProtoReflect.Descriptor instead.
func (*FeeBreakDownComponents) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{5}
}

func (x *FeeBreakDownComponents) GetFeeType() string {
	if x != nil {
		return x.FeeType
	}
	return ""
}

func (x *FeeBreakDownComponents) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *FeeBreakDownComponents) GetFeeAmountType() string {
	if x != nil {
		return x.FeeAmountType
	}
	return ""
}

type ExtraRewardsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text              string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	RewardCoinsEarned int32  `protobuf:"varint,2,opt,name=reward_coins_earned,json=rewardCoinsEarned,proto3" json:"reward_coins_earned,omitempty"`
	RewardTypeLogo    string `protobuf:"bytes,3,opt,name=reward_type_logo,json=rewardTypeLogo,proto3" json:"reward_type_logo,omitempty"`
}

func (x *ExtraRewardsInfo) Reset() {
	*x = ExtraRewardsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtraRewardsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraRewardsInfo) ProtoMessage() {}

func (x *ExtraRewardsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraRewardsInfo.ProtoReflect.Descriptor instead.
func (*ExtraRewardsInfo) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{6}
}

func (x *ExtraRewardsInfo) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ExtraRewardsInfo) GetRewardCoinsEarned() int32 {
	if x != nil {
		return x.RewardCoinsEarned
	}
	return 0
}

func (x *ExtraRewardsInfo) GetRewardTypeLogo() string {
	if x != nil {
		return x.RewardTypeLogo
	}
	return ""
}

type EmiSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumberOfActiveEmi int64        `protobuf:"varint,1,opt,name=number_of_active_emi,json=numberOfActiveEmi,proto3" json:"number_of_active_emi,omitempty"`
	DueAmount         *money.Money `protobuf:"bytes,2,opt,name=due_amount,json=dueAmount,proto3" json:"due_amount,omitempty"`
	EmiDetails        []*EmiDetail `protobuf:"bytes,3,rep,name=emi_details,json=emiDetails,proto3" json:"emi_details,omitempty"`
}

func (x *EmiSummary) Reset() {
	*x = EmiSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmiSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmiSummary) ProtoMessage() {}

func (x *EmiSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmiSummary.ProtoReflect.Descriptor instead.
func (*EmiSummary) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{7}
}

func (x *EmiSummary) GetNumberOfActiveEmi() int64 {
	if x != nil {
		return x.NumberOfActiveEmi
	}
	return 0
}

func (x *EmiSummary) GetDueAmount() *money.Money {
	if x != nil {
		return x.DueAmount
	}
	return nil
}

func (x *EmiSummary) GetEmiDetails() []*EmiDetail {
	if x != nil {
		return x.EmiDetails
	}
	return nil
}

type EmiDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MerchantName      string       `protobuf:"bytes,1,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
	InstallmentNumber int64        `protobuf:"varint,2,opt,name=installment_number,json=installmentNumber,proto3" json:"installment_number,omitempty"`
	TotalInstallments int64        `protobuf:"varint,3,opt,name=total_installments,json=totalInstallments,proto3" json:"total_installments,omitempty"`
	Amount            *money.Money `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *EmiDetail) Reset() {
	*x = EmiDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_billing_statement_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmiDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmiDetail) ProtoMessage() {}

func (x *EmiDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_billing_statement_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmiDetail.ProtoReflect.Descriptor instead.
func (*EmiDetail) Descriptor() ([]byte, []int) {
	return file_api_firefly_billing_statement_proto_rawDescGZIP(), []int{8}
}

func (x *EmiDetail) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

func (x *EmiDetail) GetInstallmentNumber() int64 {
	if x != nil {
		return x.InstallmentNumber
	}
	return 0
}

func (x *EmiDetail) GetTotalInstallments() int64 {
	if x != nil {
		return x.TotalInstallments
	}
	return 0
}

func (x *EmiDetail) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

var File_api_firefly_billing_statement_proto protoreflect.FileDescriptor

var file_api_firefly_billing_statement_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x02, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x45, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x75,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2e, 0x0a, 0x09, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x74, 0x6f, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x06, 0x74,
	0x6f, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x49, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9f, 0x05,
	0x0a, 0x10, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x38, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x10,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x3c, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x75, 0x65, 0x12, 0x38, 0x0a, 0x0e, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0c, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x75, 0x65, 0x12, 0x3b,
	0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x6f, 0x70, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x73,
	0x70, 0x65, 0x6e, 0x64, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x06, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x12, 0x3d, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x04, 0x66, 0x65, 0x65, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x04, 0x66, 0x65, 0x65, 0x73, 0x12, 0x46,
	0x0a, 0x15, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x64, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x13, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x47, 0x0a, 0x16, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x5f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x69,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x73, 0x70, 0x65, 0x6e,
	0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x45, 0x6d, 0x69, 0x22,
	0xb7, 0x01, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x39,
	0x0a, 0x19, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x16, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x92, 0x04, 0x0a, 0x14, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4f,
	0x0a, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x4f, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x52, 0x11,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x60, 0x0a, 0x19, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x17, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x88,
	0x01, 0x0a, 0x0c, 0x46, 0x65, 0x65, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x62, 0x0a, 0x19, 0x66, 0x65, 0x65, 0x5f, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x65, 0x42, 0x72,
	0x65, 0x61, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x16, 0x66, 0x65, 0x65, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x16, 0x46, 0x65,
	0x65, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x66,
	0x65, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x65, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x10, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x2e, 0x0a, 0x13,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x65, 0x61, 0x72,
	0x6e, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x22, 0xad, 0x01, 0x0a, 0x0a, 0x45, 0x6d, 0x69, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x2f, 0x0a, 0x14, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x6f, 0x66, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x65, 0x6d, 0x69, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x45, 0x6d, 0x69, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09,
	0x64, 0x75, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x6d, 0x69,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x2e, 0x45, 0x6d, 0x69, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x65, 0x6d, 0x69, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x09, 0x45, 0x6d, 0x69, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_billing_statement_proto_rawDescOnce sync.Once
	file_api_firefly_billing_statement_proto_rawDescData = file_api_firefly_billing_statement_proto_rawDesc
)

func file_api_firefly_billing_statement_proto_rawDescGZIP() []byte {
	file_api_firefly_billing_statement_proto_rawDescOnce.Do(func() {
		file_api_firefly_billing_statement_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_billing_statement_proto_rawDescData)
	})
	return file_api_firefly_billing_statement_proto_rawDescData
}

var file_api_firefly_billing_statement_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_firefly_billing_statement_proto_goTypes = []interface{}{
	(*Statement)(nil),                    // 0: firefly.billing.Statement
	(*StatementSummary)(nil),             // 1: firefly.billing.StatementSummary
	(*StatementUserDetail)(nil),          // 2: firefly.billing.StatementUserDetail
	(*StatementTransaction)(nil),         // 3: firefly.billing.StatementTransaction
	(*FeeBreakDown)(nil),                 // 4: firefly.billing.FeeBreakDown
	(*FeeBreakDownComponents)(nil),       // 5: firefly.billing.FeeBreakDownComponents
	(*ExtraRewardsInfo)(nil),             // 6: firefly.billing.ExtraRewardsInfo
	(*EmiSummary)(nil),                   // 7: firefly.billing.EmiSummary
	(*EmiDetail)(nil),                    // 8: firefly.billing.EmiDetail
	(*date.Date)(nil),                    // 9: google.type.Date
	(*money.Money)(nil),                  // 10: google.type.Money
	(*timestamppb.Timestamp)(nil),        // 11: google.protobuf.Timestamp
	(enums.TransactionOrigin)(0),         // 12: firefly.enums.TransactionOrigin
	(typesv2.TransactionTransferType)(0), // 13: api.typesv2.TransactionTransferType
}
var file_api_firefly_billing_statement_proto_depIdxs = []int32{
	2,  // 0: firefly.billing.Statement.user_detail:type_name -> firefly.billing.StatementUserDetail
	9,  // 1: firefly.billing.Statement.from_date:type_name -> google.type.Date
	9,  // 2: firefly.billing.Statement.to_date:type_name -> google.type.Date
	1,  // 3: firefly.billing.Statement.summary:type_name -> firefly.billing.StatementSummary
	3,  // 4: firefly.billing.Statement.transactions:type_name -> firefly.billing.StatementTransaction
	9,  // 5: firefly.billing.StatementSummary.statement_date:type_name -> google.type.Date
	9,  // 6: firefly.billing.StatementSummary.payment_due_date:type_name -> google.type.Date
	10, // 7: firefly.billing.StatementSummary.available_limit:type_name -> google.type.Money
	10, // 8: firefly.billing.StatementSummary.total_amount_due:type_name -> google.type.Money
	10, // 9: firefly.billing.StatementSummary.min_amount_due:type_name -> google.type.Money
	10, // 10: firefly.billing.StatementSummary.opening_balance:type_name -> google.type.Money
	10, // 11: firefly.billing.StatementSummary.spends:type_name -> google.type.Money
	10, // 12: firefly.billing.StatementSummary.interest_charges:type_name -> google.type.Money
	10, // 13: firefly.billing.StatementSummary.fees:type_name -> google.type.Money
	10, // 14: firefly.billing.StatementSummary.repayment_and_refunds:type_name -> google.type.Money
	10, // 15: firefly.billing.StatementSummary.spend_converted_to_emi:type_name -> google.type.Money
	10, // 16: firefly.billing.StatementTransaction.amount:type_name -> google.type.Money
	11, // 17: firefly.billing.StatementTransaction.transaction_timestamp:type_name -> google.protobuf.Timestamp
	12, // 18: firefly.billing.StatementTransaction.transaction_origin:type_name -> firefly.enums.TransactionOrigin
	13, // 19: firefly.billing.StatementTransaction.transaction_transfer_type:type_name -> api.typesv2.TransactionTransferType
	5,  // 20: firefly.billing.FeeBreakDown.fee_break_down_components:type_name -> firefly.billing.FeeBreakDownComponents
	10, // 21: firefly.billing.FeeBreakDownComponents.amount:type_name -> google.type.Money
	10, // 22: firefly.billing.EmiSummary.due_amount:type_name -> google.type.Money
	8,  // 23: firefly.billing.EmiSummary.emi_details:type_name -> firefly.billing.EmiDetail
	10, // 24: firefly.billing.EmiDetail.amount:type_name -> google.type.Money
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_api_firefly_billing_statement_proto_init() }
func file_api_firefly_billing_statement_proto_init() {
	if File_api_firefly_billing_statement_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_firefly_billing_statement_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Statement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_statement_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatementSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_statement_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatementUserDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_statement_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatementTransaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_statement_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeeBreakDown); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_statement_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeeBreakDownComponents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_statement_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtraRewardsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_statement_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmiSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_billing_statement_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmiDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_billing_statement_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_firefly_billing_statement_proto_goTypes,
		DependencyIndexes: file_api_firefly_billing_statement_proto_depIdxs,
		MessageInfos:      file_api_firefly_billing_statement_proto_msgTypes,
	}.Build()
	File_api_firefly_billing_statement_proto = out.File
	file_api_firefly_billing_statement_proto_rawDesc = nil
	file_api_firefly_billing_statement_proto_goTypes = nil
	file_api_firefly_billing_statement_proto_depIdxs = nil
}
