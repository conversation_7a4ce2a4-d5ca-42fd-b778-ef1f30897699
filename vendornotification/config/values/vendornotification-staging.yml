Application:
  Environment: "staging"
  Name: "vendornotification"

KarzaEPANWhitelist:
  EnableWhitelist: false

KarzaVkycWhitelist:
  EnableWhitelist: false

AclSmsWhitelist:
  EnableWhitelist: false

KaleyraSmsWhitelist:
  EnableWhitelist: false

AclWhatsappWhitelist:
  EnableWhitelist: false

GupshupWhitelist:
  EnableWhitelist: false

NetCoreWhitelist:
  EnableWhitelist: false

PaisabazaarWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "***********,***************,*************"
  SoftBlock: false

SetuWhiteList:
  EnableWhitelist: false

DPandaWhitelist:
  EnableWhitelist: false

PoshVineWhitelist:
  EnableWhitelist: false

OzonetelWhitelist:
  EnableWhitelist: false

TssWhitelist:
  EnableWhitelist: false

FederalWhitelist:
  EnableWhitelist: false

M2PWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,************,************"
  SoftBlock: true

MoneyviewWhiteList:
  EnableWhitelist: true
  WhitelistedIPs: "***********,*************"
  SoftBlock: true

UpdateTransactionEventsPublisher:
  QueueName: "staging-payment-callback-update-queue"

InboundTxnPublisher:
  QueueName: "staging-inbound-txn-queue"

InboundUpiTxnPublisher:
  QueueName: "staging-inbound-upi-txn-queue"

InboundLoanTxnPublisher:
  QueueName: "staging-loan-inbound-transaction-queue"

CreateCardCallbackPublisher:
  QueueName: "staging-card-creation-callback-queue"

DispatchPhysicalCardCallbackPublisher:
  QueueName: "staging-card-dispatch-request-callback-queue"

CheckLivenessCallbackPublisher:
  QueueName: "staging-check-liveness-callback-queue"

UPIReqAuthEventPublisher:
  QueueName: "staging-upi-req-auth-processing-queue"

UPIReqAuthMandateEventPublisher:
  QueueName: "staging-upi-req-auth-mandate-processing-queue"

UPIReqAuthValCustEventPublisher:
  QueueName: "staging-upi-req-auth-val-cust-processing-queue"

UPIReqMandateConfirmationEventPublisher:
  QueueName: "staging-upi-req-mandate-confirmation-processing-queue"

UPIRespMandateEventPublisher:
  QueueName: "staging-upi-resp-mandate-processing-queue"

UPIRespPayEventPublisher:
  QueueName: "staging-upi-resp-pay-processing-queue"

UPIReqTxnConfirmationEventPublisher:
  QueueName: "staging-upi-req-txn-confirmation-processing-queue"

UPIReqValAddressEventPublisher:
  QueueName: "staging-upi-req-val-address-processing-queue"

UPIListPspKeysEventPublisher:
  QueueName: "staging-list-psp-keys-processing-queue"

UPIListVaePublisher:
  QueueName: "staging-list-vae-processing-queue"
  BucketName: "epifi-staging-extended-sqs"

CreateDepositCallbackPublisher:
  QueueName: "staging-create-deposit-callback-queue"

PreCloseDepositCallbackPublisher:
  QueueName: "staging-preclose-deposit-callback-queue"

FdAutoRenewCallbackPublisher:
  QueueName: "staging-deposit-maturity-action-callback-queue"

AclSmsCallbackPublisher:
  QueueName: "staging-vn-acl-sms-callback-queue"

KaleyraSmsCallbackPublisher:
  QueueName: "staging-vn-kaleyra-sms-callback-queue"

AclWhatsappCallbackPublisher:
  QueueName: "staging-vn-acl-whatsapp-callback-queue"

AclWhatsappReplyPublisher:
  QueueName: "staging-vn-acl-whatsapp-reply-queue"

GupshupWhatsappCallbackPublisher:
  QueueName: "staging-comms-gupshup-whatsapp-callback-queue"

GupshupRcsCallbackPublisher:
  QueueName: "staging-comms-gupshup-rcs-callback-queue"

NetCoreSmsCallbackPublisher:
  QueueName: "staging-comms-netcore-sms-callback-queue"

AirtelSmsCallbackPublisher:
  QueueName: "staging-comms-airtel-sms-callback-queue"

AirtelWhatsappCallbackPublisher:
  QueueName: "staging-comms-airtel-whatsapp-callback-queue"

DeviceReRegCallbackPublisher:
  QueueName: "staging-device-rereg-callback-queue"

DeviceRegSMSAckPublisher:
  QueueName: "staging-device-reg-sms-ack-queue"

CustomerCreationCallbackPublisher:
  QueueName: "staging-customer-creation-callback-queue"

BankCustCallbackPublisher:
  QueueName: "staging-bankcust-customer-creation-callback-queue"

AccountCreationCallbackPublisher:
  QueueName: "staging-savings-creation-callback-queue"

FederalVkycUpdatePublisher:
  QueueName: "staging-vn-federal-vkyc-update-queue"

UpdateShippingAddressCallbackPublisher:
  QueueName: "staging-shipping-address-update-callback-queue"

KarzaVkycAgentResponsePublisher:
  QueueName: "staging-vn-karza-vkyc-agent-response-queue"

KarzaVkycAuditorResponsePublisher:
  QueueName: "staging-vn-karza-vkyc-auditor-response-queue"

KarzaVkycCallEventPublisher:
  QueueName: "staging-vn-karza-vkyc-call-event-queue"

EmailCallbackPublisher:
  QueueName: "staging-vn-email-callback-queue"

ConsentCallbackPublisher:
  QueueName: "staging-vn-aa-consent-callback-queue"

FICallbackPublisher:
  QueueName: "staging-vn-aa-fi-callback-queue"

CardTrackingCallbackPublisher:
  QueueName: "staging-card-tracking-callback-queue"

AccountLinkStatusCallbackPublisher:
  QueueName: "staging-vn-aa-account-link-status-callback-queue"

UPIReqTxnConfirmationComplaintEventPublisher:
  QueueName: "staging-upi-req-txn-confirmation-complaint-processing-queue"

OzonetelCallDetailsPublisher:
  QueueName: "staging-vn-ozonetel-call-details-queue"

UPIRespComplaintEventPublisher:
  QueueName: "staging-upi-resp-complaint-queue"

FreshchatActionCallbackPublisher:
  QueueName: "staging-vn-freshchat-action-callback-queue"

TssWebhookCallBackPublisher:
  QueueName: "staging-tss-webhook-callback-queue"

HealthInsurancePolicyIssuanceEventPublisher:
  QueueName: "staging-salaryprogram-healthinsurance-policy-issuance-completion-queue"

CCNonFinancialNotificationPublisher:
  QueueName: "staging-cc-non-financial-notification-queue"

SignalWorkflowPublisher:
  QueueName: "staging-celestial-signal-workflow-queue"

SmallcaseProcessMFHoldingsWebhookPublisher:
  QueueName: "staging-vn-process-mf-holdings-webhook-extended-queue"
  BucketName: "epifi-wealth-staging-extended-sqs"

UPIReqMapperConfirmationEventPublisher:
  QueueName: "staging-upi-req-mapper-confirmation-processing-queue"

LoansFiftyfinCallbackPublisher:
  QueueName: "staging-vn-loans-fiftyfin-callback-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "staging-celestial-initiate-procrastinator-workflow-queue"

FederalEscalationUpdateEventPublisher:
  QueueName : "staging-cx-escalation-update-queue"

CcOnboardingStateUpdateEventPublisher:
  QueueName: "staging-cc-onboarding-state-update-event-callback-queue"

VendorRewardFulfillmentPublisher:
  QueueName: "staging-vendor-reward-fulfillment-event-queue"

Server:
  Ports:
    GrpcPort: 8088
    GrpcSecurePort: 9524
    HttpPort: 9999
    HttpPProfPort: 9990

Aws:
  Region: "ap-south-1"

#json file path
PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"
CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"

SyncRespHandler:
  SyncPublisher:
    Publisher:
      QueueName: "staging-vn-sync-wrapper-queue"

Flags:
  TrimDebugMessageFromStatus: false

Secrets:
  Ids:
    #Federal
    SenderCode: "staging/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "staging/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "staging/vg-vn-vgpci/federal-auth-service-access-code"
    SenderCodeLoans: "staging/vg-vn-vgpci/federal-auth-service-access-code"
    AaVgVnSecretsV1: "staging/vg-vn/aa-secrets-v1"
    SahamatiPublicKeyJwk: "staging/vn/sahamati-public-key"
    SenseforthAuthApiKey: "staging/vn/senseforth-auth-api-key"
    SprinklrAuthApiKey: "staging/vg-vn/sprinklr-auth-api-key"
    DpandaAuthApiKey: "staging/vn/dpanda-auth-api-key"
    PoshvineAuthApiKey: "staging/vn/poshvine-auth-api-key"
    RazorpayAuthApiKey: "staging/vn/razorpay-auth-api-key"
    DpandaVnSecrets: "staging/vn/dpanda-secrets"
    PoshvineVnSecrets: "staging/vn/poshvine-secrets"
    RazorpayVnSecrets: "staging/vn/razorpay-secrets"

SavenRewardVnSecrets:
  Path: "staging/vn/saven-rewards-secrets"

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendornotification/secure.log"
  MaxSizeInMBs: 50
  MaxBackups: 20

FeatureFlags:
  AllowCustomerCallbackProcessing: true
  AllowAccountCallbackProcessing: true

AA:
  AaSecretsVersionToUse: "V1"
  EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
  FinvuCrId: "<EMAIL>"
  IPWhiteListing:
    EnableWhitelist: false
    SoftBlock: false
  OneMoneyCrId: "onemoney-aa"
  TokenIssuer: "epifi-simulator"
  VerifyApiKeyAndJws: false

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CCTransactionNotificationPublisher:
  QueueName: "staging-cc-txn-notification-queue"

CCAcsNotificationPublisher:
  QueueName: "staging-cc-acs-notification-queue"

Ozonetel:
  IsPriorityRoutingEnabled: true

CCStatementNotificationPublisher:
  QueueName: "staging-cc-statement-notification-queue"

CardSwitchFinancialNotificationPublisher:
  QueueName: "staging-card-switch-financial-notification-queue"

CardSwitchNonFinancialNotificationPublisher:
  QueueName: "staging-card-switch-non-financial-notification-queue"

AccountStatusCallbackPublisher:
  QueueName: "staging-account-status-callback-queue"

EnachRegistrationAuthorisationCallbackPublisher:
  QueueName: "staging-recurringpayment-creation-auth-vendor-callback-queue"

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: vendor-notification-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

KycStatusUpdatePublisher:
  QueueName: "staging-kyc-v2-update-status-queue"

CcSwitchNotificationsBucketName: "epifi-staging-cc-switch-notifications"
CcRawSwitchNotificationsBucketName: "epifi-raw-dev"
EpanCallbackBucketName: "epifi-staging-pan"
M2pFederalSwitchNotificationFilePath: "m2p/federal/%s/%s-switchTxnNotifications.csv"
RawBucketM2pFederalSwitchNotificationFilePath: "staging/data/vendor/federal_cc_reports/switch_transaction_notifications/%s/%s-switchTxnNotifications.csv"

CredgenicsCallbackStreamProducer:
  EmailStream:
    StreamName: "staging-credgenics-webhook-generic-event-publish-stream"
  SmsStream:
    StreamName: "staging-credgenics-webhook-generic-event-publish-stream"
  WhatsappStream:
    StreamName: "staging-credgenics-webhook-generic-event-publish-stream"
  CallingStream:
    StreamName: "staging-credgenics-webhook-calling-event-publish-stream"
  VoiceMessageStream:
    StreamName: "staging-credgenics-webhook-generic-event-publish-stream"

FederalBankCustKycStateChangePublisher:
  QueueName: "staging-bankcust-kyc-state-change-event-consumer-queue"

FederalResidentialStatusUpdatePublisher:
  QueueName: "staging-bank-customer-residential-status-update-consumer-queue"

FederalMobileNumberUpdatePublisher:
  QueueName: "staging-bank-customer-mobile-number-update-consumer-queue"

PgRazorpayInboundEventPublisher:
  QueueName: "staging-pg-razorpay-inbound-event-queue"

Auth:
  JwtEncryption:
    RSAPrivateKeyPEMPath: "staging/vn/auth/jwt-encryption-private-key"
  ClientCredentials:
    IdToSecretPath: "staging/vn/auth/client-credentials"

Nugget:
  NuggetAccountFreezeDummyDetails:
    U1:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: NO_FREEZE_CODE
      FreezeType: FREEZE_STATUS_UNSPECIFIED
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U2:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: NO_FREEZE_CODE
      FreezeType: ACCOUNT_FREEZE_STATUS_UNFROZEN
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U3:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: '[{"id":"***********","date":"2024-11-01T00:00:00Z","contact":{"grievanceOfficerDetails":{"name":"Sh Anup Kuruvilla John, IPS,ADGP","email":"<EMAIL>","rawData":"Sh Anup Kuruvilla John, IPS,ADGP,0471-2300042,<EMAIL>","phoneNumber":"0471-2300042"},"nodalCyberCellOfficerDetails":{"name":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT)","email":"<EMAIL>","rawData":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT),<EMAIL>"}},"state":"Kerala"}]'
    U4:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U5:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U6:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: '[{"id":"***********","date":"2024-11-01T00:00:00Z","contact":{"grievanceOfficerDetails":{"name":"Sh Anup Kuruvilla John, IPS,ADGP","email":"<EMAIL>","rawData":"Sh Anup Kuruvilla John, IPS,ADGP,0471-2300042,<EMAIL>","phoneNumber":"0471-2300042"},"nodalCyberCellOfficerDetails":{"name":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT)","email":"<EMAIL>","rawData":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT),<EMAIL>"}},"state":"Kerala"}]'
    U7:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U8:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U9:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U10:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U11:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U12:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U13:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U14:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U15:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U16:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U17:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U18:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U19:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U20:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U21:
      AccountStatus: OPERATIONAL_STATUS_DORMANT
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U22:
      AccountStatus: OPERATIONAL_STATUS_DORMANT
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U23:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "FORM123"
      FormStatus: STATUS_CREATED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "-"
    U24:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "FORM124"
      FormStatus: STATUS_SUBMITTED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "-"
    U25:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "FORM125"
      FormStatus: STATUS_CANCELLED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "re"
  NuggetTransactionDummyDetails:
    T1:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: DEBIT_CARD_CHARGES
      TransactionAmount: 500.00
      TransactionStatus: SUCCESS
    T2:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: ECS_ENACH_CHARGES
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
    T3:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: AMB_CHARGE
      TransactionAmount: 100.46
      TransactionStatus: SUCCESS
    T4:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: ANYWHERE_BANKING_CHARGE
      TransactionAmount: 4.37
      TransactionStatus: SUCCESS
    T5:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: DC_DCC_FEE_CHARGE
      TransactionAmount: ********.25
      TransactionStatus: SUCCESS
    T6:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: DEBIT_CARD_AMC_CHARGE
      TransactionAmount: 90.67
      TransactionStatus: SUCCESS
    T7:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: DC_FOREX_MARKUP_CHARGE
      TransactionAmount: 111.00
      TransactionStatus: SUCCESS
    T8:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: DC_TCS_FEE_CHARGE
      TransactionAmount: 123.00
      TransactionStatus: SUCCESS
    T9:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: FIRST_CARD_ORDER_FEE
      TransactionAmount: 1234.00
      TransactionStatus: SUCCESS
    T10:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: CHEQUE_BOOK_CHARGES
      TransactionAmount: 12345.00
      TransactionStatus: SUCCESS
    T11:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: ECOM_POS_DECLINE_CHARGE
      TransactionAmount: 98.24
      TransactionStatus: SUCCESS
    T12:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: ATM_DECLINE_CHARGE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
    T13:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: OTHER_BANK_ATM_CHARGE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
    T14:
      CreatedAt: 7-11-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-11-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: DUPLICATE_CARD_FEE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
    T15:
      CreatedAt: 7-25-2025
      ErrorCode: ""
      ExecutedAt: 7-25-2025
      P2P_P2M: P2P
      PaymentProtocol: VENDOR_BANK
      Provenance: INTRA_BANK
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
    T16:
      CreatedAt: 7-16-2025
      ErrorCode: ""
      ExecutedAt: 7-16-2025
      P2P_P2M: P2P
      PaymentProtocol: EXTERNAL
      Provenance: UPI
      Tags: ""
      TransactionAmount: 1.00
      TransactionStatus: SUCCESS
    T17:
      CreatedAt: 7-29-2025
      ErrorCode: ""
      ExecutedAt: 7-29-2025
      P2P_P2M: P2P
      PaymentProtocol: POS
      Provenance: CARD
      Tags: ""
      TransactionAmount: 2.00
      TransactionStatus: SUCCESS
    T18:
      CreatedAt: 7-3-2025
      ErrorCode: ""
      ExecutedAt: 7-3-2025
      P2P_P2M: P2P
      PaymentProtocol: ATM
      Provenance: CARD
      Tags: ""
      TransactionAmount: 3.00
      TransactionStatus: SUCCESS
    T19:
      CreatedAt: 4-1-2025
      ErrorCode: SUCCESS
      ExecutedAt: 4-1-2025
      P2P_P2M: P2P
      PaymentProtocol: USER_APP
      Provenance: INTRA_BANK
      Tags: ""
      TransactionAmount: 4.00
      TransactionStatus: SUCCESS
    T20:
      CreatedAt: 7-25-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-25-2025
      P2P_P2M: P2P
      PaymentProtocol: ECOMM
      Provenance: CARD
      Tags: ""
      TransactionAmount: 5.00
      TransactionStatus: SUCCESS
    T21:
      CreatedAt: 7-25-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-25-2025
      P2P_P2M: P2P
      PaymentProtocol: USER_APP
      Provenance: RTGS
      Tags: ""
      TransactionAmount: 6.00
      TransactionStatus: SUCCESS
    T22:
      CreatedAt: 7-25-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-25-2025
      P2P_P2M: P2P
      PaymentProtocol: USER_APP
      Provenance: INTRA_BANK
      Tags: ""
      TransactionAmount: 7.00
      TransactionStatus: SUCCESS
    T23:
      CreatedAt: 7-25-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-25-2025
      P2P_P2M: P2P
      PaymentProtocol: USER_APP
      Provenance: UPI
      Tags: ""
      TransactionAmount: 10.00
      TransactionStatus: SUCCESS
    T24:
      CreatedAt: 7-25-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-25-2025
      P2P_P2M: P2P
      PaymentProtocol: POS
      Provenance: CARD
      Tags: ""
      TransactionAmount: 200.00
      TransactionStatus: SUCCESS
    T25:
      CreatedAt: 7-25-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-25-2025
      P2P_P2M: P2P
      PaymentProtocol: USER_APP
      Provenance: NEFT
      Tags: ""
      TransactionAmount: 20000.00
      TransactionStatus: SUCCESS
    T26:
      CreatedAt: 7-25-2025
      ErrorCode: SUCCESS
      ExecutedAt: 7-25-2025
      P2P_P2M: P2P
      PaymentProtocol: USER_APP
      Provenance: IMPS
      Tags: ""
      TransactionAmount: 100000.00
      TransactionStatus: SUCCESS
    T27:
      CreatedAt: 7-25-2025
      ErrorCode: FI_POS_26
      ExecutedAt: ""
      P2P_P2M: P2M
      PaymentProtocol: POS
      Provenance: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
    T28:
      CreatedAt: 7-25-2025
      ErrorCode: FI_ATM_7
      ExecutedAt: ""
      P2P_P2M: P2M
      PaymentProtocol: ATM
      Provenance: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
    T29:
      CreatedAt: 7-25-2025
      ErrorCode: UPI1215
      ExecutedAt: ""
      P2P_P2M: P2P
      PaymentProtocol: EXTERNAL
      Provenance: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
    T30:
      CreatedAt: 7-25-2025
      ErrorCode: FI_POS_6
      ExecutedAt: ""
      P2P_P2M: P2M
      PaymentProtocol: POS
      Provenance: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
    T31:
      CreatedAt: 7-25-2025
      ErrorCode: UPI130
      ExecutedAt: ""
      P2P_P2M: P2M
      PaymentProtocol: USER_APP
      Provenance: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
    T32:
      CreatedAt: 7-25-2025
      ErrorCode: UPI129
      ExecutedAt: ""
      P2P_P2M: P2P
      PaymentProtocol: USER_APP
      Provenance: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
    T33:
      CreatedAt: 7-25-2025
      ErrorCode: UPI1095
      ExecutedAt: ""
      P2P_P2M: P2P
      PaymentProtocol: USER_APP
      Provenance: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
