package queue

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/enums"
	gammanames "github.com/epifi/gamma/pkg/names"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	json "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/retry"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accountsPb "github.com/epifi/gamma/api/accounts"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	bcPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/docs"
	kycPb "github.com/epifi/gamma/api/kyc"
	pb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	vgauthPb "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	ovgSavingsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	addressPkg "github.com/epifi/gamma/pkg/address"
	"github.com/epifi/gamma/pkg/device"
	"github.com/epifi/gamma/pkg/vendorstore"
	"github.com/epifi/gamma/savings/config/genconf"
	savingsDao "github.com/epifi/gamma/savings/dao"
	savingsEvents "github.com/epifi/gamma/savings/events"
	savingsSvc "github.com/epifi/gamma/savings/service"
	wireTypes "github.com/epifi/gamma/savings/wire/types"
)

// SavingsConsumerService contains methods to consume account creation queue message. Contains logic to retry account
// creation intelligently in accordance with the savings_accounts state machine.
// DB states are considered as the source of truth throughout.
// information in sqsMessage Body is only used to create req structs
// the account will go through following 5 states during account creation:
// INITIATED - account creation req has been registered and enqueued in a queue
// IN_PROGRESS- at least one attempt was made at calling the vendor for creating a customer. The status needs to be
// polled. Requests can be in this state -
// 1. after successful acknowledgement of receiving the request by a vendor in case the APIs are async.
// In this case status will be polled till we get a terminal status back.
// 2. after seeing transient errors e.g. vendor systems are down or the vendor call timed out.
// In this case status will be polled and appropriate retries will be done if required.
// CREATED - account creation is successful at vendor's end
// FAILED - customer creation has failed due to permanent failure at vendor's end.
// MANUAL_INTERVENTION- System has exhausted all the retries post transient errors so this needs attention from a human.
type SavingsConsumerService struct {
	dynConf       *genconf.Config
	dao           savingsDao.SavingsDao
	savingsClient ovgSavingsPb.SavingsClient
	cardClient    cardPb.CardProvisioningClient
	actorClient   actorPb.ActorClient
	producer      wireTypes.SavingsCreationPublisher
	// TODO(Nitesh): To be removed with retryer logic here
	rtyStrategy                     retry.RetryStrategy
	createVPAPublisher              wireTypes.CreateVPAPublisher
	authClient                      auth.AuthClient
	userClient                      userPb.UsersClient
	createSavingsAccountPIPublisher wireTypes.SavingsAccountPICreationPublisher
	// To broadcast savings account savings.State change
	// Starting with, will be published only when an account is created.
	// Later on, other states will be added.
	accountStatePub     wireTypes.AccountStatePublisher
	EventBroker         events.Broker
	VendorStore         vendorstore.VendorStore
	afPurchasePublisher wireTypes.EventAfPurchasePublisher
	bcClient            bcPb.BankCustomerServiceClient
	operStatusClient    operStatusPb.OperationalStatusServiceClient
	commsClient         comms.CommsClient
	docsClient          docs.DocsClient
	tieringClient       tieringPb.TieringClient
	consentClient       consent.ConsentClient
}

const (
	savingsAccountInterestRate = "3.05"
)

// NewSavingsConsumerService is factory method to initialize savings consumer service
func NewSavingsConsumerService(dynConf *genconf.Config, dao savingsDao.SavingsDao, savingsClient ovgSavingsPb.SavingsClient,
	cardClient cardPb.CardProvisioningClient, actorClient actorPb.ActorClient, producer wireTypes.SavingsCreationPublisher,
	rtyStrategy retry.RetryStrategy, createVPAPublisher wireTypes.CreateVPAPublisher, authClient auth.AuthClient,
	userClient userPb.UsersClient, createSavingsAccountPIPublisher wireTypes.SavingsAccountPICreationPublisher,
	accountStatePub wireTypes.AccountStatePublisher, eventBroker events.Broker, vendorStore vendorstore.VendorStore,
	afPurchasePublisher wireTypes.EventAfPurchasePublisher, bcClient bcPb.BankCustomerServiceClient,
	operStatusClient operStatusPb.OperationalStatusServiceClient, commsClient comms.CommsClient, docsClient docs.DocsClient,
	tieringClient tieringPb.TieringClient, consentClient consent.ConsentClient) *SavingsConsumerService {
	return &SavingsConsumerService{dynConf: dynConf, dao: dao, savingsClient: savingsClient, cardClient: cardClient, actorClient: actorClient, producer: producer, rtyStrategy: rtyStrategy, createVPAPublisher: createVPAPublisher, authClient: authClient, userClient: userClient,
		createSavingsAccountPIPublisher: createSavingsAccountPIPublisher, accountStatePub: accountStatePub, EventBroker: eventBroker, VendorStore: vendorStore, afPurchasePublisher: afPurchasePublisher, bcClient: bcClient, operStatusClient: operStatusClient, commsClient: commsClient,
		docsClient: docsClient, tieringClient: tieringClient, consentClient: consentClient}
}

// IsRetryableRPC evaluates whether a given rpc code is retryable or not
func IsRetryableRPC(status *rpc.Status) bool {
	switch status.Code {
	case rpc.StatusInternal().Code:
		return true
	default:
		return false
	}
}

// preProcessor contains set of common operations to be performed before processing any sqs message
// e.g. fetch record from DB, check for duplicate packet for idempotent system, check for max retry threshold.
func (ss *SavingsConsumerService) preProcessor(ctx context.Context, acctId string) (*pb.Account, *pb.QueueRetryInfo, error) {
	acct, retryInfo, err := ss.dao.GetAccountWithRetryInfo(ctx, acctId)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			logger.Debug(ctx, fmt.Sprintf("record not found for acctId : %v", acctId))
			return nil, nil, queue.ErrRecordNotFound
		} else {
			return nil, nil, queue.ErrDB
		}
	}

	// If savings accounts is already in created or failed state then the packet needs no processing.
	if acct.State == pb.State_CREATED || acct.State == pb.State_FAILED {
		logger.Info(ctx, fmt.Sprintf("duplicate message received, account creation is in: %v for account: %v",
			acct.State, acct.Id))
		return acct, nil, queue.ErrDuplicateMessage
	}

	// check for max threshold
	if ss.rtyStrategy.IsMaxRetryMet(uint(retryInfo.Attempts)) {
		logger.Info(ctx, fmt.Sprintf("max retry attempts crossed. Marking status as MANUAL_INTERVENTION for account: %v",
			acct.Id))

		switch acct.State {
		case pb.State_INITIATED:
			acct.State = pb.State_MAX_RETRIES_CREATE_ACCOUNT
		case pb.State_IN_PROGRESS:
			acct.State = pb.State_MAX_RETRIES_CHECK_STATUS
		default:
			logger.Info(ctx, fmt.Sprintf("unexpected db status when max retry limit reached, %v",
				acct.GetState().String()), zap.String(logger.ENTITY_ID, acct.GetPrimaryAccountHolder()))
			return nil, nil, queue.ErrMaxRetry
		}

		err := ss.dao.UpdateAccount(ctx, acct, []pb.AccountFieldMask{pb.AccountFieldMask_STATE})
		if err != nil {
			logger.Error(ctx, fmt.Sprintf(
				"error while updating account status in DB for acctId: %v", acct.Id), zap.Error(err))
			return nil, nil, queue.ErrDB
		}
		return nil, nil, queue.ErrMaxRetry
	}

	return acct, retryInfo, nil
}

// postProcessor contains a set of operations to be performed on completion of a state.
// e.g. add packet to the queue, send sms, notifications, etc.
func (ss *SavingsConsumerService) postProcessor(ctx context.Context, state pb.State, acct *pb.Account, retryInfo *pb.QueueRetryInfo) error {
	switch state {
	case pb.State_INITIATED:
		req := &pb.ProcessAccountCreationRequest{AccountId: acct.Id}
		sqsMsgId, err := ss.producer.PublishWithDelay(ctx, req, ss.dynConf.AccountCreationEnquiryDelay())
		if err != nil {
			logger.Error(ctx, "unable to add message to the queue", zap.Error(err))
			return queue.ErrPublish
		}
		retryInfo.QueueMsgId = sqsMsgId
		err = ss.dao.UpdateAccountAndQRetryInfo(ctx, acct, retryInfo, []pb.AccountFieldMask{pb.AccountFieldMask_QUEUE_RETRY_INFO})
		if err != nil {
			logger.Warn(fmt.Sprintf("db state update failed for account: %v", acct.Id), zap.Error(err))
		}
	default:
		return fmt.Errorf("post processor unimplemented for account state: %v", state)
	}
	return nil
}

// incrementRetryCounterAndUpdate, increments the retry counter, updates it in DB
// nolint:funlen
func (ss *SavingsConsumerService) incrementRetryCounterAndUpdate(ctx context.Context, acct *pb.Account, retryInfo *pb.QueueRetryInfo) {
	logger.Info(ctx, "failed attempt incrementing retry counter")
	retryInfo.Attempts++
	// ignoring the error here, as if the DB persistence fails then
	// we are anyway going to retry
	err := ss.dao.UpdateAccountAndQRetryInfo(ctx, acct, retryInfo, []pb.AccountFieldMask{pb.AccountFieldMask_QUEUE_RETRY_INFO})
	if err != nil {
		logger.Warn("failed to increment the attempts counter in DB", zap.Error(err))
	}
}

func (ss *SavingsConsumerService) createSavingsRequest(ctx context.Context, acct *pb.Account, retryInfo *pb.QueueRetryInfo, bankCustId []string) (*ovgSavingsPb.CreateAccountRequest, string, error) {
	// Get Device Auth information
	actorRes, err := ss.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: acct.GetPrimaryAccountHolder(),
	})
	if err != nil {
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		logger.Error(ctx, "Failed to fetch actor", zap.String(logger.USER_ID, acct.GetPrimaryAccountHolder()), zap.Error(err))
		return nil, "", queue.ErrTransient
	}

	deviceDetails, err := ss.authClient.GetDeviceAuth(ctx,
		&auth.GetDeviceAuthRequest{ActorId: actorRes.Actor.Id})
	if err = epifigrpc.RPCError(deviceDetails, err); err != nil {
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		logger.Error(ctx, "Failed to fetch actor: ", zap.String(logger.USER_ID, acct.GetPrimaryAccountHolder()), zap.Error(err))
		return nil, "", queue.ErrTransient
	}
	if len(deviceDetails.GetDeviceToken()) == 0 {
		logger.Error(ctx, "empty device token, device registration not successful: ",
			zap.String(logger.ACTOR_ID_V2, actorRes.GetActor().GetId()))
		return nil, "", queue.ErrPermanent
	}
	deviceDet := &vgauthPb.Auth{
		DeviceId:      deviceDetails.Device.DeviceId,
		DeviceToken:   deviceDetails.GetDeviceToken(),
		EncryptedPin:  "",
		UserProfileId: deviceDetails.GetUserProfileId(),
	}

	actorId := actorRes.GetActor().Id
	nominee, err := ss.getNominee(ctx, actorId, acct.PartnerBank)
	if err != nil {
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		logger.Error(ctx, "failed to get nominee info to form savings account creation request", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, "", queue.ErrTransient
	}

	userRes, err := ss.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: acct.GetPrimaryAccountHolder(),
		},
	})
	if err = epifigrpc.RPCError(userRes, err); err != nil {
		logger.Error(ctx, "error in get user in create savings acct", zap.Error(err))
		return nil, "", err
	}
	// if the consent exists then it won't be used for scholarship
	scholarshipFlag := false
	consentRes, err := ss.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_SCHOLARSHIP,
		ActorId:     actorId,
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(consentRes, err); err != nil && !consentRes.GetStatus().IsRecordNotFound() {
		logger.Info(ctx, "failed to fetch scholarship consent  from consent service", zap.Error(err))
	}
	if consentRes.GetStatus().IsRecordNotFound() {
		scholarshipFlag = true
	}
	// if the consent exists then it won't be used for dbt
	dbtFlag := false
	consentRes, err = ss.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_DIRECT_BENEFIT_TRANSFER,
		ActorId:     actorId,
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(consentRes, err); err != nil && !consentRes.GetStatus().IsRecordNotFound() {
		logger.Info(ctx, "failed to fetch dbt consent from consent service", zap.Error(err))
	}
	if consentRes.GetStatus().IsRecordNotFound() {
		dbtFlag = true
	}

	bcResp, errResp := ss.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if er := epifigrpc.RPCError(bcResp, errResp); er != nil {
		logger.Error(ctx, "customer details not found for user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, "", queue.ErrTransient
	}
	bankCust := bcResp.GetBankCustomer()
	if bankCust.GetVendorCustomerId() == "" {
		logger.Error(ctx, "customer id not found for acc creation", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, "", queue.ErrTransient
	}
	if len(bankCustId) > 0 && bankCustId[0] == "" {
		bankCustId[0] = bcResp.GetBankCustomer().GetVendorCustomerId()
	}
	if len(bankCustId) == 0 {
		bankCustId = append(bankCustId, bcResp.GetBankCustomer().GetVendorCustomerId())
	}
	customerName := getCustomerName(userRes.GetUser(), bcResp.GetBankCustomer().GetName())
	kycLevel, err := ss.getAccountCompletionLevel(ctx, bankCust)
	if err != nil {
		return nil, "", err
	}
	// call account creation API
	req := &ovgSavingsPb.CreateAccountRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: acct.PartnerBank,
		},
		RequestId:      retryInfo.RequestId,
		BankCustomerId: bankCustId,
		AccountNo:      acct.AccountNo,
		DeviceDetails:  deviceDet,
		MobileNo:       acct.GetPhoneNumber().ToString(),
		EmailId:        acct.GetEmailId(),
		CustomerName:   customerName.ToString(),
		Nominee:        nominee,
		KycLevel:       kycLevel, // TODO(aditya): remove usage of kyc level
		VendorSku:      savingsSvc.ExtractVendorSKU(ctx, acct.SkuInfo, acct.PartnerBank),
		SolId:          bankCust.GetVendorMetadata().GetFederalMetadata().GetSolId(),
		SourceOfFunds:  userRes.GetUser().GetProfile().GetSourceOfFunds(),
		AnnualTxnVolume: &userPb.SalaryRange{
			MinValue: userRes.GetUser().GetProfile().GetAnnualTransactionVolume().GetMinValue(),
			MaxValue: userRes.GetUser().GetProfile().GetAnnualTransactionVolume().GetMaxValue(),
		},
		PurposeOfSavingsAccount: userRes.GetUser().GetProfile().GetPurposeOfSavingsAccount(),
		ScholarshipFlag:         scholarshipFlag,
		DbtFlag:                 dbtFlag,
	}
	return req, actorRes.Actor.Id, nil
}

func (ss *SavingsConsumerService) getAccountCompletionLevel(ctx context.Context, bankCust *bcPb.BankCustomer) (ovgSavingsPb.KYCLevel, error) {
	switch bankCust.GetDedupeInfo().GetOriginalKycLevelWithVendor() {
	// 1. Dedupe customer, full kyc - full account
	case kycPb.KYCLevel_FULL_KYC:
		return ovgSavingsPb.KYCLevel_FULL_KYC, nil
	// 2. Dedupe customer, min kyc - kyc min/full
	// 3. If not dedupe customer -  kyc min/full
	case kycPb.KYCLevel_MIN_KYC, kycPb.KYCLevel_UNSPECIFIED:
		switch bankCust.GetDedupeInfo().GetKycLevel() {
		case kycPb.KYCLevel_FULL_KYC:
			return ovgSavingsPb.KYCLevel_FULL_KYC, nil
		case kycPb.KYCLevel_MIN_KYC:
			return ovgSavingsPb.KYCLevel_MIN_KYC, nil
		default:
			logger.Error(ctx, "kyc record has kyc completion level unspecified", zap.String(logger.USER_ID, bankCust.GetUserId()))
			return ovgSavingsPb.KYCLevel_UNSPECIFIED, fmt.Errorf("kyc record has kyc completion level unspecified")
		}
	default:
		logger.Error(ctx, fmt.Sprintf("unknown original kyc level with vendor %v", bankCust.GetDedupeInfo().GetOriginalKycLevelWithVendor()), zap.String(logger.USER_ID, bankCust.GetUserId()))
		return ovgSavingsPb.KYCLevel_UNSPECIFIED, fmt.Errorf("unknown original kyc level with vendor %v", bankCust.GetDedupeInfo().GetOriginalKycLevelWithVendor())
	}
}

// openAccount calls vendor gateway rpc method to open account with vendor.
func (ss *SavingsConsumerService) openAccount(ctx context.Context, acct *pb.Account, retryInfo *pb.QueueRetryInfo, bankCustId []string) error {
	// TODO(nitesh): make a check status call before making another request or check for duplicate create account call
	attemptNo := uint(retryInfo.Attempts) + 1
	req, actorId, err := ss.createSavingsRequest(ctx, acct, retryInfo, bankCustId)
	if err != nil {
		// not handling/emitting event on last retry since event is emitted in every error scenario
		failureReason := savingsEvents.FailCreateAccountVendorReq + " Error:" + err.Error()
		ss.sendInitSavingsAccountEvents(ctx, actorId, req.GetDeviceDetails().GetDeviceId(), attemptNo, acct.PartnerBank.String(), savingsEvents.Failure, failureReason, retryInfo.RequestId)
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		logger.Error(ctx, "error while creating vg request for savings account creation api:", zap.Error(err))
		return queue.ErrRPC
	}
	res, err := ss.savingsClient.CreateAccount(ctx, req)
	ss.vendorStoreAccCreation(ctx, actorId, res, req)
	switch {
	case err != nil:
		errToReturn := queue.ErrRPC
		errorString := "failure in vg savings account creation rpc "
		logger.Error(ctx, errorString, zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.VENDOR, req.Header.Vendor.String()), zap.Error(err))
		failureReason := savingsEvents.FailCreateAccountRPC + " Error:" + err.Error()
		ss.sendInitSavingsAccountEvents(ctx, actorId, req.GetDeviceDetails().GetDeviceId(), attemptNo, acct.PartnerBank.String(), savingsEvents.Failure, failureReason, retryInfo.RequestId)
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		return errToReturn
	case res.GetStatus().IsDeadlineExceeded():
		// assuming request was registered at vendor, will rely on ProcessInProgressState to push the init packet again on "no details found" at vendor from enquiry response
		return nil
	case res.GetStatus().GetCode() == uint32(ovgSavingsPb.CreateAccountResponse_DUPLICATE_REQUESTID):
		// duplicate request id indicates request already in process at vendor, returning nil to let the enquiry proc do the enquiry
		return nil
	case !res.GetStatus().IsSuccess():
		failureReason := fmt.Sprintf("CreateAccount rpc failed Response: %v", res)
		ss.sendInitSavingsAccountEvents(ctx, actorId, req.GetDeviceDetails().GetDeviceId(), attemptNo, acct.PartnerBank.String(), savingsEvents.Failure, failureReason, retryInfo.RequestId)
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		logger.Info(ctx, fmt.Sprintf("create account failed with response: %v", res))

		if IsRetryableRPC(res.Status) {
			return fmt.Errorf("unexpected response from create account: %v : %w", res, queue.ErrTransient)
		} else {
			// account state as failed
			acct.State = pb.State_FAILED
			err := ss.dao.UpdateAccount(ctx, acct, []pb.AccountFieldMask{pb.AccountFieldMask_STATE})
			if err != nil {
				return fmt.Errorf("create savings account failed to update state info in "+
					"DB with error: %w", queue.ErrDB)
			}
			return fmt.Errorf("unexpected response from create account: %v : %w", res, queue.ErrPermanent)
		}
	}
	ss.sendInitSavingsAccountEvents(ctx, actorId, req.GetDeviceDetails().GetDeviceId(), attemptNo, acct.PartnerBank.String(), savingsEvents.Success, "", retryInfo.RequestId)
	return nil
}

// ProcessAccountCreation process account both in intiated state and inprogress states,
// by redirecting to ProcessInitiatedState and ProcessInProgressState.
// TODO(keerthana): (bug id: 5995) Add tests for this
func (ss *SavingsConsumerService) ProcessAccountCreation(ctx context.Context, req *pb.ProcessAccountCreationRequest) (*pb.ProcessAccountCreationResponse, error) {
	response := &pb.ProcessAccountCreationResponse{}
	header := &queuePb.ConsumerResponseHeader{}
	response.ResponseHeader = header
	logger.Info(ctx, "processing account creation packet", zap.String(logger.ACCOUNT_ID, req.AccountId))
	acct, _, err := ss.preProcessor(ctx, req.AccountId)
	if err != nil {
		if errors.Is(err, queue.ErrDuplicateMessage) &&
			(acct != nil && acct.GetState() == pb.State_CREATED) {
			header.Status = queuePb.MessageConsumptionStatus_SUCCESS
			return response, nil
		}
		header.Status = queue.GetStatusFrom(err)
		if header.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE {
			header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(uint(1)))
		}
		return response, nil
	}

	switch acct.State {
	case pb.State_INITIATED:
		request := &pb.ProcessInitiatedStateRequest{}
		if err = copier.Copy(request, req); err != nil {
			logger.Error(ctx, "failed to copy request ProcessAccountCreationRequest to ProcessInitiatedStateRequest", zap.Error(err))
			response.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
			return response, err
		}
		res, err := ss.ProcessInitiatedState(ctx, request)
		response.ResponseHeader = res.ResponseHeader
		return response, err
	case pb.State_IN_PROGRESS:
		request := &pb.ProcessInProgressStateRequest{}
		if err = copier.Copy(request, req); err != nil {
			logger.Error(ctx, "failed to copy request ProcessAccountCreationRequest to ProcessInProgressStateRequest", zap.Error(err))
			response.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
			return response, err
		}
		res, error := ss.ProcessInProgressState(ctx, request)
		response.ResponseHeader = res.ResponseHeader
		return response, error
	case pb.State_CREATED, pb.State_FAILED:
		// Handling this case because its we have both callback and enquiry check,
		// if callback is received the enquiry retry packet can reach here with success state
		logger.Info(ctx, "account creation has reached terminal state already, ignoring this packet",
			zap.String(logger.STATE, acct.State.String()))
		response.ResponseHeader = &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		}
		return response, nil
	default:
		logger.Error(ctx, "unrecognized account status in queue")
		response.ResponseHeader = &queuePb.ConsumerResponseHeader{}
		response.ResponseHeader.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return response, nil
	}
}

// ProcessInitiatedState process  INITIATED account and moves the state machine to IN_PROGRESS on success.
// nolint:funlen
func (ss *SavingsConsumerService) ProcessInitiatedState(ctx context.Context, req *pb.ProcessInitiatedStateRequest) (*pb.ProcessInitiatedStateResponse, error) {
	res := &pb.ProcessInitiatedStateResponse{}
	header := &queuePb.ConsumerResponseHeader{}
	res.ResponseHeader = header
	attempts := uint(1)
	acct, retryInfo, err := ss.preProcessor(ctx, req.AccountId)
	if err != nil {
		header.Status = queue.GetStatusFrom(err)
		if header.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE {
			header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(attempts))
		}
		return res, nil
	}

	attempts = uint(retryInfo.Attempts) + 1

	// check if request id is pre populated or not. Since, account opening requires same request id that was used to create customer.
	// mark account status as MANUAL_INTERVENTION in case reqId is empty
	if retryInfo.RequestId == "" {
		logger.Info(ctx, "request Id empty for account creation can't proceed. Changing account state to MANUAL_INTERVENTION",
			zap.String(logger.ENTITY_ID, acct.GetPrimaryAccountHolder()))
		acct.State = pb.State_FAILED
		err = ss.dao.UpdateAccountAndQRetryInfo(ctx, acct, retryInfo, []pb.AccountFieldMask{pb.AccountFieldMask_STATE})
		if err != nil {
			header.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
			header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(attempts))
			logger.Error(ctx, "failed to update account info in DB", zap.Error(err))
		} else {
			header.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		}
		return res, nil
	}

	err = ss.openAccount(ctx, acct, retryInfo, req.BankCustomerId)
	if err != nil {
		header.Status = queue.GetStatusFrom(err)
		if header.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE {
			header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(attempts))
		}
		return res, nil
	}

	logger.Info(ctx, "account creation call successful. Changing DB state to IN_PROGRESS")

	// TODO(nitesh): perform the below operation in one transactional block

	acct.State = pb.State_IN_PROGRESS
	retryInfo.Attempts = 0
	if acct.CreationInfo == nil {
		acct.CreationInfo = &pb.AccountCreationInfo{}
	}
	acct.CreationInfo.CreationStartedAt = timestamppb.Now()
	err = ss.dao.UpdateAccountAndQRetryInfo(ctx, acct, retryInfo, []pb.AccountFieldMask{pb.AccountFieldMask_STATE,
		pb.AccountFieldMask_QUEUE_RETRY_INFO, pb.AccountFieldMask_ACCOUNT_CREATION_INFO})
	if err != nil {
		header.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		logger.Warn(fmt.Sprintf("db state update failed for account: %v", acct.Id), zap.Error(err))
		header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(attempts))
		return res, nil
	}

	err = ss.postProcessor(ctx, pb.State_INITIATED, acct, retryInfo)
	if err != nil {
		header.Status = queue.GetStatusFrom(err)
		if header.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE {
			header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(attempts))
		}
		return res, nil
	}

	header.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

// checkStatusFromVendor calls vendor gateway rpc method to check status of account opening.
// nolint:funlen
func (ss *SavingsConsumerService) checkStatusFromVendor(ctx context.Context, acct *pb.Account, retryInfo *pb.QueueRetryInfo) (string, *timestamppb.Timestamp, error) {

	actor, err := ss.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: acct.GetPrimaryAccountHolder(),
	})
	if err != nil {
		logger.Error(ctx, "Failed to fetch actor from entity id: ", zap.Error(err))
		return "", nil, err
	}
	deviceDetails, err := ss.authClient.GetDeviceAuth(ctx,
		&auth.GetDeviceAuthRequest{
			ActorId: actor.GetActor().GetId(),
		})
	if err != nil {
		logger.Error(ctx, "Failed to get device details of user ", zap.Error(err))
		return "", nil, err
	}

	deviceId := deviceDetails.GetDevice().GetDeviceId()
	appVersion, _ := device.GetAppVersion(ctx, actor.GetActor().GetId(), ss.userClient)

	req := &ovgSavingsPb.CheckAccountStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: acct.PartnerBank,
		},
		RequestId:   retryInfo.RequestId,
		PhoneNumber: acct.GetPhoneNumber(),
		DeviceDetails: &vgauthPb.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceDetails.GetDeviceToken(),
			UserProfileId: deviceDetails.GetUserProfileId(),
		},
	}
	res, err := ss.savingsClient.CheckAccountStatus(ctx, req)

	ss.vendorStoreCheckAccStatus(ctx, actor, res, req)
	attemptNo := uint(retryInfo.Attempts) + 1

	switch {
	case err != nil:
		failureReason := savingsEvents.ErrorPolling + " Error:" + err.Error()
		LogAccountOpeningEvent(ctx, ss.userClient, ss.EventBroker, attemptNo, retryInfo.GetRequestId(), deviceId, appVersion, acct, actor.GetActor(), res.GetCreatedAt(), savingsEvents.Failure, failureReason, nil)
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		logger.Error(ctx, "error while polling account creation status", zap.Error(err))
		return "", nil, queue.ErrRPC
	case res.GetStatus().GetCode() == uint32(ovgSavingsPb.CreateAccountResponse_SB_ACCOUNT_IN_PROCESS):
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		logger.Debug(ctx, "account creation in progress")
		return "", nil, epifierrors.ErrTransient
	case res.GetStatus().GetCode() == uint32(ovgSavingsPb.CheckAccountStatusResponse_DETAILS_NOT_FOUND):
		return "", nil, epifierrors.ErrRequestNotFoundAtVendor
	case res.GetStatus().GetCode() == uint32(ovgSavingsPb.CheckAccountStatusResponse_SAVINGS_ACCOUNT_FAILURE):
		acct.State = pb.State_FAILED
		err := ss.dao.UpdateAccount(ctx, acct, []pb.AccountFieldMask{pb.AccountFieldMask_STATE})
		if err != nil {
			return "", nil, queue.ErrDB
		}
		return "", nil, epifierrors.ErrPermanent
	case res.AccountNumber == "":
		LogAccountOpeningEvent(ctx, ss.userClient, ss.EventBroker, attemptNo, retryInfo.GetRequestId(), deviceId, appVersion, acct, actor.GetActor(), res.GetCreatedAt(), savingsEvents.Failure, savingsEvents.ErrorEmptyAccountNumber, nil)
		ss.incrementRetryCounterAndUpdate(ctx, acct, retryInfo)
		logger.Debug(ctx, "account status call returned empty account number",
			zap.String("requestId", retryInfo.RequestId))
		return "", nil, fmt.Errorf("unexpected response from vendor: %v:  %w", res, queue.ErrTransient)
	}

	return res.AccountNumber, res.CreatedAt, nil
}

// ProcessInProgressState process account IN_PROGRESS and moves the state machine to CREATED on success.
//
//	 Starts the following processes if account creation is successful:
//			1. Shipping Address update
//			2. UPI Address creation
//			3. Account PI and actor PI Relation creation
//	(Card creation process gets triggers post successful shipping address update)
//
//nolint:funlen
func (ss *SavingsConsumerService) ProcessInProgressState(ctx context.Context, req *pb.ProcessInProgressStateRequest) (*pb.ProcessInProgressStateResponse, error) {
	res := &pb.ProcessInProgressStateResponse{}
	header := &queuePb.ConsumerResponseHeader{}
	res.ResponseHeader = header
	attempts := uint(1)
	acct, retryInfo, err := ss.preProcessor(ctx, req.AccountId)
	if err != nil {
		header.Status = queue.GetStatusFrom(err)
		if header.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE {
			header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(attempts))
		}
		return res, nil
	}

	attempts = uint(retryInfo.Attempts) + 1

	// Validate account state before processing.
	// check if the account is in IN_PROGRESS state or not.
	if acct.State != pb.State_IN_PROGRESS {
		header.Status = queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
		return res, nil
	}

	acctNo, creationTime, err := ss.checkStatusFromVendor(ctx, acct, retryInfo)
	if err != nil {
		switch {
		case errors.Is(err, epifierrors.ErrRequestNotFoundAtVendor):
			msgStatus := queuePb.MessageConsumptionStatus_SUCCESS
			if err2 := ss.handleRequestNotFoundAtVendor(ctx, acct, retryInfo); err2 != nil {
				msgStatus = queue.GetStatusFrom(err2)
			}
			header.Status = msgStatus
		default:
			header.Status = queue.GetStatusFrom(err)
			if header.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE {
				header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(attempts))
			}
		}
		return res, nil
	}

	tnxErr := storagev2.RunCRDBIdempotentTxn(ctx, transactionMaxRetry, func(txnCtx context.Context) error {
		return UpdateAccountStatusInTransaction(ctx, ss.dao, ss.actorClient, ss.createVPAPublisher, ss.cardClient, ss.userClient, ss.authClient, ss.accountStatePub, req.GetAccountId(), acctNo, pb.State_CREATED, creationTime, ss.EventBroker, ss.createSavingsAccountPIPublisher, ss.afPurchasePublisher, ss.dynConf, ss.bcClient, ss.operStatusClient, ss.commsClient, ss.docsClient, ss.tieringClient)
	})
	if tnxErr != nil {
		header.Status = queue.GetStatusFrom(err)
		// update header for transient failure
		if header.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE {
			header.NextTimeout = int64(ss.rtyStrategy.GetNextRetryInterval(attempts))
		}
		return res, nil
	}

	header.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}

func UpdateAccountStatusInTransaction(ctx context.Context, dao savingsDao.SavingsDao, actorClient actorPb.ActorClient,
	createVPAPublisher wireTypes.CreateVPAPublisher, cardClient cardPb.CardProvisioningClient, userClient userPb.UsersClient,
	authClient auth.AuthClient, accountStatePub wireTypes.AccountStatePublisher,
	acctId string, acctNo string, state pb.State, creationTime *timestamppb.Timestamp,
	eventBroker events.Broker, createSavingsAccountPIPublisher wireTypes.SavingsAccountPICreationPublisher,
	afPurchasePublisher wireTypes.EventAfPurchasePublisher, dynConf *genconf.Config, bcClient bcPb.BankCustomerServiceClient,
	operStatusClient operStatusPb.OperationalStatusServiceClient, commsClient comms.CommsClient, docsClient docs.DocsClient,
	tieringClient tieringPb.TieringClient) error {

	acct, err := dao.GetAccountById(ctx, acctId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to fetch account by id, %v", acctId))
		return fmt.Errorf("failed to fetch account by id, %v, %w", acctId, queue.ErrTransient)
	}
	return UpdateAccountCreationStatus(ctx, dao, actorClient, createVPAPublisher, cardClient, userClient, authClient, accountStatePub, acct, acctNo, state, creationTime, eventBroker, createSavingsAccountPIPublisher, afPurchasePublisher, dynConf, bcClient, operStatusClient, commsClient, docsClient, tieringClient)
}

// nolint:funlen
func UpdateAccountCreationStatus(ctx context.Context, dao savingsDao.SavingsDao, actorClient actorPb.ActorClient,
	createVPAPublisher wireTypes.CreateVPAPublisher, cardClient cardPb.CardProvisioningClient, userClient userPb.UsersClient,
	authClient auth.AuthClient, accountStateUpdatePublisher wireTypes.AccountStatePublisher,
	acct *pb.Account, acctNo string, state pb.State, creationTime *timestamppb.Timestamp,
	eventBroker events.Broker, createSavingsAccountPIPublisher wireTypes.SavingsAccountPICreationPublisher,
	afPurchasePublisher wireTypes.EventAfPurchasePublisher, dynConf *genconf.Config, bcClient bcPb.BankCustomerServiceClient,
	operStatusClient operStatusPb.OperationalStatusServiceClient, commsClient comms.CommsClient,
	docsClient docs.DocsClient, tieringClient tieringPb.TieringClient) error {
	var err error

	actor, _ := getActorByEntityId(ctx, actorClient, acct.GetPrimaryAccountHolder())
	ctx = epificontext.CtxWithActorId(ctx, actor.GetId())
	isNewStatusSuccess := state == pb.State_CREATED
	currStatus := acct.GetState()
	// current account creation status validations
	if currStatus == pb.State_CREATED {
		logger.Info(ctx, "account creation already successful")
		return nil
	}
	if currStatus == pb.State_FAILED && !isNewStatusSuccess {
		logger.Info(ctx, fmt.Sprintf("cannot update failed account creation state to %v", state))
		return nil
	}
	acct.State = state
	acct.AccountNo = acctNo
	acct.BalanceFromPartner = money.ZeroINR().GetPb()
	acct.BalanceFromPartnerUpdatedAt = timestamppb.Now()

	if isNewStatusSuccess {
		if acct.CreationInfo == nil {
			acct.CreationInfo = &pb.AccountCreationInfo{}
		}
		acct.CreationInfo.FiCreationSucceededAt = timestamppb.Now()
		acct.CreationInfo.VendorCreationSucceededAt = creationTime
	}

	err = dao.UpdateAccount(ctx, acct, []pb.AccountFieldMask{pb.AccountFieldMask_STATE, pb.AccountFieldMask_ACCOUNT_NO,
		pb.AccountFieldMask_IFSC_CODE, pb.AccountFieldMask_BALANCE_FROM_PARTNER_UPDATE_AT,
		pb.AccountFieldMask_BALANCE_FROM_PARTNER, pb.AccountFieldMask_ACCOUNT_CREATION_INFO})
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error while updating account status in DB for account Id: %v", acct.Id),
			zap.Error(err))
		return fmt.Errorf("error while updating account status in DB for account %w", queue.ErrTransient)
	}
	if acct.GetState() != pb.State_CREATED {
		logger.Info(ctx, "account was not created", zap.String(logger.STATE, acct.GetState().String()))
		return nil
	}
	logger.Info(ctx, fmt.Sprintf("account creation successful for account Id: %v", acct.Id))

	// sendAccountSummaryComms(ctx, acct, userClient, commsClient, docsClient)

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		_, _ = operStatusClient.GetOperationalStatus(ctx, &operStatusPb.GetOperationalStatusRequest{
			DataFreshness: operStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
			AccountIdentifier: &operStatusPb.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: acct.GetId(),
			},
		})
	})

	// Publish account details concurrently
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		publishAccount(ctx, actorClient, accountStateUpdatePublisher, acct)
	})

	/*	 Start the following process post account creation:
		1. Shipping Address update
		2. UPI Address creation
		3. Account PI and actor PI Relation creation
	(Card creation process gets triggers post successful shipping address update)
	*/

	deviceId, _ := device.GetDeviceId(ctx, actor.GetId(), authClient)
	appVersion, _ := device.GetAppVersion(ctx, actor.GetId(), userClient)

	LogAccountOpeningEvent(ctx, userClient, eventBroker, 0, "", deviceId, appVersion, acct, actor, creationTime, "", "", afPurchasePublisher)

	// TODO(team): Introduce a mechanism for retrying for failures in the following calls
	_ = createDebitCard(ctx, cardClient, actor, acct.GetId(), commonvgpb.Vendor_FEDERAL_BANK)

	_ = createVPA(ctx, acct, createVPAPublisher)

	_ = createAccountPi(ctx, acct, actor, createSavingsAccountPIPublisher)

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		initTieringEvaluation(ctx, actor.GetId(), tieringClient)
	})

	return nil
}

func initTieringEvaluation(ctx context.Context, actorId string, tieringClient tieringPb.TieringClient) {
	upgradeResp, upgradeErr := tieringClient.Upgrade(ctx, &tieringPb.UpgradeRequest{
		ActorId:    actorId,
		Provenance: enums.Provenance_PROVENANCE_SAVINGS_ACCOUNT_CREATION,
	})
	if rpcErr := epifigrpc.RPCError(upgradeResp, upgradeErr); rpcErr != nil {
		logger.Error(ctx, "tiering Upgrade rpc failed", zap.Error(rpcErr))
	}
}

func isUserMinKyc(ctx context.Context, userId string, bcClient bcPb.BankCustomerServiceClient) (bool, error) {
	bcResp, errResp := bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_UserId{UserId: userId},
	})
	if er := epifigrpc.RPCError(bcResp, errResp); er != nil {
		logger.Error(ctx, "error in bank customer details", zap.Error(er))
		return false, er
	}
	if bcResp.GetBankCustomer().GetDedupeInfo().GetKycLevel() == kycPb.KYCLevel_MIN_KYC {
		return true, nil
	}
	return false, nil
}

func createDebitCard(ctx context.Context, cardClient cardPb.CardProvisioningClient, actor *types.Actor, accountId string, vendor commonvgpb.Vendor) error {
	ccReq := &cardPb.CreateCardRequest{
		Actor:            actor,
		IssueType:        card.CardIssueType_FRESH,
		IssuingBank:      vendor,
		Type:             card.CardType_DEBIT,
		SavingsAccountId: accountId,
		CardSkuType:      card.CardSKUType_CLASSIC,
	}

	ccResp, err := cardClient.CreateCard(ctx, ccReq)
	if er := epifigrpc.RPCError(ccResp, err); er != nil {
		logger.Error(ctx, "failed initiate Debit card creation post savings account creation",
			zap.String(logger.ACCOUNT_ID, accountId), zap.String(logger.VENDOR, vendor.String()), zap.Error(er))
		return er
	}

	logger.Info(ctx, "Debit card creation successfully initiated")
	return nil
}

// Create VPA handle against the account
func createVPA(ctx context.Context, account *pb.Account, createVPAPublisher wireTypes.CreateVPAPublisher) error {
	if _, err := createVPAPublisher.PublishWithDelay(ctx, &pb.CreateVPARequest{
		UserId:      account.GetPrimaryAccountHolder(),
		AccountId:   account.GetId(),
		PartnerBank: account.GetPartnerBank(),
		AccountType: accountsPb.Type_SAVINGS,
	}, 2*time.Second); err != nil {
		logger.Error(ctx, "error in publishing createVPA event",
			zap.String("AccountID", account.GetId()),
			zap.Error(err),
		)
		return err
	}
	return nil
}

// createAccountPi publishes packet to create PI for given account and the actor PI relation
// Todo(raunak) make the client calls transactional
func createAccountPi(ctx context.Context, acct *pb.Account, actor *types.Actor, createSavingsAccountPIPublisher wireTypes.SavingsAccountPICreationPublisher) error {
	msgId, err := createSavingsAccountPIPublisher.PublishWithDelay(ctx, &pb.ProcessSavingsAccountPICreationRequest{
		ActorId:                actor.GetId(),
		EntityId:               actor.GetEntityId(),
		AccountNo:              acct.GetAccountNo(),
		AccountId:              acct.GetId(),
		IfscCode:               acct.GetIfscCode(),
		AccountProductOffering: acct.GetSkuInfo().GetAccountProductOffering(),
	}, 2*time.Second)
	if err != nil {
		logger.Error(ctx, "error while publishing msg for savings account PI", zap.Error(err))
		return err
	}
	logger.Info(ctx, "msg published successfully to create savings account PI", zap.String(logger.ACTOR_ID_V2, actor.GetId()), zap.String(logger.QUEUE_MESSAGE_ID, msgId))
	return nil
}

func sendAccountSummaryComms(ctx context.Context, acct *pb.Account, userClient userPb.UsersClient, commsClient comms.CommsClient, docsClient docs.DocsClient) {
	const (
		fileName = "Your Federal Bank Account Details"
	)
	var (
		year, month, day = timestamppb.Now().AsTime().Date()
		currentDate      = fmt.Sprintf("%d %s, %d", day, month, year)
	)
	goroutine.Run(ctx, 120*time.Second, func(ctx context.Context) {
		profile, err := GetUserProfile(ctx, userClient, acct.GetPrimaryAccountHolder())
		if err != nil {
			logger.Error(ctx, "error in fetching user profile", zap.Error(err))
			return
		}
		name := gammanames.BestNameFromProfile(ctx, profile)
		password := datetime.DateToDDMMYYYYV2(profile.GetDateOfBirth(), "")
		kycLevel := savingsSvc.GetSavingsAccountKYCLevel(ctx, acct.GetSkuInfo().GetSku())
		data, err := json.Marshal(&pb.SavingsAccountSummaryFileData{
			FirstName:     name.GetFirstName(),
			LastName:      name.GetLastName(),
			PhoneNumber:   profile.GetPhoneNumber().ToString(),
			EmailId:       profile.GetEmail(),
			AccountNumber: acct.GetAccountNo(),
			Ifsc:          acct.GetIfscCode(),
			CurrentDate:   currentDate,
			KycLevel:      go_utils.EnumToTitle(kycLevel),
		})
		if err != nil {
			logger.Error(ctx, "error in marshalling proto", zap.Error(err))
			return
		}
		resp, errResp := docsClient.GeneratePdf(ctx, &docs.GeneratePdfRequest{
			PdfTemplate:         docs.PDFTemplate_FEDERAL_SAVINGS_ACCOUNT_SUMMARY,
			Data:                data,
			OwnerPassword:       password,
			UserPassword:        password,
			FileName:            fmt.Sprintf("%s.pdf", fileName),
			FileNamePrefix:      docs.FileNamePrefix_SAVINGS_ACCOUNT_SUMMARY,
			ExpiryTimeInSeconds: 7 * 86400, // 7 days
		})
		if err = epifigrpc.RPCError(resp, errResp); err != nil {
			logger.Error(ctx, "error while generating account summary PDF", zap.Error(err))
			return
		}
		logger.Debug(ctx, fmt.Sprintf("account summary pdf url: %v", resp.GetFileUrl()))
		commResp, errResp := commsClient.SendMessageBatch(ctx, &comms.SendMessageBatchRequest{
			Type: comms.QoS_BEST_EFFORT,
			UserIdentifier: &comms.SendMessageBatchRequest_UserId{
				UserId: acct.GetPrimaryAccountHolder(),
			},
			CommunicationList: getAccountSummaryComms(ctx, resp.GetFileUrl(), name.GetFirstName(), fileName, currentDate),
		})
		if err = epifigrpc.RPCError(commResp, errResp); err != nil {
			logger.Error(ctx, "error in sending account summary comms", zap.Error(err))
			return
		}
	})
}

func getAccountSummaryComms(ctx context.Context, pdfURL, firstName, fileName, currentDate string) []*comms.Communication {
	summaryComms := []*comms.Communication{
		{
			Medium: comms.Medium_WHATSAPP,
			Message: &comms.Communication_Whatsapp{
				Whatsapp: getAccountSummaryWhatsApp(firstName, pdfURL),
			},
		},
	}
	// nolint:gosec
	fileContent, err := http.Get(pdfURL)
	if err != nil {
		logger.Error(ctx, "failed to get content from the pre-signed url", zap.Error(err))
		return summaryComms
	}

	defer func() {
		err = fileContent.Body.Close()
		if err != nil {
			logger.Error(ctx, "error occurred while closing file", zap.Error(err))
		}
	}()

	body, err := ioutil.ReadAll(fileContent.Body)
	if err != nil {
		logger.Error(ctx, "failed to get the file content", zap.Error(err))
		return summaryComms
	}
	summaryComms = append(summaryComms, &comms.Communication{
		Medium: comms.Medium_EMAIL,
		Message: &comms.Communication_Email{
			Email: getAccountSummaryEmail(fileName, currentDate, body),
		},
	})
	return summaryComms
}

func getAccountSummaryWhatsApp(firstName, pdfURL string) *comms.WhatsappMessage {
	return &comms.WhatsappMessage{
		WhatsappOption: &comms.WhatsappOption{
			Option: &comms.WhatsappOption_SavingsAccountSummaryWhatsappOption{
				SavingsAccountSummaryWhatsappOption: &comms.SavingsAccountSummaryWhatsappOption{
					WhatsappType: comms.WhatsappType_WA_SAVINGS_ACCOUNT_SUMMARY,
					Option: &comms.SavingsAccountSummaryWhatsappOption_SavingsAccountSummaryWhatsappOptionV1{
						SavingsAccountSummaryWhatsappOptionV1: &comms.SavingsAccountSummaryWhatsappOptionV1{
							TemplateVersion:  comms.TemplateVersion_VERSION_V1,
							MediaContentType: comms.WhatsappMediaContentType_WHATSAPP_MEDIA_CONTENT_TYPE_DOCUMENT_PDF,
							MediaUrl:         pdfURL,
							FirstName:        firstName,
						},
					},
				},
			},
		},
	}
}

func getAccountSummaryEmail(filename, date string, pdfData []byte) *comms.EmailMessage {
	return &comms.EmailMessage{
		FromEmailId:   "<EMAIL>",
		FromEmailName: "Fi Money",
		Attachment: []*comms.EmailMessage_Attachment{
			{
				FileContent:    pdfData,
				FileName:       filename,
				Disposition:    comms.Disposition_ATTACHMENT,
				AttachmentType: "application/pdf",
			},
		},
		EmailOption: &comms.EmailOption{
			Option: &comms.EmailOption_SavingsAccountSummaryEmailOption{
				SavingsAccountSummaryEmailOption: &comms.SavingsAccountSummaryEmailOption{
					EmailType: comms.EmailType_EMAIL_SAVINGS_ACCOUNT_SUMMARY,
					Option: &comms.SavingsAccountSummaryEmailOption_SavingsAccountSummaryEmailV1{
						SavingsAccountSummaryEmailV1: &comms.SavingsAccountSummaryEmailV1{
							TemplateVersion: comms.TemplateVersion_VERSION_V1,
							InterestRate:    savingsAccountInterestRate,
							CurrentDate:     date,
						},
					},
				},
			},
		},
	}
}

// GetUserProfile returns User profile for requested entityId
func GetUserProfile(ctx context.Context, userClient userPb.UsersClient, entityId string) (*userPb.Profile, error) {
	userRes, err := userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: entityId,
		},
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("error while getting user for entityId %s %w", entityId, err)
	case !userRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("non-success response %s returned from user.GetUser for entityId %s", userRes.GetStatus().String(), entityId)
	default:
		return userRes.GetUser().GetProfile(), nil
	}
}

func publishAccount(ctx context.Context, actorClient actorPb.ActorClient, accountStatePub queue.Publisher, ac *pb.Account) {
	// get actor
	actorRes, err := actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: ac.GetPrimaryAccountHolder(),
	})
	if err = epifigrpc.RPCError(actorRes, err); err != nil {
		logger.Error(
			ctx, "error in get actor", zap.Error(err),
			zap.String(logger.USER_ID, ac.GetPrimaryAccountHolder()),
		)
		return
	}

	// publish event
	if messageId, err := accountStatePub.Publish(ctx, &pb.AccountStateUpdateEvent{
		Actor:   actorRes.GetActor(),
		Account: ac,
	}); err != nil {
		logger.Error(
			ctx, "error in publishing account details", zap.Error(err),
			zap.String(logger.ACCOUNT_ID, ac.GetAccountNo()),
			zap.String("message id", messageId),
		)
	}
}

// Fetches actor for the given 'entity id'
func getActorByEntityId(ctx context.Context, actorClient actorPb.ActorClient, userId string) (*types.Actor, error) {
	actorResponse, err := actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: userId,
	})
	if er := epifigrpc.RPCError(actorResponse, err); er != nil {
		logger.Error(ctx, "failed fetch actor from Actor service",
			zap.String(logger.USER_ID, userId), zap.Error(er))
		return nil, er
	}
	return actorResponse.Actor, nil
}

// Returns nominee associated with the savings account
func (ss *SavingsConsumerService) getNominee(ctx context.Context, actorId string, vendor commonvgpb.Vendor) (*types.Nominee, error) {
	var nominee *types.Nominee

	// TODO(bhrigu): bugId(2185) : Get nominee ID from savings account table once nominee ID is added to the table.
	// 						 	 Use nominee ID to fetch nominee Details from nominee Table.
	nomineesRes, err := ss.userClient.GetNominees(ctx, &userPb.GetNomineesRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(nomineesRes, err); rpcErr != nil {
		if err == nil && nomineesRes.GetStatus().IsRecordNotFound() {
			return nil, nil
		}
		logger.Error(ctx, "failed to fetch nominee for savings account creation", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, rpcErr
	}

	nominees := nomineesRes.Nominees
	if len(nominees) == 0 {
		logger.Debug(ctx, "Get Nominees response from User service", zap.Any("nominees Resp", nomineesRes))
		logger.Debug(ctx, "no nominees for actor")
		return nil, nil
	}

	if len(nominees) > 1 {
		logger.Error(ctx, "Unexpected behaviour - Multiple nominees to choose from for Savings account creation.\n"+
			"Choosing the latest one in order to unblock onboarding")
		// TODO(bhrigu): (bug ID: 6607) Uncomment the below line once root cause for multiple nominees is identified OR
		// 				we have mapping of nominee -> savings account in place. More details in the monorail ticket
		// return nil, fmt.Errorf("failed to create savings request: More than 1 nominee for Savings account (not allowed)")
	}

	nominee = nominees[0]

	address := nominee.ContactInfo.Address
	if nominee.ContactInfo.Address, err = ss.getVendorFormattedAddress(ctx, vendor, address); err != nil {
		logger.Error(ctx, "failed to convert nominee address to vendor formatted address",
			zap.String(logger.NOMINEE_ID, nominee.Id), zap.Error(err))
		return nil, err
	}

	if nominee.GuardianInfo != nil {
		address = nominee.GuardianInfo.ContactInfo.Address
		if nominee.GuardianInfo.ContactInfo.Address, err = ss.getVendorFormattedAddress(ctx, vendor, address); err != nil {
			logger.Error(ctx, "failed to convert guardian address to vendor formatted address",
				zap.String(logger.NOMINEE_ID, nominee.Id), zap.Error(err))
			return nil, err
		}
	}

	return nominee, nil
}

func (ss *SavingsConsumerService) getVendorFormattedAddress(ctx context.Context, vendor commonvgpb.Vendor, address *types.PostalAddress) (*types.PostalAddress, error) {
	postalAddress, err := convertToGooglePostalAddress(address)
	if err != nil {
		return nil, err
	}

	formattedAddress, err := addressPkg.GetVendorFormattedAddress(ctx, ss.userClient, postalAddress, vendor)
	if err != nil {
		return nil, fmt.Errorf("failed to convert address to vendor formatted address: %v", err)
	}

	expectedAddress, err := convertToInternalPostalAddressType(formattedAddress)
	if err != nil {
		return nil, err
	}
	return expectedAddress, nil
}

func getCustomerName(user *userPb.User, custName *commontypes.Name) *commontypes.Name {
	// TODO (keerthana): Update in new PR to use the customer name in fed_customer_info always
	if custName != nil && strings.TrimSpace(custName.ToString()) != "" {
		return custName
	}
	return user.GetProfile().GetKycName()
}

func (ss *SavingsConsumerService) vendorStoreAccCreation(ctx context.Context, actorId string, res *ovgSavingsPb.CreateAccountResponse, req *ovgSavingsPb.CreateAccountRequest) {
	_ = ss.VendorStore.Insert(ctx, actorId, commonvgpb.Vendor_FEDERAL_BANK, res.GetVendorStatus(), res.GetStatus(), vendorstore.API_ACCOUNT_CREATION_INIT, req.GetRequestId())
}

func (ss *SavingsConsumerService) vendorStoreCheckAccStatus(ctx context.Context, actor *actorPb.GetActorByEntityIdResponse, res *ovgSavingsPb.CheckAccountStatusResponse, req *ovgSavingsPb.CheckAccountStatusRequest) {
	_ = ss.VendorStore.Insert(ctx, actor.GetActor().GetId(), commonvgpb.Vendor_FEDERAL_BANK, res.GetVendorStatus(), res.GetStatus(), vendorstore.API_ACCOUNT_CREATION_ENQUIRY, req.GetRequestId())
}

func convertToGooglePostalAddress(address *types.PostalAddress) (*postaladdress.PostalAddress, error) {
	postalAddr := &postaladdress.PostalAddress{}
	if err := copier.Copy(postalAddr, address); err != nil {
		return nil, fmt.Errorf("failed to copy address: %w", err)
	}
	return postalAddr, nil
}

func convertToInternalPostalAddressType(postalAddr *postaladdress.PostalAddress) (*types.PostalAddress, error) {
	addr := &types.PostalAddress{}
	if err := copier.Copy(addr, postalAddr); err != nil {
		return nil, fmt.Errorf("failed to copy address: %w", err)
	}
	return addr, nil
}

// handleRequestNotFoundAtVendor triggers a new account creation request if the request was never received at partner bank.
func (ss *SavingsConsumerService) handleRequestNotFoundAtVendor(ctx context.Context, acct *pb.Account, retryInfo *pb.QueueRetryInfo) error {

	logger.Info(ctx, fmt.Sprintf("request not found at vendor, re-initiating with same req id: %v", retryInfo.GetRequestId()),
		zap.String("original requestId", retryInfo.GetRequestId()))
	acct.State = pb.State_INITIATED
	if err := ss.dao.UpdateAccount(ctx, acct, []pb.AccountFieldMask{pb.AccountFieldMask_STATE}); err != nil {
		logger.Error(ctx, "failed to update the user account state to initiated", zap.Error(err))
		return err
	}
	userResp, err := ss.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: acct.GetPrimaryAccountHolder(),
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		logger.Error(ctx, "error in get user in handle req not found at vendor", zap.Error(te))
		return te
	}
	bcResp, errResp := ss.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_UserId{
			UserId: userResp.GetUser().GetId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(bcResp, errResp); rpcErr != nil {
		if !bcResp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in fetching bank customer by user id", zap.Error(rpcErr))
		}
		return rpcErr
	}
	// not setting RequestId to empty string so same request id will be used to initiate/retry account creation
	if err = savingsSvc.EnQueueRequestAndUpdateRetryInfo(ctx, acct, bcResp.GetBankCustomer(), ss.dao, ss.producer); err != nil {
		logger.Error(ctx, "error in enqueue account creation req",
			zap.Error(err), zap.String(logger.ENTITY_ID, acct.GetPrimaryAccountHolder()))
		return err
	}
	return nil
}

func (ss *SavingsConsumerService) sendInitSavingsAccountEvents(ctx context.Context, actorId, deviceId string, attemptNo uint, partnerBank, status, failureReason, requestId string) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		ss.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), savingsEvents.NewInitiatedOpenSavingsAccount(actorId, deviceId, attemptNo, partnerBank, status, failureReason, requestId))
	})
}
