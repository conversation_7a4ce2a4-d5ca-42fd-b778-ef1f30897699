//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	extacct2 "github.com/epifi/gamma/api/savings/extacct"
	tieringPb "github.com/epifi/gamma/api/tiering"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/retry"

	celestialPb "github.com/epifi/be-common/api/celestial"

	accountsDao "github.com/epifi/gamma/accounts/dao"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/user"

	bankcustPb "github.com/epifi/gamma/api/bankcust"
	bcPb "github.com/epifi/gamma/api/bankcust"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/consent"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	docsPb "github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/nudge"
	orderPb "github.com/epifi/gamma/api/order"
	reconPb "github.com/epifi/gamma/api/order/recon"
	"github.com/epifi/gamma/api/pay"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	productPb "github.com/epifi/gamma/api/product"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	searchPb "github.com/epifi/gamma/api/search"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	userPb "github.com/epifi/gamma/api/user"
	usersPb "github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"
	userOnbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/extvalidate"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgAccPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgAccountPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	ovgSavings "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	vgTieringPb "github.com/epifi/gamma/api/vendorgateway/tiering"
	docstypes "github.com/epifi/gamma/docs/wire/types"
	dynamicelewireTypes "github.com/epifi/gamma/dynamicelements/wire/types"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/pkg/vendorstore"
	"github.com/epifi/gamma/savings/activity"
	"github.com/epifi/gamma/savings/config"
	"github.com/epifi/gamma/savings/config/genconf"
	"github.com/epifi/gamma/savings/config/worker"
	aggregationsConsumer "github.com/epifi/gamma/savings/consumer"
	savingsDao "github.com/epifi/gamma/savings/dao"
	"github.com/epifi/gamma/savings/data"
	"github.com/epifi/gamma/savings/developer"
	"github.com/epifi/gamma/savings/developer/processor"
	"github.com/epifi/gamma/savings/extacct"
	extAcctConsumer "github.com/epifi/gamma/savings/extacct/consumer"
	extacctDao "github.com/epifi/gamma/savings/extacct/dao"
	savingsService "github.com/epifi/gamma/savings/service"
	savingsQueue "github.com/epifi/gamma/savings/service/queue"
	txnAggregate "github.com/epifi/gamma/savings/service/transactionAggregate"
	"github.com/epifi/gamma/savings/statement"
	"github.com/epifi/gamma/savings/statement/consumer"
	watsonSavingsClient "github.com/epifi/gamma/savings/watson"
	wireTypes "github.com/epifi/gamma/savings/wire/types"
	userProcessor "github.com/epifi/gamma/upi/helper/user"
)

func GormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}
func RedisOptionProvider(conf *config.Config) *cfg.RedisOptions { return conf.RedisOptions }

func InitializeService(
	db types.EpifiCRDB,
	publisher wireTypes.SavingsCreationPublisher,
	userClient userPb.UsersClient,
	client actorPb.ActorClient,
	vgSavingsClient ovgSavings.SavingsClient,
	authClient authPb.AuthClient,
	searchClient searchPb.ActionBarClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	conf *config.Config,
	reconClient reconPb.LedgerReconciliationClient,
	userGroupClient usergroupPb.GroupClient,
	balanceUpdatePublisher wireTypes.BalanceUpdateEventPublisher,
	redisClient wireTypes.UserSavingsRedisStore,
	inAppCommsClient dynamicelewireTypes.InAppTargetedCommsClientWithInterceptors,
	balHistoryRedisClient wireTypes.BalHistoryRedisStore,
	bcClient bcPb.BankCustomerServiceClient,
	payClient pay.PayClient,
	dynConf *genconf.Config,
	operStatusClient operStatusPb.OperationalStatusServiceClient,
	esignClient docstypes.ESignClientWithInterceptors,
	orderClient orderPb.OrderServiceClient,
	balanceChangeEventPub wireTypes.BalanceChangeEventPublisher,
	broker events.Broker,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
	nudgeClient nudge.NudgeServiceClient,
	savingsRueidisCache wireTypes.SavingsRueidisCacheStorage,
	onboardingClient userOnbPb.OnboardingClient,
	paymentClient paymentPb.PaymentClient,
	externalAccountsClient extacct2.ExternalAccountsClient,
) *savingsService.SavingsService {

	wire.Build(
		docstypes.ESignClientProvider,
		dynamicelewireTypes.InAppTargetedCommsClientProvider,
		savingsDao.WireSet,
		savingsService.NewSavingsService,
		userProcessor.WireSet,
		idgen.WireSet,
		savingsDao.NewSavingsAggregationsDao,
		types.EpifiCRDBGormDBProvider,
		changefeed.ChangefeedWireSet,
		wire.Bind(new(savingsDao.SavingsAggregationDao), new(*savingsDao.SavingsAggregationsDaoCrdb)),
		redisClientProvider,
		savingsDao.CBTWireSet,
		txnAggregate.TxnAggregateWireSet,
		lock.DefaultLockMangerWireSet,
		saClosureMaxPageSizeProvider,
		savingsDao.SaClosureRequestDaoWireSet,
		data.SaClosureDataProcessorWireSet,
		datetime.WireDefaultTimeSet,
	)
	return &savingsService.SavingsService{}
}

func retryStrategyParamsProvider(conf *config.Config) *cfg.RetryParams {
	return conf.SavingsCallbackSubscriber.RetryStrategy
}

func InitializeSavingsConsumerService(db types.EpifiCRDB, vgSavingsClient ovgSavings.SavingsClient,
	cardClient cardPb.CardProvisioningClient, actorClient actorPb.ActorClient, authClient authPb.AuthClient,
	createVPAPublisher wireTypes.CreateVPAPublisher, accountStatePub wireTypes.AccountStatePublisher,
	savingsCreationPub wireTypes.SavingsCreationPublisher, userClient userPb.UsersClient,
	broker events.Broker, cfg *config.Config, createSavingsAccountPIPublisher wireTypes.SavingsAccountPICreationPublisher,
	afPurchasePublisher wireTypes.EventAfPurchasePublisher, dynConf *genconf.Config, redisClient wireTypes.UserSavingsRedisStore,
	bcClient bcPb.BankCustomerServiceClient, operStatusClient operStatusPb.OperationalStatusServiceClient, commsClient wireTypes.SavingsCommsClientWithInterceptors,
	docsClient docstypes.DocsClientWithInterceptors, savingsRueidisCache wireTypes.SavingsRueidisCacheStorage,
	tieringClient tieringPb.TieringClient, consentClient consent.ConsentClient) (*savingsQueue.SavingsConsumerService, error) {
	wire.Build(
		wireTypes.CommsClientProvider,
		docstypes.DocsClientProvider,
		savingsDao.WireSet,
		GormProvider,
		savingsQueue.NewSavingsConsumerService,
		vendorstore.NewVendorStore,
		vendorstore.VendorStoreDAOWireSet,
		changefeed.ChangefeedWireSet,
		retryStrategyParamsProvider,
		retry.NewStrategyFromConfig,
	)
	return &savingsQueue.SavingsConsumerService{}, nil
}

func InitialiseCreateVPAConsumer(db types.EpifiCRDB, piClient piPb.PiClient, accountPiClient accountPiPb.AccountPIRelationClient,
	actorClient actorPb.ActorClient, upiClient upiPb.UPIClient, userClient userPb.UsersClient, authClient authPb.AuthClient, upiOnboardingClient upiOnboardingPb.UpiOnboardingClient, conf *config.Config, redisClient wireTypes.UserSavingsRedisStore, gconf *genconf.Config, savingsRueidisCache wireTypes.SavingsRueidisCacheStorage) *savingsQueue.PIConsumerService {
	wire.Build(savingsDao.WireSet,
		savingsQueue.NewPIConsumerService,
		types.EpifiCRDBGormDBProvider,
		changefeed.ChangefeedWireSet,
	)
	return &savingsQueue.PIConsumerService{}
}
func InitialiseStatementConsumerService(
	commsClient commsPb.CommsClient,
	vgAccountClient vgAccountPb.AccountsClient,
	docsClient docsPb.DocsClient,
	conf *config.Config,
) *consumer.StatementConsumerService {
	wire.Build(
		consumer.NewConsumerService,
	)
	return &consumer.StatementConsumerService{}
}

func InitializeDevSavingsService(
	db types.EpifiCRDB,
	conf *config.Config,
	gconf *genconf.Config,
	redisClient wireTypes.UserSavingsRedisStore,
	broker events.Broker,
	nudgeClient nudge.NudgeServiceClient,
	savingsRueidisCache wireTypes.SavingsRueidisCacheStorage,
) *developer.SavingsDbStatesService {
	wire.Build(
		savingsDao.WireSet,
		types.EpifiCRDBGormDBProvider,
		changefeed.ChangefeedWireSet,
		developer.NewDevFactory,
		developer.NewSavingsDbStatesService,
		saClosureMaxPageSizeProvider,
		savingsDao.SaClosureRequestDaoWireSet,
		data.SaClosureDataProcessorWireSet,
		processor.NewDevSaClosure,
		processor.NewDevSavings,
		processor.NewClosedAccountBalanceTransferProcessor,
		processor.NewDevBankAccountVerifications,
		extacctDao.NewBankAccountVerificationsCrdb,
		wire.Bind(new(extacctDao.BankAccountVerificationsDao), new(*extacctDao.BankAccountVerificationsCrdb)),
		accountsDao.OperationalStatusWireSetForDBState,
		savingsDao.CBTWireSet,
		idgen.WireSet,
		lock.DefaultLockMangerWireSet,
	)
	return &developer.SavingsDbStatesService{}
}

func InitialiseSavingsCallbackConsumer(db types.EpifiCRDB, actorClient actorPb.ActorClient, createVPAPublisher wireTypes.CreateVPAPublisher,
	cardClient cardPb.CardProvisioningClient, userClient userPb.UsersClient, authClient authPb.AuthClient,
	accountStateUpdatePublisher wireTypes.AccountStatePublisher, cfg *config.Config, broker events.Broker,
	createSavingsAccountPIPublisher wireTypes.SavingsAccountPICreationPublisher, afPurchasePublisher wireTypes.EventAfPurchasePublisher,
	dynConf *genconf.Config, redisClient wireTypes.UserSavingsRedisStore, bcClient bcPb.BankCustomerServiceClient, savingsRueidisCache wireTypes.SavingsRueidisCacheStorage,
	operStatusClient operStatusPb.OperationalStatusServiceClient, commsClient wireTypes.SavingsCommsClientWithInterceptors, docsClient docstypes.DocsClientWithInterceptors,
	tieringClient tieringPb.TieringClient) *savingsQueue.CallbackAccountCreationConsumer {
	wire.Build(
		wireTypes.CommsClientProvider,
		docstypes.DocsClientProvider,
		savingsDao.WireSet,
		GormProvider,
		changefeed.ChangefeedWireSet,
		savingsQueue.NewCallbackConsumer,
		vendorstore.NewVendorStore,
		vendorstore.VendorStoreDAOWireSet,
	)
	return &savingsQueue.CallbackAccountCreationConsumer{}
}

func InitialiseStatementService(db types.EpifiCRDB, accountStmtPublisher wireTypes.AccountStatementPublisher,
	userClient userPb.UsersClient, authClient authPb.AuthClient, actorClient actorPb.ActorClient, conf *config.Config, bcClient bcPb.BankCustomerServiceClient) *statement.Service {
	wire.Build(
		types.EpifiCRDBGormDBProvider,
		changefeed.ChangefeedWireSet,
		statement.NewService,
		savingsDao.NewSavingsDao,
		wire.Bind(new(savingsDao.SavingsDao), new(*savingsDao.CrdbSavingsDao)),
	)
	return &statement.Service{}
}

func InitialiseExternalAccountsService(db types.EpifiCRDB, nameCheckClient ncPb.UNNameCheckClient, usersClient usersPb.UsersClient,
	actorClient actorPb.ActorClient, conf *genconf.Config, extValidateClient extvalidate.ExternalValidateClient, savingsClient savingsPb.SavingsClient,
	watsonClient watsonPb.WatsonClient, payClient payPb.PayClient, thirdPartyAccountSharingPub wireTypes.ThirdPartyAccountSharingPublisher) *extacct.Service {
	wire.Build(extacct.NewExternalAccountsService,
		extacctDao.NewBankAccountVerificationsCrdb,
		wire.Bind(new(extacctDao.BankAccountVerificationsDao), new(*extacctDao.BankAccountVerificationsCrdb)))
	return &extacct.Service{}
}

func InitialiseBalanceUpdateConsumerService(client wireTypes.SavingsCommsClientWithInterceptors, conf *config.Config, actorClient actorPb.ActorClient, onboardingClient userOnbPb.OnboardingClient) *aggregationsConsumer.BalanceUpdateConsumer {
	wire.Build(wireTypes.CommsClientProvider, aggregationsConsumer.NewBalanceUpdateConsumer)
	return &aggregationsConsumer.BalanceUpdateConsumer{}
}

func redisClientProvider(UserSavingsRedisStore wireTypes.UserSavingsRedisStore) *redis.Client {
	return UserSavingsRedisStore
}

func InitialiseWatsonClientService(actorClient actorPb.ActorClient, savingsClient savingsPb.SavingsClient) *watsonSavingsClient.Service {
	wire.Build(watsonSavingsClient.NewService)
	return &watsonSavingsClient.Service{}
}

func InitialiseExtAcctConsumerService(vgAccountsClient vgAccPb.AccountsClient,
	bankCustClient bankcustPb.BankCustomerServiceClient,
	usersClient usersPb.UsersClient) *extAcctConsumer.Service {
	wire.Build(extAcctConsumer.NewConsumerService)
	return &extAcctConsumer.Service{}
}

func InitialiseOperStatusUpdateConsumerService(conf *config.Config, dynConf *genconf.Config,
	savingsClient savingsPb.SavingsClient, actorClient actorPb.ActorClient,
	accountStatePub wireTypes.AccountStatePublisher, broker events.Broker, nudgeClient nudge.NudgeServiceClient,
	crdb types.EpifiCRDB, redisClient wireTypes.UserSavingsRedisStore,
	savingsRueidisCache wireTypes.SavingsRueidisCacheStorage, usersClient user.UsersClient, productClient productPb.ProductClient,
) *aggregationsConsumer.OperStatusUpdateConsumer {
	wire.Build(
		aggregationsConsumer.NewOperStatusUpdateConsumer,
		savingsDao.WireSet,
		savingsDao.SaClosureRequestDaoWireSet,
		changefeed.ChangefeedWireSet,
		GormProvider,
		data.SaClosureDataProcessorWireSet,
		saClosureMaxPageSizeProvider,
	)
	return &aggregationsConsumer.OperStatusUpdateConsumer{}
}

func InitializeActivityProcessor(config2 *worker.Config, vgTieringClient vgTieringPb.TieringClient, savingsClient savingsPb.SavingsClient, vgSavingsClient ovgSavings.SavingsClient) *activity.Processor {
	processor := activity.NewProcessor(config2, vgTieringClient, savingsClient, vgSavingsClient)
	return processor
}

func InitialiseEventsSubscriberService(client celestialPb.CelestialClient, dynConf *genconf.Config, savingsClient savingsPb.SavingsClient, bcClient bcPb.BankCustomerServiceClient, salaryProgramClient salaryprogramPb.SalaryProgramClient, tieringClient tieringPb.TieringClient) *aggregationsConsumer.EventSubscriber {
	wire.Build(aggregationsConsumer.NewEventSubscriber)
	return &aggregationsConsumer.EventSubscriber{}
}

func saClosureMaxPageSizeProvider(gconf *genconf.Config) uint32 {
	return gconf.SavingsAccountClosure().MaxPageSize()
}

func InitialiseSaClosureClosureConsumerService(db types.EpifiCRDB, conf *config.Config, gconf *genconf.Config,
	redisClient wireTypes.UserSavingsRedisStore, broker events.Broker, nudgeClient nudge.NudgeServiceClient, storage wireTypes.SavingsRueidisCacheStorage) *savingsQueue.SaClosureClosureConsumerService {
	wire.Build(
		savingsQueue.NewSaClosureClosureConsumerService,
		data.SaClosureDataProcessorWireSet,
		saClosureMaxPageSizeProvider,
		savingsDao.SaClosureRequestDaoWireSet,
		savingsDao.WireSet,
		changefeed.ChangefeedWireSet,
		GormProvider,
	)
	return &savingsQueue.SaClosureClosureConsumerService{}
}
