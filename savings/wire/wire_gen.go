// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/retry"
	"github.com/epifi/gamma/accounts/dao"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/order/recon"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/salaryprogram"
	savings2 "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/upi"
	onboarding2 "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/extvalidate"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	tiering2 "github.com/epifi/gamma/api/vendorgateway/tiering"
	types4 "github.com/epifi/gamma/docs/wire/types"
	types3 "github.com/epifi/gamma/dynamicelements/wire/types"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/pkg/vendorstore"
	"github.com/epifi/gamma/savings/activity"
	"github.com/epifi/gamma/savings/config"
	"github.com/epifi/gamma/savings/config/genconf"
	"github.com/epifi/gamma/savings/config/worker"
	consumer2 "github.com/epifi/gamma/savings/consumer"
	"github.com/epifi/gamma/savings/dao"
	"github.com/epifi/gamma/savings/data"
	"github.com/epifi/gamma/savings/developer"
	"github.com/epifi/gamma/savings/developer/processor"
	extacct2 "github.com/epifi/gamma/savings/extacct"
	consumer3 "github.com/epifi/gamma/savings/extacct/consumer"
	dao2 "github.com/epifi/gamma/savings/extacct/dao"
	"github.com/epifi/gamma/savings/service"
	"github.com/epifi/gamma/savings/service/queue"
	"github.com/epifi/gamma/savings/service/transactionAggregate"
	"github.com/epifi/gamma/savings/statement"
	"github.com/epifi/gamma/savings/statement/consumer"
	"github.com/epifi/gamma/savings/watson"
	types2 "github.com/epifi/gamma/savings/wire/types"
	user2 "github.com/epifi/gamma/upi/helper/user"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeService(db types.EpifiCRDB, publisher types2.SavingsCreationPublisher, userClient user.UsersClient, client actor.ActorClient, vgSavingsClient savings.SavingsClient, authClient auth.AuthClient, searchClient search.ActionBarClient, accountPiClient account_pi.AccountPIRelationClient, conf *config.Config, reconClient recon.LedgerReconciliationClient, userGroupClient group.GroupClient, balanceUpdatePublisher types2.BalanceUpdateEventPublisher, redisClient types2.UserSavingsRedisStore, inAppCommsClient types3.InAppTargetedCommsClientWithInterceptors, balHistoryRedisClient types2.BalHistoryRedisStore, bcClient bankcust.BankCustomerServiceClient, payClient pay.PayClient, dynConf *genconf.Config, operStatusClient operstatus.OperationalStatusServiceClient, esignClient types4.ESignClientWithInterceptors, orderClient order.OrderServiceClient, balanceChangeEventPub types2.BalanceChangeEventPublisher, broker events.Broker, paySavingsBalanceClient balance.BalanceClient, nudgeClient nudge.NudgeServiceClient, savingsRueidisCache types2.SavingsRueidisCacheStorage, onboardingClient onboarding.OnboardingClient, paymentClient payment.PaymentClient, externalAccountsClient extacct.ExternalAccountsClient) *service.SavingsService {
	gormDB := types.EpifiCRDBGormDBProvider(db)
	changeFeed := changefeed.NewChangefeed(gormDB)
	crdbSavingsDao := storage.NewSavingsDao(db, changeFeed)
	cacheStorage := types2.SavingsRueidisCacheStorageProvider(savingsRueidisCache)
	savingsCache := storage.NewSavingsCache(cacheStorage, dynConf, crdbSavingsDao)
	savingsDao := storage.ProvideSavingsDao(crdbSavingsDao, savingsCache, conf)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	savingsAggregationsDaoCrdb := storage.NewSavingsAggregationsDao(db, domainIdGenerator)
	processor := user2.NewProcessor(userClient, userGroupClient)
	inAppTargetedCommsClient := types3.InAppTargetedCommsClientProvider(inAppCommsClient)
	client2 := redisClientProvider(redisClient)
	closedAccountsBalanceTransferDaoCRDB := storage.NewClosedAccountsBalanceTransferDaoCRDB(db, domainIdGenerator)
	payTxnAggregate := transactionAggregate.NewPayTxnAggregate(payClient)
	searchTxnAggregate := transactionAggregate.NewSearchTxnAggregate(searchClient)
	eSignClient := types4.ESignClientProvider(esignClient)
	redisRwLock := lock.NewRedisRwLock(client2, clock)
	cacheSavingsAccountEssentials := storage.NewCacheSavingsAccountEssentials(cacheStorage, crdbSavingsDao, savingsCache, dynConf)
	uint32_2 := saClosureMaxPageSizeProvider(dynConf)
	saClosureRequestImpl := storage.NewSaClosureRequestImpl(db, uint32_2)
	saClosureDataProcessor := data.NewSaClosureDataProcessor(dynConf, saClosureRequestImpl, savingsDao, broker, nudgeClient)
	defaultTime := datetime.NewDefaultTime()
	savingsService := service.NewSavingsService(savingsDao, savingsAggregationsDaoCrdb, domainIdGenerator, userClient, publisher, vgSavingsClient, client, authClient, accountPiClient, conf, reconClient, processor, balanceUpdatePublisher, inAppTargetedCommsClient, client2, closedAccountsBalanceTransferDaoCRDB, balHistoryRedisClient, bcClient, payClient, dynConf, payTxnAggregate, searchTxnAggregate, operStatusClient, eSignClient, redisRwLock, orderClient, cacheSavingsAccountEssentials, balanceChangeEventPub, broker, paySavingsBalanceClient, saClosureRequestImpl, saClosureDataProcessor, cacheStorage, defaultTime, onboardingClient, paymentClient, externalAccountsClient)
	return savingsService
}

func InitializeSavingsConsumerService(db types.EpifiCRDB, vgSavingsClient savings.SavingsClient, cardClient provisioning.CardProvisioningClient, actorClient actor.ActorClient, authClient auth.AuthClient, createVPAPublisher types2.CreateVPAPublisher, accountStatePub types2.AccountStatePublisher, savingsCreationPub types2.SavingsCreationPublisher, userClient user.UsersClient, broker events.Broker, cfg *config.Config, createSavingsAccountPIPublisher types2.SavingsAccountPICreationPublisher, afPurchasePublisher types2.EventAfPurchasePublisher, dynConf *genconf.Config, redisClient types2.UserSavingsRedisStore, bcClient bankcust.BankCustomerServiceClient, operStatusClient operstatus.OperationalStatusServiceClient, commsClient types2.SavingsCommsClientWithInterceptors, docsClient types4.DocsClientWithInterceptors, savingsRueidisCache types2.SavingsRueidisCacheStorage, tieringClient tiering.TieringClient, consentClient consent.ConsentClient) (*queue.SavingsConsumerService, error) {
	gormDB := GormProvider(db)
	changeFeed := changefeed.NewChangefeed(gormDB)
	crdbSavingsDao := storage.NewSavingsDao(db, changeFeed)
	cacheStorage := types2.SavingsRueidisCacheStorageProvider(savingsRueidisCache)
	savingsCache := storage.NewSavingsCache(cacheStorage, dynConf, crdbSavingsDao)
	savingsDao := storage.ProvideSavingsDao(crdbSavingsDao, savingsCache, cfg)
	retryParams := retryStrategyParamsProvider(cfg)
	retryStrategy, err := retry.NewStrategyFromConfig(retryParams)
	if err != nil {
		return nil, err
	}
	vendorResponseCRDB := vendorstore.NewVendorResponseDAO(gormDB)
	vendorStore := vendorstore.NewVendorStore(vendorResponseCRDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	docsDocsClient := types4.DocsClientProvider(docsClient)
	savingsConsumerService := queue.NewSavingsConsumerService(dynConf, savingsDao, vgSavingsClient, cardClient, actorClient, savingsCreationPub, retryStrategy, createVPAPublisher, authClient, userClient, createSavingsAccountPIPublisher, accountStatePub, broker, vendorStore, afPurchasePublisher, bcClient, operStatusClient, commsCommsClient, docsDocsClient, tieringClient, consentClient)
	return savingsConsumerService, nil
}

func InitialiseCreateVPAConsumer(db types.EpifiCRDB, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, actorClient actor.ActorClient, upiClient upi.UPIClient, userClient user.UsersClient, authClient auth.AuthClient, upiOnboardingClient onboarding2.UpiOnboardingClient, conf *config.Config, redisClient types2.UserSavingsRedisStore, gconf *genconf.Config, savingsRueidisCache types2.SavingsRueidisCacheStorage) *queue.PIConsumerService {
	gormDB := types.EpifiCRDBGormDBProvider(db)
	changeFeed := changefeed.NewChangefeed(gormDB)
	crdbSavingsDao := storage.NewSavingsDao(db, changeFeed)
	cacheStorage := types2.SavingsRueidisCacheStorageProvider(savingsRueidisCache)
	savingsCache := storage.NewSavingsCache(cacheStorage, gconf, crdbSavingsDao)
	savingsDao := storage.ProvideSavingsDao(crdbSavingsDao, savingsCache, conf)
	piConsumerService := queue.NewPIConsumerService(savingsDao, piClient, accountPiClient, actorClient, upiClient, userClient, authClient, upiOnboardingClient)
	return piConsumerService
}

func InitialiseStatementConsumerService(commsClient comms.CommsClient, vgAccountClient accounts.AccountsClient, docsClient docs.DocsClient, conf *config.Config) *consumer.StatementConsumerService {
	statementConsumerService := consumer.NewConsumerService(commsClient, vgAccountClient, docsClient, conf)
	return statementConsumerService
}

func InitializeDevSavingsService(db types.EpifiCRDB, conf *config.Config, gconf *genconf.Config, redisClient types2.UserSavingsRedisStore, broker events.Broker, nudgeClient nudge.NudgeServiceClient, savingsRueidisCache types2.SavingsRueidisCacheStorage) *developer.SavingsDbStatesService {
	gormDB := types.EpifiCRDBGormDBProvider(db)
	changeFeed := changefeed.NewChangefeed(gormDB)
	crdbSavingsDao := storage.NewSavingsDao(db, changeFeed)
	cacheStorage := types2.SavingsRueidisCacheStorageProvider(savingsRueidisCache)
	savingsCache := storage.NewSavingsCache(cacheStorage, gconf, crdbSavingsDao)
	savingsDao := storage.ProvideSavingsDao(crdbSavingsDao, savingsCache, conf)
	operationalStatusDaoCRDB := dao.NewOperationalStatusDaoCRDBForDBState(db)
	devSavings := processor.NewDevSavings(savingsDao, operationalStatusDaoCRDB)
	uint32_2 := saClosureMaxPageSizeProvider(gconf)
	saClosureRequestImpl := storage.NewSaClosureRequestImpl(db, uint32_2)
	saClosureDataProcessor := data.NewSaClosureDataProcessor(gconf, saClosureRequestImpl, savingsDao, broker, nudgeClient)
	devSaClosure := processor.NewDevSaClosure(saClosureDataProcessor)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	closedAccountsBalanceTransferDaoCRDB := storage.NewClosedAccountsBalanceTransferDaoCRDB(db, domainIdGenerator)
	devClosedAccountBalanceTransfer := processor.NewClosedAccountBalanceTransferProcessor(closedAccountsBalanceTransferDaoCRDB)
	bankAccountVerificationsCrdb := dao2.NewBankAccountVerificationsCrdb(db)
	devBankAccountVerifications := processor.NewDevBankAccountVerifications(bankAccountVerificationsCrdb)
	devFactory := developer.NewDevFactory(devSavings, devSaClosure, devClosedAccountBalanceTransfer, devBankAccountVerifications)
	savingsDbStatesService := developer.NewSavingsDbStatesService(devFactory)
	return savingsDbStatesService
}

func InitialiseSavingsCallbackConsumer(db types.EpifiCRDB, actorClient actor.ActorClient, createVPAPublisher types2.CreateVPAPublisher, cardClient provisioning.CardProvisioningClient, userClient user.UsersClient, authClient auth.AuthClient, accountStateUpdatePublisher types2.AccountStatePublisher, cfg *config.Config, broker events.Broker, createSavingsAccountPIPublisher types2.SavingsAccountPICreationPublisher, afPurchasePublisher types2.EventAfPurchasePublisher, dynConf *genconf.Config, redisClient types2.UserSavingsRedisStore, bcClient bankcust.BankCustomerServiceClient, savingsRueidisCache types2.SavingsRueidisCacheStorage, operStatusClient operstatus.OperationalStatusServiceClient, commsClient types2.SavingsCommsClientWithInterceptors, docsClient types4.DocsClientWithInterceptors, tieringClient tiering.TieringClient) *queue.CallbackAccountCreationConsumer {
	gormDB := GormProvider(db)
	changeFeed := changefeed.NewChangefeed(gormDB)
	crdbSavingsDao := storage.NewSavingsDao(db, changeFeed)
	cacheStorage := types2.SavingsRueidisCacheStorageProvider(savingsRueidisCache)
	savingsCache := storage.NewSavingsCache(cacheStorage, dynConf, crdbSavingsDao)
	savingsDao := storage.ProvideSavingsDao(crdbSavingsDao, savingsCache, cfg)
	vendorResponseCRDB := vendorstore.NewVendorResponseDAO(gormDB)
	vendorStore := vendorstore.NewVendorStore(vendorResponseCRDB)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	docsDocsClient := types4.DocsClientProvider(docsClient)
	callbackAccountCreationConsumer := queue.NewCallbackConsumer(dynConf, savingsDao, actorClient, createVPAPublisher, cardClient, userClient, authClient, accountStateUpdatePublisher, broker, vendorStore, createSavingsAccountPIPublisher, afPurchasePublisher, gormDB, bcClient, operStatusClient, commsCommsClient, docsDocsClient, tieringClient)
	return callbackAccountCreationConsumer
}

func InitialiseStatementService(db types.EpifiCRDB, accountStmtPublisher types2.AccountStatementPublisher, userClient user.UsersClient, authClient auth.AuthClient, actorClient actor.ActorClient, conf *config.Config, bcClient bankcust.BankCustomerServiceClient) *statement.Service {
	gormDB := types.EpifiCRDBGormDBProvider(db)
	changeFeed := changefeed.NewChangefeed(gormDB)
	crdbSavingsDao := storage.NewSavingsDao(db, changeFeed)
	statementService := statement.NewService(crdbSavingsDao, accountStmtPublisher, userClient, authClient, actorClient, bcClient)
	return statementService
}

func InitialiseExternalAccountsService(db types.EpifiCRDB, nameCheckClient namecheck.UNNameCheckClient, usersClient user.UsersClient, actorClient actor.ActorClient, conf *genconf.Config, extValidateClient extvalidate.ExternalValidateClient, savingsClient savings2.SavingsClient, watsonClient watson.WatsonClient, payClient pay.PayClient, thirdPartyAccountSharingPub types2.ThirdPartyAccountSharingPublisher) *extacct2.Service {
	bankAccountVerificationsCrdb := dao2.NewBankAccountVerificationsCrdb(db)
	extacctService := extacct2.NewExternalAccountsService(nameCheckClient, usersClient, actorClient, conf, bankAccountVerificationsCrdb, extValidateClient, savingsClient, watsonClient, payClient, thirdPartyAccountSharingPub)
	return extacctService
}

func InitialiseBalanceUpdateConsumerService(client types2.SavingsCommsClientWithInterceptors, conf *config.Config, actorClient actor.ActorClient, onboardingClient onboarding.OnboardingClient) *consumer2.BalanceUpdateConsumer {
	commsClient := types2.CommsClientProvider(client)
	balanceUpdateConsumer := consumer2.NewBalanceUpdateConsumer(commsClient, conf, actorClient, onboardingClient)
	return balanceUpdateConsumer
}

func InitialiseWatsonClientService(actorClient actor.ActorClient, savingsClient savings2.SavingsClient) *watson_client.Service {
	watson_clientService := watson_client.NewService(actorClient, savingsClient)
	return watson_clientService
}

func InitialiseExtAcctConsumerService(vgAccountsClient accounts.AccountsClient, bankCustClient bankcust.BankCustomerServiceClient, usersClient user.UsersClient) *consumer3.Service {
	consumerService := consumer3.NewConsumerService(vgAccountsClient, bankCustClient, usersClient)
	return consumerService
}

func InitialiseOperStatusUpdateConsumerService(conf *config.Config, dynConf *genconf.Config, savingsClient savings2.SavingsClient, actorClient actor.ActorClient, accountStatePub types2.AccountStatePublisher, broker events.Broker, nudgeClient nudge.NudgeServiceClient, crdb types.EpifiCRDB, redisClient types2.UserSavingsRedisStore, savingsRueidisCache types2.SavingsRueidisCacheStorage, usersClient user.UsersClient, productClient product.ProductClient) *consumer2.OperStatusUpdateConsumer {
	uint32_2 := saClosureMaxPageSizeProvider(dynConf)
	saClosureRequestImpl := storage.NewSaClosureRequestImpl(crdb, uint32_2)
	db := GormProvider(crdb)
	changeFeed := changefeed.NewChangefeed(db)
	crdbSavingsDao := storage.NewSavingsDao(crdb, changeFeed)
	cacheStorage := types2.SavingsRueidisCacheStorageProvider(savingsRueidisCache)
	savingsCache := storage.NewSavingsCache(cacheStorage, dynConf, crdbSavingsDao)
	savingsDao := storage.ProvideSavingsDao(crdbSavingsDao, savingsCache, conf)
	saClosureDataProcessor := data.NewSaClosureDataProcessor(dynConf, saClosureRequestImpl, savingsDao, broker, nudgeClient)
	operStatusUpdateConsumer := consumer2.NewOperStatusUpdateConsumer(dynConf, savingsClient, actorClient, accountStatePub, saClosureDataProcessor, usersClient, productClient)
	return operStatusUpdateConsumer
}

func InitialiseEventsSubscriberService(client celestial.CelestialClient, dynConf *genconf.Config, savingsClient savings2.SavingsClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, tieringClient tiering.TieringClient) *consumer2.EventSubscriber {
	eventSubscriber := consumer2.NewEventSubscriber(dynConf, client, savingsClient, bcClient, salaryProgramClient, tieringClient)
	return eventSubscriber
}

func InitialiseSaClosureClosureConsumerService(db types.EpifiCRDB, conf *config.Config, gconf *genconf.Config, redisClient types2.UserSavingsRedisStore, broker events.Broker, nudgeClient nudge.NudgeServiceClient, storage2 types2.SavingsRueidisCacheStorage) *queue.SaClosureClosureConsumerService {
	uint32_2 := saClosureMaxPageSizeProvider(gconf)
	saClosureRequestImpl := storage.NewSaClosureRequestImpl(db, uint32_2)
	gormDB := GormProvider(db)
	changeFeed := changefeed.NewChangefeed(gormDB)
	crdbSavingsDao := storage.NewSavingsDao(db, changeFeed)
	cacheStorage := types2.SavingsRueidisCacheStorageProvider(storage2)
	savingsCache := storage.NewSavingsCache(cacheStorage, gconf, crdbSavingsDao)
	savingsDao := storage.ProvideSavingsDao(crdbSavingsDao, savingsCache, conf)
	saClosureDataProcessor := data.NewSaClosureDataProcessor(gconf, saClosureRequestImpl, savingsDao, broker, nudgeClient)
	saClosureClosureConsumerService := queue.NewSaClosureClosureConsumerService(saClosureDataProcessor)
	return saClosureClosureConsumerService
}

// wire.go:

func GormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}

func RedisOptionProvider(conf *config.Config) *cfg.RedisOptions { return conf.RedisOptions }

func retryStrategyParamsProvider(conf *config.Config) *cfg.RetryParams {
	return conf.SavingsCallbackSubscriber.RetryStrategy
}

func redisClientProvider(UserSavingsRedisStore types2.UserSavingsRedisStore) *redis.Client {
	return UserSavingsRedisStore
}

func InitializeActivityProcessor(config2 *worker.Config, vgTieringClient tiering2.TieringClient, savingsClient savings2.SavingsClient, vgSavingsClient savings.SavingsClient) *activity.Processor {
	processor2 := activity.NewProcessor(config2, vgTieringClient, savingsClient, vgSavingsClient)
	return processor2
}

func saClosureMaxPageSizeProvider(gconf *genconf.Config) uint32 {
	return gconf.SavingsAccountClosure().MaxPageSize()
}
