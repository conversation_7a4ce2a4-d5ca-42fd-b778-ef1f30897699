// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	genconfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	common "github.com/epifi/gamma/preapprovedloan/config/common"
)

type DeeplinkConfig struct {
	callbacks                           *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                            questsdk.Client
	questFieldPath                      string
	_IsInitiateMandateEnrichmentEnabled uint32
	// IsAlternateAccountFlowEnabled flag determines if alternate account flow is enabled for the user
	// this variable is marked as quest for AB experimentation
	_IsAlternateAccountFlowEnabled uint32
	// whether the mandate needs to be done in the app with web view or in the external browser
	// For Android
	_OpenAbflMandateUrlViaExternal    uint32
	_OpenAbflDigilockerUrlViaExternal uint32
	// For Ios
	_OpenAbflDigilockerUrlViaExternalForIos uint32
	_OpenAbflMandateUrlViaExternalForIos    uint32
	_IsLoanDetailsSelectionV2FlowEnabled    uint32
	// IsAlternateAccountFlowEnabledForLL flag determines if alternate account flow is enabled for the user IN liquiloans journey
	// this variable is marked as quest for AB experimentation
	_IsAlternateAccountFlowEnabledForLL uint32
	// A/B variable for LAMF experimentation
	_ChangeButtonTextDetailsPage              uint32
	_OpenIdfcVkycUrlViaExternalForAndroid     uint32
	_OpenIdfcVkycUrlViaExternalForIos         uint32
	_FedKfsExitUrl                            string
	_FedKfsExitUrlMutex                       *sync.RWMutex
	_LoanDetailsSelectionV2Flow               *LoanDetailsSelectionV2Flow
	_OfferDetailsV3Config                     *OfferDetailsV3Config
	_AbflReferencesAppVersionConstraintConfig *genconfig.AppVersionConstraintConfig
	_InfoItemV3MinVersion                     *genconfig.AppVersionConstraintConfig
}

func (obj *DeeplinkConfig) isInitiateMandateEnrichmentEnabled() bool {
	if atomic.LoadUint32(&obj._IsInitiateMandateEnrichmentEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeeplinkConfig) IsInitiateMandateEnrichmentEnabled(ctx context.Context) bool {
	defVal := obj.isInitiateMandateEnrichmentEnabled()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsInitiateMandateEnrichmentEnabled"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// IsAlternateAccountFlowEnabled flag determines if alternate account flow is enabled for the user
// this variable is marked as quest for AB experimentation
func (obj *DeeplinkConfig) isAlternateAccountFlowEnabled() bool {
	if atomic.LoadUint32(&obj._IsAlternateAccountFlowEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsAlternateAccountFlowEnabled flag determines if alternate account flow is enabled for the user
// this variable is marked as quest for AB experimentation
func (obj *DeeplinkConfig) IsAlternateAccountFlowEnabled(ctx context.Context) bool {
	defVal := obj.isAlternateAccountFlowEnabled()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsAlternateAccountFlowEnabled"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// whether the mandate needs to be done in the app with web view or in the external browser
// For Android
func (obj *DeeplinkConfig) OpenAbflMandateUrlViaExternal() bool {
	if atomic.LoadUint32(&obj._OpenAbflMandateUrlViaExternal) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeeplinkConfig) OpenAbflDigilockerUrlViaExternal() bool {
	if atomic.LoadUint32(&obj._OpenAbflDigilockerUrlViaExternal) == 0 {
		return false
	} else {
		return true
	}
}

// For Ios
func (obj *DeeplinkConfig) OpenAbflDigilockerUrlViaExternalForIos() bool {
	if atomic.LoadUint32(&obj._OpenAbflDigilockerUrlViaExternalForIos) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeeplinkConfig) OpenAbflMandateUrlViaExternalForIos() bool {
	if atomic.LoadUint32(&obj._OpenAbflMandateUrlViaExternalForIos) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeeplinkConfig) isLoanDetailsSelectionV2FlowEnabled() bool {
	if atomic.LoadUint32(&obj._IsLoanDetailsSelectionV2FlowEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeeplinkConfig) IsLoanDetailsSelectionV2FlowEnabled(ctx context.Context) bool {
	defVal := obj.isLoanDetailsSelectionV2FlowEnabled()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsLoanDetailsSelectionV2FlowEnabled"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// IsAlternateAccountFlowEnabledForLL flag determines if alternate account flow is enabled for the user IN liquiloans journey
// this variable is marked as quest for AB experimentation
func (obj *DeeplinkConfig) isAlternateAccountFlowEnabledForLL() bool {
	if atomic.LoadUint32(&obj._IsAlternateAccountFlowEnabledForLL) == 0 {
		return false
	} else {
		return true
	}
}

// IsAlternateAccountFlowEnabledForLL flag determines if alternate account flow is enabled for the user IN liquiloans journey
// this variable is marked as quest for AB experimentation
func (obj *DeeplinkConfig) IsAlternateAccountFlowEnabledForLL(ctx context.Context) bool {
	defVal := obj.isAlternateAccountFlowEnabledForLL()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsAlternateAccountFlowEnabledForLL"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// A/B variable for LAMF experimentation
func (obj *DeeplinkConfig) changeButtonTextDetailsPage() bool {
	if atomic.LoadUint32(&obj._ChangeButtonTextDetailsPage) == 0 {
		return false
	} else {
		return true
	}
}

// A/B variable for LAMF experimentation
func (obj *DeeplinkConfig) ChangeButtonTextDetailsPage(ctx context.Context) bool {
	defVal := obj.changeButtonTextDetailsPage()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "ChangeButtonTextDetailsPage"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *DeeplinkConfig) OpenIdfcVkycUrlViaExternalForAndroid() bool {
	if atomic.LoadUint32(&obj._OpenIdfcVkycUrlViaExternalForAndroid) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeeplinkConfig) OpenIdfcVkycUrlViaExternalForIos() bool {
	if atomic.LoadUint32(&obj._OpenIdfcVkycUrlViaExternalForIos) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DeeplinkConfig) FedKfsExitUrl() string {
	obj._FedKfsExitUrlMutex.RLock()
	defer obj._FedKfsExitUrlMutex.RUnlock()
	return obj._FedKfsExitUrl
}
func (obj *DeeplinkConfig) LoanDetailsSelectionV2Flow() *LoanDetailsSelectionV2Flow {
	return obj._LoanDetailsSelectionV2Flow
}
func (obj *DeeplinkConfig) OfferDetailsV3Config() *OfferDetailsV3Config {
	return obj._OfferDetailsV3Config
}
func (obj *DeeplinkConfig) AbflReferencesAppVersionConstraintConfig() *genconfig.AppVersionConstraintConfig {
	return obj._AbflReferencesAppVersionConstraintConfig
}
func (obj *DeeplinkConfig) InfoItemV3MinVersion() *genconfig.AppVersionConstraintConfig {
	return obj._InfoItemV3MinVersion
}

type LoanDetailsSelectionV2Flow struct {
	callbacks                *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                 questsdk.Client
	questFieldPath           string
	_IsEnabled               uint32
	_DefaultAmountPercentage *syncmap.Map[string, float64]
	// vendor and loan program level config. For vendors where single program is live (e.g: Federal, IDFC and ABFL), vendor name will used to enable the flow
	// for others, loan programs in the list will be checked and used to enable the flow
	_EnableLoanPrograms      roarray.ROArray[string]
	_EnableLoanProgramsMutex *sync.RWMutex
}

func (obj *LoanDetailsSelectionV2Flow) isEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *LoanDetailsSelectionV2Flow) IsEnabled(ctx context.Context) bool {
	defVal := obj.isEnabled()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsEnabled"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *LoanDetailsSelectionV2Flow) DefaultAmountPercentage() *syncmap.Map[string, float64] {
	return obj._DefaultAmountPercentage
}

// vendor and loan program level config. For vendors where single program is live (e.g: Federal, IDFC and ABFL), vendor name will used to enable the flow
// for others, loan programs in the list will be checked and used to enable the flow
func (obj *LoanDetailsSelectionV2Flow) EnableLoanPrograms() roarray.ROArray[string] {
	obj._EnableLoanProgramsMutex.RLock()
	defer obj._EnableLoanProgramsMutex.RUnlock()
	return obj._EnableLoanPrograms
}

type OfferDetailsV3Config struct {
	callbacks                   *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                    questsdk.Client
	questFieldPath              string
	_IsEnabled                  uint32
	_SkipAmountSelectionScreen  uint32
	_ShowInterestRate           uint32
	_ShowZeroPreClosureTag      uint32
	_VendorLoanProgramMap       *syncmap.Map[string, bool]
	_AppVersionConstraintConfig *genconfig.AppVersionConstraintConfig
}

func (obj *OfferDetailsV3Config) isEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OfferDetailsV3Config) IsEnabled(ctx context.Context) bool {
	defVal := obj.isEnabled()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsEnabled"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *OfferDetailsV3Config) skipAmountSelectionScreen() bool {
	if atomic.LoadUint32(&obj._SkipAmountSelectionScreen) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OfferDetailsV3Config) SkipAmountSelectionScreen(ctx context.Context) bool {
	defVal := obj.skipAmountSelectionScreen()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "SkipAmountSelectionScreen"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *OfferDetailsV3Config) showInterestRate() bool {
	if atomic.LoadUint32(&obj._ShowInterestRate) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OfferDetailsV3Config) ShowInterestRate(ctx context.Context) bool {
	defVal := obj.showInterestRate()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "ShowInterestRate"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *OfferDetailsV3Config) showZeroPreClosureTag() bool {
	if atomic.LoadUint32(&obj._ShowZeroPreClosureTag) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OfferDetailsV3Config) ShowZeroPreClosureTag(ctx context.Context) bool {
	defVal := obj.showZeroPreClosureTag()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "ShowZeroPreClosureTag"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *OfferDetailsV3Config) VendorLoanProgramMap() *syncmap.Map[string, bool] {
	return obj._VendorLoanProgramMap
}
func (obj *OfferDetailsV3Config) AppVersionConstraintConfig() *genconfig.AppVersionConstraintConfig {
	return obj._AppVersionConstraintConfig
}

type CreditReportConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// StaleExperianReportThresholdDays defines the number of days after which a credit report is considered stale
	// and will be refreshed when accessed again.
	_StaleExperianReportThresholdDays int32
	// UseCreditReportV2 tells lending servers if we want to use creditreportv2 code or user/credit_report code
	_UseCreditReportV2 uint32
}

// StaleExperianReportThresholdDays defines the number of days after which a credit report is considered stale
// and will be refreshed when accessed again.
func (obj *CreditReportConfig) StaleExperianReportThresholdDays() int64 {
	return int64(atomic.LoadInt32(&obj._StaleExperianReportThresholdDays))
}

// UseCreditReportV2 tells lending servers if we want to use creditreportv2 code or user/credit_report code
func (obj *CreditReportConfig) UseCreditReportV2() bool {
	if atomic.LoadUint32(&obj._UseCreditReportV2) == 0 {
		return false
	} else {
		return true
	}
}

type MandateConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_LiquiloansMandateConfig *LiquiloansMandateConfig
}

func (obj *MandateConfig) LiquiloansMandateConfig() *LiquiloansMandateConfig {
	return obj._LiquiloansMandateConfig
}

type LiquiloansMandateConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// denotes if cool off check is enabled between mandate attempts
	_IsMandateCoolOffCheckEnabled            uint32
	_MinCoolOffMinutesBetweenMandateAttempts int64
	_IsMandateRequestBasedCountLogicEnabled  bool
	_IsPreviousMandateStatusCheckEnabled     bool
}

// denotes if cool off check is enabled between mandate attempts
func (obj *LiquiloansMandateConfig) IsMandateCoolOffCheckEnabled() bool {
	if atomic.LoadUint32(&obj._IsMandateCoolOffCheckEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *LiquiloansMandateConfig) MinCoolOffMinutesBetweenMandateAttempts() int64 {
	return obj._MinCoolOffMinutesBetweenMandateAttempts
}
func (obj *LiquiloansMandateConfig) IsMandateRequestBasedCountLogicEnabled() bool {
	return obj._IsMandateRequestBasedCountLogicEnabled
}
func (obj *LiquiloansMandateConfig) IsPreviousMandateStatusCheckEnabled() bool {
	return obj._IsPreviousMandateStatusCheckEnabled
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// A flag to determine if the debug message in Status is to be trimmed
	_TrimDebugMessageFromStatus             uint32
	_IsFldgLoanOverdueSherlockBannerEnabled uint32
	_IsOffAppPaymentV2Enabled               uint32
	_IsRecommendationEngineEnabled          uint32
	_HideIdfcOffer                          uint32
	_IsAbflKfsGenerationV2                  uint32
	// when this flag is true we are moving SG, LDC's RTD program to creation of loec and applicant to realtime
	_MovePreQualToRealTime uint32
	// IsSgDigilockerEnabled is a flag to determine if the digilocker flow is enabled for SG
	_IsSgDigilockerEnabled uint32
	// Flag to control liveness image format - true for presigned URL, false for base64
	_UseBase64Image                  uint32
	_IsLdcApplicationMovementEnabled uint32
	_PreferUPIMandateTypeForLDC      uint32
}

// A flag to determine if the debug message in Status is to be trimmed
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	if atomic.LoadUint32(&obj._TrimDebugMessageFromStatus) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) IsFldgLoanOverdueSherlockBannerEnabled() bool {
	if atomic.LoadUint32(&obj._IsFldgLoanOverdueSherlockBannerEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) IsOffAppPaymentV2Enabled() bool {
	if atomic.LoadUint32(&obj._IsOffAppPaymentV2Enabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) IsRecommendationEngineEnabled() bool {
	if atomic.LoadUint32(&obj._IsRecommendationEngineEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) HideIdfcOffer() bool {
	if atomic.LoadUint32(&obj._HideIdfcOffer) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) IsAbflKfsGenerationV2() bool {
	if atomic.LoadUint32(&obj._IsAbflKfsGenerationV2) == 0 {
		return false
	} else {
		return true
	}
}

// when this flag is true we are moving SG, LDC's RTD program to creation of loec and applicant to realtime
func (obj *Flags) MovePreQualToRealTime() bool {
	if atomic.LoadUint32(&obj._MovePreQualToRealTime) == 0 {
		return false
	} else {
		return true
	}
}

// IsSgDigilockerEnabled is a flag to determine if the digilocker flow is enabled for SG
func (obj *Flags) IsSgDigilockerEnabled() bool {
	if atomic.LoadUint32(&obj._IsSgDigilockerEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to control liveness image format - true for presigned URL, false for base64
func (obj *Flags) UseBase64Image() bool {
	if atomic.LoadUint32(&obj._UseBase64Image) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) IsLdcApplicationMovementEnabled() bool {
	if atomic.LoadUint32(&obj._IsLdcApplicationMovementEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) PreferUPIMandateTypeForLDC() bool {
	if atomic.LoadUint32(&obj._PreferUPIMandateTypeForLDC) == 0 {
		return false
	} else {
		return true
	}
}

type Prepay struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// denotes a map of lender to the prepay black out period configuration for the loans disbursed with given lender.
	// key of the map is the string value of palPb.Vendor enum
	_LenderToPrepayBlackOutConfig *syncmap.Map[string, *PrepayBlackOutPeriodConfig]
	// denotes a map of lender to the pre closure black out period configuration for the loans disbursed with given lender.
	// key of the map is the string value of palPb.Vendor enum
	_LenderToPreClosureBlackOutConfig *syncmap.Map[string, *PreClosureBlackOutPeriodConfig]
	_PoolAccounts                     *common.PoolAccountDetails
	_UseIDFCLoanCancellationV2        bool
}

// denotes a map of lender to the prepay black out period configuration for the loans disbursed with given lender.
// key of the map is the string value of palPb.Vendor enum
func (obj *Prepay) LenderToPrepayBlackOutConfig() *syncmap.Map[string, *PrepayBlackOutPeriodConfig] {
	return obj._LenderToPrepayBlackOutConfig
}

// denotes a map of lender to the pre closure black out period configuration for the loans disbursed with given lender.
// key of the map is the string value of palPb.Vendor enum
func (obj *Prepay) LenderToPreClosureBlackOutConfig() *syncmap.Map[string, *PreClosureBlackOutPeriodConfig] {
	return obj._LenderToPreClosureBlackOutConfig
}
func (obj *Prepay) PoolAccounts() *common.PoolAccountDetails {
	return obj._PoolAccounts
}
func (obj *Prepay) UseIDFCLoanCancellationV2() bool {
	return obj._UseIDFCLoanCancellationV2
}

type PrepayBlackOutPeriodConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// denotes the number of days before the emi due date from which the prepayments should be blocked.
	_BlockDurationBeforeEmiDueDateInDays int32
	// denotes the number of days after the emi due date until which the prepayments should be blocked.
	_BlockDurationAfterEmiDueDateInDays int32
	// denotes the number of days before the emi grace end date from which the prepayments should be blocked.
	_BlockDurationBeforeEmiGraceEndDateInDays int32
	// denotes the number of days after the emi grace end date until which the prepayments should be blocked.
	_BlockDurationAfterEmiGraceEndDateInDays int32
}

// denotes the number of days before the emi due date from which the prepayments should be blocked.
func (obj *PrepayBlackOutPeriodConfig) BlockDurationBeforeEmiDueDateInDays() int32 {
	return int32(atomic.LoadInt32(&obj._BlockDurationBeforeEmiDueDateInDays))
}

// denotes the number of days after the emi due date until which the prepayments should be blocked.
func (obj *PrepayBlackOutPeriodConfig) BlockDurationAfterEmiDueDateInDays() int32 {
	return int32(atomic.LoadInt32(&obj._BlockDurationAfterEmiDueDateInDays))
}

// denotes the number of days before the emi grace end date from which the prepayments should be blocked.
func (obj *PrepayBlackOutPeriodConfig) BlockDurationBeforeEmiGraceEndDateInDays() int32 {
	return int32(atomic.LoadInt32(&obj._BlockDurationBeforeEmiGraceEndDateInDays))
}

// denotes the number of days after the emi grace end date until which the prepayments should be blocked.
func (obj *PrepayBlackOutPeriodConfig) BlockDurationAfterEmiGraceEndDateInDays() int32 {
	return int32(atomic.LoadInt32(&obj._BlockDurationAfterEmiGraceEndDateInDays))
}

type PreClosureBlackOutPeriodConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// denotes the start hour of the day from which the pre closure should be blocked.
	_BlockStartHour int64
	// denotes the end hour of the day until which the pre closure should be blocked.
	_BlockEndHour int64
}

// denotes the start hour of the day from which the pre closure should be blocked.
func (obj *PreClosureBlackOutPeriodConfig) BlockStartHour() int {
	return int(atomic.LoadInt64(&obj._BlockStartHour))
}

// denotes the end hour of the day until which the pre closure should be blocked.
func (obj *PreClosureBlackOutPeriodConfig) BlockEndHour() int {
	return int(atomic.LoadInt64(&obj._BlockEndHour))
}

type VendorProgramLevelFeature struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_VendorProgramActiveMap          *syncmap.Map[string, *FeatureConstraint]
	_NonFiCoreVendorProgramActiveMap *syncmap.Map[string, *FeatureConstraint]
	_DownTimeConfig                  *syncmap.Map[string, *DownTimeConfig]
}

func (obj *VendorProgramLevelFeature) VendorProgramActiveMap() *syncmap.Map[string, *FeatureConstraint] {
	return obj._VendorProgramActiveMap
}
func (obj *VendorProgramLevelFeature) NonFiCoreVendorProgramActiveMap() *syncmap.Map[string, *FeatureConstraint] {
	return obj._NonFiCoreVendorProgramActiveMap
}
func (obj *VendorProgramLevelFeature) DownTimeConfig() *syncmap.Map[string, *DownTimeConfig] {
	return obj._DownTimeConfig
}

type FeatureConstraint struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAllowed          uint32
	_AllowedGroups      roarray.ROArray[string]
	_AllowedGroupsMutex *sync.RWMutex
}

func (obj *FeatureConstraint) IsAllowed() bool {
	if atomic.LoadUint32(&obj._IsAllowed) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FeatureConstraint) AllowedGroups() roarray.ROArray[string] {
	obj._AllowedGroupsMutex.RLock()
	defer obj._AllowedGroupsMutex.RUnlock()
	return obj._AllowedGroups
}

type DownTimeConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// start and end time in format "DD-MM-YYYYTHH:MM:SS"
	_Start      string
	_StartMutex *sync.RWMutex
	_End        string
	_EndMutex   *sync.RWMutex
}

// start and end time in format "DD-MM-YYYYTHH:MM:SS"
func (obj *DownTimeConfig) Start() string {
	obj._StartMutex.RLock()
	defer obj._StartMutex.RUnlock()
	return obj._Start
}
func (obj *DownTimeConfig) End() string {
	obj._EndMutex.RLock()
	defer obj._EndMutex.RUnlock()
	return obj._End
}

type LopeOverrideConfig struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	// the position index (0-based) where the target loan header should be placed in the priority list
	// 0 means highest priority (first position), 1 means second position, and so on
	_TargetPosition int64
	// boolean flag to enable the loan offer prioritisation override policy
	_IsEnabled uint32
	// boolean flag to determine if this override config applies to fiCore or NonFiCore
	_IsForFiCore                        uint32
	_ShowSgEligibilityOverAbflSoftOffer uint32
	_ShowSgEligibilityOverMvSoftOffer   uint32
	_TargetLoanHeader                   *LoanHeader
	_LoanPriorityOrderNonFiCore         []*common.LoanOptionPriority
	_LoanPriorityOrderFiCore            []*common.LoanOptionPriority
}

// the position index (0-based) where the target loan header should be placed in the priority list
// 0 means highest priority (first position), 1 means second position, and so on
func (obj *LopeOverrideConfig) targetPosition() int {
	return int(atomic.LoadInt64(&obj._TargetPosition))
}

// the position index (0-based) where the target loan header should be placed in the priority list
// 0 means highest priority (first position), 1 means second position, and so on
func (obj *LopeOverrideConfig) TargetPosition(ctx context.Context) int {
	defVal := obj.targetPosition()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "TargetPosition"}, defVal)
	val, ok := res.(int64)
	if ok {
		return int(val)
	}
	return defVal
}

// boolean flag to enable the loan offer prioritisation override policy
func (obj *LopeOverrideConfig) isEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// boolean flag to enable the loan offer prioritisation override policy
func (obj *LopeOverrideConfig) IsEnabled(ctx context.Context) bool {
	defVal := obj.isEnabled()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsEnabled"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// boolean flag to determine if this override config applies to fiCore or NonFiCore
func (obj *LopeOverrideConfig) isForFiCore() bool {
	if atomic.LoadUint32(&obj._IsForFiCore) == 0 {
		return false
	} else {
		return true
	}
}

// boolean flag to determine if this override config applies to fiCore or NonFiCore
func (obj *LopeOverrideConfig) IsForFiCore(ctx context.Context) bool {
	defVal := obj.isForFiCore()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IsForFiCore"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *LopeOverrideConfig) showSgEligibilityOverAbflSoftOffer() bool {
	if atomic.LoadUint32(&obj._ShowSgEligibilityOverAbflSoftOffer) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *LopeOverrideConfig) ShowSgEligibilityOverAbflSoftOffer(ctx context.Context) bool {
	defVal := obj.showSgEligibilityOverAbflSoftOffer()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "ShowSgEligibilityOverAbflSoftOffer"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *LopeOverrideConfig) showSgEligibilityOverMvSoftOffer() bool {
	if atomic.LoadUint32(&obj._ShowSgEligibilityOverMvSoftOffer) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *LopeOverrideConfig) ShowSgEligibilityOverMvSoftOffer(ctx context.Context) bool {
	defVal := obj.showSgEligibilityOverMvSoftOffer()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "ShowSgEligibilityOverMvSoftOffer"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *LopeOverrideConfig) TargetLoanHeader() *LoanHeader {
	return obj._TargetLoanHeader
}
func (obj *LopeOverrideConfig) LoanPriorityOrderNonFiCore() []*common.LoanOptionPriority {
	return obj._LoanPriorityOrderNonFiCore
}
func (obj *LopeOverrideConfig) LoanPriorityOrderFiCore() []*common.LoanOptionPriority {
	return obj._LoanPriorityOrderFiCore
}

type LoanHeader struct {
	callbacks         *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk          questsdk.Client
	questFieldPath    string
	_LoanProgram      string
	_LoanProgramMutex *sync.RWMutex
	_Vendor           string
	_VendorMutex      *sync.RWMutex
}

func (obj *LoanHeader) loanProgram() string {
	obj._LoanProgramMutex.RLock()
	defer obj._LoanProgramMutex.RUnlock()
	return obj._LoanProgram
}
func (obj *LoanHeader) LoanProgram(ctx context.Context) string {
	defVal := obj.loanProgram()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "LoanProgram"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *LoanHeader) vendor() string {
	obj._VendorMutex.RLock()
	defer obj._VendorMutex.RUnlock()
	return obj._Vendor
}
func (obj *LoanHeader) Vendor(ctx context.Context) string {
	defVal := obj.vendor()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Vendor"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func NewDeeplinkConfig() (_obj *DeeplinkConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DeeplinkConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isinitiatemandateenrichmentenabled"] = _obj.SetIsInitiateMandateEnrichmentEnabled
	_setters["isalternateaccountflowenabled"] = _obj.SetIsAlternateAccountFlowEnabled
	_setters["openabflmandateurlviaexternal"] = _obj.SetOpenAbflMandateUrlViaExternal
	_setters["openabfldigilockerurlviaexternal"] = _obj.SetOpenAbflDigilockerUrlViaExternal
	_setters["openabfldigilockerurlviaexternalforios"] = _obj.SetOpenAbflDigilockerUrlViaExternalForIos
	_setters["openabflmandateurlviaexternalforios"] = _obj.SetOpenAbflMandateUrlViaExternalForIos
	_setters["isloandetailsselectionv2flowenabled"] = _obj.SetIsLoanDetailsSelectionV2FlowEnabled
	_setters["isalternateaccountflowenabledforll"] = _obj.SetIsAlternateAccountFlowEnabledForLL
	_setters["changebuttontextdetailspage"] = _obj.SetChangeButtonTextDetailsPage
	_setters["openidfcvkycurlviaexternalforandroid"] = _obj.SetOpenIdfcVkycUrlViaExternalForAndroid
	_setters["openidfcvkycurlviaexternalforios"] = _obj.SetOpenIdfcVkycUrlViaExternalForIos
	_setters["fedkfsexiturl"] = _obj.SetFedKfsExitUrl
	_obj._FedKfsExitUrlMutex = &sync.RWMutex{}
	_LoanDetailsSelectionV2Flow, _fieldSetters := NewLoanDetailsSelectionV2Flow()
	_obj._LoanDetailsSelectionV2Flow = _LoanDetailsSelectionV2Flow
	helper.AddFieldSetters("loandetailsselectionv2flow", _fieldSetters, _setters)
	_OfferDetailsV3Config, _fieldSetters := NewOfferDetailsV3Config()
	_obj._OfferDetailsV3Config = _OfferDetailsV3Config
	helper.AddFieldSetters("offerdetailsv3config", _fieldSetters, _setters)
	_AbflReferencesAppVersionConstraintConfig, _fieldSetters := genconfig.NewAppVersionConstraintConfig()
	_obj._AbflReferencesAppVersionConstraintConfig = _AbflReferencesAppVersionConstraintConfig
	helper.AddFieldSetters("abflreferencesappversionconstraintconfig", _fieldSetters, _setters)
	_InfoItemV3MinVersion, _fieldSetters := genconfig.NewAppVersionConstraintConfig()
	_obj._InfoItemV3MinVersion = _InfoItemV3MinVersion
	helper.AddFieldSetters("infoitemv3minversion", _fieldSetters, _setters)
	return _obj, _setters
}

func NewDeeplinkConfigWithQuest(questFieldPath string) (_obj *DeeplinkConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DeeplinkConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isinitiatemandateenrichmentenabled"] = _obj.SetIsInitiateMandateEnrichmentEnabled
	_setters["isalternateaccountflowenabled"] = _obj.SetIsAlternateAccountFlowEnabled
	_setters["openabflmandateurlviaexternal"] = _obj.SetOpenAbflMandateUrlViaExternal
	_setters["openabfldigilockerurlviaexternal"] = _obj.SetOpenAbflDigilockerUrlViaExternal
	_setters["openabfldigilockerurlviaexternalforios"] = _obj.SetOpenAbflDigilockerUrlViaExternalForIos
	_setters["openabflmandateurlviaexternalforios"] = _obj.SetOpenAbflMandateUrlViaExternalForIos
	_setters["isloandetailsselectionv2flowenabled"] = _obj.SetIsLoanDetailsSelectionV2FlowEnabled
	_setters["isalternateaccountflowenabledforll"] = _obj.SetIsAlternateAccountFlowEnabledForLL
	_setters["changebuttontextdetailspage"] = _obj.SetChangeButtonTextDetailsPage
	_setters["openidfcvkycurlviaexternalforandroid"] = _obj.SetOpenIdfcVkycUrlViaExternalForAndroid
	_setters["openidfcvkycurlviaexternalforios"] = _obj.SetOpenIdfcVkycUrlViaExternalForIos
	_setters["fedkfsexiturl"] = _obj.SetFedKfsExitUrl
	_obj._FedKfsExitUrlMutex = &sync.RWMutex{}
	_LoanDetailsSelectionV2Flow, _fieldSetters := NewLoanDetailsSelectionV2FlowWithQuest(questFieldPath + "/" + "LoanDetailsSelectionV2Flow")
	_obj._LoanDetailsSelectionV2Flow = _LoanDetailsSelectionV2Flow
	helper.AddFieldSetters("loandetailsselectionv2flow", _fieldSetters, _setters)
	_OfferDetailsV3Config, _fieldSetters := NewOfferDetailsV3ConfigWithQuest(questFieldPath + "/" + "OfferDetailsV3Config")
	_obj._OfferDetailsV3Config = _OfferDetailsV3Config
	helper.AddFieldSetters("offerdetailsv3config", _fieldSetters, _setters)
	_AbflReferencesAppVersionConstraintConfig, _fieldSetters := genconfig.NewAppVersionConstraintConfig()
	_obj._AbflReferencesAppVersionConstraintConfig = _AbflReferencesAppVersionConstraintConfig
	helper.AddFieldSetters("abflreferencesappversionconstraintconfig", _fieldSetters, _setters)
	_InfoItemV3MinVersion, _fieldSetters := genconfig.NewAppVersionConstraintConfig()
	_obj._InfoItemV3MinVersion = _InfoItemV3MinVersion
	helper.AddFieldSetters("infoitemv3minversion", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *DeeplinkConfig) Init(questFieldPath string) {
	newObj, _ := NewDeeplinkConfig()
	*obj = *newObj
}
func (obj *DeeplinkConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewDeeplinkConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *DeeplinkConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._LoanDetailsSelectionV2Flow.SetQuestSDK(questSdk)
	obj._OfferDetailsV3Config.SetQuestSDK(questSdk)
}

func (obj *DeeplinkConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DeeplinkConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsInitiateMandateEnrichmentEnabled",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsAlternateAccountFlowEnabled",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsLoanDetailsSelectionV2FlowEnabled",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsAlternateAccountFlowEnabledForLL",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "ChangeButtonTextDetailsPage",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._LoanDetailsSelectionV2Flow.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._OfferDetailsV3Config.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *DeeplinkConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.DeeplinkConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DeeplinkConfig) setDynamicField(v *common.DeeplinkConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isinitiatemandateenrichmentenabled":
		return obj.SetIsInitiateMandateEnrichmentEnabled(v.IsInitiateMandateEnrichmentEnabled, true, nil)
	case "isalternateaccountflowenabled":
		return obj.SetIsAlternateAccountFlowEnabled(v.IsAlternateAccountFlowEnabled, true, nil)
	case "openabflmandateurlviaexternal":
		return obj.SetOpenAbflMandateUrlViaExternal(v.OpenAbflMandateUrlViaExternal, true, nil)
	case "openabfldigilockerurlviaexternal":
		return obj.SetOpenAbflDigilockerUrlViaExternal(v.OpenAbflDigilockerUrlViaExternal, true, nil)
	case "openabfldigilockerurlviaexternalforios":
		return obj.SetOpenAbflDigilockerUrlViaExternalForIos(v.OpenAbflDigilockerUrlViaExternalForIos, true, nil)
	case "openabflmandateurlviaexternalforios":
		return obj.SetOpenAbflMandateUrlViaExternalForIos(v.OpenAbflMandateUrlViaExternalForIos, true, nil)
	case "isloandetailsselectionv2flowenabled":
		return obj.SetIsLoanDetailsSelectionV2FlowEnabled(v.IsLoanDetailsSelectionV2FlowEnabled, true, nil)
	case "isalternateaccountflowenabledforll":
		return obj.SetIsAlternateAccountFlowEnabledForLL(v.IsAlternateAccountFlowEnabledForLL, true, nil)
	case "changebuttontextdetailspage":
		return obj.SetChangeButtonTextDetailsPage(v.ChangeButtonTextDetailsPage, true, nil)
	case "openidfcvkycurlviaexternalforandroid":
		return obj.SetOpenIdfcVkycUrlViaExternalForAndroid(v.OpenIdfcVkycUrlViaExternalForAndroid, true, nil)
	case "openidfcvkycurlviaexternalforios":
		return obj.SetOpenIdfcVkycUrlViaExternalForIos(v.OpenIdfcVkycUrlViaExternalForIos, true, nil)
	case "fedkfsexiturl":
		return obj.SetFedKfsExitUrl(v.FedKfsExitUrl, true, nil)
	case "loandetailsselectionv2flow":
		return obj._LoanDetailsSelectionV2Flow.Set(v.LoanDetailsSelectionV2Flow, true, path)
	case "offerdetailsv3config":
		return obj._OfferDetailsV3Config.Set(v.OfferDetailsV3Config, true, path)
	case "abflreferencesappversionconstraintconfig":
		return obj._AbflReferencesAppVersionConstraintConfig.Set(v.AbflReferencesAppVersionConstraintConfig, true, path)
	case "infoitemv3minversion":
		return obj._InfoItemV3MinVersion.Set(v.InfoItemV3MinVersion, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DeeplinkConfig) setDynamicFields(v *common.DeeplinkConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsInitiateMandateEnrichmentEnabled(v.IsInitiateMandateEnrichmentEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsAlternateAccountFlowEnabled(v.IsAlternateAccountFlowEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOpenAbflMandateUrlViaExternal(v.OpenAbflMandateUrlViaExternal, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOpenAbflDigilockerUrlViaExternal(v.OpenAbflDigilockerUrlViaExternal, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOpenAbflDigilockerUrlViaExternalForIos(v.OpenAbflDigilockerUrlViaExternalForIos, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOpenAbflMandateUrlViaExternalForIos(v.OpenAbflMandateUrlViaExternalForIos, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsLoanDetailsSelectionV2FlowEnabled(v.IsLoanDetailsSelectionV2FlowEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsAlternateAccountFlowEnabledForLL(v.IsAlternateAccountFlowEnabledForLL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetChangeButtonTextDetailsPage(v.ChangeButtonTextDetailsPage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOpenIdfcVkycUrlViaExternalForAndroid(v.OpenIdfcVkycUrlViaExternalForAndroid, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOpenIdfcVkycUrlViaExternalForIos(v.OpenIdfcVkycUrlViaExternalForIos, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFedKfsExitUrl(v.FedKfsExitUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._LoanDetailsSelectionV2Flow.Set(v.LoanDetailsSelectionV2Flow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OfferDetailsV3Config.Set(v.OfferDetailsV3Config, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AbflReferencesAppVersionConstraintConfig.Set(v.AbflReferencesAppVersionConstraintConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InfoItemV3MinVersion.Set(v.InfoItemV3MinVersion, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DeeplinkConfig) setStaticFields(v *common.DeeplinkConfig) error {

	return nil
}

func (obj *DeeplinkConfig) SetIsInitiateMandateEnrichmentEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.IsInitiateMandateEnrichmentEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsInitiateMandateEnrichmentEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsInitiateMandateEnrichmentEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsInitiateMandateEnrichmentEnabled")
	}
	return nil
}
func (obj *DeeplinkConfig) SetIsAlternateAccountFlowEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.IsAlternateAccountFlowEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAlternateAccountFlowEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsAlternateAccountFlowEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAlternateAccountFlowEnabled")
	}
	return nil
}
func (obj *DeeplinkConfig) SetOpenAbflMandateUrlViaExternal(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.OpenAbflMandateUrlViaExternal", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._OpenAbflMandateUrlViaExternal, 1)
	} else {
		atomic.StoreUint32(&obj._OpenAbflMandateUrlViaExternal, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "OpenAbflMandateUrlViaExternal")
	}
	return nil
}
func (obj *DeeplinkConfig) SetOpenAbflDigilockerUrlViaExternal(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.OpenAbflDigilockerUrlViaExternal", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._OpenAbflDigilockerUrlViaExternal, 1)
	} else {
		atomic.StoreUint32(&obj._OpenAbflDigilockerUrlViaExternal, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "OpenAbflDigilockerUrlViaExternal")
	}
	return nil
}
func (obj *DeeplinkConfig) SetOpenAbflDigilockerUrlViaExternalForIos(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.OpenAbflDigilockerUrlViaExternalForIos", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._OpenAbflDigilockerUrlViaExternalForIos, 1)
	} else {
		atomic.StoreUint32(&obj._OpenAbflDigilockerUrlViaExternalForIos, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "OpenAbflDigilockerUrlViaExternalForIos")
	}
	return nil
}
func (obj *DeeplinkConfig) SetOpenAbflMandateUrlViaExternalForIos(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.OpenAbflMandateUrlViaExternalForIos", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._OpenAbflMandateUrlViaExternalForIos, 1)
	} else {
		atomic.StoreUint32(&obj._OpenAbflMandateUrlViaExternalForIos, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "OpenAbflMandateUrlViaExternalForIos")
	}
	return nil
}
func (obj *DeeplinkConfig) SetIsLoanDetailsSelectionV2FlowEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.IsLoanDetailsSelectionV2FlowEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsLoanDetailsSelectionV2FlowEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsLoanDetailsSelectionV2FlowEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsLoanDetailsSelectionV2FlowEnabled")
	}
	return nil
}
func (obj *DeeplinkConfig) SetIsAlternateAccountFlowEnabledForLL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.IsAlternateAccountFlowEnabledForLL", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAlternateAccountFlowEnabledForLL, 1)
	} else {
		atomic.StoreUint32(&obj._IsAlternateAccountFlowEnabledForLL, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAlternateAccountFlowEnabledForLL")
	}
	return nil
}
func (obj *DeeplinkConfig) SetChangeButtonTextDetailsPage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.ChangeButtonTextDetailsPage", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ChangeButtonTextDetailsPage, 1)
	} else {
		atomic.StoreUint32(&obj._ChangeButtonTextDetailsPage, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ChangeButtonTextDetailsPage")
	}
	return nil
}
func (obj *DeeplinkConfig) SetOpenIdfcVkycUrlViaExternalForAndroid(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.OpenIdfcVkycUrlViaExternalForAndroid", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._OpenIdfcVkycUrlViaExternalForAndroid, 1)
	} else {
		atomic.StoreUint32(&obj._OpenIdfcVkycUrlViaExternalForAndroid, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "OpenIdfcVkycUrlViaExternalForAndroid")
	}
	return nil
}
func (obj *DeeplinkConfig) SetOpenIdfcVkycUrlViaExternalForIos(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.OpenIdfcVkycUrlViaExternalForIos", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._OpenIdfcVkycUrlViaExternalForIos, 1)
	} else {
		atomic.StoreUint32(&obj._OpenIdfcVkycUrlViaExternalForIos, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "OpenIdfcVkycUrlViaExternalForIos")
	}
	return nil
}
func (obj *DeeplinkConfig) SetFedKfsExitUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DeeplinkConfig.FedKfsExitUrl", reflect.TypeOf(val))
	}
	obj._FedKfsExitUrlMutex.Lock()
	defer obj._FedKfsExitUrlMutex.Unlock()
	obj._FedKfsExitUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FedKfsExitUrl")
	}
	return nil
}

func NewLoanDetailsSelectionV2Flow() (_obj *LoanDetailsSelectionV2Flow, _setters map[string]dynconf.SetFunc) {
	_obj = &LoanDetailsSelectionV2Flow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled

	_obj._DefaultAmountPercentage = &syncmap.Map[string, float64]{}
	_setters["defaultamountpercentage"] = _obj.SetDefaultAmountPercentage
	_setters["enableloanprograms"] = _obj.SetEnableLoanPrograms
	_obj._EnableLoanProgramsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewLoanDetailsSelectionV2FlowWithQuest(questFieldPath string) (_obj *LoanDetailsSelectionV2Flow, _setters map[string]dynconf.SetFunc) {
	_obj = &LoanDetailsSelectionV2Flow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled

	_obj._DefaultAmountPercentage = &syncmap.Map[string, float64]{}
	_setters["defaultamountpercentage"] = _obj.SetDefaultAmountPercentage
	_setters["enableloanprograms"] = _obj.SetEnableLoanPrograms
	_obj._EnableLoanProgramsMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *LoanDetailsSelectionV2Flow) Init(questFieldPath string) {
	newObj, _ := NewLoanDetailsSelectionV2Flow()
	*obj = *newObj
}
func (obj *LoanDetailsSelectionV2Flow) InitWithQuest(questFieldPath string) {
	newObj, _ := NewLoanDetailsSelectionV2FlowWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *LoanDetailsSelectionV2Flow) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *LoanDetailsSelectionV2Flow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LoanDetailsSelectionV2Flow) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsEnabled",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *LoanDetailsSelectionV2Flow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.LoanDetailsSelectionV2Flow)
	if !ok {
		return fmt.Errorf("invalid data type %v *LoanDetailsSelectionV2Flow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LoanDetailsSelectionV2Flow) setDynamicField(v *common.LoanDetailsSelectionV2Flow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "defaultamountpercentage":
		return obj.SetDefaultAmountPercentage(v.DefaultAmountPercentage, true, path)
	case "enableloanprograms":
		return obj.SetEnableLoanPrograms(v.EnableLoanPrograms, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LoanDetailsSelectionV2Flow) setDynamicFields(v *common.LoanDetailsSelectionV2Flow, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDefaultAmountPercentage(v.DefaultAmountPercentage, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetEnableLoanPrograms(v.EnableLoanPrograms, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LoanDetailsSelectionV2Flow) setStaticFields(v *common.LoanDetailsSelectionV2Flow) error {

	return nil
}

func (obj *LoanDetailsSelectionV2Flow) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *LoanDetailsSelectionV2Flow.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *LoanDetailsSelectionV2Flow) SetDefaultAmountPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]float64)
	if !ok {
		return fmt.Errorf("invalid data type %v *LoanDetailsSelectionV2Flow.DefaultAmountPercentage", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._DefaultAmountPercentage, v, path)
}
func (obj *LoanDetailsSelectionV2Flow) SetEnableLoanPrograms(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *LoanDetailsSelectionV2Flow.EnableLoanPrograms", reflect.TypeOf(val))
	}
	obj._EnableLoanProgramsMutex.Lock()
	defer obj._EnableLoanProgramsMutex.Unlock()
	obj._EnableLoanPrograms = roarray.New[string](v)
	return nil
}

func NewOfferDetailsV3Config() (_obj *OfferDetailsV3Config, _setters map[string]dynconf.SetFunc) {
	_obj = &OfferDetailsV3Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["skipamountselectionscreen"] = _obj.SetSkipAmountSelectionScreen
	_setters["showinterestrate"] = _obj.SetShowInterestRate
	_setters["showzeropreclosuretag"] = _obj.SetShowZeroPreClosureTag

	_obj._VendorLoanProgramMap = &syncmap.Map[string, bool]{}
	_setters["vendorloanprogrammap"] = _obj.SetVendorLoanProgramMap
	_AppVersionConstraintConfig, _fieldSetters := genconfig.NewAppVersionConstraintConfig()
	_obj._AppVersionConstraintConfig = _AppVersionConstraintConfig
	helper.AddFieldSetters("appversionconstraintconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewOfferDetailsV3ConfigWithQuest(questFieldPath string) (_obj *OfferDetailsV3Config, _setters map[string]dynconf.SetFunc) {
	_obj = &OfferDetailsV3Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["skipamountselectionscreen"] = _obj.SetSkipAmountSelectionScreen
	_setters["showinterestrate"] = _obj.SetShowInterestRate
	_setters["showzeropreclosuretag"] = _obj.SetShowZeroPreClosureTag

	_obj._VendorLoanProgramMap = &syncmap.Map[string, bool]{}
	_setters["vendorloanprogrammap"] = _obj.SetVendorLoanProgramMap
	_AppVersionConstraintConfig, _fieldSetters := genconfig.NewAppVersionConstraintConfig()
	_obj._AppVersionConstraintConfig = _AppVersionConstraintConfig
	helper.AddFieldSetters("appversionconstraintconfig", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *OfferDetailsV3Config) Init(questFieldPath string) {
	newObj, _ := NewOfferDetailsV3Config()
	*obj = *newObj
}
func (obj *OfferDetailsV3Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewOfferDetailsV3ConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *OfferDetailsV3Config) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *OfferDetailsV3Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OfferDetailsV3Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsEnabled",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "SkipAmountSelectionScreen",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "ShowInterestRate",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "ShowZeroPreClosureTag",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *OfferDetailsV3Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.OfferDetailsV3Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *OfferDetailsV3Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OfferDetailsV3Config) setDynamicField(v *common.OfferDetailsV3Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "skipamountselectionscreen":
		return obj.SetSkipAmountSelectionScreen(v.SkipAmountSelectionScreen, true, nil)
	case "showinterestrate":
		return obj.SetShowInterestRate(v.ShowInterestRate, true, nil)
	case "showzeropreclosuretag":
		return obj.SetShowZeroPreClosureTag(v.ShowZeroPreClosureTag, true, nil)
	case "vendorloanprogrammap":
		return obj.SetVendorLoanProgramMap(v.VendorLoanProgramMap, true, path)
	case "appversionconstraintconfig":
		return obj._AppVersionConstraintConfig.Set(v.AppVersionConstraintConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OfferDetailsV3Config) setDynamicFields(v *common.OfferDetailsV3Config, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipAmountSelectionScreen(v.SkipAmountSelectionScreen, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShowInterestRate(v.ShowInterestRate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShowZeroPreClosureTag(v.ShowZeroPreClosureTag, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVendorLoanProgramMap(v.VendorLoanProgramMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AppVersionConstraintConfig.Set(v.AppVersionConstraintConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OfferDetailsV3Config) setStaticFields(v *common.OfferDetailsV3Config) error {

	return nil
}

func (obj *OfferDetailsV3Config) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OfferDetailsV3Config.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *OfferDetailsV3Config) SetSkipAmountSelectionScreen(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OfferDetailsV3Config.SkipAmountSelectionScreen", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipAmountSelectionScreen, 1)
	} else {
		atomic.StoreUint32(&obj._SkipAmountSelectionScreen, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipAmountSelectionScreen")
	}
	return nil
}
func (obj *OfferDetailsV3Config) SetShowInterestRate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OfferDetailsV3Config.ShowInterestRate", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShowInterestRate, 1)
	} else {
		atomic.StoreUint32(&obj._ShowInterestRate, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShowInterestRate")
	}
	return nil
}
func (obj *OfferDetailsV3Config) SetShowZeroPreClosureTag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OfferDetailsV3Config.ShowZeroPreClosureTag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShowZeroPreClosureTag, 1)
	} else {
		atomic.StoreUint32(&obj._ShowZeroPreClosureTag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShowZeroPreClosureTag")
	}
	return nil
}
func (obj *OfferDetailsV3Config) SetVendorLoanProgramMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OfferDetailsV3Config.VendorLoanProgramMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._VendorLoanProgramMap, v, path)
}

func NewCreditReportConfig() (_obj *CreditReportConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CreditReportConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["staleexperianreportthresholddays"] = _obj.SetStaleExperianReportThresholdDays
	_setters["usecreditreportv2"] = _obj.SetUseCreditReportV2
	return _obj, _setters
}

func (obj *CreditReportConfig) Init() {
	newObj, _ := NewCreditReportConfig()
	*obj = *newObj
}

func (obj *CreditReportConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CreditReportConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.CreditReportConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditReportConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CreditReportConfig) setDynamicField(v *common.CreditReportConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "staleexperianreportthresholddays":
		return obj.SetStaleExperianReportThresholdDays(v.StaleExperianReportThresholdDays, true, nil)
	case "usecreditreportv2":
		return obj.SetUseCreditReportV2(v.UseCreditReportV2, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CreditReportConfig) setDynamicFields(v *common.CreditReportConfig, dynamic bool, path []string) (err error) {

	err = obj.SetStaleExperianReportThresholdDays(v.StaleExperianReportThresholdDays, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseCreditReportV2(v.UseCreditReportV2, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CreditReportConfig) setStaticFields(v *common.CreditReportConfig) error {

	return nil
}

func (obj *CreditReportConfig) SetStaleExperianReportThresholdDays(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditReportConfig.StaleExperianReportThresholdDays", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._StaleExperianReportThresholdDays, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "StaleExperianReportThresholdDays")
	}
	return nil
}
func (obj *CreditReportConfig) SetUseCreditReportV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditReportConfig.UseCreditReportV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseCreditReportV2, 1)
	} else {
		atomic.StoreUint32(&obj._UseCreditReportV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseCreditReportV2")
	}
	return nil
}

func NewMandateConfig() (_obj *MandateConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &MandateConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_LiquiloansMandateConfig, _fieldSetters := NewLiquiloansMandateConfig()
	_obj._LiquiloansMandateConfig = _LiquiloansMandateConfig
	helper.AddFieldSetters("liquiloansmandateconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *MandateConfig) Init() {
	newObj, _ := NewMandateConfig()
	*obj = *newObj
}

func (obj *MandateConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MandateConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.MandateConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *MandateConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MandateConfig) setDynamicField(v *common.MandateConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "liquiloansmandateconfig":
		return obj._LiquiloansMandateConfig.Set(v.LiquiloansMandateConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MandateConfig) setDynamicFields(v *common.MandateConfig, dynamic bool, path []string) (err error) {

	err = obj._LiquiloansMandateConfig.Set(v.LiquiloansMandateConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *MandateConfig) setStaticFields(v *common.MandateConfig) error {

	return nil
}

func NewLiquiloansMandateConfig() (_obj *LiquiloansMandateConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LiquiloansMandateConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ismandatecooloffcheckenabled"] = _obj.SetIsMandateCoolOffCheckEnabled
	return _obj, _setters
}

func (obj *LiquiloansMandateConfig) Init() {
	newObj, _ := NewLiquiloansMandateConfig()
	*obj = *newObj
}

func (obj *LiquiloansMandateConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LiquiloansMandateConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.LiquiloansMandateConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LiquiloansMandateConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LiquiloansMandateConfig) setDynamicField(v *common.LiquiloansMandateConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ismandatecooloffcheckenabled":
		return obj.SetIsMandateCoolOffCheckEnabled(v.IsMandateCoolOffCheckEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LiquiloansMandateConfig) setDynamicFields(v *common.LiquiloansMandateConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsMandateCoolOffCheckEnabled(v.IsMandateCoolOffCheckEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LiquiloansMandateConfig) setStaticFields(v *common.LiquiloansMandateConfig) error {

	obj._MinCoolOffMinutesBetweenMandateAttempts = v.MinCoolOffMinutesBetweenMandateAttempts
	obj._IsMandateRequestBasedCountLogicEnabled = v.IsMandateRequestBasedCountLogicEnabled
	obj._IsPreviousMandateStatusCheckEnabled = v.IsPreviousMandateStatusCheckEnabled
	return nil
}

func (obj *LiquiloansMandateConfig) SetIsMandateCoolOffCheckEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *LiquiloansMandateConfig.IsMandateCoolOffCheckEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsMandateCoolOffCheckEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsMandateCoolOffCheckEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsMandateCoolOffCheckEnabled")
	}
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["trimdebugmessagefromstatus"] = _obj.SetTrimDebugMessageFromStatus
	_setters["isfldgloanoverduesherlockbannerenabled"] = _obj.SetIsFldgLoanOverdueSherlockBannerEnabled
	_setters["isoffapppaymentv2enabled"] = _obj.SetIsOffAppPaymentV2Enabled
	_setters["isrecommendationengineenabled"] = _obj.SetIsRecommendationEngineEnabled
	_setters["hideidfcoffer"] = _obj.SetHideIdfcOffer
	_setters["isabflkfsgenerationv2"] = _obj.SetIsAbflKfsGenerationV2
	_setters["moveprequaltorealtime"] = _obj.SetMovePreQualToRealTime
	_setters["issgdigilockerenabled"] = _obj.SetIsSgDigilockerEnabled
	_setters["usebase64image"] = _obj.SetUseBase64Image
	_setters["isldcapplicationmovementenabled"] = _obj.SetIsLdcApplicationMovementEnabled
	_setters["preferupimandatetypeforldc"] = _obj.SetPreferUPIMandateTypeForLDC
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *common.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "trimdebugmessagefromstatus":
		return obj.SetTrimDebugMessageFromStatus(v.TrimDebugMessageFromStatus, true, nil)
	case "isfldgloanoverduesherlockbannerenabled":
		return obj.SetIsFldgLoanOverdueSherlockBannerEnabled(v.IsFldgLoanOverdueSherlockBannerEnabled, true, nil)
	case "isoffapppaymentv2enabled":
		return obj.SetIsOffAppPaymentV2Enabled(v.IsOffAppPaymentV2Enabled, true, nil)
	case "isrecommendationengineenabled":
		return obj.SetIsRecommendationEngineEnabled(v.IsRecommendationEngineEnabled, true, nil)
	case "hideidfcoffer":
		return obj.SetHideIdfcOffer(v.HideIdfcOffer, true, nil)
	case "isabflkfsgenerationv2":
		return obj.SetIsAbflKfsGenerationV2(v.IsAbflKfsGenerationV2, true, nil)
	case "moveprequaltorealtime":
		return obj.SetMovePreQualToRealTime(v.MovePreQualToRealTime, true, nil)
	case "issgdigilockerenabled":
		return obj.SetIsSgDigilockerEnabled(v.IsSgDigilockerEnabled, true, nil)
	case "usebase64image":
		return obj.SetUseBase64Image(v.UseBase64Image, true, nil)
	case "isldcapplicationmovementenabled":
		return obj.SetIsLdcApplicationMovementEnabled(v.IsLdcApplicationMovementEnabled, true, nil)
	case "preferupimandatetypeforldc":
		return obj.SetPreferUPIMandateTypeForLDC(v.PreferUPIMandateTypeForLDC, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *common.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetTrimDebugMessageFromStatus(v.TrimDebugMessageFromStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsFldgLoanOverdueSherlockBannerEnabled(v.IsFldgLoanOverdueSherlockBannerEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsOffAppPaymentV2Enabled(v.IsOffAppPaymentV2Enabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsRecommendationEngineEnabled(v.IsRecommendationEngineEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHideIdfcOffer(v.HideIdfcOffer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsAbflKfsGenerationV2(v.IsAbflKfsGenerationV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMovePreQualToRealTime(v.MovePreQualToRealTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsSgDigilockerEnabled(v.IsSgDigilockerEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseBase64Image(v.UseBase64Image, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsLdcApplicationMovementEnabled(v.IsLdcApplicationMovementEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPreferUPIMandateTypeForLDC(v.PreferUPIMandateTypeForLDC, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *common.Flags) error {

	return nil
}

func (obj *Flags) SetTrimDebugMessageFromStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.TrimDebugMessageFromStatus", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._TrimDebugMessageFromStatus, 1)
	} else {
		atomic.StoreUint32(&obj._TrimDebugMessageFromStatus, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrimDebugMessageFromStatus")
	}
	return nil
}
func (obj *Flags) SetIsFldgLoanOverdueSherlockBannerEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.IsFldgLoanOverdueSherlockBannerEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsFldgLoanOverdueSherlockBannerEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsFldgLoanOverdueSherlockBannerEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsFldgLoanOverdueSherlockBannerEnabled")
	}
	return nil
}
func (obj *Flags) SetIsOffAppPaymentV2Enabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.IsOffAppPaymentV2Enabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsOffAppPaymentV2Enabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsOffAppPaymentV2Enabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsOffAppPaymentV2Enabled")
	}
	return nil
}
func (obj *Flags) SetIsRecommendationEngineEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.IsRecommendationEngineEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsRecommendationEngineEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsRecommendationEngineEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsRecommendationEngineEnabled")
	}
	return nil
}
func (obj *Flags) SetHideIdfcOffer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.HideIdfcOffer", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._HideIdfcOffer, 1)
	} else {
		atomic.StoreUint32(&obj._HideIdfcOffer, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "HideIdfcOffer")
	}
	return nil
}
func (obj *Flags) SetIsAbflKfsGenerationV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.IsAbflKfsGenerationV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAbflKfsGenerationV2, 1)
	} else {
		atomic.StoreUint32(&obj._IsAbflKfsGenerationV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAbflKfsGenerationV2")
	}
	return nil
}
func (obj *Flags) SetMovePreQualToRealTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.MovePreQualToRealTime", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._MovePreQualToRealTime, 1)
	} else {
		atomic.StoreUint32(&obj._MovePreQualToRealTime, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "MovePreQualToRealTime")
	}
	return nil
}
func (obj *Flags) SetIsSgDigilockerEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.IsSgDigilockerEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsSgDigilockerEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsSgDigilockerEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsSgDigilockerEnabled")
	}
	return nil
}
func (obj *Flags) SetUseBase64Image(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.UseBase64Image", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseBase64Image, 1)
	} else {
		atomic.StoreUint32(&obj._UseBase64Image, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseBase64Image")
	}
	return nil
}
func (obj *Flags) SetIsLdcApplicationMovementEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.IsLdcApplicationMovementEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsLdcApplicationMovementEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsLdcApplicationMovementEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsLdcApplicationMovementEnabled")
	}
	return nil
}
func (obj *Flags) SetPreferUPIMandateTypeForLDC(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.PreferUPIMandateTypeForLDC", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._PreferUPIMandateTypeForLDC, 1)
	} else {
		atomic.StoreUint32(&obj._PreferUPIMandateTypeForLDC, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "PreferUPIMandateTypeForLDC")
	}
	return nil
}

func NewPrepay() (_obj *Prepay, _setters map[string]dynconf.SetFunc) {
	_obj = &Prepay{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._LenderToPrepayBlackOutConfig = &syncmap.Map[string, *PrepayBlackOutPeriodConfig]{}
	_setters["lendertoprepayblackoutconfig"] = _obj.SetLenderToPrepayBlackOutConfig

	_obj._LenderToPreClosureBlackOutConfig = &syncmap.Map[string, *PreClosureBlackOutPeriodConfig]{}
	_setters["lendertopreclosureblackoutconfig"] = _obj.SetLenderToPreClosureBlackOutConfig
	return _obj, _setters
}

func (obj *Prepay) Init() {
	newObj, _ := NewPrepay()
	*obj = *newObj
}

func (obj *Prepay) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Prepay) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.Prepay)
	if !ok {
		return fmt.Errorf("invalid data type %v *Prepay", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Prepay) setDynamicField(v *common.Prepay, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "lendertoprepayblackoutconfig":
		return obj.SetLenderToPrepayBlackOutConfig(v.LenderToPrepayBlackOutConfig, true, path)
	case "lendertopreclosureblackoutconfig":
		return obj.SetLenderToPreClosureBlackOutConfig(v.LenderToPreClosureBlackOutConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Prepay) setDynamicFields(v *common.Prepay, dynamic bool, path []string) (err error) {

	err = obj.SetLenderToPrepayBlackOutConfig(v.LenderToPrepayBlackOutConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetLenderToPreClosureBlackOutConfig(v.LenderToPreClosureBlackOutConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Prepay) setStaticFields(v *common.Prepay) error {

	obj._PoolAccounts = v.PoolAccounts
	obj._UseIDFCLoanCancellationV2 = v.UseIDFCLoanCancellationV2
	return nil
}

func (obj *Prepay) SetLenderToPrepayBlackOutConfig(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*common.PrepayBlackOutPeriodConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *Prepay.LenderToPrepayBlackOutConfig", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._LenderToPrepayBlackOutConfig, v, dynamic, path)

}
func (obj *Prepay) SetLenderToPreClosureBlackOutConfig(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*common.PreClosureBlackOutPeriodConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *Prepay.LenderToPreClosureBlackOutConfig", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._LenderToPreClosureBlackOutConfig, v, dynamic, path)

}

func NewPrepayBlackOutPeriodConfig() (_obj *PrepayBlackOutPeriodConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PrepayBlackOutPeriodConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["blockdurationbeforeemiduedateindays"] = _obj.SetBlockDurationBeforeEmiDueDateInDays
	_setters["blockdurationafteremiduedateindays"] = _obj.SetBlockDurationAfterEmiDueDateInDays
	_setters["blockdurationbeforeemigraceenddateindays"] = _obj.SetBlockDurationBeforeEmiGraceEndDateInDays
	_setters["blockdurationafteremigraceenddateindays"] = _obj.SetBlockDurationAfterEmiGraceEndDateInDays
	return _obj, _setters
}

func (obj *PrepayBlackOutPeriodConfig) Init() {
	newObj, _ := NewPrepayBlackOutPeriodConfig()
	*obj = *newObj
}

func (obj *PrepayBlackOutPeriodConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PrepayBlackOutPeriodConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.PrepayBlackOutPeriodConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PrepayBlackOutPeriodConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PrepayBlackOutPeriodConfig) setDynamicField(v *common.PrepayBlackOutPeriodConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "blockdurationbeforeemiduedateindays":
		return obj.SetBlockDurationBeforeEmiDueDateInDays(v.BlockDurationBeforeEmiDueDateInDays, true, nil)
	case "blockdurationafteremiduedateindays":
		return obj.SetBlockDurationAfterEmiDueDateInDays(v.BlockDurationAfterEmiDueDateInDays, true, nil)
	case "blockdurationbeforeemigraceenddateindays":
		return obj.SetBlockDurationBeforeEmiGraceEndDateInDays(v.BlockDurationBeforeEmiGraceEndDateInDays, true, nil)
	case "blockdurationafteremigraceenddateindays":
		return obj.SetBlockDurationAfterEmiGraceEndDateInDays(v.BlockDurationAfterEmiGraceEndDateInDays, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PrepayBlackOutPeriodConfig) setDynamicFields(v *common.PrepayBlackOutPeriodConfig, dynamic bool, path []string) (err error) {

	err = obj.SetBlockDurationBeforeEmiDueDateInDays(v.BlockDurationBeforeEmiDueDateInDays, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockDurationAfterEmiDueDateInDays(v.BlockDurationAfterEmiDueDateInDays, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockDurationBeforeEmiGraceEndDateInDays(v.BlockDurationBeforeEmiGraceEndDateInDays, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockDurationAfterEmiGraceEndDateInDays(v.BlockDurationAfterEmiGraceEndDateInDays, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PrepayBlackOutPeriodConfig) setStaticFields(v *common.PrepayBlackOutPeriodConfig) error {

	return nil
}

func (obj *PrepayBlackOutPeriodConfig) SetBlockDurationBeforeEmiDueDateInDays(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *PrepayBlackOutPeriodConfig.BlockDurationBeforeEmiDueDateInDays", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._BlockDurationBeforeEmiDueDateInDays, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockDurationBeforeEmiDueDateInDays")
	}
	return nil
}
func (obj *PrepayBlackOutPeriodConfig) SetBlockDurationAfterEmiDueDateInDays(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *PrepayBlackOutPeriodConfig.BlockDurationAfterEmiDueDateInDays", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._BlockDurationAfterEmiDueDateInDays, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockDurationAfterEmiDueDateInDays")
	}
	return nil
}
func (obj *PrepayBlackOutPeriodConfig) SetBlockDurationBeforeEmiGraceEndDateInDays(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *PrepayBlackOutPeriodConfig.BlockDurationBeforeEmiGraceEndDateInDays", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._BlockDurationBeforeEmiGraceEndDateInDays, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockDurationBeforeEmiGraceEndDateInDays")
	}
	return nil
}
func (obj *PrepayBlackOutPeriodConfig) SetBlockDurationAfterEmiGraceEndDateInDays(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *PrepayBlackOutPeriodConfig.BlockDurationAfterEmiGraceEndDateInDays", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._BlockDurationAfterEmiGraceEndDateInDays, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockDurationAfterEmiGraceEndDateInDays")
	}
	return nil
}

func NewPreClosureBlackOutPeriodConfig() (_obj *PreClosureBlackOutPeriodConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PreClosureBlackOutPeriodConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["blockstarthour"] = _obj.SetBlockStartHour
	_setters["blockendhour"] = _obj.SetBlockEndHour
	return _obj, _setters
}

func (obj *PreClosureBlackOutPeriodConfig) Init() {
	newObj, _ := NewPreClosureBlackOutPeriodConfig()
	*obj = *newObj
}

func (obj *PreClosureBlackOutPeriodConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PreClosureBlackOutPeriodConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.PreClosureBlackOutPeriodConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreClosureBlackOutPeriodConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PreClosureBlackOutPeriodConfig) setDynamicField(v *common.PreClosureBlackOutPeriodConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "blockstarthour":
		return obj.SetBlockStartHour(v.BlockStartHour, true, nil)
	case "blockendhour":
		return obj.SetBlockEndHour(v.BlockEndHour, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PreClosureBlackOutPeriodConfig) setDynamicFields(v *common.PreClosureBlackOutPeriodConfig, dynamic bool, path []string) (err error) {

	err = obj.SetBlockStartHour(v.BlockStartHour, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockEndHour(v.BlockEndHour, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PreClosureBlackOutPeriodConfig) setStaticFields(v *common.PreClosureBlackOutPeriodConfig) error {

	return nil
}

func (obj *PreClosureBlackOutPeriodConfig) SetBlockStartHour(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreClosureBlackOutPeriodConfig.BlockStartHour", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BlockStartHour, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockStartHour")
	}
	return nil
}
func (obj *PreClosureBlackOutPeriodConfig) SetBlockEndHour(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreClosureBlackOutPeriodConfig.BlockEndHour", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BlockEndHour, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockEndHour")
	}
	return nil
}

func NewVendorProgramLevelFeature() (_obj *VendorProgramLevelFeature, _setters map[string]dynconf.SetFunc) {
	_obj = &VendorProgramLevelFeature{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._VendorProgramActiveMap = &syncmap.Map[string, *FeatureConstraint]{}
	_setters["vendorprogramactivemap"] = _obj.SetVendorProgramActiveMap

	_obj._NonFiCoreVendorProgramActiveMap = &syncmap.Map[string, *FeatureConstraint]{}
	_setters["nonficorevendorprogramactivemap"] = _obj.SetNonFiCoreVendorProgramActiveMap

	_obj._DownTimeConfig = &syncmap.Map[string, *DownTimeConfig]{}
	_setters["downtimeconfig"] = _obj.SetDownTimeConfig
	return _obj, _setters
}

func (obj *VendorProgramLevelFeature) Init() {
	newObj, _ := NewVendorProgramLevelFeature()
	*obj = *newObj
}

func (obj *VendorProgramLevelFeature) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VendorProgramLevelFeature) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.VendorProgramLevelFeature)
	if !ok {
		return fmt.Errorf("invalid data type %v *VendorProgramLevelFeature", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VendorProgramLevelFeature) setDynamicField(v *common.VendorProgramLevelFeature, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "vendorprogramactivemap":
		return obj.SetVendorProgramActiveMap(v.VendorProgramActiveMap, true, path)
	case "nonficorevendorprogramactivemap":
		return obj.SetNonFiCoreVendorProgramActiveMap(v.NonFiCoreVendorProgramActiveMap, true, path)
	case "downtimeconfig":
		return obj.SetDownTimeConfig(v.DownTimeConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VendorProgramLevelFeature) setDynamicFields(v *common.VendorProgramLevelFeature, dynamic bool, path []string) (err error) {

	err = obj.SetVendorProgramActiveMap(v.VendorProgramActiveMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetNonFiCoreVendorProgramActiveMap(v.NonFiCoreVendorProgramActiveMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetDownTimeConfig(v.DownTimeConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VendorProgramLevelFeature) setStaticFields(v *common.VendorProgramLevelFeature) error {

	return nil
}

func (obj *VendorProgramLevelFeature) SetVendorProgramActiveMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*common.FeatureConstraint)
	if !ok {
		return fmt.Errorf("invalid data type %v *VendorProgramLevelFeature.VendorProgramActiveMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._VendorProgramActiveMap, v, dynamic, path)

}
func (obj *VendorProgramLevelFeature) SetNonFiCoreVendorProgramActiveMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*common.FeatureConstraint)
	if !ok {
		return fmt.Errorf("invalid data type %v *VendorProgramLevelFeature.NonFiCoreVendorProgramActiveMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._NonFiCoreVendorProgramActiveMap, v, dynamic, path)

}
func (obj *VendorProgramLevelFeature) SetDownTimeConfig(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*common.DownTimeConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *VendorProgramLevelFeature.DownTimeConfig", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._DownTimeConfig, v, dynamic, path)

}

func NewFeatureConstraint() (_obj *FeatureConstraint, _setters map[string]dynconf.SetFunc) {
	_obj = &FeatureConstraint{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isallowed"] = _obj.SetIsAllowed
	_setters["allowedgroups"] = _obj.SetAllowedGroups
	_obj._AllowedGroupsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *FeatureConstraint) Init() {
	newObj, _ := NewFeatureConstraint()
	*obj = *newObj
}

func (obj *FeatureConstraint) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeatureConstraint) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.FeatureConstraint)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureConstraint", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeatureConstraint) setDynamicField(v *common.FeatureConstraint, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isallowed":
		return obj.SetIsAllowed(v.IsAllowed, true, nil)
	case "allowedgroups":
		return obj.SetAllowedGroups(v.AllowedGroups, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeatureConstraint) setDynamicFields(v *common.FeatureConstraint, dynamic bool, path []string) (err error) {

	err = obj.SetIsAllowed(v.IsAllowed, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowedGroups(v.AllowedGroups, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeatureConstraint) setStaticFields(v *common.FeatureConstraint) error {

	return nil
}

func (obj *FeatureConstraint) SetIsAllowed(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureConstraint.IsAllowed", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAllowed, 1)
	} else {
		atomic.StoreUint32(&obj._IsAllowed, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAllowed")
	}
	return nil
}
func (obj *FeatureConstraint) SetAllowedGroups(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureConstraint.AllowedGroups", reflect.TypeOf(val))
	}
	obj._AllowedGroupsMutex.Lock()
	defer obj._AllowedGroupsMutex.Unlock()
	obj._AllowedGroups = roarray.New[string](v)
	return nil
}

func NewDownTimeConfig() (_obj *DownTimeConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DownTimeConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["start"] = _obj.SetStart
	_obj._StartMutex = &sync.RWMutex{}
	_setters["end"] = _obj.SetEnd
	_obj._EndMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *DownTimeConfig) Init() {
	newObj, _ := NewDownTimeConfig()
	*obj = *newObj
}

func (obj *DownTimeConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DownTimeConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.DownTimeConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DownTimeConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DownTimeConfig) setDynamicField(v *common.DownTimeConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "start":
		return obj.SetStart(v.Start, true, nil)
	case "end":
		return obj.SetEnd(v.End, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DownTimeConfig) setDynamicFields(v *common.DownTimeConfig, dynamic bool, path []string) (err error) {

	err = obj.SetStart(v.Start, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnd(v.End, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DownTimeConfig) setStaticFields(v *common.DownTimeConfig) error {

	return nil
}

func (obj *DownTimeConfig) SetStart(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DownTimeConfig.Start", reflect.TypeOf(val))
	}
	obj._StartMutex.Lock()
	defer obj._StartMutex.Unlock()
	obj._Start = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Start")
	}
	return nil
}
func (obj *DownTimeConfig) SetEnd(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DownTimeConfig.End", reflect.TypeOf(val))
	}
	obj._EndMutex.Lock()
	defer obj._EndMutex.Unlock()
	obj._End = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "End")
	}
	return nil
}

func NewLopeOverrideConfig() (_obj *LopeOverrideConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LopeOverrideConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["targetposition"] = _obj.SetTargetPosition
	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["isforficore"] = _obj.SetIsForFiCore
	_setters["showsgeligibilityoverabflsoftoffer"] = _obj.SetShowSgEligibilityOverAbflSoftOffer
	_setters["showsgeligibilityovermvsoftoffer"] = _obj.SetShowSgEligibilityOverMvSoftOffer
	_TargetLoanHeader, _fieldSetters := NewLoanHeader()
	_obj._TargetLoanHeader = _TargetLoanHeader
	helper.AddFieldSetters("targetloanheader", _fieldSetters, _setters)
	return _obj, _setters
}

func NewLopeOverrideConfigWithQuest(questFieldPath string) (_obj *LopeOverrideConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LopeOverrideConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["targetposition"] = _obj.SetTargetPosition
	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["isforficore"] = _obj.SetIsForFiCore
	_setters["showsgeligibilityoverabflsoftoffer"] = _obj.SetShowSgEligibilityOverAbflSoftOffer
	_setters["showsgeligibilityovermvsoftoffer"] = _obj.SetShowSgEligibilityOverMvSoftOffer
	_TargetLoanHeader, _fieldSetters := NewLoanHeaderWithQuest(questFieldPath + "/" + "TargetLoanHeader")
	_obj._TargetLoanHeader = _TargetLoanHeader
	helper.AddFieldSetters("targetloanheader", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *LopeOverrideConfig) Init(questFieldPath string) {
	newObj, _ := NewLopeOverrideConfig()
	*obj = *newObj
}
func (obj *LopeOverrideConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewLopeOverrideConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *LopeOverrideConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._TargetLoanHeader.SetQuestSDK(questSdk)
}

func (obj *LopeOverrideConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LopeOverrideConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:        obj.questFieldPath + "/" + "TargetPosition",
		Datatype:    &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_INT},
		Area:        "", // from quest tag annotation
		Description: `the position index (0-based) where the target loan header should be placed in the priority list 0 means highest priority (first position), 1 means second position, and so on`,
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsEnabled",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IsForFiCore",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "ShowSgEligibilityOverAbflSoftOffer",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "ShowSgEligibilityOverMvSoftOffer",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._TargetLoanHeader.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *LopeOverrideConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.LopeOverrideConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LopeOverrideConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LopeOverrideConfig) setDynamicField(v *common.LopeOverrideConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "targetposition":
		return obj.SetTargetPosition(v.TargetPosition, true, nil)
	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "isforficore":
		return obj.SetIsForFiCore(v.IsForFiCore, true, nil)
	case "showsgeligibilityoverabflsoftoffer":
		return obj.SetShowSgEligibilityOverAbflSoftOffer(v.ShowSgEligibilityOverAbflSoftOffer, true, nil)
	case "showsgeligibilityovermvsoftoffer":
		return obj.SetShowSgEligibilityOverMvSoftOffer(v.ShowSgEligibilityOverMvSoftOffer, true, nil)
	case "targetloanheader":
		return obj._TargetLoanHeader.Set(v.TargetLoanHeader, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LopeOverrideConfig) setDynamicFields(v *common.LopeOverrideConfig, dynamic bool, path []string) (err error) {

	err = obj.SetTargetPosition(v.TargetPosition, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsForFiCore(v.IsForFiCore, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShowSgEligibilityOverAbflSoftOffer(v.ShowSgEligibilityOverAbflSoftOffer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShowSgEligibilityOverMvSoftOffer(v.ShowSgEligibilityOverMvSoftOffer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._TargetLoanHeader.Set(v.TargetLoanHeader, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LopeOverrideConfig) setStaticFields(v *common.LopeOverrideConfig) error {

	obj._LoanPriorityOrderNonFiCore = v.LoanPriorityOrderNonFiCore
	obj._LoanPriorityOrderFiCore = v.LoanPriorityOrderFiCore
	return nil
}

func (obj *LopeOverrideConfig) SetTargetPosition(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *LopeOverrideConfig.TargetPosition", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._TargetPosition, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "TargetPosition")
	}
	return nil
}
func (obj *LopeOverrideConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *LopeOverrideConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *LopeOverrideConfig) SetIsForFiCore(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *LopeOverrideConfig.IsForFiCore", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsForFiCore, 1)
	} else {
		atomic.StoreUint32(&obj._IsForFiCore, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsForFiCore")
	}
	return nil
}
func (obj *LopeOverrideConfig) SetShowSgEligibilityOverAbflSoftOffer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *LopeOverrideConfig.ShowSgEligibilityOverAbflSoftOffer", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShowSgEligibilityOverAbflSoftOffer, 1)
	} else {
		atomic.StoreUint32(&obj._ShowSgEligibilityOverAbflSoftOffer, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShowSgEligibilityOverAbflSoftOffer")
	}
	return nil
}
func (obj *LopeOverrideConfig) SetShowSgEligibilityOverMvSoftOffer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *LopeOverrideConfig.ShowSgEligibilityOverMvSoftOffer", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShowSgEligibilityOverMvSoftOffer, 1)
	} else {
		atomic.StoreUint32(&obj._ShowSgEligibilityOverMvSoftOffer, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShowSgEligibilityOverMvSoftOffer")
	}
	return nil
}

func NewLoanHeader() (_obj *LoanHeader, _setters map[string]dynconf.SetFunc) {
	_obj = &LoanHeader{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["loanprogram"] = _obj.SetLoanProgram
	_obj._LoanProgramMutex = &sync.RWMutex{}
	_setters["vendor"] = _obj.SetVendor
	_obj._VendorMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewLoanHeaderWithQuest(questFieldPath string) (_obj *LoanHeader, _setters map[string]dynconf.SetFunc) {
	_obj = &LoanHeader{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["loanprogram"] = _obj.SetLoanProgram
	_obj._LoanProgramMutex = &sync.RWMutex{}
	_setters["vendor"] = _obj.SetVendor
	_obj._VendorMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *LoanHeader) Init(questFieldPath string) {
	newObj, _ := NewLoanHeader()
	*obj = *newObj
}
func (obj *LoanHeader) InitWithQuest(questFieldPath string) {
	newObj, _ := NewLoanHeaderWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *LoanHeader) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *LoanHeader) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LoanHeader) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "LoanProgram",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Vendor",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *LoanHeader) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*common.LoanHeader)
	if !ok {
		return fmt.Errorf("invalid data type %v *LoanHeader", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LoanHeader) setDynamicField(v *common.LoanHeader, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "loanprogram":
		return obj.SetLoanProgram(v.LoanProgram, true, nil)
	case "vendor":
		return obj.SetVendor(v.Vendor, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LoanHeader) setDynamicFields(v *common.LoanHeader, dynamic bool, path []string) (err error) {

	err = obj.SetLoanProgram(v.LoanProgram, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVendor(v.Vendor, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LoanHeader) setStaticFields(v *common.LoanHeader) error {

	return nil
}

func (obj *LoanHeader) SetLoanProgram(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *LoanHeader.LoanProgram", reflect.TypeOf(val))
	}
	obj._LoanProgramMutex.Lock()
	defer obj._LoanProgramMutex.Unlock()
	obj._LoanProgram = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "LoanProgram")
	}
	return nil
}
func (obj *LoanHeader) SetVendor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *LoanHeader.Vendor", reflect.TypeOf(val))
	}
	obj._VendorMutex.Lock()
	defer obj._VendorMutex.Unlock()
	obj._Vendor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Vendor")
	}
	return nil
}
