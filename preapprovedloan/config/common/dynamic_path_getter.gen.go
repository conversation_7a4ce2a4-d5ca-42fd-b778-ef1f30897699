// Code generated by tools/conf_gen/dynamic_conf_gen.go
package common

import (
	"fmt"
	"strings"
)

func (obj *DeeplinkConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isinitiatemandateenrichmentenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsInitiateMandateEnrichmentEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsInitiateMandateEnrichmentEnabled, nil
	case "isalternateaccountflowenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAlternateAccountFlowEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsAlternateAccountFlowEnabled, nil
	case "openabflmandateurlviaexternal":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OpenAbflMandateUrlViaExternal\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OpenAbflMandateUrlViaExternal, nil
	case "openabfldigilockerurlviaexternal":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OpenAbflDigilockerUrlViaExternal\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OpenAbflDigilockerUrlViaExternal, nil
	case "openabfldigilockerurlviaexternalforios":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OpenAbflDigilockerUrlViaExternalForIos\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OpenAbflDigilockerUrlViaExternalForIos, nil
	case "openabflmandateurlviaexternalforios":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OpenAbflMandateUrlViaExternalForIos\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OpenAbflMandateUrlViaExternalForIos, nil
	case "isloandetailsselectionv2flowenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsLoanDetailsSelectionV2FlowEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsLoanDetailsSelectionV2FlowEnabled, nil
	case "isalternateaccountflowenabledforll":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAlternateAccountFlowEnabledForLL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsAlternateAccountFlowEnabledForLL, nil
	case "changebuttontextdetailspage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ChangeButtonTextDetailsPage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ChangeButtonTextDetailsPage, nil
	case "openidfcvkycurlviaexternalforandroid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OpenIdfcVkycUrlViaExternalForAndroid\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OpenIdfcVkycUrlViaExternalForAndroid, nil
	case "openidfcvkycurlviaexternalforios":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OpenIdfcVkycUrlViaExternalForIos\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OpenIdfcVkycUrlViaExternalForIos, nil
	case "fedkfsexiturl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FedKfsExitUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FedKfsExitUrl, nil
	case "loandetailsselectionv2flow":
		return obj.LoanDetailsSelectionV2Flow.Get(dynamicFieldPath[1:])
	case "offerdetailsv3config":
		return obj.OfferDetailsV3Config.Get(dynamicFieldPath[1:])
	case "abflreferencesappversionconstraintconfig":
		return obj.AbflReferencesAppVersionConstraintConfig.Get(dynamicFieldPath[1:])
	case "infoitemv3minversion":
		return obj.InfoItemV3MinVersion.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DeeplinkConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LoanDetailsSelectionV2Flow) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "defaultamountpercentage":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DefaultAmountPercentage, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"DefaultAmountPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.DefaultAmountPercentage[dynamicFieldPath[1]], nil

		}
		return obj.DefaultAmountPercentage, nil
	case "enableloanprograms":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableLoanPrograms\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableLoanPrograms, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LoanDetailsSelectionV2Flow", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OfferDetailsV3Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "skipamountselectionscreen":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipAmountSelectionScreen\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipAmountSelectionScreen, nil
	case "showinterestrate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowInterestRate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShowInterestRate, nil
	case "showzeropreclosuretag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowZeroPreClosureTag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShowZeroPreClosureTag, nil
	case "vendorloanprogrammap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.VendorLoanProgramMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"VendorLoanProgramMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.VendorLoanProgramMap[dynamicFieldPath[1]], nil

		}
		return obj.VendorLoanProgramMap, nil
	case "appversionconstraintconfig":
		return obj.AppVersionConstraintConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OfferDetailsV3Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CreditReportConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "staleexperianreportthresholddays":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StaleExperianReportThresholdDays\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.StaleExperianReportThresholdDays, nil
	case "usecreditreportv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseCreditReportV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseCreditReportV2, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CreditReportConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MandateConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "liquiloansmandateconfig":
		return obj.LiquiloansMandateConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MandateConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LiquiloansMandateConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ismandatecooloffcheckenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsMandateCoolOffCheckEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsMandateCoolOffCheckEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LiquiloansMandateConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "trimdebugmessagefromstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TrimDebugMessageFromStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrimDebugMessageFromStatus, nil
	case "isfldgloanoverduesherlockbannerenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsFldgLoanOverdueSherlockBannerEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsFldgLoanOverdueSherlockBannerEnabled, nil
	case "isoffapppaymentv2enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsOffAppPaymentV2Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsOffAppPaymentV2Enabled, nil
	case "isrecommendationengineenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRecommendationEngineEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRecommendationEngineEnabled, nil
	case "hideidfcoffer":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HideIdfcOffer\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HideIdfcOffer, nil
	case "isabflkfsgenerationv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAbflKfsGenerationV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsAbflKfsGenerationV2, nil
	case "moveprequaltorealtime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MovePreQualToRealTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MovePreQualToRealTime, nil
	case "issgdigilockerenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsSgDigilockerEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsSgDigilockerEnabled, nil
	case "usebase64image":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseBase64Image\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseBase64Image, nil
	case "isldcapplicationmovementenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsLdcApplicationMovementEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsLdcApplicationMovementEnabled, nil
	case "preferupimandatetypeforldc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PreferUPIMandateTypeForLDC\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PreferUPIMandateTypeForLDC, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Prepay) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "lendertoprepayblackoutconfig":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.LenderToPrepayBlackOutConfig, nil
		case len(dynamicFieldPath) > 1:

			return obj.LenderToPrepayBlackOutConfig[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.LenderToPrepayBlackOutConfig, nil
	case "lendertopreclosureblackoutconfig":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.LenderToPreClosureBlackOutConfig, nil
		case len(dynamicFieldPath) > 1:

			return obj.LenderToPreClosureBlackOutConfig[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.LenderToPreClosureBlackOutConfig, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Prepay", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PrepayBlackOutPeriodConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "blockdurationbeforeemiduedateindays":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockDurationBeforeEmiDueDateInDays\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockDurationBeforeEmiDueDateInDays, nil
	case "blockdurationafteremiduedateindays":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockDurationAfterEmiDueDateInDays\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockDurationAfterEmiDueDateInDays, nil
	case "blockdurationbeforeemigraceenddateindays":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockDurationBeforeEmiGraceEndDateInDays\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockDurationBeforeEmiGraceEndDateInDays, nil
	case "blockdurationafteremigraceenddateindays":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockDurationAfterEmiGraceEndDateInDays\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockDurationAfterEmiGraceEndDateInDays, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PrepayBlackOutPeriodConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PreClosureBlackOutPeriodConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "blockstarthour":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockStartHour\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockStartHour, nil
	case "blockendhour":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockEndHour\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockEndHour, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PreClosureBlackOutPeriodConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VendorProgramLevelFeature) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "vendorprogramactivemap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.VendorProgramActiveMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.VendorProgramActiveMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.VendorProgramActiveMap, nil
	case "nonficorevendorprogramactivemap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.NonFiCoreVendorProgramActiveMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.NonFiCoreVendorProgramActiveMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.NonFiCoreVendorProgramActiveMap, nil
	case "downtimeconfig":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DownTimeConfig, nil
		case len(dynamicFieldPath) > 1:

			return obj.DownTimeConfig[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DownTimeConfig, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VendorProgramLevelFeature", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FeatureConstraint) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isallowed":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAllowed\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsAllowed, nil
	case "allowedgroups":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowedGroups\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowedGroups, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FeatureConstraint", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DownTimeConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "start":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Start\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Start, nil
	case "end":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"End\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.End, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DownTimeConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LopeOverrideConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "targetposition":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TargetPosition\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TargetPosition, nil
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "isforficore":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsForFiCore\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsForFiCore, nil
	case "showsgeligibilityoverabflsoftoffer":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowSgEligibilityOverAbflSoftOffer\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShowSgEligibilityOverAbflSoftOffer, nil
	case "showsgeligibilityovermvsoftoffer":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowSgEligibilityOverMvSoftOffer\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShowSgEligibilityOverMvSoftOffer, nil
	case "targetloanheader":
		return obj.TargetLoanHeader.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LopeOverrideConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LoanHeader) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "loanprogram":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LoanProgram\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LoanProgram, nil
	case "vendor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Vendor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Vendor, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LoanHeader", strings.Join(dynamicFieldPath, "."))
	}
}
