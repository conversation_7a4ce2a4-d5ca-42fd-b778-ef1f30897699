Application:
  Environment: "prod"
  Name: "preapprovedloan"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

PgDbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_federal_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LIQUILOANS_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 10
    MaxIdleConn: 5
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_liquiloans_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  IDFC_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 10s
    Name: "loans_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_idfc_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  FIFTYFIN_LAMF:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_fiftyfin"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_fiftyfin_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  MONEYVIEW_PL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_moneyview"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_moneyview_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  EPIFI_TECH_V2:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_epifi_tech"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_epifi_tech_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_ABFL:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_abfl"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_abfl_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_LENDEN:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_lenden"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_lenden_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    StatementTimeout: 5s
    Name: "loans_stock_guardian_lsp"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/rds/rds-ca-root-2061"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "prod/rds/epifimetis/loans_stock_guardian_lsp_dev_user"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

FederalPgDb:
  DbType: "PGDB"
  AppName: "preapprovedloan"
  StatementTimeout: 10s
  Name: "loans_federal"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FederalDb:
  Username: "federal_dev_user"
  Password: ""
  Name: "federal"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.federal_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.federal_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "preapprovedloan"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "preapprovedloan"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "federal_dev_user"
    Password: ""
    Name: "federal"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.federal_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.federal_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "preapprovedloan"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  LIQUILOANS_PL:
    DBType: "CRDB"
    Username: "pl_liquiloans_dev_user"
    Password: ""
    Name: "pl_liquiloans"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_liquiloans_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_liquiloans_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "preapprovedloan"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  IDFC_PL:
    DBType: "CRDB"
    Username: "pl_idfc_dev_user"
    Password: ""
    Name: "pl_idfc"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.pl_idfc_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.pl_idfc_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "preapprovedloan"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    # Federal
    EpifiFederalPgpPrivateKey: "prod/pgp/pgp-epifi-fed-api-private-key"
    EpifiFederalPgpPassphrase: "prod/pgp/pgp-epifi-fed-api-password"
    FederalPgpPublicKey: "prod/pgp/federal-pgp-pub-key-for-epifi"
    FederalDbCredentials: "prod/rds/epifimetis/loans_federal_dev_user"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"

Vendor:
  # TODO(Kantikumar): Update vendor sftp remote path whenever available
  SftpUploadRemotePath: ""

ProcessLoanInboundTransactionSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-loan-inbound-transaction-queue"
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 5
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 5

OrderUpdatePreApprovedLoanSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-order-update-preapprovedloan-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~68 min post that regular interval is followed for next 7 days with interval of 2 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 12
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 84
          TimeUnit: "Hour"
      MaxAttempts: 96
      CutOff: 12

ProcessLoansFiftyfinCallbackSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-vn-loans-fiftyfin-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

Tracing:
  Enable: true

SignalWorkflowPublisher:
  QueueName: "prod-celestial-signal-workflow-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

Notification:
  VkycSuccess:
    Title: "Yay, time to move forward ➡️"
    Desc: "Your video KYC was successful! Tap to continue your loan application"
    Icon: ""
  ManualReviewSuccess:
    Title: "You are verified on Fi ✅"
    Desc: "Our bank agents have given you the go-ahead! Tap to continue your loan application"
    Icon: ""
  VkycFailed:
    Title: "There seems to be an issue 🤔"
    Desc: "Our partner regulated entity cannot process your loan application due to a verification issue"
    Icon: ""
  ManualReviewFailed:
    Title: "Loan application cancelled"
    Desc: "According to our partner regulated entity, it was due to a verification issue"
    Icon: ""
  LoanAccountCreation:
    Title: "Yay, good news!"
    Desc: "Your loan account number #accNum is now ready for active use ✅"
    Icon: ""
  LoanAccountClosure:
    Title: "Congrats on wrapping up the loan 🚀"
    Desc: "Your loan account number %v is now closed"
    Icon: ""
  LoanPrePay:
    Title: "Money Received"
    Desc: "You have received %v amount in your loan account %v"
    Icon: ""
  DropOffComms:
    Enable: false
    BlackoutTimeStart: "00:00"
    BlackoutTimeStop: "06:00"
    # Waiting times in minutes
    VkycWaitingTimes: [ 15, 1440, 2880 ]
    LivenessWaitingTimes: [ 15, 1440, 2880 ]
    ESignWaitingTimes: [ 15, 1440, 2880 ]

VendorUsed: 1

NudgeExitEventPublisher:
  QueueName: "prod-nudge-preapprovedloan-event-queue"

EnableDynamicElementForAllUsers: true

InstantCashSegmentId: "562e2816-dcfb-4812-b7d4-c1c739e3aa3b"

HomeWidgetV1Config:
  IsEnabled: true
  PersonalLoanDisplayConfig:
    ActiveOfferDisplayConfig:
      Heading:
        PlainString: "Get cash today"
        FontColor: "#313234"
        StandardFontStyle: "HEADLINE_M"
      HorizontalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Bag.png"
      VerticalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Bag.png"
      WidgetTiles:
        "1":
          "ArrayElement":
            "Position": 1
          "HeadingTemplate": "Get upto"
          "SubHeadingTemplate": "OFFER_MAX_AMOUNT"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Offer.png"
          "BgColor": "#FFFFFF"
        "2":
          "ArrayElement":
            "Position": 2
          "HeadingTemplate": "Low interest from"
          "SubHeadingTemplate": "12% pa"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
          "BgColor": "#FFFFFF"
        "3":
          "ArrayElement":
            "Position": 3
          "HeadingTemplate": "Get loan in"
          "SubHeadingTemplate": "5 mins"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Gift.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
        "4":
          "ArrayElement":
            "Position": 4
          "HeadingTemplate": "Approval odds"
          "SubHeadingTemplate": "Excellent"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Smiley.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
      PrimaryCta:
        Text:
          PlainString: "Get cash now"
          FontColor: "#6294A6"
          StandardFontStyle: "BUTTON_S"
        Deeplink:
          Screen: "PRE_APPROVED_LOAN_LANDING_SCREEN"
          PreApprovedLoanLandingScreenOptions:
            EntryPoint: 9
        BgColor: "#E4F1F5"
    NoActiveOfferDisplayConfig:
      Heading:
        PlainString: "Get cash today"
        FontColor: "#313234"
        StandardFontStyle: "HEADLINE_M"
      HorizontalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Bag.png"
      VerticalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Bag.png"
      WidgetTiles:
        "1":
          "ArrayElement":
            "Position": 1
          "HeadingTemplate": "Get upto"
          "SubHeadingTemplate": "₹5L"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Offer.png"
          "BgColor": "#FFFFFF"
        "2":
          "ArrayElement":
            "Position": 2
          "HeadingTemplate": "Low interest from"
          "SubHeadingTemplate": "12% pa"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
          "BgColor": "#FFFFFF"
        "3":
          "ArrayElement":
            "Position": 3
          "HeadingTemplate": "Get loan in"
          "SubHeadingTemplate": "5 mins"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Gift.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
        "4":
          "ArrayElement":
            "Position": 4
          "HeadingTemplate": "Approval odds"
          "SubHeadingTemplate": "Excellent"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Smiley.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
      PrimaryCta:
        Text:
          PlainString: "Check eligibility"
          FontColor: "#6294A6"
          StandardFontStyle: "BUTTON_S"
        Deeplink:
          Screen: "PRE_APPROVED_LOAN_LANDING_SCREEN"
          PreApprovedLoanLandingScreenOptions:
            EntryPoint: 9
        BgColor: "#E4F1F5"

  #    CampaignDisplayConfig:
  #      Heading:
  #        PlainString: "Get cash today"
  #        FontColor: "#313234"
  #        StandardFontStyle: "HEADLINE_M"
  #      HorizontalPrimaryBannerImageUrl : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Bag.png"
  #      VerticalPrimaryBannerImageUrl : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Bag.png"
  #      WidgetTiles :
  #        "1" :
  #          "ArrayElement":
  #            "Position": 1
  #          "HeadingTemplate" : "Get up to"
  #          "SubHeadingTemplate": "60L"
  #          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
  #          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Offer.png"
  #          "BgColor" : "#FFFFFF"
  #        "2" :
  #          "ArrayElement":
  #            "Position": 2
  #          "HeadingTemplate" : "Interest rates"
  #          "SubHeadingTemplate": "1%/month"
  #          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
  #          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
  #          "BgColor" : "#FFFFFF"
  #        "3" :
  #          "ArrayElement":
  #            "Position": 3
  #          "HeadingTemplate" : "Get loan in"
  #          "SubHeadingTemplate": "5 min"
  #          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Gift.png"
  #          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
  #          "BgColor" : "#FFFFFF"
  #        "4" :
  #          "ArrayElement":
  #            "Position": 4
  #          "HeadingTemplate" : "Approval odds"
  #          "SubHeadingTemplate": "Excellent"
  #          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Smiley.png"
  #          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
  #          "BgColor" : "#FFFFFF"
  #      PrimaryCta:
  #        Text:
  #          PlainString: "Check eligibility"
  #          FontColor: "#6294A6"
  #          StandardFontStyle: "BUTTON_S"
  #        Deeplink:
  #          Screen: "PRE_APPROVED_LOAN_DASHBOARD_SCREEN"
  #        BgColor: "#E4F1F5"
  LAMFDisplayConfig:
    ActiveOfferDisplayConfig:
      Heading:
        PlainString: "Get cash today"
        FontColor: "#313234"
        StandardFontStyle: "HEADLINE_M"
      HorizontalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Balance.png"
      VerticalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Balance.png"
      WidgetTiles:
        "1":
          "ArrayElement":
            "Position": 1
          "HeadingTemplate": "Credit score"
          "SubHeadingTemplate": "NOT needed"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_smile.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_smile.png"
          "BgColor": "#FFFFFF"
        "2":
          "ArrayElement":
            "Position": 2
          "HeadingTemplate": "Interest rate"
          "SubHeadingTemplate": "Flat 10.50%"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
          "BgColor": "#FFFFFF"
        "3":
          "ArrayElement":
            "Position": 3
          "HeadingTemplate": "Own your funds"
          "SubHeadingTemplate": "Earn returns"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_UpArrow.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_UpArrow.png"
          "BgColor": "#FFFFFF"
        "4":
          "ArrayElement":
            "Position": 4
          "HeadingTemplate": "Get up to"
          "SubHeadingTemplate": "₹20 Lakh"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
      PrimaryCta:
        Text:
          PlainString: "Check loan amount"
          FontColor: "#6294A6"
          StandardFontStyle: "BUTTON_S"
        Deeplink:
          Screen: "PRE_APPROVED_LOAN_LANDING_SCREEN"
          PreApprovedLoanLandingScreenOptions:
            LoanProgram: 7
            EntryPoint: 9
        BgColor: "#E4F1F5"
    NoActiveOfferDisplayConfig:
      Heading:
        PlainString: "Get cash today"
        FontColor: "#313234"
        StandardFontStyle: "HEADLINE_M"
      HorizontalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Balance.png"
      VerticalPrimaryBannerImageUrl: "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Balance.png"
      WidgetTiles:
        "1":
          "ArrayElement":
            "Position": 1
          "HeadingTemplate": "Credit score"
          "SubHeadingTemplate": "NOT needed"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_smile.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_smile.png"
          "BgColor": "#FFFFFF"
        "2":
          "ArrayElement":
            "Position": 2
          "HeadingTemplate": "Interest rate"
          "SubHeadingTemplate": "Flat 10.50%"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
          "BgColor": "#FFFFFF"
        "3":
          "ArrayElement":
            "Position": 3
          "HeadingTemplate": "Own your funds"
          "SubHeadingTemplate": "Earn returns"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_UpArrow.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_UpArrow.png"
          "BgColor": "#FFFFFF"
        "4":
          "ArrayElement":
            "Position": 4
          "HeadingTemplate": "Get up to"
          "SubHeadingTemplate": "₹20 Lakh"
          "LeftIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
          "RightIconUrl": "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
          "BgColor": "#FFFFFF"
      PrimaryCta:
        Text:
          PlainString: "Check loan amount"
          FontColor: "#6294A6"
          StandardFontStyle: "BUTTON_S"
        Deeplink:
          Screen: "PRE_APPROVED_LOAN_LANDING_SCREEN"
          PreApprovedLoanLandingScreenOptions:
            LoanProgram: 7
            EntryPoint: 9
        BgColor: "#E4F1F5"
#    CampaignDisplayConfig:
#      Heading:
#        PlainString: "Get cash today"
#        FontColor: "#313234"
#        StandardFontStyle: "HEADLINE_M"
#      HorizontalPrimaryBannerImageUrl : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetVerticalPrimaryIcon_Bag.png"
#      VerticalPrimaryBannerImageUrl : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetPrimaryIcon_Bag.png"
#      WidgetTiles :
#        "1" :
#          "ArrayElement":
#            "Position": 1
#          "HeadingTemplate" : "Get up to"
#          "SubHeadingTemplate": "60L"
#          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Bag.png"
#          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Offer.png"
#          "BgColor" : "#FFFFFF"
#        "2" :
#          "ArrayElement":
#            "Position": 2
#          "HeadingTemplate" : "Interest rates"
#          "SubHeadingTemplate": "1%/month"
#          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Percentage.png"
#          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Interest.png"
#          "BgColor" : "#FFFFFF"
#        "3" :
#          "ArrayElement":
#            "Position": 3
#          "HeadingTemplate" : "Get loan in"
#          "SubHeadingTemplate": "5 min"
#          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Gift.png"
#          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
#          "BgColor" : "#FFFFFF"
#        "4" :
#          "ArrayElement":
#            "Position": 4
#          "HeadingTemplate" : "Approval odds"
#          "SubHeadingTemplate": "Excellent"
#          "LeftIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetLeftIcon_Smiley.png"
#          "RightIconUrl" : "https://epifi-icons.pointz.in/preapprovedloan/homeWidgetRightIcon_Timer.png"
#          "BgColor" : "#FFFFFF"
#      PrimaryCta:
#        Text:
#          PlainString: "Check eligibility"
#          FontColor: "#6294A6"
#          StandardFontStyle: "BUTTON_S"
#        Deeplink:
#          Screen: "PRE_APPROVED_LOAN_DASHBOARD_SCREEN"
#        BgColor: "#E4F1F5"


PgdbMigrationFlag: true

IsCampaignRelatedHomeDynamicElementEnabled: true

Flags:
  IsFldgLoanOverdueSherlockBannerEnabled: true
  IsOffAppPaymentV2Enabled: true
  IsRecommendationEngineEnabled: true
  HideIdfcOffer: false
  ShowNewSecondLookScreen: true
  MovePreQualToRealTime: true
  IsSgDigilockerEnabled: false
  UseBase64Image: true
  IsLdcApplicationMovementEnabled: false
  PreferUPIMandateTypeForLDC: false


PreApprovedLoanBucketName: "epifi-prod-preapprovedloan"

DeeplinkConfig:
  IsInitiateMandateEnrichmentEnabled: false
  IsAlternateAccountFlowEnabled: false
  OpenAbflMandateUrlViaExternal: true
  OpenAbflDigilockerUrlViaExternal: true
  OpenAbflDigilockerUrlViaExternalForIos: false
  OpenAbflMandateUrlViaExternalForIos: false
  OpenIdfcVkycUrlViaExternalForAndroid: true
  OpenIdfcVkycUrlViaExternalForIos: false
  # this config is duplicate version present at frontend as well, make sure to change the value at both places whenever needed.
  # this config is extended version of IsLoanDetailsSelectionV2FlowEnabled, make sure to change the value at both places whenever needed.
  LoanDetailsSelectionV2Flow:
    IsEnabled: true
    EnableLoanPrograms:
      - IDFC
      - FEDERAL_BANK
      - LOAN_PROGRAM_PRE_APPROVED_LOAN
      - LOAN_PROGRAM_FLDG
      - LOAN_PROGRAM_STPL
      - LOAN_PROGRAM_ACQ_TO_LEND
    DefaultAmountPercentage:
      - "LIQUILOANS": 0.95
      - "IDFC": 1
      - "FEDERAL": 1
  OfferDetailsV3Config:
    IsEnabled: true
    AppVersionConstraintConfig:
      MinAndroidVersion: 393
      MinIOSVersion: 545
    VendorLoanProgramMap:
      - "LIQUILOANS:LOAN_PROGRAM_FLDG": true
      - "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
      - "LIQUILOANS:LOAN_PROGRAM_STPL": true
      - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
      - "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN": false
      - "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN": false
      - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": false
      - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB": true
  AbflReferencesAppVersionConstraintConfig:
    MinAndroidVersion: 364
    MinIOSVersion: 514
  InfoItemV3MinVersion:
    MinAndroidVersion: 407
    MinIOSVersion: 568


CreditReportConfig:
  UseCreditReportV2: true
  StaleExperianReportThresholdDays: 45

QuestSdk:
  Disable: false

AllowedSegmentIdsForDisplayingLAMFHomeDynamicElement:
  - "79b60a3b-964d-41c4-a448-1d1de5658884"

FeatureReleaseConfig:
  FeatureConstraints:
    LOANS_FEDERAL_V2:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    # string to be in format "REPEAT_LOANS_<VENDOR>_<LOAN_PROGRAM>" e.g. REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL
    # use the same string as in enums to add feature release config if needed for controlled rollout.
    REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100

EsignLinkExpirationInSecs: 36000

EnableDataExistenceManager: true

MandateConfig:
  LiquiloansMandateConfig:
    IsMandateCoolOffCheckEnabled: true
    MinCoolOffMinutesBetweenMandateAttempts: 8
    IsMandateRequestBasedCountLogicEnabled: false
    IsPreviousMandateStatusCheckEnabled: false

LamfConfig:
  ReleaseSegmentDetails:
    Expression: "IsMember('79b60a3b-964d-41c4-a448-1d1de5658884')"

WealthToTechDataSharingConsentConfig:
  FeatureConfig:
    DisableFeature: false
  ScreenConfig:
    - "NET_WORTH_HUB_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 360h

    - "ANALYSER_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 360h

    - "SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 360h

    - "LOANS_DASHBOARD_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 360h

SmsFetchingConsentConfig:
  FeatureConfig:
    DisableFeature: false
    UnsupportedPlatforms: [ 2 ]
    MinAndroidVersion: 404
  ScreenConfig:
    - "NET_WORTH_HUB_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 360h

    - "ANALYSER_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 360h

    - "SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN":
        Enabled: true
        CollectionAttemptCooloffDuration: 360h

VendorProgramLevelFeature:
  VendorProgramActiveMap:
    "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_FLDG":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_STPL":
      IsAllowed: false
    "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: false
    "FEDERAL:LOAN_PROGRAM_FED_REAL_TIME":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
      IsAllowed: false
    "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
      IsAllowed: false
    "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: false
    "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_EARLY_SALARY_V2":
      IsAllowed: true
  NonFiCoreVendorProgramActiveMap:
    - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: false
    - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
        IsAllowed: true
    - "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true

  DownTimeConfig:
    - FEDERAL:
        # Time is specified in IST (Indian Standard Time)
        Start: "31-03-2025T15:00:00"
        End: "02-04-2025T10:00:00"

Lendability:
  Url: "https://loan-default.data-prod.epifi.in"
  EvaluationRuleMatrix:
    - "PD_CATEGORY_LOW:LOAN_AFFINITY_CATEGORY_HIGH":
        Value: 100
    - "PD_CATEGORY_LOW:LOAN_AFFINITY_CATEGORY_MEDIUM":
        Value: 100
    - "PD_CATEGORY_LOW:LOAN_AFFINITY_CATEGORY_LOW":
        Value: 100
    - "PD_CATEGORY_MEDIUM:LOAN_AFFINITY_CATEGORY_HIGH":
        Value: 100
    - "PD_CATEGORY_MEDIUM:LOAN_AFFINITY_CATEGORY_MEDIUM":
        Value: 100
    - "PD_CATEGORY_MEDIUM:LOAN_AFFINITY_CATEGORY_LOW":
        Value: 10
    - "PD_CATEGORY_HIGH:LOAN_AFFINITY_CATEGORY_HIGH":
        Value: 10
    - "PD_CATEGORY_HIGH:LOAN_AFFINITY_CATEGORY_MEDIUM":
        Value: 10
    - "PD_CATEGORY_HIGH:LOAN_AFFINITY_CATEGORY_LOW":
        Value: 10

SgEtbNewEligibilityFlow:
  IsAllowed: true


LopeOverrideConfig:
  ShowSgEligibilityOverAbflSoftOffer: false
  ShowSgEligibilityOverMvSoftOffer: false
  #Vendor and LoanProgram are mandatory
  #One of IsOffer or IsEligibillity is mandatory
  #Offer type is optional
  LoanPriorityOrderNonFiCore:
    - Vendor: "FEDERAL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "MONEYVIEW"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "STOCK_GUARDIAN_LSP"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "LENDEN"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "ABFL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_HARD"
    - Vendor: "FEDERAL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_SOFT"
    - Vendor: "MONEYVIEW"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_SOFT"
    - Vendor: "STOCK_GUARDIAN_LSP"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_SOFT"
    - Vendor: "LENDEN"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_SOFT"
    - Vendor: "ABFL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_SOFT"
    - Vendor: "FEDERAL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_PRE_QUALIFIED"
    - Vendor: "MONEYVIEW"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_PRE_QUALIFIED"
    - Vendor: "STOCK_GUARDIAN_LSP"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_PRE_QUALIFIED"
    - Vendor: "LENDEN"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_PRE_QUALIFIED"
    - Vendor: "ABFL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: true
      IsEligibility: false
      LoanOfferType: "LOAN_OFFER_TYPE_PRE_QUALIFIED"
    - Vendor: "STOCK_GUARDIAN_LSP"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: false
      IsEligibility: true
      LoanOfferType: ""
    - Vendor: "LENDEN"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: false
      IsEligibility: true
      LoanOfferType: ""
    - Vendor: "FEDERAL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB"
      IsOffer: false
      IsEligibility: true
      LoanOfferType: ""
    - Vendor: "MONEYVIEW"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: false
      IsEligibility: true
      LoanOfferType: ""
    - Vendor: "ABFL"
      LoanProgram: "LOAN_PROGRAM_REAL_TIME_DISTRIBUTION"
      IsOffer: false
      IsEligibility: true
      LoanOfferType: ""

  LoanPriorityOrderFiCore:
AutoCancelCurrentLrConfig:
  MinAndroidVersion: 461
  MinIOSVersion: 625

ConcurrentLoansConfig:
  Enabled: true
  LoanTypeToMinFreezeDuration:
    LOAN_TYPE_PERSONAL: 1080h # 45 days
    LOAN_TYPE_EARLY_SALARY: 1080h # 45 days
    LOAN_TYPE_SECURED_LOAN: 0
