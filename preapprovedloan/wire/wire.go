//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"errors"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/google/wire"
	temporalClient "go.temporal.io/sdk/client"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cmd/types"
	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/vendorapi"
	vendorapiGenConf "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	lendenPkg "github.com/epifi/gamma/preapprovedloan/pkg/lenden"

	palEvents "github.com/epifi/gamma/preapprovedloan/events"

	"github.com/epifi/gamma/preapprovedloan/dao/wrapper"
	"github.com/epifi/gamma/preapprovedloan/dao/wrapper/observers"
	"github.com/epifi/gamma/preapprovedloan/preeligibility"
	"github.com/epifi/gamma/preapprovedloan/preeligibility/steps"

	priorityPostProcessor "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers/postproc"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	locationpb "github.com/epifi/gamma/api/auth/location"
	orchPb "github.com/epifi/gamma/api/auth/orchestrator"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	brePb "github.com/epifi/gamma/api/bre"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/collection"
	commsPb "github.com/epifi/gamma/api/comms"
	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	consentpb "github.com/epifi/gamma/api/consent"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	docsPb "github.com/epifi/gamma/api/docs"
	esignPb "github.com/epifi/gamma/api/docs/esign"
	beEmploymentPb "github.com/epifi/gamma/api/employment"
	epfoPb "github.com/epifi/gamma/api/epfo"
	catalogManagerPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mfexternalorderpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	leadsPb "github.com/epifi/gamma/api/leads"
	"github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/omegle"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	securedLoansPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	"github.com/epifi/gamma/api/product"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	riskPb "github.com/epifi/gamma/api/risk"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	sgApplicantApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/applicant"
	sgApplicantionApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	sgCustomerApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/customer"
	sgEsignApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/esign"
	sgKycApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	sgLmsPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	sgMatrixApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/matrix"
	userPb "github.com/epifi/gamma/api/user"
	creditReportPb "github.com/epifi/gamma/api/user/credit_report"
	"github.com/epifi/gamma/api/user/group"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	abflVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	idfcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	lendenVgClient "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	mvVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	ffVgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	setuVgPb "github.com/epifi/gamma/api/vendorgateway/lending/setu"
	"github.com/epifi/gamma/api/vendorgateway/location"
	accountVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	savingsVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	panVgPb "github.com/epifi/gamma/api/vendorgateway/pan"
	profileValidationPb "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	httpPkg "github.com/epifi/gamma/pkg/http"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/activity/abfl"
	"github.com/epifi/gamma/preapprovedloan/activity/brecaller"
	common2 "github.com/epifi/gamma/preapprovedloan/activity/common"
	"github.com/epifi/gamma/preapprovedloan/activity/datacompleteness"
	"github.com/epifi/gamma/preapprovedloan/activity/federal"
	"github.com/epifi/gamma/preapprovedloan/activity/fiftyfin"
	"github.com/epifi/gamma/preapprovedloan/activity/idfc"
	"github.com/epifi/gamma/preapprovedloan/activity/lenden"
	"github.com/epifi/gamma/preapprovedloan/activity/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/activity/moneyview"
	"github.com/epifi/gamma/preapprovedloan/activity/stock_guardian"
	"github.com/epifi/gamma/preapprovedloan/activity/syncproxy"
	"github.com/epifi/gamma/preapprovedloan/activity/vendors"
	calculatorWire "github.com/epifi/gamma/preapprovedloan/calculator/wire"
	"github.com/epifi/gamma/preapprovedloan/config"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	configWorker "github.com/epifi/gamma/preapprovedloan/config/worker"
	palWorkerGConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	palConsent "github.com/epifi/gamma/preapprovedloan/consent"
	"github.com/epifi/gamma/preapprovedloan/consumer"
	preapprovedloanCx "github.com/epifi/gamma/preapprovedloan/cx"
	"github.com/epifi/gamma/preapprovedloan/cx/factory"
	"github.com/epifi/gamma/preapprovedloan/cx/providers"
	"github.com/epifi/gamma/preapprovedloan/cx/providers/impl"
	palDao "github.com/epifi/gamma/preapprovedloan/dao"
	daoImpl "github.com/epifi/gamma/preapprovedloan/dao/impl"
	dataExistenceManager "github.com/epifi/gamma/preapprovedloan/data_existence_manager"
	"github.com/epifi/gamma/preapprovedloan/datacollectors/p2p"
	"github.com/epifi/gamma/preapprovedloan/dcpp"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	fiftyfinDeeplink "github.com/epifi/gamma/preapprovedloan/deeplink/provider/fiftyfin"
	vkycFailureHandlers "github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc/vkyc/failure_handlers"
	lenden2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/lenden"
	"github.com/epifi/gamma/preapprovedloan/developer"
	"github.com/epifi/gamma/preapprovedloan/developer/processor"
	"github.com/epifi/gamma/preapprovedloan/downtime"
	"github.com/epifi/gamma/preapprovedloan/dynamic_elements"
	deProvider "github.com/epifi/gamma/preapprovedloan/dynamic_elements/provider"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/helper/agreement"
	palInboundNotification "github.com/epifi/gamma/preapprovedloan/inbound_notification"
	"github.com/epifi/gamma/preapprovedloan/landing_provider"
	"github.com/epifi/gamma/preapprovedloan/lendability"
	ldbtDc "github.com/epifi/gamma/preapprovedloan/lendability/datacollector"
	"github.com/epifi/gamma/preapprovedloan/lms"
	loandataprovider "github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	loanTxnProvider "github.com/epifi/gamma/preapprovedloan/loan_txn_provider"
	loanPlans "github.com/epifi/gamma/preapprovedloan/loanplans"
	mandateView "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view"
	federalMandateView "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view/providers/federal"
	idfcMandateView "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view/providers/idfc"
	lendenMandateView "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view/providers/lenden"
	liquiloans2 "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view/providers/liquiloans"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	de "github.com/epifi/gamma/preapprovedloan/multidb_provider/decision_engine"
	"github.com/epifi/gamma/preapprovedloan/offer_manager"
	"github.com/epifi/gamma/preapprovedloan/offer_manager/providers/fiftyfin/lamf"
	"github.com/epifi/gamma/preapprovedloan/preclose"
	"github.com/epifi/gamma/preapprovedloan/prepay"
	eligibilityEvaluator "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator"
	eligibilityEvaluatorProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	fedEligibilityEvaluatorProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/federal"
	fiftyfinEligibilityEvaluatorProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/fiftyfin"
	lendenEligibilityEvaluatorProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/lenden"
	llEligibilityEvaluatorProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/liquiloans"
	moneyViewEtbEligibilityEvaluatorProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/moneyview"
	SgEligibilityEvaluatorProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/stock_guardian"
	priorityProviderFactory "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider"
	dcPriorityProviderFactory "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/data_collector"
	dcPriorityProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/data_collector/provider"
	priorityProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers"
	reProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
	reconciliationWire "github.com/epifi/gamma/preapprovedloan/reconciliation/wire"
	"github.com/epifi/gamma/preapprovedloan/secured_loans"
	fiftyfin2 "github.com/epifi/gamma/preapprovedloan/secured_loans/activity/fiftyfin"
	daoImpl2 "github.com/epifi/gamma/preapprovedloan/secured_loans/dao/impl"
	"github.com/epifi/gamma/preapprovedloan/sherlock_banners"
	"github.com/epifi/gamma/preapprovedloan/userdata"
	fiftyfin3 "github.com/epifi/gamma/preapprovedloan/vendor_data_provider/fiftyfin"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
)

func newGormTxnExecutorProvider(dbConn *gorm.DB) storageV2.TxnExecutor {
	return storageV2.NewGormTxnExecutor(dbConn)
}

func newMultiDbDoOnce(dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB]) onceV2.MultiDbDoOnce {
	return onceV2.NewMultiDbDoOnce(dbResourceProvider)
}

func notificationConfigProvider(conf *config.Config) *common.Notification {
	return conf.Notification
}

func prePayConfigProvider(conf *config.Config) *common.Prepay {
	return conf.Prepay
}

func getDeeplinkConf(conf *genconf.Config) *commonGenConf.DeeplinkConfig {
	return conf.DeeplinkConfig()
}

func getFlagsConf(conf *genconf.Config) *commonGenConf.Flags {
	return conf.Flags()
}

func getFeaturesConf(conf *genconf.Config) *commonGenConf.VendorProgramLevelFeature {
	return conf.VendorProgramLevelFeature()
}

func getLoanCalculatorConstraintConf(conf *config.Config) *common.LoanCalculator {
	return conf.LoanCalculator
}

func getCreditReportConfig(conf *genconf.Config) *commonGenConf.CreditReportConfig {
	return conf.CreditReportConfig()
}

func getPlClientProvider(cl types2.PreApprovedLoanClient) temporalClient.Client {
	return cl
}

func InitialisePreApprovedLoanSvc(
	crdb types.LoansFederalPGDB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	preApprovedLoanClient palVgPb.PreApprovedLoanClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	broker events.Broker,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	signalPublisher types2.SignalWorkflowPublisher,
	authClient authPb.AuthClient,
	eSignClient esignPb.ESignClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	conf *config.Config,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	llPalVgClient llVgPb.LiquiloansClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	palEventPublisher types2.NudgeExitEventPublisher,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	idfcVgClient idfcVgPb.IdfcClient,
	segmentationServiceClient segment.SegmentationServiceClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	collClient collection.CollectionClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	dynConf *genconf.Config,
	awsConf aws.Config,
	cardProvisioningClient cardPb.CardProvisioningClient,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mvVgClient mvVgPb.MoneyviewClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	securedLoansClient securedLoansPb.SecuredLoansClient,
	userGroupClient userGroupPb.GroupClient,
	nudgeClient nudge.NudgeServiceClient,
	finFluxVgClient finflux.FinfluxClient,
	userIntelClient userIntelPb.UserIntelServiceClient,
	temporalClient types2.PreApprovedLoanClient,
	redisStore types2.FireflyRedisStore,
	targetedCommsClient tcPb.InAppTargetedCommsClient,
	consentClient consentpb.ConsentClient,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	ldcClient ldcVgPb.LendenClient,
	orchClient orchPb.OrchestratorClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	locationClient locationpb.LocationClient,
	sgKycClient sgKycApiGatewayPb.KYCClient,
) (*preapprovedloan.Service, error) {
	wire.Build(
		types.LoansFederalPGDBGormDBProvider,
		newGormTxnExecutorProvider,
		notificationConfigProvider,
		prePayConfigProvider,
		getPlClientProvider,
		idgen.NewClock,
		idgen.WireSet,
		calculatorWire.Set,
		loanPlans.WireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanRequestsDaoMultiDBWireSet,
		daoImpl.LoanStepExecutionsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		observers.NewAppsFlyerEventObserver,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		daoImpl.RegularLoanOffersEligibilityCriteriaDaoWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.MandateRequestDaoWireSet,
		multiDbProvider.MultiDbProviderWireSet,
		datetimePkg.WireDefaultTimeSet,
		reconciliationWire.Set,
		downtime.WireSet,
		vkycFailureHandlers.WireSet,
		baseprovider.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		helper.NewRpcHelper,
		loanTxnProvider.WireSet,
		de.SimpleOffersDecisionEngineWireSet,
		loandataprovider.WireSet,
		getDeeplinkConf,
		getFeaturesConf,
		getLoanCalculatorConstraintConf,
		getCreditReportConfig,
		newMultiDbDoOnce,
		landing_provider.LandingProviderWireSet,
		deProvider.AllDynamicElementsProvidersWireSet,
		dynamic_elements.FactoryWireSet,
		preapprovedloan.NewService,
		palConsent.NewConsentManagerHelper,
		preclose.WireSet,
		lmsConfigProvider,
		lms.WireSet,
		prepay.WireSet,
		release.EvaluatorWireSet,
		featureReleaseConfigProvider,
		mandateView.MandateViewFactoryWireSet,
		liquiloans2.NewLLMandateViewProvider,
		getMandateConf,
		idfcMandateView.NewIdfcMandateViewProvider,
		lendenMandateView.NewLendenMandateViewProvider,
		lendenPkg.NewMandateSetupProcessor,
		getPreferUPIMandateTypeFlagFromServerConf,
		federalMandateView.NewProvider,
		daoImpl.PartnerLmsUserDaoWireSet,
		idgen.UuidGeneratorWireSet,
		types2.CacheStorageProvider,
		dataExistenceManager.WireSet,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		priorityProviderFactory.PriorityProviderFactoryWireSet,
		priorityProvider.NewCommon,
		priorityProvider.NewNonFiCore,
		priorityProvider.NewSecuredLoanProvider,
		priorityPostProcessor.PriorityProviderPostProcessorWireSet,
		lopeOverrideServiceConfigProvider,
		dcPriorityProviderFactory.DataCollPrioProviderFactoryWireSet,
		dcPriorityProvider.NewNonFiCoreDataCollectorPriorityProvider,
		eligibilityEvaluator.EligibilityEvaluatorFactoryWireSet,
		eligibilityEvaluatorProviders.NewCommonEligibilityProvider,
		eligibilityEvaluatorProviders.NewRealtimeCommonProvider,
		llEligibilityEvaluatorProviders.NewLLFiliteProvider,
		llEligibilityEvaluatorProviders.NewRealtimeDistEligibilityProvider,
		SgEligibilityEvaluatorProviders.NewSgRealtimeDistEligibilityProvider,
		eligibilityEvaluatorProviders.NewNonFiCoreProvider,
		moneyViewEtbEligibilityEvaluatorProviders.NewMoneyViewRTDETBProvider,
		fedEligibilityEvaluatorProviders.NewFedRealtimeDistEligibilityProvider,
		fedEligibilityEvaluatorProviders.NewFedPlEtbProvider,
		fiftyfinEligibilityEvaluatorProviders.NewFiftyfinLamfProvider,
		fedEligibilityEvaluatorProviders.NewFedRealtimeDistNtbEligibilityProvider,
		lendenEligibilityEvaluatorProviders.NewRealtimeDistEligibilityProvider,
		fiftyfin3.WireVendorDataProviderSet,
		reProvider.WireSet,
		palConsent.AllConsentManagerWireSet,
		palConsent.IConsentManagerFactoryWireSet,
		p2p.PreBreLoanPreferencesSaverWireSet,
		p2p.NewPreBreConsentDataSaverWireSet,
		p2p.NewModifiedRoiDataSaverWireSet,
		lenden2.NewEligibilityProvider,
		dcpp.DefaultDataCollectionPriorityProviderWireSet,
		userdata.CommonUserDataProviderWireSet,
		helper.NewConnectedSalaryAccountProvider,
		getSgNewEligibilityFlowConf,
		getFlagsConf,
	)
	return &preapprovedloan.Service{}, nil
}

func lmsConfigProvider(conf *config.Config) *common.Lms {
	return conf.Lms
}

func lmsWorkerConfigProvider(conf *configWorker.Config) *common.Lms {
	return conf.Lms
}

func featureReleaseConfigProvider(conf *genconf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func featureReleaseWorkerConfigProvider(conf *palWorkerGConf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func InitializeConsumerService(
	db types.LoansFederalPGDB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	actorClient actorPb.ActorClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	awsConf aws.Config,
	userClient userPb.UsersClient,
	savingsClient savings.SavingsClient,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	eSignClient esignPb.ESignClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	conf *config.Config,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	eventsBroker events.Broker,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	llPalVgClient llVgPb.LiquiloansClient,
	profileClient profilePb.ProfileClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	idfcVgClient idfcVgPb.IdfcClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	dynConf *genconf.Config,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	abflVgClient abflVgPb.AbflClient,
	segmentationClient segment.SegmentationServiceClient,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	redisStore types2.FireflyRedisStore,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	locationClient locationpb.LocationClient,
) (*consumer.Service, error) {
	wire.Build(
		types.LoansFederalPGDBGormDBProvider,
		notificationConfigProvider,
		newGormTxnExecutorProvider,
		idgen.NewClock,
		idgen.WireSet,
		consumer.NewService,
		helper.NewRpcHelper,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanOffersDaoWireSet,
		daoImpl.LoanStepExecutionDaoWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		observers.NewAppsFlyerEventObserver,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		daoImpl.LoanActivityDaoWireSet,
		daoImpl.LoanInstallmentInfoDaoWireSet,
		daoImpl.LoanAccountsDaoWireSet,
		daoImpl.LoanInstallmentPayoutDaoWireSet,
		daoImpl.LoanRequestDaoWireSet,
		daoImpl.LoanApplicantDaoWireSet,
		getCreditReportConfig,
		InitCryptors,
		types2.CacheStorageProvider,
	)
	return &consumer.Service{}, nil
}

func InitCryptors(conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	// create a pgp cryptor to be used for vendor communication encryption
	pgpCryptor := pgp.New(conf.Secrets.Ids[config.FederalPgpPublicKey],
		conf.Secrets.Ids[config.EpifiFederalPgpPrivateKey], conf.Secrets.Ids[config.EpifiFederalPgpPassphrase])
	if pgpCryptor == nil {
		return nil, errors.New("failed to create PGP cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP, pgpCryptor)

	return cryptorStore, nil
}

func getDeeplinkConfFromWorkerGenConf(conf *palWorkerGConf.Config) *commonGenConf.DeeplinkConfig {
	return conf.DeeplinkConfig()
}

func getMandateConfFromWorkerGenConf(conf *palWorkerGConf.Config) *commonGenConf.MandateConfig {
	return conf.MandateConfig()
}

func getFeaturesFromWorkerGenConf(conf *palWorkerGConf.Config) *commonGenConf.VendorProgramLevelFeature {
	return conf.VendorProgramLevelFeature()
}

func lopeOverrideWorkerConfigProvider(conf *palWorkerGConf.Config) *commonGenConf.LopeOverrideConfig {
	return conf.LopeOverrideConfig()
}

func lopeOverrideServiceConfigProvider(conf *genconf.Config) *commonGenConf.LopeOverrideConfig {
	return conf.LopeOverrideConfig()
}

func getMandateConf(conf *genconf.Config) *commonGenConf.MandateConfig {
	return conf.MandateConfig()
}

func getFlagsConfFromWorkerGenConf(conf *palWorkerGConf.Config) *commonGenConf.Flags {
	return conf.Flags()
}

func getLoanCalculatorConfFromWorker(conf *configWorker.Config) *common.LoanCalculator {
	return conf.LoanCalculator
}

func getCreditReportConfFromWorkerGenConf(conf *palWorkerGConf.Config) *commonGenConf.CreditReportConfig {
	return conf.CreditReportConfig()
}

func InitialiseActivityProcessor(db *gorm.DB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	txnExecutor storageV2.TxnExecutor,
	persistentQueue persistentqueue.PersistentQueue,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	client profileValidationPb.ProfileValidationClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	orchClient orchPb.OrchestratorClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	conf *configWorker.Config,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	idfcVgClient idfcVgPb.IdfcClient,
	docsClient docsPb.DocsClient,
	userGroupClient userGroupPb.GroupClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	riskClient riskPb.RiskClient,
	collectionClient collection.CollectionClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	epfoClient epfoPb.EpfoClient,
	productClient product.ProductClient,
	segmentationClient segment.SegmentationServiceClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerGenConf *palWorkerGConf.Config,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	nudgeClient nudge.NudgeServiceClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	finFluxVgClient finflux.FinfluxClient,
	userIntelClient userIntelPb.UserIntelServiceClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	redisStore types2.FireflyRedisStore,
	breClient brePb.BreClient,
	dataDevS3Client types2.DataDevS3Client,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	ldcClient ldcVgPb.LendenClient,
	mvVgClient mvVgPb.MoneyviewClient,
	locationClient locationpb.LocationClient,
	salaryEstimationClient salaryestimation.SalaryEstimationClient,
) (*activity.Processor, error) {
	wire.Build(
		datetimePkg.WireDefaultTimeSet,
		httpPkg.ClientWireSet,
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		prePayConfigProviderWorker,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		observers.NewAppsFlyerEventObserver,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		baseprovider.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		prepay.WireSet,
		preclose.WireSet,
		vkycFailureHandlers.WireSet,
		activity.NewProcessor,
		activity.NewFactory,
		vendors.NewFederal,
		vendors.NewLiquiloansPersonalLoan,
		vendors.NewLiquiloansEarlySalary,
		lmsWorkerConfigProvider,
		lms.WireSet,
		agreement.NewAgreementProvider,
		loandataprovider.WireSet,
		getFeaturesFromWorkerGenConf,
		getDeeplinkConfFromWorkerGenConf,
		getFlagsConfFromWorkerGenConf,
		getLoanCalculatorConfFromWorker,
		getCreditReportConfFromWorkerGenConf,
		release.EvaluatorWireSet,
		featureReleaseWorkerConfigProvider,
		daoImpl.PartnerLmsUserDaoWireSet,
		idgen.UuidGeneratorWireSet,
		types2.CacheStorageProvider,
		daoImpl.RegularLoanOffersEligibilityCriteriaDaoWireSet,
		priorityProviderFactory.PriorityProviderFactoryWireSet,
		priorityProvider.NewCommon,
		priorityProvider.NewNonFiCore,
		priorityProvider.NewSecuredLoanProvider,
		priorityPostProcessor.PriorityProviderPostProcessorWireSet,
		lopeOverrideWorkerConfigProvider,
		calculatorWire.Set,
		eligibilityEvaluator.EligibilityEvaluatorFactoryWireSet,
		eligibilityEvaluatorProviders.NewCommonEligibilityProvider,
		eligibilityEvaluatorProviders.NewRealtimeCommonProvider,
		llEligibilityEvaluatorProviders.NewLLFiliteProvider,
		llEligibilityEvaluatorProviders.NewRealtimeDistEligibilityProvider,
		SgEligibilityEvaluatorProviders.NewSgRealtimeDistEligibilityProvider,
		eligibilityEvaluatorProviders.NewNonFiCoreProvider,
		moneyViewEtbEligibilityEvaluatorProviders.NewMoneyViewRTDETBProvider,
		fedEligibilityEvaluatorProviders.NewFedRealtimeDistEligibilityProvider,
		fedEligibilityEvaluatorProviders.NewFedPlEtbProvider,
		fiftyfinEligibilityEvaluatorProviders.NewFiftyfinLamfProvider,
		fedEligibilityEvaluatorProviders.NewFedRealtimeDistNtbEligibilityProvider,
		lendenEligibilityEvaluatorProviders.NewRealtimeDistEligibilityProvider,
		de.SimpleOffersDecisionEngineWireSet,
		multiDbProvider.MultiDbProviderWireSet,
		reProvider.WireSet,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		fiftyfin3.WireVendorDataProviderSet,
		lenden2.NewEligibilityProvider,
		datacompleteness.DataCompletenessFactoryWireSet,
		datacompleteness.NewLendenDataCompletenessChecker,
		datacompleteness.NewEpifiDataCompletenessChecker,
		brecaller.FactoryWireSet,
		brecaller.NewLendenBreCaller,
		brecaller.NewEpifiBreCaller,
		brecaller.NewFederalBreCaller,
		userdata.CommonUserDataProviderWireSet,
		getSgNewEligibilityFlowActivityConf,
	)
	return &activity.Processor{}, nil
}

func InitializePreApprovedLoanCxService(
	db types.LoansFederalPGDB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	paymentClient paymentPb.PaymentClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	commsClient commsPb.CommsClient,
	conf *config.Config,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	savingsVgClient savingsVgPb.SavingsClient,
	llPalVgClient llVgPb.LiquiloansClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	idfcVgClient idfcVgPb.IdfcClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	dynConf *genconf.Config,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	epifiCRDB types.EpifiCRDB,
	segmentationClient segment.SegmentationServiceClient,
	awsConf aws.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	finFluxVgClient finflux.FinfluxClient,
	redisStore types2.FireflyRedisStore,
	broker events.Broker,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	ldcClient ldcVgPb.LendenClient,
	mvVgClient mvVgPb.MoneyviewClient,
	locationClient locationpb.LocationClient,
	userGroupClient group.GroupClient,
) *preapprovedloanCx.Service {
	wire.Build(
		datetimePkg.WireDefaultTimeSet,
		types.LoansFederalPGDBGormDBProvider,
		notificationConfigProvider,
		idgen.NewClock,
		idgen.WireSet,
		multiDbProvider.MultiDbProviderWireSet,
		de.SimpleOffersDecisionEngineWireSet,
		daoImpl.RegularLoanOffersEligibilityCriteriaDaoWireSet,
		getFlagsConf,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		observers.NewAppsFlyerEventObserver,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanRequestsDaoMultiDBWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanStepExecutionsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		getFeaturesConf,
		getLoanCalculatorConstraintConf,
		calculatorWire.Set,
		lopeOverrideServiceConfigProvider,
		priorityPostProcessor.PriorityProviderPostProcessorWireSet,
		priorityProvider.NewCommon,
		priorityProvider.NewNonFiCore,
		priorityProvider.NewSecuredLoanProvider,
		priorityProviderFactory.PriorityProviderFactoryWireSet,
		llEligibilityEvaluatorProviders.NewLLFiliteProvider,
		fedEligibilityEvaluatorProviders.NewFedRealtimeDistEligibilityProvider,
		fedEligibilityEvaluatorProviders.NewFedPlEtbProvider,
		fiftyfinEligibilityEvaluatorProviders.NewFiftyfinLamfProvider,
		fedEligibilityEvaluatorProviders.NewFedRealtimeDistNtbEligibilityProvider,
		eligibilityEvaluatorProviders.NewNonFiCoreProvider,
		moneyViewEtbEligibilityEvaluatorProviders.NewMoneyViewRTDETBProvider,
		eligibilityEvaluatorProviders.NewRealtimeCommonProvider,
		SgEligibilityEvaluatorProviders.NewSgRealtimeDistEligibilityProvider,
		lendenEligibilityEvaluatorProviders.NewRealtimeDistEligibilityProvider,
		eligibilityEvaluatorProviders.NewCommonEligibilityProvider,
		llEligibilityEvaluatorProviders.NewRealtimeDistEligibilityProvider,
		eligibilityEvaluator.EligibilityEvaluatorFactoryWireSet,
		reProvider.WireSet,
		preapprovedloanCx.NewService,
		persistentqueue.NewPersistentQueue,
		helper.NewRpcHelper,
		getCreditReportConfig,
		loandataprovider.WireSet,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		types2.CacheStorageProvider,
		factory.NewProviderFactory,
		providers.NewBaseDataProvider,
		impl.NewLamfDataProvider,
		fiftyfin3.WireVendorDataProviderSet,
		getSgNewEligibilityFlowConf,
	)
	return &preapprovedloanCx.Service{}
}

func InitializePreApprovedLoanDevEntityService(
	db types.LoansFederalPGDB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	redisStore types2.FireflyRedisStore,
	eventsBroker events.Broker,
	savingsClient savings.SavingsClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
) *developer.PreApprovedLoanDevEntity {
	wire.Build(
		// types.LoansFederalPGDBGormDBProvider,
		idgen.NewClock,
		idgen.WireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanRequestsDaoMultiDBWireSet,
		daoImpl.LoanStepExecutionsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		daoImpl.PgdbPreEligibilityOfferDaoWireSet,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		observers.NewAppsFlyerEventObserver,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.PartnerLmsUserDaoWireSet,
		daoImpl2.FetchedAssetDaoWireSet,
		daoImpl.MandateRequestDaoWireSet,
		developer.NewPreApprovedLoanDevEntity,
		developer.NewDevFactory,
		processor.NewDevLoanOfferEntity,
		processor.NewDevLoanRequestsEntity,
		processor.NewDevLoanAccountsEntity,
		processor.NewDevLoanStepExecutionEntity,
		processor.NewDevLoanOfferEligibilityCriteriaEntity,
		processor.NewDevLoanActivityEntity,
		processor.NewDevLoanInstallmentInfoEntity,
		processor.NewDevLoanInstallmentPayoutEntity,
		processor.NewDevLoanPaymentRequestEntity,
		processor.NewDevLoanApplicantsEntity,
		processor.NewDevPartnerLmsUserEntity,
		processor.NewDevPreEligibilityOfferEntity,
		processor.NewFetchedAssetEntity,
		processor.NewDevMandateRequestsEntity,
		processor.NewDevLoansMasterEntity,
		types2.CacheStorageProvider,
	)
	return &developer.PreApprovedLoanDevEntity{}
}

func InitializeInboundNotificationService(
	dbConn types.LoansFederalPGDB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	preApprovedLoanClient palVgPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	eSignClient esignPb.ESignClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	commsClient commsPb.CommsClient,
	conf *config.Config,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	llPalVgClient llVgPb.LiquiloansClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	idfcVgClient idfcVgPb.IdfcClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	dynConf *genconf.Config,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	awsConf aws.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	redisStore types2.FireflyRedisStore,
	broker events.Broker,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	locationClient locationpb.LocationClient,
) *palInboundNotification.Service {
	wire.Build(
		types.LoansFederalPGDBGormDBProvider,
		newGormTxnExecutorProvider,
		notificationConfigProvider,
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanStepExecutionDaoWireSet,
		daoImpl.LoanAccountsDaoWireSet,
		daoImpl.LoanRequestDaoWireSet,
		daoImpl.LoanActivityDaoWireSet,
		daoImpl.LoanInstallmentInfoDaoWireSet,
		daoImpl.LoanInstallmentPayoutDaoWireSet,
		daoImpl.LoanApplicantDaoWireSet,
		palInboundNotification.NewService,
		getCreditReportConfig,
		types2.CacheStorageProvider,
	)
	return &palInboundNotification.Service{}
}

func InitialiseFederalActivityProcessor(db *gorm.DB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	txnExecutor storageV2.TxnExecutor,
	persistentQueue persistentqueue.PersistentQueue,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	client profileValidationPb.ProfileValidationClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	orchClient orchPb.OrchestratorClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	awsConf aws.Config,
	conf *configWorker.Config,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	idfcVgClient idfcVgPb.IdfcClient,
	docsClient docsPb.DocsClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	nudgeClient nudge.NudgeServiceClient,
	finFluxVgClient finflux.FinfluxClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	redisStore types2.FireflyRedisStore,
	setuClient setuVgPb.SetuClient,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	ldcClient ldcVgPb.LendenClient,
	mvVgClient mvVgPb.MoneyviewClient,
	enachClient enachPb.EnachServiceClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	locationClient locationpb.LocationClient,
) *federal.Processor {
	wire.Build(
		datetimePkg.WireDefaultTimeSet,
		idgen.NewClock,
		idgen.WireSet,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		observers.NewAppsFlyerEventObserver,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		helper.NewRpcHelper,
		helper.NewCommsHelper,
		baseprovider.WireSet,
		federal.NewProcessor,
		lmsWorkerConfigProvider,
		lms.WireSet,
		loandataprovider.WireSet,
		getDeeplinkConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		deeplink.WireDeeplinkProviderFactorySet,
		vkycFailureHandlers.WireSet,
		idgen.UuidGeneratorWireSet,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		types2.CacheStorageProvider,
		newMultiDbDoOnce,
		fiftyfin3.WireVendorDataProviderSet,
		lenden2.NewEligibilityProvider,
	)
	return &federal.Processor{}
}

func InitialiseLlActivityProcessor(db *gorm.DB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	llPalVgClient llVgPb.LiquiloansClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	client profileValidationPb.ProfileValidationClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	txnExecutor storageV2.TxnExecutor,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	eventsBroker events.Broker,
	orchClient orchPb.OrchestratorClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	idfcVgClient idfcVgPb.IdfcClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	breClient brePb.BreClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	dataDevS3Client types2.DataDevS3Client,
	abflVgClient abflVgPb.AbflClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	incomeEstimatorClient incomeestimator.IncomeEstimatorClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	nudgeClient nudge.NudgeServiceClient,
	userGroupClient userGroupPb.GroupClient,
	finFluxVgClient finflux.FinfluxClient,
	userIntelClient userIntelPb.UserIntelServiceClient,
	compClient compliancePb.ComplianceClient,
	redisStore types2.FireflyRedisStore,
	riskClient riskPb.RiskClient,
	oprStatusClient operStatusPb.OperationalStatusServiceClient,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	ldcClient ldcVgPb.LendenClient,
	mvVgClient mvVgPb.MoneyviewClient,
	locationClient locationpb.LocationClient,
) *liquiloans.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		observers.NewAppsFlyerEventObserver,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		daoImpl.MandateRequestDaoWireSet,
		vkycFailureHandlers.WireSet,
		baseprovider.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		liquiloans.NewProcessor,
		activity.NewCreditReportHelper,
		loandataprovider.WireSet,
		lmsWorkerConfigProvider,
		lms.WireSet,
		datetimePkg.WireDefaultTimeSet,
		getDeeplinkConfFromWorkerGenConf,
		getMandateConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		newMultiDbDoOnce,
		helper.NewCommsHelper,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		release.EvaluatorWireSet,
		idgen.UuidGeneratorWireSet,
		featureReleaseWorkerConfigProvider,
		types2.CacheStorageProvider,
		fiftyfin3.WireVendorDataProviderSet,
		calculatorWire.Set,
		getLoanCalculatorConfFromWorker,
		lenden2.NewEligibilityProvider,
	)
	return &liquiloans.Processor{}
}

func InitialiseIdfcActivityProcessor(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcPalVgClient idfcVgPb.IdfcClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	orchestratorClient orchPb.OrchestratorClient,
	txnExecutor storageV2.TxnExecutor,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	consentClient consentpb.ConsentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	redisStore types2.FireflyRedisStore,
	userGroupClient userGroupPb.GroupClient,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	locationClient locationpb.LocationClient,
) *idfc.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		httpPkg.ClientWireSet,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanOffersDaoWireSet,
		baseprovider.WireSet,
		vkycFailureHandlers.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		idfc.NewProcessor,
		getDeeplinkConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		newMultiDbDoOnce,
		release.EvaluatorWireSet,
		featureReleaseWorkerConfigProvider,
		helper.NewCommsHelper,
		idgen.UuidGeneratorWireSet,
		types2.CacheStorageProvider,
		lenden2.NewEligibilityProvider,
	)
	return &idfc.Processor{}
}

func InitialiseCommonActivityProcessor(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcPalVgClient idfcVgPb.IdfcClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	orchestratorClient orchPb.OrchestratorClient,
	txnExecutor storageV2.TxnExecutor,
	accountVgClient accountVgPb.AccountsClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	userIntelClient userIntelPb.UserIntelServiceClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	redisStore types2.FireflyRedisStore,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	salaryEstimationClient salaryestimation.SalaryEstimationClient,
	locationClient locationpb.LocationClient,
	userGroupClient userGroupPb.GroupClient,
	finFluxVgClient finflux.FinfluxClient,
	sgLmsClient sgLmsPb.LmsClient,
	mvVgClient mvVgPb.MoneyviewClient,
	ldcClient ldcVgPb.LendenClient,
) *common2.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		release.EvaluatorWireSet,
		featureReleaseWorkerConfigProvider,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		observers.NewAppsFlyerEventObserver,
		userdata.CommonUserDataProviderWireSet,
		activity.NewCreditReportHelper,
		common2.NewProcessor,
		loandataprovider.WireSet,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		vkycFailureHandlers.WireSet,
		baseprovider.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		getDeeplinkConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		idgen.UuidGeneratorWireSet,
		newMultiDbDoOnce,
		helper.NewCommsHelper,
		types2.CacheStorageProvider,
		fiftyfin3.WireVendorDataProviderSet,
		lenden2.NewEligibilityProvider,
	)
	return &common2.Processor{}
}

func InitialiseFiftyfinActivityProcessor(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcPalVgClient idfcVgPb.IdfcClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	groupClient userGroupPb.GroupClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	orchestratorClient orchPb.OrchestratorClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	consentClient consentpb.ConsentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	securedLoanClient securedLoansPb.SecuredLoansClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	segmentationClient segment.SegmentationServiceClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	finFluxVgClient finflux.FinfluxClient,
	redisStore types2.FireflyRedisStore,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	ldcClient ldcVgPb.LendenClient,
	mvVgClient mvVgPb.MoneyviewClient,
	locationClient locationpb.LocationClient,
) *fiftyfin.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		observers.NewAppsFlyerEventObserver,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		daoImpl2.FetchedAssetDaoWireSet,
		baseprovider.WireSet,
		loandataprovider.WireSet,
		datetimePkg.WireDefaultTimeSet,
		vkycFailureHandlers.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		getFeatureReleaseConfig,
		release.EvaluatorWireSet,
		fiftyfin.NewProcessor,
		offer_manager.OfferManagerFactoryWireSet,
		lamf.NewFiftyFinLamfOfferManager,
		newMultiDbDoOnce,
		helper.NewCommsHelper,
		getDeeplinkConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		lamf.NewMFCentralOfferManager,
		idgen.UuidGeneratorWireSet,
		lamf.NewMfcCasSummaryOfferManager,
		types2.CacheStorageProvider,
		fiftyfin3.WireVendorDataProviderSet,
		lenden2.NewEligibilityProvider,
	)
	return &fiftyfin.Processor{}
}

func InitializeSecuredLoansFiftyfinActivityProvider(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcPalVgClient idfcVgPb.IdfcClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	orchestratorClient orchPb.OrchestratorClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	consentClient consentpb.ConsentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	securedLoanClient securedLoansPb.SecuredLoansClient,
	segmentationClient segment.SegmentationServiceClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	abflVgClient abflVgPb.AbflClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	catalogClient catalogManagerPb.CatalogManagerClient,
	redisStore types2.FireflyRedisStore,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	locationClient locationpb.LocationClient,
) *fiftyfin2.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanOffersDaoWireSet,
		daoImpl2.FetchedAssetDaoWireSet,
		baseprovider.WireSet,
		idgen.UuidGeneratorWireSet,
		fiftyfinDeeplink.NewProvider,
		fiftyfin2.NewProcessor,
		getDeeplinkConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		types2.CacheStorageProvider,
	)
	return &fiftyfin2.Processor{}
}

func InitialiseLoanOfferDao(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	redisStore types2.FireflyRedisStore,
) palDao.LoanOffersDao {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		daoImpl.LoanOffersDaoMultiDBWireSet,
		types2.CacheStorageProvider,
	)
	return &daoImpl.CrdbLoanOfferDao{}
}

func prePayConfigProviderWorker(conf *configWorker.Config) *common.Prepay {
	return conf.Prepay
}

func InitializeSherlockBannersService(
	config *config.Config,
	config2 *genconf.Config,
	crdb types.LoansFederalPGDB,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	broker events.Broker,
	palVgClient palVgPb.PreApprovedLoanClient,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcVgClient idfcVgPb.IdfcClient,
	fiftyFinClient ffVgPb.FiftyFinClient,
	abflVgClient abflVgPb.AbflClient,
	nudgeClient nudge.NudgeServiceClient,
	finFluxVgClient finflux.FinfluxClient,
	redisStore types2.FireflyRedisStore,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	orchestratorClient orchPb.OrchestratorClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	consentClient consentpb.ConsentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	locationClient locationpb.LocationClient,
	userLocationClient userLocationPb.LocationClient,
	segmentationClient segment.SegmentationServiceClient,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	ldcClient ldcVgPb.LendenClient,
	mvVgClient mvVgPb.MoneyviewClient,
) *sherlock_banners.Service {
	wire.Build(
		idgen.NewClock,
		notificationConfigProvider,
		idgen.WireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanInstallmentPayoutDaoMultiDBWireSet,
		daoImpl.LoanStepExecutionsDaoMultiDBWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		loandataprovider.WireSet,
		lmsConfigProvider,
		lms.WireSet,
		datetimePkg.WireDefaultTimeSet,
		sherlock_banners.NewService,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		types2.CacheStorageProvider,
		helper.NewRpcHelper,
		getCreditReportConfig,
		fiftyfin3.WireVendorDataProviderSet,
	)

	return &sherlock_banners.Service{}
}

func InitialiseAbflActivityProcessor(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcPalVgClient idfcVgPb.IdfcClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	orchestratorClient orchPb.OrchestratorClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	consentClient consentpb.ConsentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	abflVgClient abflVgPb.AbflClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	locationClient locationpb.LocationClient,
	userLocationClient userLocationPb.LocationClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	redisStore types2.FireflyRedisStore,
	txnExecutor storageV2.TxnExecutor,
	finFluxVgClient finflux.FinfluxClient,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	// TODO(Brijesh): Lenden VG client should not be needed for ABFL activities. Remove coupling.
	lendenVgClient lendenVgClient.LendenClient,
) *abfl.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		baseprovider.WireSet,
		vkycFailureHandlers.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		abfl.NewProcessor,
		getDeeplinkConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		idgen.UuidGeneratorWireSet,
		types2.CacheStorageProvider,
		calculatorWire.Set,
		getLoanCalculatorConfFromWorker,
		lenden2.NewEligibilityProvider,
		userdata.CommonUserDataProviderWireSet,
	)
	return &abfl.Processor{}
}

func InitialiseMvActivityProcessor(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcPalVgClient idfcVgPb.IdfcClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	orchestratorClient orchPb.OrchestratorClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	consentClient consentpb.ConsentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	abflVgClient abflVgPb.AbflClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	mvVgClient mvVgPb.MoneyviewClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	redisStore types2.FireflyRedisStore,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	locationClient locationpb.LocationClient,
) *moneyview.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		observers.NewAppsFlyerEventObserver,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		moneyview.NewProcessor,
		getCreditReportConfFromWorkerGenConf,
		types2.CacheStorageProvider,
		userdata.CommonUserDataProviderWireSet,
	)
	return &moneyview.Processor{}
}

func InitialiseLendenActivityProcessor(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcPalVgClient idfcVgPb.IdfcClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	orchestratorClient orchPb.OrchestratorClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	consentClient consentpb.ConsentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	abflVgClient abflVgPb.AbflClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	mvVgClient mvVgPb.MoneyviewClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	redisStore types2.FireflyRedisStore,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgLmsClient sgLmsPb.LmsClient,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	lendenVgClient lendenVgClient.LendenClient,
	locationClient locationpb.LocationClient,
) *lenden.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		httpPkg.ClientWireSet,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		daoImpl.LoanPaymentRequestsDaoMultiDBWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		observers.NewAppsFlyerEventObserver,
		baseprovider.WireSet,
		vkycFailureHandlers.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		lenden.NewProcessor,
		lendenPkg.NewMandateSetupProcessor,
		getPreferUPIMandateTypeFlagFromWorkerConf,
		getDeeplinkConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		idgen.UuidGeneratorWireSet,
		types2.CacheStorageProvider,
		lenden2.NewEligibilityProvider,
		helper.NewConnectedSalaryAccountProvider,
	)
	return &lenden.Processor{}
}

func InitialiseStockGuardianActivityProcessor(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	eventsBroker events.Broker,
	llPalVgClient llVgPb.LiquiloansClient,
	idfcPalVgClient idfcVgPb.IdfcClient,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	vkycClient vkycPb.VKYCClient,
	kycClient kycPb.KycClient,
	authClient authPb.AuthClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	eSignClient esignPb.ESignClient,
	notifConf *common.Notification,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	employmentClient beEmploymentPb.EmploymentClient,
	docsClient docsPb.DocsClient,
	awsConf aws.Config,
	conf *configWorker.Config,
	orchestratorClient orchPb.OrchestratorClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportManager creditReportPb.CreditReportManagerClient,
	creditReportManagerV2 creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	consentClient consentpb.ConsentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	abflVgClient abflVgPb.AbflClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	workerConf *palWorkerGConf.Config,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	s3Client types2.PreApprovedLoanS3Client,
	connAccClient connectedAccountPb.ConnectedAccountClient,
	mfExternalClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	catalogClient catalogManagerPb.CatalogManagerClient,
	redisStore types2.FireflyRedisStore,
	txnExecutor storageV2.TxnExecutor,
	finFluxVgClient finflux.FinfluxClient,
	breClient brePb.BreClient,
	dataDevS3Client types2.DataDevS3Client,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgApplicantClient sgApplicantApiGatewayPb.ApplicantServiceClient,
	sgApplicationclient sgApplicantionApiGatewayPb.ApplicationClient,
	sgMatrixClient sgMatrixApiGatewayPb.MatrixClient,
	sgCustomerClient sgCustomerApiGatewayPb.CustomerServiceClient,
	sgKycClient sgKycApiGatewayPb.KYCClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	sgLmsClient sgLmsPb.LmsClient,
	riskClient riskPb.RiskClient,
	omegleClient omegle.OmegleClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	ldcClient ldcVgPb.LendenClient,
	mvVgClient mvVgPb.MoneyviewClient,
	locationClient locationpb.LocationClient,
) *stock_guardian.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		helper.NewRpcHelper,
		datetimePkg.WireDefaultTimeSet,
		daoImpl.LoanStepExecutionsDaoWithInstrumentationWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanRequestsDaoWithInstrumentationWireSet,
		daoImpl.LoanOffersDaoWithInstrumentationWireSet,
		daoImpl.LoanInstallmentPayoutDaoWireSet,
		daoImpl.LoanPaymentRequestsDaoWireSet,
		daoImpl.LoanAccountsDaoMultiDBWireSet,
		daoImpl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB,
		observers.NewAppsFlyerEventObserver,
		wrapper.LoecWrapperWireSet,
		palEvents.AcqEventPublisherWireSet,
		daoImpl.LoanInstallmentInfoDaoMultiDBWireSet,
		baseprovider.WireSet,
		vkycFailureHandlers.WireSet,
		deeplink.WireDeeplinkProviderFactorySet,
		stock_guardian.NewProcessor,
		activity.NewCreditReportHelper,
		getDeeplinkConfFromWorkerGenConf,
		getCreditReportConfFromWorkerGenConf,
		idgen.UuidGeneratorWireSet,
		wire.NewSet(cache.NewInMemoryCacheService, wire.Bind(new(cache.CacheStorage), new(*cache.InMemoryCacheService))),
		types2.CacheStorageProvider,
		calculatorWire.Set,
		getLoanCalculatorConfFromWorker,
		loandataprovider.WireSet,
		fiftyfin3.WireVendorDataProviderSet,
		lenden2.NewEligibilityProvider,
		userdata.CommonUserDataProviderWireSet,
	)
	return &stock_guardian.Processor{}
}

func InitialiseSyncActivityProcessor(
	sgProc *stock_guardian.Processor,
	lendenProc *lenden.Processor,
	abflProc *abfl.Processor,
	commonProc *common2.Processor,
	federalProc *federal.Processor,
	activityProc *activity.Processor,
) *syncproxy.Processor {
	wire.Build(
		syncproxy.NewProcessor,
	)
	return &syncproxy.Processor{}
}

func InitialiseSecuredLoansService(
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	celestialClient celestialPb.CelestialClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	cardProvisioningClient cardPb.CardProvisioningClient,
	conf *config.Config,
	dynConf *genconf.Config,
	caClient connectedAccountPb.ConnectedAccountClient,
	client catalogManagerPb.CatalogManagerClient,
	redisStore types2.FireflyRedisStore,
	palVgClient palVgPb.PreApprovedLoanClient,
	actorClient actorPb.ActorClient,
	usersClient userPb.UsersClient,
	savingsClient savings.SavingsClient,
	lvClient livenessPb.LivenessClient,
	kycClient kycPb.KycClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	authClient authPb.AuthClient,
	eSignClient esignPb.ESignClient,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	llPalVgClient llVgPb.LiquiloansClient,
	profileClient profilePb.ProfileClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryClient salaryprogram.SalaryProgramClient,
	idfcVgClient idfcVgPb.IdfcClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportClient creditReportPb.CreditReportManagerClient,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segment.SegmentationServiceClient,
	s3Client types2.PreApprovedLoanS3Client,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	mfExternalOrdersClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	eventBroker events.Broker,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	locationClient locationpb.LocationClient,
) *secured_loans.Service {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		daoImpl.LoanRequestsDaoMultiDBWireSet,
		vkycFailureHandlers.WireSet,
		baseprovider.WireSet,
		helper.NewRpcHelper,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		getCreditReportConfig,
		deeplink.WireDeeplinkProviderFactorySet,
		secured_loans.NewService,
		daoImpl.LoanStepExecutionsDaoMultiDBWireSet,
		IDbResourceProviderProvider,
		getDeeplinkConf,
		idgen.UuidGeneratorWireSet,
		types2.CacheStorageProvider,
		lenden2.NewEligibilityProvider,
		datetimePkg.WireDefaultTimeSet,
		notificationConfigProvider,
	)
	return &secured_loans.Service{}
}

func IDbResourceProviderProvider(dbConnProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]) storageV2.IDbResourceProvider[storageV2.IdempotentTxnExecutor] {
	return dbConnProvider
}

func getFeatureReleaseConfig(conf *palWorkerGConf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func InitiateLendabilityService(
	conf *genconf.Config,
	crV2Client creditReportV2Pb.CreditReportManagerClient,
	userClient userPb.UsersClient,
	empClient beEmploymentPb.EmploymentClient,
	locationClient userLocationPb.LocationClient,
	locVgClient location.LocationClient,
	broker events.Broker,
	vendorApiGenConf *vendorapiGenConf.Config,
) *lendability.Service {
	wire.Build(
		lendability.NewService,
		ldbtDc.NewDataCollector,
		envProvider,
		vendorapi.SecureHttpClientNilSignCtxWireSet,
		vendorapi.New,
		httpcontentredactor.GetInstance,
		wire.Bind(new(vendorapi.HttpDoer), new(*http.Client)),
	)
	return &lendability.Service{}
}

func InitiatePreEligibilityService(
	dynConf *genconf.Config,
	conf *config.Config,
	preApprovedLoanVgClient palVgPb.PreApprovedLoanClient,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	redisStore types2.FireflyRedisStore,
	actorClient actorPb.ActorClient,
	usersClient userPb.UsersClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	kycClient kycPb.KycClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	authClient authPb.AuthClient,
	eSignClient esignPb.ESignClient,
	commsClient commsPb.CommsClient,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient, piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	llPalVgClient llVgPb.LiquiloansClient,
	profileClient profilePb.ProfileClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportClient creditReportPb.CreditReportManagerClient,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	s3Client types2.PreApprovedLoanS3Client,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	mfExternalOrdersClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	eventBroker events.Broker,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	idfcVgClient idfcVgPb.IdfcClient,
	segmentationServiceClient segment.SegmentationServiceClient,
	temporalClient types2.PreApprovedLoanClient,
	consentClient consentpb.ConsentClient,
	locationClient locationpb.LocationClient,
	breClient brePb.BreClient,
	operationalStatusClient operStatusPb.OperationalStatusServiceClient,
	palClient preApprovedLoanPb.PreApprovedLoanClient,
	mvVgClient mvVgPb.MoneyviewClient,
	leadsClient leadsPb.UserLeadSvcClient,
) *preeligibility.Service {
	wire.Build(
		preeligibility.NewService,
		idgen.NewClock,
		idgen.WireSet,
		getCreditReportConfig,
		notificationConfigProvider,
		datetimePkg.WireDefaultTimeSet,
		helper.NewRpcHelper,
		types2.CacheStorageProvider,
		daoImpl.PgdbPreEligibilityOfferDaoWireSet,
		daoImpl.LoanRequestsDaoMultiDBWireSet,
		daoImpl.LoanStepExecutionsDaoMultiDBWireSet,
		daoImpl.LoanActivityDaoMultiDBWireSet,
		daoImpl.LoanApplicantDaoMultiDBWireSet,
		preeligibility.OrchestratorWireSet,
		userdata.CommonUserDataProviderWireSet,
		steps.NewAddUserDetailsStep,
		steps.NewOfferGenerationStep,
	)
	return &preeligibility.Service{}
}

func envProvider(conf *genconf.Config) string {
	return conf.Application().Environment
}

func getSgNewEligibilityFlowConf(conf *genconf.Config) *commonGenConf.FeatureConstraint {
	return conf.SgEtbNewEligibilityFlow()
}

func getSgNewEligibilityFlowActivityConf(conf *palWorkerGConf.Config) *commonGenConf.FeatureConstraint {
	return conf.SgEtbNewEligibilityFlow()
}

func getPreferUPIMandateTypeFlagFromServerConf(conf *genconf.Config) bool {
	return conf.Flags().PreferUPIMandateTypeForLDC()
}

func getPreferUPIMandateTypeFlagFromWorkerConf(conf *palWorkerGConf.Config) bool {
	return conf.Flags().PreferUPIMandateTypeForLDC()
}
