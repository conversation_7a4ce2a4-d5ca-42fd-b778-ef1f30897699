// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"errors"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/vendorapi"
	genconf3 "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	location2 "github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/gamma/api/bre"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/collection"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/credit_limit_estimator"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/docs/esign"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/epfo"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/leads"
	"github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/omegle"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	preapprovedloan2 "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/applicant"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	customer2 "github.com/epifi/gamma/api/stockguardian/sgapigateway/customer"
	esign2 "github.com/epifi/gamma/api/stockguardian/sgapigateway/esign"
	kyc2 "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/matrix"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/credit_report"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	"github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	"github.com/epifi/gamma/api/vendorgateway/lending/setu"
	location3 "github.com/epifi/gamma/api/vendorgateway/location"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	savings2 "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	"github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf5 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/pkg/http"
	"github.com/epifi/gamma/pkg/persistentqueue"
	preapprovedloan3 "github.com/epifi/gamma/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/activity"
	abfl4 "github.com/epifi/gamma/preapprovedloan/activity/abfl"
	"github.com/epifi/gamma/preapprovedloan/activity/brecaller"
	common2 "github.com/epifi/gamma/preapprovedloan/activity/common"
	"github.com/epifi/gamma/preapprovedloan/activity/datacompleteness"
	federal4 "github.com/epifi/gamma/preapprovedloan/activity/federal"
	fiftyfin8 "github.com/epifi/gamma/preapprovedloan/activity/fiftyfin"
	idfc8 "github.com/epifi/gamma/preapprovedloan/activity/idfc"
	lenden9 "github.com/epifi/gamma/preapprovedloan/activity/lenden"
	liquiloans7 "github.com/epifi/gamma/preapprovedloan/activity/liquiloans"
	moneyview5 "github.com/epifi/gamma/preapprovedloan/activity/moneyview"
	stock_guardian5 "github.com/epifi/gamma/preapprovedloan/activity/stock_guardian"
	"github.com/epifi/gamma/preapprovedloan/activity/syncproxy"
	"github.com/epifi/gamma/preapprovedloan/activity/vendors"
	"github.com/epifi/gamma/preapprovedloan/calculator"
	finflux4 "github.com/epifi/gamma/preapprovedloan/calculator/providers/finflux"
	idfc5 "github.com/epifi/gamma/preapprovedloan/calculator/providers/idfc"
	lenden5 "github.com/epifi/gamma/preapprovedloan/calculator/providers/lenden"
	stockguardian2 "github.com/epifi/gamma/preapprovedloan/calculator/providers/stockguardian"
	wrapper2 "github.com/epifi/gamma/preapprovedloan/calculator/providers/wrapper"
	"github.com/epifi/gamma/preapprovedloan/config"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	genconf4 "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	"github.com/epifi/gamma/preapprovedloan/config/worker"
	genconf2 "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	consent2 "github.com/epifi/gamma/preapprovedloan/consent"
	"github.com/epifi/gamma/preapprovedloan/consumer"
	"github.com/epifi/gamma/preapprovedloan/cx"
	"github.com/epifi/gamma/preapprovedloan/cx/factory"
	providers4 "github.com/epifi/gamma/preapprovedloan/cx/providers"
	impl2 "github.com/epifi/gamma/preapprovedloan/cx/providers/impl"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/dao/impl"
	"github.com/epifi/gamma/preapprovedloan/dao/wrapper"
	"github.com/epifi/gamma/preapprovedloan/dao/wrapper/observers"
	"github.com/epifi/gamma/preapprovedloan/data_existence_manager"
	"github.com/epifi/gamma/preapprovedloan/datacollectors/p2p"
	"github.com/epifi/gamma/preapprovedloan/dcpp"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	abfl2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/abfl"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/epifitech"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/federal"
	fiftyfin2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/fiftyfin"
	idfc2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc/vkyc/failure_handlers"
	lenden2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/lenden"
	liquiloans2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/liquiloans"
	moneyview2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/moneyview"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/realtimeetb"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/stock_guardian"
	"github.com/epifi/gamma/preapprovedloan/developer"
	"github.com/epifi/gamma/preapprovedloan/developer/processor"
	"github.com/epifi/gamma/preapprovedloan/downtime"
	"github.com/epifi/gamma/preapprovedloan/dynamic_elements"
	provider2 "github.com/epifi/gamma/preapprovedloan/dynamic_elements/provider"
	events2 "github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/helper/agreement"
	"github.com/epifi/gamma/preapprovedloan/inbound_notification"
	"github.com/epifi/gamma/preapprovedloan/landing_provider"
	"github.com/epifi/gamma/preapprovedloan/lendability"
	"github.com/epifi/gamma/preapprovedloan/lendability/datacollector"
	lms2 "github.com/epifi/gamma/preapprovedloan/lms"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	abfl3 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/abfl"
	baseprovider2 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/baseprovider"
	fiftyfin4 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/fiftyfin"
	finflux2 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/finflux"
	idfc3 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/idfc"
	lenden3 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/lenden"
	liquiloans3 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/liquiloans"
	moneyview3 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/moneyview"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/stockguardian"
	"github.com/epifi/gamma/preapprovedloan/loan_txn_provider"
	"github.com/epifi/gamma/preapprovedloan/loanplans"
	"github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view"
	federal3 "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view/providers/federal"
	idfc6 "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view/providers/idfc"
	lenden8 "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view/providers/lenden"
	liquiloans6 "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view/providers/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/multidb_provider"
	"github.com/epifi/gamma/preapprovedloan/multidb_provider/decision_engine"
	"github.com/epifi/gamma/preapprovedloan/offer_manager"
	"github.com/epifi/gamma/preapprovedloan/offer_manager/providers/fiftyfin/lamf"
	lenden7 "github.com/epifi/gamma/preapprovedloan/pkg/lenden"
	"github.com/epifi/gamma/preapprovedloan/preclose"
	fiftyfin7 "github.com/epifi/gamma/preapprovedloan/preclose/providers/fiftyfin"
	idfc7 "github.com/epifi/gamma/preapprovedloan/preclose/providers/idfc"
	stock_guardian4 "github.com/epifi/gamma/preapprovedloan/preclose/providers/stock_guardian"
	"github.com/epifi/gamma/preapprovedloan/preeligibility"
	"github.com/epifi/gamma/preapprovedloan/preeligibility/steps"
	"github.com/epifi/gamma/preapprovedloan/prepay"
	fiftyfin5 "github.com/epifi/gamma/preapprovedloan/prepay/providers/fiftyfin"
	finflux3 "github.com/epifi/gamma/preapprovedloan/prepay/providers/finflux"
	idfc4 "github.com/epifi/gamma/preapprovedloan/prepay/providers/idfc"
	lenden4 "github.com/epifi/gamma/preapprovedloan/prepay/providers/lenden"
	liquiloans4 "github.com/epifi/gamma/preapprovedloan/prepay/providers/liquiloans"
	stock_guardian2 "github.com/epifi/gamma/preapprovedloan/prepay/providers/stock_guardian"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator"
	providers3 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	federal2 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/federal"
	fiftyfin6 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/fiftyfin"
	lenden6 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/lenden"
	liquiloans5 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/liquiloans"
	moneyview4 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/moneyview"
	stock_guardian3 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/stock_guardian"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/data_collector"
	provider3 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/data_collector/provider"
	providers2 "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers/postproc"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
	"github.com/epifi/gamma/preapprovedloan/reconciliation"
	"github.com/epifi/gamma/preapprovedloan/reconciliation/providers/db_liquiloans"
	secured_loans2 "github.com/epifi/gamma/preapprovedloan/secured_loans"
	fiftyfin9 "github.com/epifi/gamma/preapprovedloan/secured_loans/activity/fiftyfin"
	impl3 "github.com/epifi/gamma/preapprovedloan/secured_loans/dao/impl"
	"github.com/epifi/gamma/preapprovedloan/sherlock_banners"
	"github.com/epifi/gamma/preapprovedloan/userdata"
	fiftyfin3 "github.com/epifi/gamma/preapprovedloan/vendor_data_provider/fiftyfin"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
	"go.temporal.io/sdk/client"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitialisePreApprovedLoanSvc(crdb types.LoansFederalPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, palClient preapprovedloan2.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, broker events.Broker, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, signalPublisher types2.SignalWorkflowPublisher, authClient auth.AuthClient, eSignClient esign.ESignClient, vgCustomerClient customer.CustomerClient, conf *config.Config, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, llPalVgClient liquiloans.LiquiloansClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], palEventPublisher types2.NudgeExitEventPublisher, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, idfcVgClient idfc.IdfcClient, segmentationServiceClient segment.SegmentationServiceClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, collClient collection.CollectionClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, dynConf *genconf.Config, awsConf aws.Config, cardProvisioningClient provisioning.CardProvisioningClient, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, connAccClient connected_account.ConnectedAccountClient, mvVgClient moneyview.MoneyviewClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, catalogClient catalog.CatalogManagerClient, securedLoansClient secured_loans.SecuredLoansClient, userGroupClient group.GroupClient, nudgeClient nudge.NudgeServiceClient, finFluxVgClient finflux.FinfluxClient, userIntelClient userintel.UserIntelServiceClient, temporalClient types2.PreApprovedLoanClient, redisStore types2.FireflyRedisStore, targetedCommsClient inapptargetedcomms.InAppTargetedCommsClient, consentClient consent.ConsentClient, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, ldcClient lenden.LendenClient, orchClient orchestrator.OrchestratorClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, locationClient location2.LocationClient, sgKycClient kyc2.KYCClient) (*preapprovedloan3.Service, error) {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, broker)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanRequestsDaoMultiDB := impl.NewCrdbLoanRequestsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanStepExecutionsDaoMultiDB := impl.NewCrdbLoanStepExecutionsDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, broker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	flags := getFlagsConf(dynConf)
	simpleOffersDecisionEngine := decision_engine.NewSimpleOffersDecisionEngine(flags)
	crdbLoanOfferEligibilityCriteriaDao := impl.NewCrdbLoanOfferEligibilityCriteriaDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	multiDbProviderImpl := multidb_provider.NewMultiDBProvider(simpleOffersDecisionEngine, crdbLoanAccountsDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanRequestsDaoMultiDB, crdbLoanOfferEligibilityCriteriaDao)
	defaultTime := datetime.NewDefaultTime()
	notification := notificationConfigProvider(conf)
	creditReportConfig := getCreditReportConfig(dynConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoMultiDB, notification, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationServiceClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, broker, crdbLoanRequestsDaoMultiDB, sgEsignApiGateway, locationClient)
	db := types.LoansFederalPGDBGormDBProvider(crdb)
	txnExecutor := newGormTxnExecutorProvider(db)
	deeplinkConfig := getDeeplinkConf(dynConf)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(preApprovedLoanClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	provider4 := idfc3.NewProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoMultiDB, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoMultiDB)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	provider5 := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoMultiDB)
	factory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, provider4, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, provider5, mvLoanDataProvider, abflPwaJourneyDataProvider)
	commonLms := lmsConfigProvider(conf)
	lmsLms := lms2.NewLms(crdbLoanInstallmentPayoutDaoMultiDB, factory, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanActivityDaoMultiDB, txnExecutorProvider, crdbLoanAccountsDaoMultiDB, broker, nudgeClient, commonLms, defaultTime, idfcVgClient, crdbLoanApplicantDaoMultiDB, rpcHelper)
	commonPrepay := prePayConfigProvider(conf)
	liquiloansLiquiloans, err := liquiloans4.NewLiquiloans(rpcHelper, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, llPalVgClient, lmsLms, collClient, piClient, orderClient, factory, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, txnExecutorProvider)
	if err != nil {
		return nil, err
	}
	liquiloansPL := liquiloans4.NewLiquiloansPL(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	liquiloansEarlySalary := liquiloans4.NewLiquiloansEarlySalary(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	idfcBaseProvider := idfc4.NewBaseProvider(crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanApplicantDaoMultiDB, factory, rpcHelper, commonPrepay, idfcVgClient, piClient, orderClient)
	personalLoanProvider := idfc4.NewPersonalLoanProvider(idfcBaseProvider, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, baseproviderProvider)
	liquiloansFLDG := liquiloans4.NewLiquiloansFLDG(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	liquiloansAcqToLend := liquiloans4.NewLiquiloansAcqToLend(commonPrepay, baseproviderProvider, liquiloansLiquiloans, onbClient)
	lamfProvider := fiftyfin5.NewLAMFProvider(providerFactory, fiftyFinVgClient, accountPiClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, rpcHelper, piClient, orderClient, lamfVendorDataProvider, baseProvider)
	liquiloansSTPL := liquiloans4.NewLiquiloansSTPL(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	finfluxFinfluxBaseProvider := finflux3.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB)
	liquiloansRTSubvention := liquiloans4.NewLiquiloansRTSubvention(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	liquiloansRTStpl := liquiloans4.NewLiquiloansRTStpl(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	stockGuardianBaseProvider := stock_guardian2.NewStockGuardianBaseProvider(sgLmsClient, crdbLoanAccountsDaoMultiDB, factory, rpcHelper, crdbLoanPaymentRequestsDaoMultiDB, piClient, crdbLoanInstallmentInfoDaoMultiDB, commonPrepay, baseproviderProvider, collClient)
	provider6 := lenden4.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, factory)
	prepayFactory := prepay.NewFactory(liquiloansPL, liquiloansEarlySalary, personalLoanProvider, liquiloansFLDG, liquiloansAcqToLend, lamfProvider, liquiloansSTPL, finfluxFinfluxBaseProvider, liquiloansRTSubvention, liquiloansRTStpl, stockGuardianBaseProvider, provider6)
	loan_txn_providerFactory := loan_txn_provider.NewFactory(preApprovedLoanClient, rpcHelper, crdbLoanAccountsDaoMultiDB, accountVgClient, actorClient)
	downtimeImpl := downtime.NewImpl(dynConf, defaultTime)
	finfluxProvider := finflux4.NewProvider(finFluxVgClient)
	wrapperProvider := wrapper2.NewProvider()
	loanCalculator := getLoanCalculatorConstraintConf(conf)
	provider7 := idfc5.NewProvider(loanCalculator)
	stockguardianProvider := stockguardian2.NewProvider(sgLmsClient)
	calculatorProvider := lenden5.NewCalculatorProvider(ldcClient)
	calculatorFactoryImpl := calculator.NewFactoryImpl(finfluxProvider, wrapperProvider, provider7, stockguardianProvider, calculatorProvider)
	vendorProgramLevelFeature := getFeaturesConf(dynConf)
	basePostProcessor := postproc.NewBasePostProcessor()
	lopeOverrideConfig := lopeOverrideServiceConfigProvider(dynConf)
	highestPriorityPostProcessor := postproc.NewHighestPriorityPostProcessor(basePostProcessor, lopeOverrideConfig)
	iPostProcessor := postproc.ProvidePostProcessorChain(highestPriorityPostProcessor)
	common := providers2.NewCommon(userGroupClient, userClient, vendorProgramLevelFeature, iPostProcessor)
	nonFiCore := providers2.NewNonFiCore(userGroupClient, userClient, vendorProgramLevelFeature, iPostProcessor)
	securedLoanProvider := providers2.NewSecuredLoanProvider()
	priorityProviderFactoryImpl := priority_provider.NewPriorityProviderFactoryImpl(common, nonFiCore, securedLoanProvider)
	commonEligibilityProvider := providers3.NewCommonEligibilityProvider(limitEstimatorClient, calculatorFactoryImpl)
	realtimeDistEligibilityProvider := liquiloans5.NewRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	liquiloansLLFiliteProvider := liquiloans5.NewLLFiliteProvider(loecWrapper, commonEligibilityProvider)
	fedRealtimeDistEligibilityProvider := federal2.NewFedRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	fedRealtimeDistNtbEligibilityProvider := federal2.NewFedRealtimeDistNtbEligibilityProvider(loecWrapper, commonEligibilityProvider)
	nonFiCoreProvider := providers3.NewNonFiCoreProvider(loecWrapper, commonEligibilityProvider)
	realtimeCommonProvider := providers3.NewRealtimeCommonProvider(loecWrapper, commonEligibilityProvider)
	featureConstraint := getSgNewEligibilityFlowConf(dynConf)
	sgRealtimeDistEligibilityProvider := stock_guardian3.NewSgRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider, featureConstraint)
	lendenRealtimeDistEligibilityProvider := lenden6.NewRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	fedPlEtbProvider := federal2.NewFedPlEtbProvider(commonEligibilityProvider, limitEstimatorClient, crdbLoanOffersDaoWithInstrumentation)
	fiftyfinLamfProvider := fiftyfin6.NewFiftyfinLamfProvider(commonEligibilityProvider)
	moneyViewRTDETBProvider := moneyview4.NewMoneyViewRTDETBProvider(loecWrapper, commonEligibilityProvider)
	eligibilityEvaluatorFactoryImpl := eligibility_evaluator.NewEligibilityEvaluatorFactoryImpl(commonEligibilityProvider, realtimeDistEligibilityProvider, limitEstimatorClient, calculatorFactoryImpl, liquiloansLLFiliteProvider, fedRealtimeDistEligibilityProvider, fedRealtimeDistNtbEligibilityProvider, nonFiCoreProvider, realtimeCommonProvider, sgRealtimeDistEligibilityProvider, lendenRealtimeDistEligibilityProvider, fedPlEtbProvider, fiftyfinLamfProvider, flags, moneyViewRTDETBProvider)
	featureReleaseConfig := featureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	loanOptionRecommender := provider.NewLoanOptionRecommender(multiDbProviderImpl, priorityProviderFactoryImpl, eligibilityEvaluatorFactoryImpl, rpcHelper, onbClient, vendorProgramLevelFeature, savingsClient, lopeOverrideConfig)
	landingProvider := landing_provider.NewLandingProvider(rpcHelper, simpleOffersDecisionEngine, crdbLoanOffersDaoWithInstrumentation, onbClient, limitEstimatorClient, calculatorFactoryImpl, priorityProviderFactoryImpl, eligibilityEvaluatorFactoryImpl, dynConf, multiDbProviderImpl, evaluator, loanOptionRecommender, crdbLoanStepExecutionsDaoMultiDB, loecWrapper, savingsClient)
	managerImpl := data_existence_manager.NewManagerImpl(lendingCacheStorage, crdbLoanAccountsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, loecWrapper, dynConf)
	homeScreenBodySectionProvider := provider2.NewHomeScreenBodySectionProvider(landingProvider, rpcHelper, dynConf, managerImpl)
	homeScreenFeatureSectionProvider := provider2.NewHomeScreenFeatureSectionProvider(multiDbProviderImpl, crdbLoanOffersDaoWithInstrumentation, crdbLoanRequestsDaoMultiDB, rpcHelper, dynConf, managerImpl)
	multiDbDoOnce := newMultiDbDoOnce(dbConnProvider)
	homeScreenPopUpProvider := provider2.NewHomeScreenPopUpProvider(crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, rpcHelper, dynConf, multiDbDoOnce, accountBalanceClient, managerImpl, multiDbProviderImpl)
	homeScreenTopBannerProvider := provider2.NewHomeScreenTopBannerProvider(crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, rpcHelper, dynConf, managerImpl, multiDbProviderImpl, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB)
	consentManagerHelper := consent2.NewConsentManagerHelper(consentClient, targetedCommsClient)
	wealthToTechDataSharingConsentManager := consent2.NewWealthToTechDataSharingConsentManager(connAccClient, consentManagerHelper)
	smsFetchingConsentManager := consent2.NewSmsFetchingConsentManager(consentManagerHelper)
	consentManagerFactory := consent2.NewConsentManagerFactory(wealthToTechDataSharingConsentManager, smsFetchingConsentManager)
	dynamicElementsProviderHelper := provider2.NewDynamicElementsProviderHelper(consentManagerFactory, dynConf, defaultTime, broker, consentManagerHelper)
	analyserScreenProvider := provider2.NewAnalyserScreenProvider(dynamicElementsProviderHelper)
	netWorthScreenProvider := provider2.NewNetWorthScreenProvider(dynamicElementsProviderHelper)
	savingsAccountBalancesScreenProvider := provider2.NewSavingsAccountBalancesScreenProvider(dynamicElementsProviderHelper)
	loansDashboardScreenProvider := provider2.NewLoansDashboardScreenProvider(dynamicElementsProviderHelper)
	dynamic_elementsFactory := dynamic_elements.NewFactory(homeScreenBodySectionProvider, homeScreenFeatureSectionProvider, homeScreenPopUpProvider, homeScreenTopBannerProvider, analyserScreenProvider, netWorthScreenProvider, savingsAccountBalancesScreenProvider, loansDashboardScreenProvider)
	idfcMandateViewProvider := idfc6.NewIdfcMandateViewProvider(rpcHelper, providerFactory, crdbLoanStepExecutionsDaoMultiDB, savingsClient, crdbLoanRequestsDaoMultiDB)
	mandateRequestDao := impl.NewMandateRequestDao(dbConnProvider, domainIdGenerator)
	mandateConfig := getMandateConf(dynConf)
	llMandateViewProvider := liquiloans6.NewLLMandateViewProvider(rpcHelper, providerFactory, crdbLoanStepExecutionsDaoMultiDB, savingsClient, llPalVgClient, crdbLoanApplicantDaoMultiDB, deeplinkConfig, userClient, crdbLoanRequestsDaoMultiDB, mandateRequestDao, mandateConfig)
	bool2 := getPreferUPIMandateTypeFlagFromServerConf(dynConf)
	mandateSetupProcessor := lenden7.NewMandateSetupProcessor(crdbLoanApplicantDaoMultiDB, ldcClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, rpcHelper, userClient, bool2)
	lendenMandateViewProvider := lenden8.NewLendenMandateViewProvider(rpcHelper, providerFactory, crdbLoanStepExecutionsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanRequestsDaoMultiDB, ldcClient, userClient, crdbLoanOffersDaoWithInstrumentation, mandateSetupProcessor)
	federalProvider := federal3.NewProvider(rpcHelper, providerFactory, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, recurringPaymentClient, piClient)
	mandateViewFactoryImpl := mandate_view.NewMandateViewFactoryImpl(idfcMandateViewProvider, llMandateViewProvider, lendenMandateViewProvider, federalProvider)
	client := getPlClientProvider(temporalClient)
	crdbPartnerLmsUserDao := impl.NewCrdbPartnerLmsUserDao(dbConnProvider, domainIdGenerator)
	db_liquiloansProvider := db_liquiloans.NewProvider(llPalVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	lmsDataDifferenceFinderFactoryImpl := reconciliation.NewLmsDataDifferenceFinderFactoryImpl(db_liquiloansProvider)
	preClosureProvider := idfc7.NewPreClosureProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanApplicantDaoMultiDB)
	lenderPreCloseProvider := fiftyfin7.NewLenderPreCloseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, lamfVendorDataProvider)
	sgLenderPreCloseProvider := stock_guardian4.NewSgLenderPreCloseProvider(crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, sgLmsClient)
	precloseFactory := preclose.NewFactory(preClosureProvider, lenderPreCloseProvider, sgLenderPreCloseProvider)
	nonFiCoreDataCollectorPriorityProvider := provider3.NewNonFiCoreDataCollectorPriorityProvider(priorityProviderFactoryImpl)
	dataCollectorFactory := data_collector.NewDataCollectorFactory(nonFiCoreDataCollectorPriorityProvider)
	preBreLoanPreferencesSaver := p2p.NewPreBreLoanPreferencesSaver(crdbLoanRequestsDaoMultiDB, loecWrapper, rpcHelper)
	preBreConsentDataSaver := p2p.NewPreBreConsentDataSaver(crdbLoanRequestsDaoMultiDB, loecWrapper, rpcHelper, ldcClient, crdbLoanApplicantDaoMultiDB)
	defaultDataCollectionPriorityProvider := dcpp.NewDefaultDataCollectionPriorityProvider(conf, dynConf)
	commonUserDataProvider := userdata.NewCommonUserDataProvider(rpcHelper)
	connectedSalaryAccountProvider := helper.NewConnectedSalaryAccountProvider(connAccClient, crdbLoanRequestsDaoMultiDB, crdbLoanOffersDaoWithInstrumentation)
	loanplansProviderFactory := loanplans.NewProviderFactory(calculatorFactoryImpl)
	modifiedRoiDataSaver := p2p.NewModifiedRoiDataSaver(crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, rpcHelper, ldcClient, crdbLoanApplicantDaoMultiDB)
	service := preapprovedloan3.NewService(crdbLoanOffersDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, loecWrapper, crdbLoanApplicantDaoMultiDB, multiDbProviderImpl, broker, rpcHelper, signalPublisher, conf, txnExecutor, txnExecutorProvider, palEventPublisher, providerFactory, lmsLms, prepayFactory, segmentationServiceClient, loan_txn_providerFactory, factory, downtimeImpl, celestialClient, dynamic_elementsFactory, dynConf, connAccClient, mvVgClient, mandateViewFactoryImpl, multiDbDoOnce, securedLoansClient, payClient, evaluator, userClient, piClient, authClient, preApprovedLoanClient, salaryProgramClient, creditReportConfig, creditReportManager, creditReportManagerV2, onbClient, s3Client, limitEstimatorClient, client, crdbPartnerLmsUserDao, userIntelClient, calculatorFactoryImpl, lmsDataDifferenceFinderFactoryImpl, uuidGenerator, defaultTime, mfExternalClient, managerImpl, precloseFactory, landingProvider, mandateRequestDao, consentManagerFactory, consentManagerHelper, dataCollectorFactory, loanOptionRecommender, preBreLoanPreferencesSaver, preBreConsentDataSaver, orchClient, defaultDataCollectionPriorityProvider, priorityProviderFactoryImpl, commonUserDataProvider, connectedSalaryAccountProvider, savingsClient, loanplansProviderFactory, acqEventPublisherImpl, modifiedRoiDataSaver, eligibilityEvaluatorFactoryImpl, sgKycClient, lvClient, ldcClient)
	return service, nil
}

func InitializeConsumerService(db types.LoansFederalPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], actorClient actor.ActorClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, awsConf aws.Config, userClient user.UsersClient, savingsClient savings.SavingsClient, bankCustClient bankcust.BankCustomerServiceClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, eSignClient esign.ESignClient, vgCustomerClient customer.CustomerClient, conf *config.Config, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, eventsBroker events.Broker, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, llPalVgClient liquiloans.LiquiloansClient, profileClient profile.ProfileClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, idfcVgClient idfc.IdfcClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, dynConf *genconf.Config, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, abflVgClient abfl.AbflClient, segmentationClient segment.SegmentationServiceClient, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, redisStore types2.FireflyRedisStore, sgEsignApiGateway esign2.EsignClient, locationClient location2.LocationClient) (*consumer.Service, error) {
	defaultTime := datetime.NewDefaultTime()
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbLoanStepExecutionsDao := impl.NewCrdbLoanStepExecutionsDao(dbConnProvider, domainIdGenerator)
	notification := notificationConfigProvider(conf)
	crdbLoanActivityDao := impl.NewCrdbLoanActivityDao(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDao := impl.NewCrdbLoanApplicantDao(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfig(dynConf)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDao := impl.NewCrdbLoanRequestsDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDao, notification, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDao, llPalVgClient, crdbLoanApplicantDao, profileClient, bankCustClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDao, sgEsignApiGateway, locationClient)
	crdbLoanOfferDao := impl.NewCrdbLoanOfferDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	crdbLoanAccountsDao := impl.NewCrdbLoanAccountsDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDao := impl.NewCrdbLoanInstallmentInfoDao(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentPayoutDao := impl.NewCrdbLoanInstallmentPayoutDao(dbConnProvider, domainIdGenerator)
	gormDB := types.LoansFederalPGDBGormDBProvider(db)
	txnExecutor := newGormTxnExecutorProvider(gormDB)
	inMemoryCryptorStore, err := InitCryptors(conf)
	if err != nil {
		return nil, err
	}
	service := consumer.NewService(actorClient, preApprovedLoanVgClient, s3Client, userClient, bankCustClient, rpcHelper, crdbLoanOfferDao, loecWrapper, crdbLoanAccountsDao, crdbLoanRequestsDao, crdbLoanActivityDao, crdbLoanInstallmentInfoDao, crdbLoanInstallmentPayoutDao, txnExecutor, conf, eventsBroker, inMemoryCryptorStore, piClient, celestialClient)
	return service, nil
}

func InitialiseActivityProcessor(db *gorm.DB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, txnExecutor storagev2.TxnExecutor, persistentQueue persistentqueue.PersistentQueue, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, client profilevalidation.ProfileValidationClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, orchClient orchestrator.OrchestratorClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], conf *worker.Config, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, idfcVgClient idfc.IdfcClient, docsClient docs.DocsClient, userGroupClient group.GroupClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, riskClient risk.RiskClient, collectionClient collection.CollectionClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, epfoClient epfo.EpfoClient, productClient product.ProductClient, segmentationClient segment.SegmentationServiceClient, cardProvisioningClient provisioning.CardProvisioningClient, workerGenConf *genconf2.Config, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, nudgeClient nudge.NudgeServiceClient, catalogClient catalog.CatalogManagerClient, palClient preapprovedloan2.PreApprovedLoanClient, finFluxVgClient finflux.FinfluxClient, userIntelClient userintel.UserIntelServiceClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, redisStore types2.FireflyRedisStore, breClient bre.BreClient, dataDevS3Client types2.DataDevS3Client, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, ldcClient lenden.LendenClient, mvVgClient moneyview.MoneyviewClient, locationClient location2.LocationClient, salaryEstimationClient salaryestimation.SalaryEstimationClient) (*activity.Processor, error) {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	defaultTime := datetime.NewDefaultTime()
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerGenConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerGenConf)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	vendorsFederal := vendors.NewFederal(bcClient, loecWrapper)
	liquiLoansPersonalLoan := vendors.NewLiquiloansPersonalLoan(bcClient)
	liquiLoansEarlySalary := vendors.NewLiquiloansEarlySalary()
	factory := activity.NewFactory(vendorsFederal, liquiLoansPersonalLoan, liquiLoansEarlySalary)
	httpClient := http.NewClient()
	commonPrepay := prePayConfigProviderWorker(conf)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(preApprovedLoanVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	provider4 := idfc3.NewProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoWithInstrumentation)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	provider5 := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoWithInstrumentation)
	loandataproviderFactory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, provider4, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, provider5, mvLoanDataProvider, abflPwaJourneyDataProvider)
	commonLms := lmsWorkerConfigProvider(conf)
	lmsLms := lms2.NewLms(crdbLoanInstallmentPayoutDaoMultiDB, loandataproviderFactory, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanActivityDaoMultiDB, txnExecutorProvider, crdbLoanAccountsDaoMultiDB, eventsBroker, nudgeClient, commonLms, defaultTime, idfcVgClient, crdbLoanApplicantDaoMultiDB, rpcHelper)
	liquiloansLiquiloans, err := liquiloans4.NewLiquiloans(rpcHelper, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, llPalVgClient, lmsLms, collectionClient, piClient, orderClient, loandataproviderFactory, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, txnExecutorProvider)
	if err != nil {
		return nil, err
	}
	liquiloansPL := liquiloans4.NewLiquiloansPL(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	liquiloansEarlySalary := liquiloans4.NewLiquiloansEarlySalary(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	idfcBaseProvider := idfc4.NewBaseProvider(crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanApplicantDaoMultiDB, loandataproviderFactory, rpcHelper, commonPrepay, idfcVgClient, piClient, orderClient)
	personalLoanProvider := idfc4.NewPersonalLoanProvider(idfcBaseProvider, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, baseproviderProvider)
	liquiloansFLDG := liquiloans4.NewLiquiloansFLDG(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	liquiloansAcqToLend := liquiloans4.NewLiquiloansAcqToLend(commonPrepay, baseproviderProvider, liquiloansLiquiloans, onbClient)
	lamfProvider := fiftyfin5.NewLAMFProvider(providerFactory, fiftyFinVgClient, accountPiClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, rpcHelper, piClient, orderClient, lamfVendorDataProvider, baseProvider)
	liquiloansSTPL := liquiloans4.NewLiquiloansSTPL(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	finfluxFinfluxBaseProvider := finflux3.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB)
	liquiloansRTSubvention := liquiloans4.NewLiquiloansRTSubvention(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	liquiloansRTStpl := liquiloans4.NewLiquiloansRTStpl(commonPrepay, baseproviderProvider, liquiloansLiquiloans)
	stockGuardianBaseProvider := stock_guardian2.NewStockGuardianBaseProvider(sgLmsClient, crdbLoanAccountsDaoMultiDB, loandataproviderFactory, rpcHelper, crdbLoanPaymentRequestsDaoMultiDB, piClient, crdbLoanInstallmentInfoDaoMultiDB, commonPrepay, baseproviderProvider, collectionClient)
	provider6 := lenden4.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, loandataproviderFactory)
	prepayFactory := prepay.NewFactory(liquiloansPL, liquiloansEarlySalary, personalLoanProvider, liquiloansFLDG, liquiloansAcqToLend, lamfProvider, liquiloansSTPL, finfluxFinfluxBaseProvider, liquiloansRTSubvention, liquiloansRTStpl, stockGuardianBaseProvider, provider6)
	agreementProvider := agreement.NewAgreementProvider(rpcHelper, abflVgClient, crdbLoanStepExecutionsDaoWithInstrumentation)
	preClosureProvider := idfc7.NewPreClosureProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanApplicantDaoMultiDB)
	lenderPreCloseProvider := fiftyfin7.NewLenderPreCloseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, lamfVendorDataProvider)
	sgLenderPreCloseProvider := stock_guardian4.NewSgLenderPreCloseProvider(crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, sgLmsClient)
	precloseFactory := preclose.NewFactory(preClosureProvider, lenderPreCloseProvider, sgLenderPreCloseProvider)
	crdbPartnerLmsUserDao := impl.NewCrdbPartnerLmsUserDao(dbConnProvider, domainIdGenerator)
	flags := getFlagsConfFromWorkerGenConf(workerGenConf)
	simpleOffersDecisionEngine := decision_engine.NewSimpleOffersDecisionEngine(flags)
	crdbLoanOfferEligibilityCriteriaDao := impl.NewCrdbLoanOfferEligibilityCriteriaDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	multiDbProviderImpl := multidb_provider.NewMultiDBProvider(simpleOffersDecisionEngine, crdbLoanAccountsDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanOfferEligibilityCriteriaDao)
	vendorProgramLevelFeature := getFeaturesFromWorkerGenConf(workerGenConf)
	basePostProcessor := postproc.NewBasePostProcessor()
	lopeOverrideConfig := lopeOverrideWorkerConfigProvider(workerGenConf)
	highestPriorityPostProcessor := postproc.NewHighestPriorityPostProcessor(basePostProcessor, lopeOverrideConfig)
	iPostProcessor := postproc.ProvidePostProcessorChain(highestPriorityPostProcessor)
	providersCommon := providers2.NewCommon(userGroupClient, userClient, vendorProgramLevelFeature, iPostProcessor)
	nonFiCore := providers2.NewNonFiCore(userGroupClient, userClient, vendorProgramLevelFeature, iPostProcessor)
	securedLoanProvider := providers2.NewSecuredLoanProvider()
	priorityProviderFactoryImpl := priority_provider.NewPriorityProviderFactoryImpl(providersCommon, nonFiCore, securedLoanProvider)
	finfluxProvider := finflux4.NewProvider(finFluxVgClient)
	wrapperProvider := wrapper2.NewProvider()
	loanCalculator := getLoanCalculatorConfFromWorker(conf)
	provider7 := idfc5.NewProvider(loanCalculator)
	stockguardianProvider := stockguardian2.NewProvider(sgLmsClient)
	calculatorProvider := lenden5.NewCalculatorProvider(ldcClient)
	calculatorFactoryImpl := calculator.NewFactoryImpl(finfluxProvider, wrapperProvider, provider7, stockguardianProvider, calculatorProvider)
	commonEligibilityProvider := providers3.NewCommonEligibilityProvider(limitEstimatorClient, calculatorFactoryImpl)
	realtimeDistEligibilityProvider := liquiloans5.NewRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	liquiloansLLFiliteProvider := liquiloans5.NewLLFiliteProvider(loecWrapper, commonEligibilityProvider)
	fedRealtimeDistEligibilityProvider := federal2.NewFedRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	fedRealtimeDistNtbEligibilityProvider := federal2.NewFedRealtimeDistNtbEligibilityProvider(loecWrapper, commonEligibilityProvider)
	nonFiCoreProvider := providers3.NewNonFiCoreProvider(loecWrapper, commonEligibilityProvider)
	realtimeCommonProvider := providers3.NewRealtimeCommonProvider(loecWrapper, commonEligibilityProvider)
	featureConstraint := getSgNewEligibilityFlowActivityConf(workerGenConf)
	sgRealtimeDistEligibilityProvider := stock_guardian3.NewSgRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider, featureConstraint)
	lendenRealtimeDistEligibilityProvider := lenden6.NewRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	fedPlEtbProvider := federal2.NewFedPlEtbProvider(commonEligibilityProvider, limitEstimatorClient, crdbLoanOffersDaoWithInstrumentation)
	fiftyfinLamfProvider := fiftyfin6.NewFiftyfinLamfProvider(commonEligibilityProvider)
	moneyViewRTDETBProvider := moneyview4.NewMoneyViewRTDETBProvider(loecWrapper, commonEligibilityProvider)
	eligibilityEvaluatorFactoryImpl := eligibility_evaluator.NewEligibilityEvaluatorFactoryImpl(commonEligibilityProvider, realtimeDistEligibilityProvider, limitEstimatorClient, calculatorFactoryImpl, liquiloansLLFiliteProvider, fedRealtimeDistEligibilityProvider, fedRealtimeDistNtbEligibilityProvider, nonFiCoreProvider, realtimeCommonProvider, sgRealtimeDistEligibilityProvider, lendenRealtimeDistEligibilityProvider, fedPlEtbProvider, fiftyfinLamfProvider, flags, moneyViewRTDETBProvider)
	loanOptionRecommender := provider.NewLoanOptionRecommender(multiDbProviderImpl, priorityProviderFactoryImpl, eligibilityEvaluatorFactoryImpl, rpcHelper, onbClient, vendorProgramLevelFeature, savingsClient, lopeOverrideConfig)
	featureReleaseConfig := featureReleaseWorkerConfigProvider(workerGenConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	commonUserDataProvider := userdata.NewCommonUserDataProvider(rpcHelper)
	epifiDataCompletenessChecker := datacompleteness.NewEpifiDataCompletenessChecker(salaryEstimationClient, commonUserDataProvider, breClient, eventsBroker, workerGenConf, dataDevS3Client, userClient, userGroupClient, onbClient, savingsClient, evaluator)
	lendenDataCompletenessChecker := datacompleteness.NewLendenDataCompletenessChecker(ldcClient, rpcHelper, crdbLoanApplicantDaoMultiDB, epifiDataCompletenessChecker, commonUserDataProvider, loecWrapper, salaryEstimationClient, connAccClient, userClient, eventsBroker, onbClient, savingsClient, acqEventPublisherImpl)
	datacompletenessFactory := datacompleteness.NewFactory(epifiDataCompletenessChecker, lendenDataCompletenessChecker, workerGenConf)
	lendenBreCaller := brecaller.NewLendenBreCaller(ldcClient, rpcHelper, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, txnExecutorProvider, commonUserDataProvider, loecWrapper, salaryEstimationClient, connAccClient, userClient, eventsBroker, onbClient, savingsClient, crdbLoanRequestsDaoWithInstrumentation, workerGenConf)
	epifiBreCaller := brecaller.NewEpifiBreCaller(rpcHelper, breClient, txnExecutorProvider, commonUserDataProvider, workerGenConf, dataDevS3Client, onbClient, savingsClient, salaryEstimationClient, eventsBroker)
	federalBreCaller := brecaller.NewFederalBreCaller(rpcHelper, preApprovedLoanVgClient, commonUserDataProvider, evaluator)
	brecallerFactory := brecaller.NewFactory(lendenBreCaller, epifiBreCaller, federalBreCaller)
	processor := activity.NewProcessor(crdbLoanRequestsDaoWithInstrumentation, crdbLoanOffersDaoWithInstrumentation, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanActivityDaoMultiDB, loecWrapper, crdbLoanApplicantDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, rpcHelper, lvClient, txnExecutor, preApprovedLoanVgClient, persistentQueue, eSignClient, client, eventsBroker, providerFactory, limitEstimatorClient, orchClient, txnExecutorProvider, s3Client, bcClient, factory, docsClient, httpClient, payClient, defaultTime, llPalVgClient, prepayFactory, lmsLms, agreementProvider, precloseFactory, riskClient, epfoClient, productClient, vkycClient, nudgeClient, workerGenConf, palClient, piClient, accountPiClient, orderClient, idfcVgClient, crdbPartnerLmsUserDao, finFluxVgClient, recurringPaymentClient, loanOptionRecommender, calculatorFactoryImpl, breClient, dataDevS3Client, evaluator, ldcClient, datacompletenessFactory, brecallerFactory, commonUserDataProvider, userClient, mvVgClient, acqEventPublisherImpl, onbClient, savingsClient)
	return processor, nil
}

func InitializePreApprovedLoanCxService(db types.LoansFederalPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], paymentClient payment.PaymentClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, palClient preapprovedloan2.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, commsClient comms.CommsClient, conf *config.Config, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, savingsVgClient savings2.SavingsClient, llPalVgClient liquiloans.LiquiloansClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, idfcVgClient idfc.IdfcClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, dynConf *genconf.Config, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, epifiCRDB types.EpifiCRDB, segmentationClient segment.SegmentationServiceClient, awsConf aws.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, finFluxVgClient finflux.FinfluxClient, redisStore types2.FireflyRedisStore, broker events.Broker, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, ldcClient lenden.LendenClient, mvVgClient moneyview.MoneyviewClient, locationClient location2.LocationClient, userGroupClient group.GroupClient) *cx.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, broker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, broker)
	crdbLoanRequestsDaoMultiDB := impl.NewCrdbLoanRequestsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanStepExecutionsDaoMultiDB := impl.NewCrdbLoanStepExecutionsDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	gormDB := types.LoansFederalPGDBGormDBProvider(db)
	persistentQueue := persistentqueue.NewPersistentQueue(gormDB)
	defaultTime := datetime.NewDefaultTime()
	notification := notificationConfigProvider(conf)
	creditReportConfig := getCreditReportConfig(dynConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoMultiDB, notification, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, broker, crdbLoanRequestsDaoMultiDB, sgEsignApiGateway, locationClient)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(preApprovedLoanVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	idfcProvider := idfc3.NewProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoMultiDB, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoMultiDB)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	lendenProvider := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoMultiDB)
	loandataproviderFactory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, idfcProvider, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, lendenProvider, mvLoanDataProvider, abflPwaJourneyDataProvider)
	baseDataProvider := providers4.NewBaseDataProvider(crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB)
	lamfDataProvider := impl2.NewLamfDataProvider(baseDataProvider, crdbLoanApplicantDaoMultiDB, lamfVendorDataProvider)
	providerFactory := factory.NewProviderFactory(baseDataProvider, lamfDataProvider)
	flags := getFlagsConf(dynConf)
	simpleOffersDecisionEngine := decision_engine.NewSimpleOffersDecisionEngine(flags)
	crdbLoanOfferEligibilityCriteriaDao := impl.NewCrdbLoanOfferEligibilityCriteriaDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	multiDbProviderImpl := multidb_provider.NewMultiDBProvider(simpleOffersDecisionEngine, crdbLoanAccountsDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanRequestsDaoMultiDB, crdbLoanOfferEligibilityCriteriaDao)
	vendorProgramLevelFeature := getFeaturesConf(dynConf)
	basePostProcessor := postproc.NewBasePostProcessor()
	lopeOverrideConfig := lopeOverrideServiceConfigProvider(dynConf)
	highestPriorityPostProcessor := postproc.NewHighestPriorityPostProcessor(basePostProcessor, lopeOverrideConfig)
	iPostProcessor := postproc.ProvidePostProcessorChain(highestPriorityPostProcessor)
	providersCommon := providers2.NewCommon(userGroupClient, userClient, vendorProgramLevelFeature, iPostProcessor)
	nonFiCore := providers2.NewNonFiCore(userGroupClient, userClient, vendorProgramLevelFeature, iPostProcessor)
	securedLoanProvider := providers2.NewSecuredLoanProvider()
	priorityProviderFactoryImpl := priority_provider.NewPriorityProviderFactoryImpl(providersCommon, nonFiCore, securedLoanProvider)
	finfluxProvider := finflux4.NewProvider(finFluxVgClient)
	wrapperProvider := wrapper2.NewProvider()
	loanCalculator := getLoanCalculatorConstraintConf(conf)
	provider4 := idfc5.NewProvider(loanCalculator)
	stockguardianProvider := stockguardian2.NewProvider(sgLmsClient)
	calculatorProvider := lenden5.NewCalculatorProvider(ldcClient)
	factoryImpl := calculator.NewFactoryImpl(finfluxProvider, wrapperProvider, provider4, stockguardianProvider, calculatorProvider)
	commonEligibilityProvider := providers3.NewCommonEligibilityProvider(limitEstimatorClient, factoryImpl)
	realtimeDistEligibilityProvider := liquiloans5.NewRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	liquiloansLLFiliteProvider := liquiloans5.NewLLFiliteProvider(loecWrapper, commonEligibilityProvider)
	fedRealtimeDistEligibilityProvider := federal2.NewFedRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	fedRealtimeDistNtbEligibilityProvider := federal2.NewFedRealtimeDistNtbEligibilityProvider(loecWrapper, commonEligibilityProvider)
	nonFiCoreProvider := providers3.NewNonFiCoreProvider(loecWrapper, commonEligibilityProvider)
	realtimeCommonProvider := providers3.NewRealtimeCommonProvider(loecWrapper, commonEligibilityProvider)
	featureConstraint := getSgNewEligibilityFlowConf(dynConf)
	sgRealtimeDistEligibilityProvider := stock_guardian3.NewSgRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider, featureConstraint)
	lendenRealtimeDistEligibilityProvider := lenden6.NewRealtimeDistEligibilityProvider(loecWrapper, commonEligibilityProvider)
	fedPlEtbProvider := federal2.NewFedPlEtbProvider(commonEligibilityProvider, limitEstimatorClient, crdbLoanOffersDaoWithInstrumentation)
	fiftyfinLamfProvider := fiftyfin6.NewFiftyfinLamfProvider(commonEligibilityProvider)
	moneyViewRTDETBProvider := moneyview4.NewMoneyViewRTDETBProvider(loecWrapper, commonEligibilityProvider)
	eligibilityEvaluatorFactoryImpl := eligibility_evaluator.NewEligibilityEvaluatorFactoryImpl(commonEligibilityProvider, realtimeDistEligibilityProvider, limitEstimatorClient, factoryImpl, liquiloansLLFiliteProvider, fedRealtimeDistEligibilityProvider, fedRealtimeDistNtbEligibilityProvider, nonFiCoreProvider, realtimeCommonProvider, sgRealtimeDistEligibilityProvider, lendenRealtimeDistEligibilityProvider, fedPlEtbProvider, fiftyfinLamfProvider, flags, moneyViewRTDETBProvider)
	loanOptionRecommender := provider.NewLoanOptionRecommender(multiDbProviderImpl, priorityProviderFactoryImpl, eligibilityEvaluatorFactoryImpl, rpcHelper, onbClient, vendorProgramLevelFeature, savingsClient, lopeOverrideConfig)
	service := cx.NewService(loecWrapper, crdbLoanOffersDaoWithInstrumentation, crdbLoanRequestsDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanApplicantDaoMultiDB, paymentClient, persistentQueue, rpcHelper, dbConnProvider, epifiCRDB, loandataproviderFactory, segmentationClient, providerFactory, dynConf, multiDbProviderImpl, loanOptionRecommender)
	return service
}

func InitializePreApprovedLoanDevEntityService(db types.LoansFederalPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], redisStore types2.FireflyRedisStore, eventsBroker events.Broker, savingsClient savings.SavingsClient, mfExternalClient external.MFExternalOrdersClient) *developer.PreApprovedLoanDevEntity {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	devLoanAccountsEntity := processor.NewDevLoanAccountsEntity(crdbLoanAccountsDaoMultiDB)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	devLoanOfferEntity := processor.NewDevLoanOfferEntity(crdbLoanOffersDaoWithInstrumentation)
	crdbLoanRequestsDaoMultiDB := impl.NewCrdbLoanRequestsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	devLoanRequestsEntity := processor.NewDevLoanRequestsEntity(crdbLoanRequestsDaoMultiDB)
	crdbLoanStepExecutionsDaoMultiDB := impl.NewCrdbLoanStepExecutionsDaoMultiDB(dbConnProvider, domainIdGenerator)
	devLoanStepExecutionEntity := processor.NewDevLoanStepExecutionEntity(crdbLoanStepExecutionsDaoMultiDB)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	devLoanOfferEligibilityCriteriaEntity := processor.NewDevLoanOfferEligibilityCriteriaEntity(loecWrapper)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	devLoanActivityEntity := processor.NewDevLoanActivityEntity(crdbLoanActivityDaoMultiDB)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	devLoanInstallmentInfoEntity := processor.NewDevLoanInstallmentInfoEntity(crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	devLoanInstallmentPayoutEntity := processor.NewDevLoanInstallmentPayoutEntity(crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	devLoanPaymentRequestEntity := processor.NewDevLoanPaymentRequestEntity(crdbLoanPaymentRequestsDaoMultiDB)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	devLoanApplicantsEntity := processor.NewDevLoanApplicantsEntity(crdbLoanApplicantDaoMultiDB)
	crdbPartnerLmsUserDao := impl.NewCrdbPartnerLmsUserDao(dbConnProvider, domainIdGenerator)
	devPartnerLmsUserEntity := processor.NewDevPartnerLmsUserEntity(crdbPartnerLmsUserDao)
	fetchedAssetDao := impl3.NewFetchedAssetDao(dbConnProvider)
	devFetchedAssetEntity := processor.NewFetchedAssetEntity(fetchedAssetDao)
	mandateRequestDao := impl.NewMandateRequestDao(dbConnProvider, domainIdGenerator)
	devMandateRequestsEntity := processor.NewDevMandateRequestsEntity(mandateRequestDao)
	pgdbPreEligibilityOfferDao := impl.NewPgdbPreEligibilityOfferDao(dbConnProvider, domainIdGenerator)
	devPreEligibilityOfferEntity := processor.NewDevPreEligibilityOfferEntity(pgdbPreEligibilityOfferDao)
	devLoansMasterEntity := processor.NewDevLoansMasterEntity(crdbLoanRequestsDaoMultiDB, loecWrapper, crdbLoanOffersDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, crdbLoanApplicantDaoMultiDB)
	devFactory := developer.NewDevFactory(devLoanAccountsEntity, devLoanOfferEntity, devLoanRequestsEntity, devLoanStepExecutionEntity, devLoanOfferEligibilityCriteriaEntity, devLoanActivityEntity, devLoanInstallmentInfoEntity, devLoanInstallmentPayoutEntity, devLoanPaymentRequestEntity, devLoanApplicantsEntity, devPartnerLmsUserEntity, devFetchedAssetEntity, devMandateRequestsEntity, devPreEligibilityOfferEntity, devLoansMasterEntity)
	preApprovedLoanDevEntity := developer.NewPreApprovedLoanDevEntity(devFactory, crdbLoanRequestsDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanStepExecutionsDaoMultiDB, loecWrapper, crdbLoanActivityDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbPartnerLmsUserDao, crdbLoanPaymentRequestsDaoMultiDB, fetchedAssetDao, mandateRequestDao)
	return preApprovedLoanDevEntity
}

func InitializeInboundNotificationService(dbConn types.LoansFederalPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, eSignClient esign.ESignClient, vgCustomerClient customer.CustomerClient, commsClient comms.CommsClient, conf *config.Config, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, orderClient order.OrderServiceClient, payClient pay.PayClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, llPalVgClient liquiloans.LiquiloansClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, idfcVgClient idfc.IdfcClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, dynConf *genconf.Config, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, awsConf aws.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, redisStore types2.FireflyRedisStore, broker events.Broker, sgEsignApiGateway esign2.EsignClient, locationClient location2.LocationClient) *inbound_notification.Service {
	defaultTime := datetime.NewDefaultTime()
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbLoanStepExecutionsDao := impl.NewCrdbLoanStepExecutionsDao(dbConnProvider, domainIdGenerator)
	notification := notificationConfigProvider(conf)
	crdbLoanActivityDao := impl.NewCrdbLoanActivityDao(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDao := impl.NewCrdbLoanApplicantDao(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfig(dynConf)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDao := impl.NewCrdbLoanRequestsDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDao, notification, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDao, llPalVgClient, crdbLoanApplicantDao, profileClient, bcClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, broker, crdbLoanRequestsDao, sgEsignApiGateway, locationClient)
	crdbLoanAccountsDao := impl.NewCrdbLoanAccountsDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDao := impl.NewCrdbLoanInstallmentInfoDao(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentPayoutDao := impl.NewCrdbLoanInstallmentPayoutDao(dbConnProvider, domainIdGenerator)
	db := types.LoansFederalPGDBGormDBProvider(dbConn)
	txnExecutor := newGormTxnExecutorProvider(db)
	service := inbound_notification.NewService(rpcHelper, crdbLoanAccountsDao, crdbLoanRequestsDao, crdbLoanActivityDao, crdbLoanInstallmentInfoDao, crdbLoanInstallmentPayoutDao, txnExecutor, piClient, actorClient, orderClient, accountPiClient)
	return service
}

func InitialiseFederalActivityProcessor(db *gorm.DB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, palClient preapprovedloan2.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, txnExecutor storagev2.TxnExecutor, persistentQueue persistentqueue.PersistentQueue, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, client profilevalidation.ProfileValidationClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, orchClient orchestrator.OrchestratorClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], awsConf aws.Config, conf *worker.Config, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, idfcVgClient idfc.IdfcClient, docsClient docs.DocsClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, nudgeClient nudge.NudgeServiceClient, finFluxVgClient finflux.FinfluxClient, catalogClient catalog.CatalogManagerClient, redisStore types2.FireflyRedisStore, setuClient setu.SetuClient, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, ldcClient lenden.LendenClient, mvVgClient moneyview.MoneyviewClient, enachClient enach.EnachServiceClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, locationClient location2.LocationClient) *federal4.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	defaultTime := datetime.NewDefaultTime()
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(preApprovedLoanVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	idfcProvider := idfc3.NewProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoWithInstrumentation)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	lendenProvider := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoWithInstrumentation)
	loandataproviderFactory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, idfcProvider, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, lendenProvider, mvLoanDataProvider, abflPwaJourneyDataProvider)
	commonLms := lmsWorkerConfigProvider(conf)
	lmsLms := lms2.NewLms(crdbLoanInstallmentPayoutDaoMultiDB, loandataproviderFactory, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanActivityDaoMultiDB, txnExecutorProvider, crdbLoanAccountsDaoMultiDB, eventsBroker, nudgeClient, commonLms, defaultTime, idfcVgClient, crdbLoanApplicantDaoMultiDB, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	provider4 := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	provider5 := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, provider4, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, provider5, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	multiDbDoOnce := newMultiDbDoOnce(dbConnProvider)
	commsHelper := helper.NewCommsHelper(commsClient, rpcHelper)
	federalProcessor := federal4.NewProcessor(crdbLoanRequestsDaoWithInstrumentation, enachClient, crdbLoanOffersDaoWithInstrumentation, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanActivityDaoMultiDB, loecWrapper, crdbLoanApplicantDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, rpcHelper, lvClient, txnExecutor, preApprovedLoanVgClient, persistentQueue, eSignClient, client, eventsBroker, baseproviderProvider, limitEstimatorClient, orchClient, txnExecutorProvider, s3Client, bcClient, docsClient, payClient, defaultTime, llPalVgClient, lmsLms, vkycClient, piClient, accountPiClient, orderClient, providerFactory, setuClient, userClient, celestialClient, realTimeFedProvider, multiDbDoOnce, commsHelper, recurringPaymentClient, onbClient, savingsClient)
	return federalProcessor
}

func InitialiseLlActivityProcessor(db *gorm.DB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], llPalVgClient liquiloans.LiquiloansClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, client profilevalidation.ProfileValidationClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, txnExecutor storagev2.TxnExecutor, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, eventsBroker events.Broker, orchClient orchestrator.OrchestratorClient, awsConf aws.Config, conf *worker.Config, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], idfcVgClient idfc.IdfcClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, palClient preapprovedloan2.PreApprovedLoanClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, breClient bre.BreClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, dataDevS3Client types2.DataDevS3Client, abflVgClient abfl.AbflClient, connectedAccountClient connected_account.ConnectedAccountClient, incomeEstimatorClient incomeestimator.IncomeEstimatorClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, catalogClient catalog.CatalogManagerClient, nudgeClient nudge.NudgeServiceClient, userGroupClient group.GroupClient, finFluxVgClient finflux.FinfluxClient, userIntelClient userintel.UserIntelServiceClient, compClient compliance.ComplianceClient, redisStore types2.FireflyRedisStore, riskClient risk.RiskClient, oprStatusClient operstatus.OperationalStatusServiceClient, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, ldcClient lenden.LendenClient, mvVgClient moneyview.MoneyviewClient, locationClient location2.LocationClient) *liquiloans7.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	defaultTime := datetime.NewDefaultTime()
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connectedAccountClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(preApprovedLoanVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	provider4 := idfc3.NewProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoWithInstrumentation)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	provider5 := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoWithInstrumentation)
	loandataproviderFactory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, provider4, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, provider5, mvLoanDataProvider, abflPwaJourneyDataProvider)
	commonLms := lmsWorkerConfigProvider(conf)
	lmsLms := lms2.NewLms(crdbLoanInstallmentPayoutDaoMultiDB, loandataproviderFactory, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanActivityDaoMultiDB, txnExecutorProvider, crdbLoanAccountsDaoMultiDB, eventsBroker, nudgeClient, commonLms, defaultTime, idfcVgClient, crdbLoanApplicantDaoMultiDB, rpcHelper)
	commsHelper := helper.NewCommsHelper(commsClient, rpcHelper)
	multiDbDoOnce := newMultiDbDoOnce(dbConnProvider)
	featureReleaseConfig := featureReleaseWorkerConfigProvider(workerConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	mandateRequestDao := impl.NewMandateRequestDao(dbConnProvider, domainIdGenerator)
	mandateConfig := getMandateConfFromWorkerGenConf(workerConf)
	finfluxProvider := finflux4.NewProvider(finFluxVgClient)
	wrapperProvider := wrapper2.NewProvider()
	loanCalculator := getLoanCalculatorConfFromWorker(conf)
	provider6 := idfc5.NewProvider(loanCalculator)
	stockguardianProvider := stockguardian2.NewProvider(sgLmsClient)
	calculatorProvider := lenden5.NewCalculatorProvider(ldcClient)
	calculatorFactoryImpl := calculator.NewFactoryImpl(finfluxProvider, wrapperProvider, provider6, stockguardianProvider, calculatorProvider)
	creditReportHelper := activity.NewCreditReportHelper(creditReportManagerV2)
	liquiloansProcessor := liquiloans7.NewProcessor(crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, loecWrapper, crdbLoanInstallmentInfoDaoMultiDB, llPalVgClient, rpcHelper, txnExecutorProvider, orchClient, plProvider, s3Client, conf, esProvider, fiLiteProvider, providerFactory, recurringPaymentClient, creditReportManager, creditReportManagerV2, creditReportConfig, palClient, lmsLms, orderClient, loandataproviderFactory, eventsBroker, authClient, breClient, dataDevS3Client, connectedAccountClient, incomeEstimatorClient, acqToLendProvider, commsHelper, multiDbDoOnce, workerConf, employmentClient, piClient, userClient, salaryProgramClient, onbClient, panVgClient, evaluator, finFluxVgClient, userIntelClient, bcClient, compClient, riskClient, oprStatusClient, savingsClient, mandateRequestDao, mandateConfig, calculatorFactoryImpl, creditReportHelper)
	return liquiloansProcessor
}

func InitialiseIdfcActivityProcessor(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, idfcPalVgClient idfc.IdfcClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, conf *worker.Config, orchestratorClient orchestrator.OrchestratorClient, txnExecutor storagev2.TxnExecutor, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, consentClient consent.ConsentClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, catalogClient catalog.CatalogManagerClient, redisStore types2.FireflyRedisStore, userGroupClient group.GroupClient, sgEsignApiGateway esign2.EsignClient, locationClient location2.LocationClient) *idfc8.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanOfferDao := impl.NewCrdbLoanOfferDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	defaultTime := datetime.NewDefaultTime()
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcPalVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	client := http.NewClient()
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	commsHelper := helper.NewCommsHelper(commsClient, rpcHelper)
	multiDbDoOnce := newMultiDbDoOnce(dbConnProvider)
	featureReleaseConfig := featureReleaseWorkerConfigProvider(workerConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	idfcProcessor := idfc8.NewProcessor(crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanApplicantDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanOfferDao, crdbLoanInstallmentInfoDaoMultiDB, rpcHelper, idfcPalVgClient, idfcProvider, docsClient, client, s3Client, orchestratorClient, lvClient, txnExecutor, providerFactory, consentClient, kycClient, workerConf, commsHelper, multiDbDoOnce, defaultTime, evaluator)
	return idfcProcessor
}

func InitialiseCommonActivityProcessor(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, idfcPalVgClient idfc.IdfcClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, palClient preapprovedloan2.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, conf *worker.Config, orchestratorClient orchestrator.OrchestratorClient, txnExecutor storagev2.TxnExecutor, accountVgClient accounts.AccountsClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, catalogClient catalog.CatalogManagerClient, userIntelClient userintel.UserIntelServiceClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], redisStore types2.FireflyRedisStore, sgEsignApiGateway esign2.EsignClient, salaryEstimationClient salaryestimation.SalaryEstimationClient, locationClient location2.LocationClient, userGroupClient group.GroupClient, finFluxVgClient finflux.FinfluxClient, sgLmsClient lms.LmsClient, mvVgClient moneyview.MoneyviewClient, ldcClient lenden.LendenClient) *common2.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanPaymentRequestsDao := impl.NewCrdbLoanPaymentRequestsDao(dbConnProvider, domainIdGenerator)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	defaultTime := datetime.NewDefaultTime()
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcPalVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	commsHelper := helper.NewCommsHelper(commsClient, rpcHelper)
	multiDbDoOnce := newMultiDbDoOnce(dbConnProvider)
	commonUserDataProvider := userdata.NewCommonUserDataProvider(rpcHelper)
	featureReleaseConfig := featureReleaseWorkerConfigProvider(workerConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(preApprovedLoanVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	provider4 := idfc3.NewProvider(idfcPalVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoWithInstrumentation)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDao, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDao, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	provider5 := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoWithInstrumentation)
	loandataproviderFactory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, provider4, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, provider5, mvLoanDataProvider, abflPwaJourneyDataProvider)
	creditReportHelper := activity.NewCreditReportHelper(creditReportManagerV2)
	commonProcessor := common2.NewProcessor(crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanApplicantDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDao, crdbLoanOffersDaoWithInstrumentation, loecWrapper, rpcHelper, orderClient, recurringPaymentClient, conf, providerFactory, accountPiClient, piClient, accountBalanceClient, userIntelClient, txnExecutorProvider, commsHelper, multiDbDoOnce, commonUserDataProvider, salaryEstimationClient, onbClient, savingsClient, evaluator, loandataproviderFactory, creditReportManagerV2, creditReportHelper, palClient)
	return commonProcessor
}

func InitialiseFiftyfinActivityProcessor(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, idfcPalVgClient idfc.IdfcClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, palClient preapprovedloan2.PreApprovedLoanClient, userClient user.UsersClient, groupClient group.GroupClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, conf *worker.Config, orchestratorClient orchestrator.OrchestratorClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, consentClient consent.ConsentClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, securedLoanClient secured_loans.SecuredLoansClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], segmentationClient segment.SegmentationServiceClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, catalogClient catalog.CatalogManagerClient, finFluxVgClient finflux.FinfluxClient, redisStore types2.FireflyRedisStore, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, ldcClient lenden.LendenClient, mvVgClient moneyview.MoneyviewClient, locationClient location2.LocationClient) *fiftyfin8.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcPalVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(preApprovedLoanVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	provider4 := idfc3.NewProvider(idfcPalVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoWithInstrumentation)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	provider5 := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoWithInstrumentation)
	loandataproviderFactory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, provider4, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, provider5, mvLoanDataProvider, abflPwaJourneyDataProvider)
	fetchedAssetDao := impl3.NewFetchedAssetDao(dbConnProvider)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	fiftyFinLamfOfferManager := lamf.NewFiftyFinLamfOfferManager(fiftyFinVgClient, crdbLoanOffersDaoWithInstrumentation, loecWrapper, crdbLoanApplicantDaoMultiDB, txnExecutorProvider, workerConf)
	mfCentralOfferManager := lamf.NewMFCentralOfferManager(defaultTime, fiftyFinVgClient, crdbLoanOffersDaoWithInstrumentation, loecWrapper, mfExternalClient, txnExecutorProvider, uuidGenerator, workerConf)
	mfcCasSummaryOfferManager := lamf.NewMfcCasSummaryOfferManager(defaultTime, fiftyFinVgClient, crdbLoanOffersDaoWithInstrumentation, crdbLoanApplicantDaoMultiDB, loecWrapper, mfExternalClient, txnExecutorProvider, uuidGenerator, workerConf)
	featureReleaseConfig := getFeatureReleaseConfig(workerConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, groupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	offerManagerFactoryImpl := offer_manager.NewOfferManagerFactoryImpl(fiftyFinLamfOfferManager, mfCentralOfferManager, mfcCasSummaryOfferManager, evaluator)
	multiDbDoOnce := newMultiDbDoOnce(dbConnProvider)
	commsHelper := helper.NewCommsHelper(commsClient, rpcHelper)
	fiftyfinProcessor := fiftyfin8.NewProcessor(crdbLoanApplicantDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, crdbLoanOffersDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, providerFactory, loandataproviderFactory, fiftyFinVgClient, fetchedAssetDao, crdbLoanStepExecutionsDaoWithInstrumentation, rpcHelper, fiftyfinProvider, securedLoanClient, offerManagerFactoryImpl, txnExecutorProvider, multiDbDoOnce, commsHelper, loecWrapper, celestialClient, piClient, defaultTime, accountPiClient, eventsBroker, mfExternalClient, uuidGenerator, catalogClient, workerConf, evaluator, lamfVendorDataProvider)
	return fiftyfinProcessor
}

func InitializeSecuredLoansFiftyfinActivityProvider(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, idfcPalVgClient idfc.IdfcClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, conf *worker.Config, orchestratorClient orchestrator.OrchestratorClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, consentClient consent.ConsentClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, securedLoanClient secured_loans.SecuredLoansClient, segmentationClient segment.SegmentationServiceClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, abflVgClient abfl.AbflClient, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], catalogClient catalog.CatalogManagerClient, redisStore types2.FireflyRedisStore, sgEsignApiGateway esign2.EsignClient, locationClient location2.LocationClient) *fiftyfin9.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	fetchedAssetDao := impl3.NewFetchedAssetDao(dbConnProvider)
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcPalVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	crdbLoanOfferDao := impl.NewCrdbLoanOfferDao(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	fiftyfinProcessor := fiftyfin9.NewProcessor(crdbLoanApplicantDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, fiftyFinVgClient, fetchedAssetDao, crdbLoanStepExecutionsDaoWithInstrumentation, rpcHelper, crdbLoanOfferDao, fiftyfinProvider, mfExternalClient, txnExecutorProvider, defaultTime, uuidGenerator)
	return fiftyfinProcessor
}

func InitialiseLoanOfferDao(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], redisStore types2.FireflyRedisStore) dao.LoanOffersDao {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanOffersDaoMultiDB := impl.NewCrdbLoanOffersDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	return crdbLoanOffersDaoMultiDB
}

func InitializeSherlockBannersService(config2 *config.Config, config2_2 *genconf.Config, crdb types.LoansFederalPGDB, palClient preapprovedloan2.PreApprovedLoanClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], broker events.Broker, palVgClient preapprovedloan.PreApprovedLoanClient, llPalVgClient liquiloans.LiquiloansClient, idfcVgClient idfc.IdfcClient, fiftyFinClient fiftyfin.FiftyFinClient, abflVgClient abfl.AbflClient, nudgeClient nudge.NudgeServiceClient, finFluxVgClient finflux.FinfluxClient, redisStore types2.FireflyRedisStore, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, orchestratorClient orchestrator.OrchestratorClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, consentClient consent.ConsentClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, locationClient location2.LocationClient, userLocationClient location.LocationClient, segmentationClient segment.SegmentationServiceClient, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, ldcClient lenden.LendenClient, mvVgClient moneyview.MoneyviewClient) *sherlock_banners.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentPayoutDaoMultiDB := impl.NewCrdbLoanInstallmentPayoutDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	federalLoanDataProvider := providers.NewFederalDataProvider(palVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, broker, lendingCacheStorage)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanStepExecutionsDaoMultiDB := impl.NewCrdbLoanStepExecutionsDaoMultiDB(dbConnProvider, domainIdGenerator)
	notification := notificationConfigProvider(config2)
	creditReportConfig := getCreditReportConfig(config2_2)
	rpcHelper := helper.NewRpcHelper(palVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoMultiDB, notification, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, broker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	idfcProvider := idfc3.NewProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoMultiDB)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, broker)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDaoMultiDB, crdbLoanInstallmentPayoutDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	lendenProvider := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoWithInstrumentation)
	loandataproviderFactory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, idfcProvider, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, lendenProvider, mvLoanDataProvider, abflPwaJourneyDataProvider)
	commonLms := lmsConfigProvider(config2)
	lmsLms := lms2.NewLms(crdbLoanInstallmentPayoutDaoMultiDB, loandataproviderFactory, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanActivityDaoMultiDB, txnExecutorProvider, crdbLoanAccountsDaoMultiDB, broker, nudgeClient, commonLms, defaultTime, idfcVgClient, crdbLoanApplicantDaoMultiDB, rpcHelper)
	service := sherlock_banners.NewService(config2_2, crdbLoanAccountsDaoMultiDB, lmsLms)
	return service
}

func InitialiseAbflActivityProcessor(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, idfcPalVgClient idfc.IdfcClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, conf *worker.Config, orchestratorClient orchestrator.OrchestratorClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, consentClient consent.ConsentClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, abflVgClient abfl.AbflClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, locationClient location2.LocationClient, userLocationClient location.LocationClient, catalogClient catalog.CatalogManagerClient, redisStore types2.FireflyRedisStore, txnExecutor storagev2.TxnExecutor, finFluxVgClient finflux.FinfluxClient, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, lendenVgClient lenden.LendenClient) *abfl4.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcPalVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	finfluxProvider := finflux4.NewProvider(finFluxVgClient)
	wrapperProvider := wrapper2.NewProvider()
	loanCalculator := getLoanCalculatorConfFromWorker(conf)
	provider4 := idfc5.NewProvider(loanCalculator)
	stockguardianProvider := stockguardian2.NewProvider(sgLmsClient)
	calculatorProvider := lenden5.NewCalculatorProvider(lendenVgClient)
	calculatorFactoryImpl := calculator.NewFactoryImpl(finfluxProvider, wrapperProvider, provider4, stockguardianProvider, calculatorProvider)
	commonUserDataProvider := userdata.NewCommonUserDataProvider(rpcHelper)
	abflProcessor := abfl4.NewProcessor(crdbLoanRequestsDaoWithInstrumentation, crdbLoanOffersDaoWithInstrumentation, providerFactory, crdbLoanStepExecutionsDaoWithInstrumentation, rpcHelper, abflVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, s3Client, txnExecutorProvider, userClient, locationClient, abflProvider, txnExecutor, calculatorFactoryImpl, eventsBroker, commonUserDataProvider, onbClient, savingsClient)
	return abflProcessor
}

func InitialiseMvActivityProcessor(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, idfcPalVgClient idfc.IdfcClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, conf *worker.Config, orchestratorClient orchestrator.OrchestratorClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, consentClient consent.ConsentClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, abflVgClient abfl.AbflClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, mvVgClient moneyview.MoneyviewClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, redisStore types2.FireflyRedisStore, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, locationClient location2.LocationClient) *moneyview5.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcPalVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	commonUserDataProvider := userdata.NewCommonUserDataProvider(rpcHelper)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	moneyviewProcessor := moneyview5.NewProcessor(crdbLoanRequestsDaoWithInstrumentation, crdbLoanOffersDaoWithInstrumentation, crdbLoanStepExecutionsDaoWithInstrumentation, rpcHelper, mvVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, txnExecutorProvider, celestialClient, consentClient, userClient, eventsBroker, employmentClient, commonUserDataProvider, loecWrapper)
	return moneyviewProcessor
}

func InitialiseLendenActivityProcessor(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, idfcPalVgClient idfc.IdfcClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, conf *worker.Config, orchestratorClient orchestrator.OrchestratorClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, consentClient consent.ConsentClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, abflVgClient abfl.AbflClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, mvVgClient moneyview.MoneyviewClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, redisStore types2.FireflyRedisStore, sgEsignApiGateway esign2.EsignClient, sgLmsClient lms.LmsClient, connAccClient connected_account.ConnectedAccountClient, catalogClient catalog.CatalogManagerClient, lendenVgClient lenden.LendenClient, locationClient location2.LocationClient) *lenden9.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcPalVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	client := http.NewClient()
	connectedSalaryAccountProvider := helper.NewConnectedSalaryAccountProvider(connAccClient, crdbLoanRequestsDaoWithInstrumentation, crdbLoanOffersDaoWithInstrumentation)
	crdbLoanPaymentRequestsDaoMultiDB := impl.NewCrdbLoanPaymentRequestsDaoMultiDB(dbConnProvider, domainIdGenerator)
	bool2 := getPreferUPIMandateTypeFlagFromWorkerConf(workerConf)
	mandateSetupProcessor := lenden7.NewMandateSetupProcessor(crdbLoanApplicantDaoMultiDB, lendenVgClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, rpcHelper, userClient, bool2)
	lendenProcessor := lenden9.NewProcessor(crdbLoanRequestsDaoWithInstrumentation, crdbLoanOffersDaoWithInstrumentation, crdbLoanStepExecutionsDaoWithInstrumentation, rpcHelper, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, lendenVgClient, crdbLoanActivityDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, providerFactory, lendenProvider, txnExecutorProvider, loecWrapper, client, s3Client, connectedSalaryAccountProvider, consentClient, crdbLoanPaymentRequestsDaoMultiDB, userClient, mandateSetupProcessor)
	return lendenProcessor
}

func InitialiseStockGuardianActivityProcessor(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventsBroker events.Broker, llPalVgClient liquiloans.LiquiloansClient, idfcPalVgClient idfc.IdfcClient, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, authClient auth.AuthClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, notifConf *common.Notification, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, docsClient docs.DocsClient, awsConf aws.Config, conf *worker.Config, orchestratorClient orchestrator.OrchestratorClient, accountVgClient accounts.AccountsClient, creditReportManager credit_report.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, consentClient consent.ConsentClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, abflVgClient abfl.AbflClient, cardProvisioningClient provisioning.CardProvisioningClient, workerConf *genconf2.Config, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, s3Client types2.PreApprovedLoanS3Client, connAccClient connected_account.ConnectedAccountClient, mfExternalClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, catalogClient catalog.CatalogManagerClient, redisStore types2.FireflyRedisStore, txnExecutor storagev2.TxnExecutor, finFluxVgClient finflux.FinfluxClient, breClient bre.BreClient, dataDevS3Client types2.DataDevS3Client, sgEsignApiGateway esign2.EsignClient, sgApplicantClient applicant.ApplicantServiceClient, sgApplicationclient application.ApplicationClient, sgMatrixClient matrix.MatrixClient, sgCustomerClient customer2.CustomerServiceClient, sgKycClient kyc2.KYCClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, sgLmsClient lms.LmsClient, riskClient risk.RiskClient, omegleClient omegle.OmegleClient, palClient preapprovedloan2.PreApprovedLoanClient, ldcClient lenden.LendenClient, mvVgClient moneyview.MoneyviewClient, locationClient location2.LocationClient) *stock_guardian5.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoWithInstrumentation := impl.NewCrdbLoanRequestsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker, lendingCacheStorage)
	crdbLoanOffersDaoWithInstrumentation := impl.NewCrdbLoanOffersDaoWithInstrumentation(dbConnProvider, domainIdGenerator, lendingCacheStorage, eventsBroker)
	deeplinkConfig := getDeeplinkConfFromWorkerGenConf(workerConf)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanStepExecutionsDaoWithInstrumentation := impl.NewCrdbLoanStepExecutionsDaoWithInstrumentation(dbConnProvider, domainIdGenerator, eventsBroker)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfFromWorkerGenConf(workerConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoWithInstrumentation, notifConf, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcPalVgClient, accountVgClient, creditReportManager, creditReportManagerV2, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalClient, userLocationClient, eventsBroker, crdbLoanRequestsDaoWithInstrumentation, sgEsignApiGateway, locationClient)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, connAccClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, catalogClient, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	finfluxProvider := finflux4.NewProvider(finFluxVgClient)
	wrapperProvider := wrapper2.NewProvider()
	loanCalculator := getLoanCalculatorConfFromWorker(conf)
	provider4 := idfc5.NewProvider(loanCalculator)
	stockguardianProvider := stockguardian2.NewProvider(sgLmsClient)
	calculatorProvider := lenden5.NewCalculatorProvider(ldcClient)
	calculatorFactoryImpl := calculator.NewFactoryImpl(finfluxProvider, wrapperProvider, provider4, stockguardianProvider, calculatorProvider)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans3.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(preApprovedLoanVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	crdbLoanInstallmentPayoutDao := impl.NewCrdbLoanInstallmentPayoutDao(dbConnProvider, domainIdGenerator)
	llEsProvider := liquiloans3.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDao)
	provider5 := idfc3.NewProvider(idfcPalVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoWithInstrumentation, rpcHelper)
	llFiliteProvider := liquiloans3.NewLlFiliteProvider(crdbLoanStepExecutionsDaoWithInstrumentation)
	llLoanFLDGDataProvider := liquiloans3.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	crdbLoanPaymentRequestsDao := impl.NewCrdbLoanPaymentRequestsDao(dbConnProvider, domainIdGenerator)
	lamfVendorDataProvider := fiftyfin3.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin4.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoWithInstrumentation, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDao, crdbLoanPaymentRequestsDao, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl3.NewAbflDataProvider(abflVgClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDao, crdbLoanInstallmentPayoutDao, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider2.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsClient, crdbLoanStepExecutionsDaoWithInstrumentation, crdbLoanRequestsDaoWithInstrumentation, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	provider6 := lenden3.NewProvider(ldcClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview3.NewMvLoanDataProvider(mvVgClient)
	abflPwaJourneyDataProvider := abfl3.NewABFLPwaJourneyDataProvider(abflVgClient, crdbLoanRequestsDaoWithInstrumentation)
	loandataproviderFactory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, provider5, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, provider6, mvLoanDataProvider, abflPwaJourneyDataProvider)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalClient, eventsBroker)
	appsFlyerEventObserver := observers.NewAppsFlyerEventObserver(acqEventPublisherImpl)
	loecWrapper := wrapper.NewLoecWrapper(crdbLoanOfferEligibilityCriteriaDaoMultiDB, appsFlyerEventObserver)
	commonUserDataProvider := userdata.NewCommonUserDataProvider(rpcHelper)
	creditReportHelper := activity.NewCreditReportHelper(creditReportManagerV2)
	stock_guardianProcessor := stock_guardian5.NewProcessor(crdbLoanRequestsDaoWithInstrumentation, crdbLoanOffersDaoWithInstrumentation, providerFactory, crdbLoanStepExecutionsDaoWithInstrumentation, rpcHelper, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, s3Client, txnExecutorProvider, userClient, stock_guardianProvider, txnExecutor, calculatorFactoryImpl, orchestratorClient, loandataproviderFactory, creditReportConfig, creditReportManagerV2, loecWrapper, breClient, workerConf, dataDevS3Client, sgApplicantClient, sgApplicationclient, sgCustomerClient, sgEsignApiGateway, sgMatrixClient, crdbLoanApplicantDaoMultiDB, recurringPaymentClient, sgKycClient, riskClient, omegleClient, employmentClient, sgLmsClient, palClient, commonUserDataProvider, onbClient, savingsClient, creditReportHelper)
	return stock_guardianProcessor
}

func InitialiseSyncActivityProcessor(sgProc *stock_guardian5.Processor, lendenProc *lenden9.Processor, abflProc *abfl4.Processor, commonProc *common2.Processor, federalProc *federal4.Processor, activityProc *activity.Processor) *syncproxy.Processor {
	syncproxyProcessor := syncproxy.NewProcessor(sgProc, lendenProc, abflProc, commonProc, federalProc, activityProc)
	return syncproxyProcessor
}

func InitialiseSecuredLoansService(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], celestialClient celestial.CelestialClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], cardProvisioningClient provisioning.CardProvisioningClient, conf *config.Config, dynConf *genconf.Config, caClient connected_account.ConnectedAccountClient, client catalog.CatalogManagerClient, redisStore types2.FireflyRedisStore, palVgClient preapprovedloan.PreApprovedLoanClient, actorClient actor.ActorClient, usersClient user.UsersClient, savingsClient savings.SavingsClient, lvClient liveness.LivenessClient, kycClient kyc.KycClient, vgCustomerClient customer.CustomerClient, authClient auth.AuthClient, eSignClient esign.ESignClient, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, llPalVgClient liquiloans.LiquiloansClient, profileClient profile.ProfileClient, bcClient bankcust.BankCustomerServiceClient, salaryClient salaryprogram.SalaryProgramClient, idfcVgClient idfc.IdfcClient, accountVgClient accounts.AccountsClient, creditReportClient credit_report.CreditReportManagerClient, creditReportV2Client creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationClient segment.SegmentationServiceClient, s3Client types2.PreApprovedLoanS3Client, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, mfExternalOrdersClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, eventBroker events.Broker, sgEsignApiGateway esign2.EsignClient, locationClient location2.LocationClient) *secured_loans2.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoMultiDB := impl.NewCrdbLoanRequestsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	deeplinkConfig := getDeeplinkConf(dynConf)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanStepExecutionsDaoMultiDB := impl.NewCrdbLoanStepExecutionsDaoMultiDB(dbConnProvider, domainIdGenerator)
	notification := notificationConfigProvider(conf)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfig(dynConf)
	rpcHelper := helper.NewRpcHelper(palVgClient, actorClient, usersClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoMultiDB, notification, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryClient, idfcVgClient, accountVgClient, creditReportClient, creditReportV2Client, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationClient, s3Client, limitEstimatorClient, mfExternalOrdersClient, userLocationClient, eventBroker, crdbLoanRequestsDaoMultiDB, sgEsignApiGateway, locationClient)
	baseproviderProvider := baseprovider.NewProvider(cardProvisioningClient, deeplinkConfig, caClient, onbClient, savingsClient, rpcHelper)
	plProvider := liquiloans2.NewProvider(baseproviderProvider)
	esProvider := liquiloans2.NewLiquiloansEarlySalaryProvider(baseproviderProvider)
	factoryImpl := failure_handlers.NewFactoryImpl()
	idfcProvider := idfc2.NewProvider(baseproviderProvider, factoryImpl)
	fldgProvider := liquiloans2.NewFldgProvider(baseproviderProvider)
	fiLiteProvider := liquiloans2.NewLiquiloansFiLiteProvider(plProvider)
	acqToLendProvider := liquiloans2.NewAcqToLendProvider(fiLiteProvider)
	uuidGenerator := idgen.NewUuidGenerator()
	fiftyfinProvider := fiftyfin2.NewProvider(baseproviderProvider, client, uuidGenerator)
	abflProvider := abfl2.NewAbflProvider(baseproviderProvider)
	realtimeetbProvider := realtimeetb.NewProvider(baseproviderProvider)
	realtimeDistProvider := liquiloans2.NewRealtimeDistProvider(plProvider)
	mvProvider := moneyview2.NewMvProvider(baseproviderProvider)
	stplProvider := liquiloans2.NewStplProvider(fldgProvider)
	realtimeSubventionProvider := liquiloans2.NewRealtimeSubventionProvider(plProvider)
	etbFedProvider := federal.NewEtbFedProvider(baseproviderProvider, deeplinkConfig)
	realTimeFedProvider := federal.NewRealTimeFedProvider(etbFedProvider)
	realTimeStplProvider := liquiloans2.NewRealTimeStplProvider(realtimeSubventionProvider)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseproviderProvider)
	nonFiCoreStplProvider := liquiloans2.NewNonFiCoreStplProvider(fiLiteProvider)
	nonFiCoreSubventionProvider := liquiloans2.NewNonFiCoreSubventionProvider(fiLiteProvider)
	stock_guardianProvider := stock_guardian.NewStockGuardianProvider(baseproviderProvider)
	lendenEligibilityProvider := lenden2.NewEligibilityProvider(baseproviderProvider)
	lendenProvider := lenden2.NewLendenProvider(baseproviderProvider, lendenEligibilityProvider)
	abflPwaProvider := abfl2.NewAbflPwaProvider(baseproviderProvider)
	federalEligibilityProvider := federal.NewEligibilityProvider(etbFedProvider)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(realTimeFedProvider)
	nonFiCoreMvProvider := moneyview2.NewNonFiCoreMvProvider(mvProvider)
	stockGuardianProviderEarlySalary := stock_guardian.NewStockGuardianProviderEarlySalary(stock_guardianProvider)
	providerFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, plProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, fiftyfinProvider, abflProvider, realtimeetbProvider, realtimeDistProvider, mvProvider, stplProvider, realtimeSubventionProvider, realTimeFedProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stock_guardianProvider, lendenProvider, lendenEligibilityProvider, abflPwaProvider, federalEligibilityProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary, etbFedProvider)
	iDbResourceProvider := IDbResourceProviderProvider(txnExecutorProvider)
	service := secured_loans2.NewService(crdbLoanRequestsDaoMultiDB, celestialClient, providerFactory, crdbLoanStepExecutionsDaoMultiDB, iDbResourceProvider)
	return service
}

func InitiateLendabilityService(conf *genconf.Config, crV2Client creditreportv2.CreditReportManagerClient, userClient user.UsersClient, empClient employment.EmploymentClient, locationClient location.LocationClient, locVgClient location3.LocationClient, broker events.Broker, vendorApiGenConf *genconf3.Config) *lendability.Service {
	string2 := envProvider(conf)
	client := vendorapi.SecureHttpClientProvider(vendorApiGenConf, string2)
	signingContext := vendorapi.NilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorApiGenConf, string2)
	dataCollector := datacollector.NewDataCollector(crV2Client, userClient, empClient, locationClient, locVgClient)
	service := lendability.NewService(conf, broker, httpRequestHandler, dataCollector)
	return service
}

func InitiatePreEligibilityService(dynConf *genconf.Config, conf *config.Config, preApprovedLoanVgClient preapprovedloan.PreApprovedLoanClient, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], redisStore types2.FireflyRedisStore, actorClient actor.ActorClient, usersClient user.UsersClient, savingsClient savings.SavingsClient, celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, kycClient kyc.KycClient, vgCustomerClient customer.CustomerClient, authClient auth.AuthClient, eSignClient esign.ESignClient, commsClient comms.CommsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, llPalVgClient liquiloans.LiquiloansClient, profileClient profile.ProfileClient, accountVgClient accounts.AccountsClient, creditReportClient credit_report.CreditReportManagerClient, creditReportV2Client creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, s3Client types2.PreApprovedLoanS3Client, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, mfExternalOrdersClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, eventBroker events.Broker, sgEsignApiGateway esign2.EsignClient, bcClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, idfcVgClient idfc.IdfcClient, segmentationServiceClient segment.SegmentationServiceClient, temporalClient types2.PreApprovedLoanClient, consentClient consent.ConsentClient, locationClient location2.LocationClient, breClient bre.BreClient, operationalStatusClient operstatus.OperationalStatusServiceClient, palClient preapprovedloan2.PreApprovedLoanClient, mvVgClient moneyview.MoneyviewClient, leadsClient leads.UserLeadSvcClient) *preeligibility.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types2.CacheStorageProvider(redisStore)
	crdbLoanRequestsDaoMultiDB := impl.NewCrdbLoanRequestsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	pgdbPreEligibilityOfferDao := impl.NewPgdbPreEligibilityOfferDao(dbConnProvider, domainIdGenerator)
	crdbLoanStepExecutionsDaoMultiDB := impl.NewCrdbLoanStepExecutionsDaoMultiDB(dbConnProvider, domainIdGenerator)
	defaultTime := datetime.NewDefaultTime()
	notification := notificationConfigProvider(conf)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	creditReportConfig := getCreditReportConfig(dynConf)
	rpcHelper := helper.NewRpcHelper(preApprovedLoanVgClient, actorClient, usersClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoMultiDB, notification, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bcClient, salaryProgramClient, idfcVgClient, accountVgClient, creditReportClient, creditReportV2Client, creditReportConfig, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationServiceClient, s3Client, limitEstimatorClient, mfExternalOrdersClient, userLocationClient, eventBroker, crdbLoanRequestsDaoMultiDB, sgEsignApiGateway, locationClient)
	commonUserDataProvider := userdata.NewCommonUserDataProvider(rpcHelper)
	addUserDetailsStep := steps.NewAddUserDetailsStep(usersClient, rpcHelper, commonUserDataProvider, leadsClient)
	offerGenerationStep := steps.NewOfferGenerationStep(consentClient, pgdbPreEligibilityOfferDao, creditReportV2Client, breClient, commonUserDataProvider, creditReportConfig, eventBroker, crdbLoanStepExecutionsDaoMultiDB, mvVgClient)
	preeligibilityOrchestrator := preeligibility.NewOrchestrator(crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, addUserDetailsStep, offerGenerationStep)
	service := preeligibility.NewService(crdbLoanRequestsDaoMultiDB, pgdbPreEligibilityOfferDao, usersClient, preeligibilityOrchestrator, operationalStatusClient, savingsClient, palClient)
	return service
}

// wire.go:

func newGormTxnExecutorProvider(dbConn *gorm.DB) storagev2.TxnExecutor {
	return storagev2.NewGormTxnExecutor(dbConn)
}

func newMultiDbDoOnce(dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB]) once.MultiDbDoOnce {
	return once.NewMultiDbDoOnce(dbResourceProvider)
}

func notificationConfigProvider(conf *config.Config) *common.Notification {
	return conf.Notification
}

func prePayConfigProvider(conf *config.Config) *common.Prepay {
	return conf.Prepay
}

func getDeeplinkConf(conf *genconf.Config) *genconf4.DeeplinkConfig {
	return conf.DeeplinkConfig()
}

func getFlagsConf(conf *genconf.Config) *genconf4.Flags {
	return conf.Flags()
}

func getFeaturesConf(conf *genconf.Config) *genconf4.VendorProgramLevelFeature {
	return conf.VendorProgramLevelFeature()
}

func getLoanCalculatorConstraintConf(conf *config.Config) *common.LoanCalculator {
	return conf.LoanCalculator
}

func getCreditReportConfig(conf *genconf.Config) *genconf4.CreditReportConfig {
	return conf.CreditReportConfig()
}

func getPlClientProvider(cl types2.PreApprovedLoanClient) client.Client {
	return cl
}

func lmsConfigProvider(conf *config.Config) *common.Lms {
	return conf.Lms
}

func lmsWorkerConfigProvider(conf *worker.Config) *common.Lms {
	return conf.Lms
}

func featureReleaseConfigProvider(conf *genconf.Config) *genconf5.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func featureReleaseWorkerConfigProvider(conf *genconf2.Config) *genconf5.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func InitCryptors(conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {

	pgpCryptor := pgp.New(conf.Secrets.Ids[config.FederalPgpPublicKey],
		conf.Secrets.Ids[config.EpifiFederalPgpPrivateKey], conf.Secrets.Ids[config.EpifiFederalPgpPassphrase])
	if pgpCryptor == nil {
		return nil, errors.New("failed to create PGP cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_PGP, pgpCryptor)

	return cryptorStore, nil
}

func getDeeplinkConfFromWorkerGenConf(conf *genconf2.Config) *genconf4.DeeplinkConfig {
	return conf.DeeplinkConfig()
}

func getMandateConfFromWorkerGenConf(conf *genconf2.Config) *genconf4.MandateConfig {
	return conf.MandateConfig()
}

func getFeaturesFromWorkerGenConf(conf *genconf2.Config) *genconf4.VendorProgramLevelFeature {
	return conf.VendorProgramLevelFeature()
}

func lopeOverrideWorkerConfigProvider(conf *genconf2.Config) *genconf4.LopeOverrideConfig {
	return conf.LopeOverrideConfig()
}

func lopeOverrideServiceConfigProvider(conf *genconf.Config) *genconf4.LopeOverrideConfig {
	return conf.LopeOverrideConfig()
}

func getMandateConf(conf *genconf.Config) *genconf4.MandateConfig {
	return conf.MandateConfig()
}

func getFlagsConfFromWorkerGenConf(conf *genconf2.Config) *genconf4.Flags {
	return conf.Flags()
}

func getLoanCalculatorConfFromWorker(conf *worker.Config) *common.LoanCalculator {
	return conf.LoanCalculator
}

func getCreditReportConfFromWorkerGenConf(conf *genconf2.Config) *genconf4.CreditReportConfig {
	return conf.CreditReportConfig()
}

func prePayConfigProviderWorker(conf *worker.Config) *common.Prepay {
	return conf.Prepay
}

func IDbResourceProviderProvider(dbConnProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) storagev2.IDbResourceProvider[storagev2.IdempotentTxnExecutor] {
	return dbConnProvider
}

func getFeatureReleaseConfig(conf *genconf2.Config) *genconf5.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func envProvider(conf *genconf.Config) string {
	return conf.Application().Environment
}

func getSgNewEligibilityFlowConf(conf *genconf.Config) *genconf4.FeatureConstraint {
	return conf.SgEtbNewEligibilityFlow()
}

func getSgNewEligibilityFlowActivityConf(conf *genconf2.Config) *genconf4.FeatureConstraint {
	return conf.SgEtbNewEligibilityFlow()
}

func getPreferUPIMandateTypeFlagFromServerConf(conf *genconf.Config) bool {
	return conf.Flags().PreferUPIMandateTypeForLDC()
}

func getPreferUPIMandateTypeFlagFromWorkerConf(conf *genconf2.Config) bool {
	return conf.Flags().PreferUPIMandateTypeForLDC()
}
