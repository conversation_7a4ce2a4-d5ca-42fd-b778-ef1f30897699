package preeligibility

import (
	"context"
	"time"

	"github.com/pkg/errors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"go.uber.org/zap"

	enums2 "github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	preEPb "github.com/epifi/gamma/api/preapprovedloan/pre_eligibility"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/preapprovedloan/dao"
)

type Service struct {
	loanRequestsDao       dao.LoanRequestsDao
	preEligibilityDao     dao.PreEligibilityOfferDao
	userClient            userPb.UsersClient
	orchestrator          IOrchestrator
	opStatusClient        operationalStatusPb.OperationalStatusServiceClient
	savingsClient         savingsPb.SavingsClient
	preApprovedLoanClient palPb.PreApprovedLoanClient
}

func NewService(
	loanRequestsDao dao.LoanRequestsDao,
	preEligibilityDao dao.PreEligibilityOfferDao,
	userClient userPb.UsersClient,
	orchestrator IOrchestrator,
	opStatusClient operationalStatusPb.OperationalStatusServiceClient,
	savingsClient savingsPb.SavingsClient,
	preApprovedLoanClient palPb.PreApprovedLoanClient,
) *Service {

	return &Service{
		loanRequestsDao:       loanRequestsDao,
		preEligibilityDao:     preEligibilityDao,
		userClient:            userClient,
		orchestrator:          orchestrator,
		opStatusClient:        opStatusClient,
		savingsClient:         savingsClient,
		preApprovedLoanClient: preApprovedLoanClient,
	}
}

// GetLandingInfo retrieves loan-related details for the landing page, considering the pre-eligibility status.
// It determines what to display on the initial loan page (offer, ongoing check, or existing loan).
func (s *Service) GetLandingInfo(ctx context.Context, req *preEPb.GetLandingInfoRequest) (*preEPb.GetLandingInfoResponse, error) {
	var (
		actorId = req.GetActorId()
	)
	ctx = epificontext.WithOwnership(ctx, common.Ownership_EPIFI_TECH_V2)

	// Step 1: Check if user already has an active pre-eligibility offer
	if response := s.checkExistingPreEligibilityOffer(ctx, actorId); response != nil {
		return response, nil
	}

	// Step 2: Get valid loan request for the user
	loanRequest, err := s.getValidLoanRequest(ctx, actorId)
	if err != nil {
		return s.createErrorResponse(rpcPb.StatusInternal()), nil
	}

	// Step 3: Handle based on loan requests
	if loanRequest == nil {
		// No loan requests - handle new user flow
		return s.handleNoLoanRequests(ctx, actorId)
	} else {
		// Has loan requests - handle existing user flow
		return s.handleExistingLoanRequest(ctx, actorId, loanRequest)
	}
}

// checkExistingPreEligibilityOffer checks if the user already has an active pre-eligibility offer
// Returns a response if an offer exists or if there's an error, otherwise returns nil
func (s *Service) checkExistingPreEligibilityOffer(ctx context.Context, actorId string) *preEPb.GetLandingInfoResponse {
	preEligibilityOffer, err := s.preEligibilityDao.GetActiveOfferByActorId(ctx, actorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch from preEligibility dao by actor id", zap.Error(err))
		return s.createErrorResponse(rpcPb.StatusInternal())
	}

	if preEligibilityOffer != nil {
		return s.createPreEligibilityOfferResponse(preEligibilityOffer)
	}

	return nil
}

// getValidLoanRequest retrieves a single valid loan request for the user if one exists within the cool-off period
func (s *Service) getValidLoanRequest(ctx context.Context, actorId string) (*palPb.LoanRequest, error) {
	loanRequests, err := s.loanRequestsDao.GetByActorIdTypesStatusAndLoanProgram(
		ctx,
		actorId,
		[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
		[]palPb.LoanRequestStatus{},
		[]palPb.LoanProgram{},
		nil, nil,
	)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch loan request from db", zap.Error(err))
		return nil, err
	}

	// If no loan requests or empty, just return the empty slice
	if len(loanRequests) == 0 {
		return nil, nil
	}

	// Since the DAO returns sorted by created_at DESC,
	// just check the first item (most recent loan request)
	latestLoanRequest := loanRequests[0]

	var referenceTime time.Time
	var isWithinCoolOffPeriod bool

	// If there's no completed_at (in progress), use created_at for cool-off calculation
	if latestLoanRequest.GetCompletedAt() == nil {
		referenceTime = latestLoanRequest.GetCreatedAt().AsTime()
	} else {
		referenceTime = latestLoanRequest.GetCompletedAt().AsTime()
	}

	// Check if still within cool-off period
	coolOffEndTime := referenceTime.Add(time.Duration(CoolOffDays) * 24 * time.Hour)
	isWithinCoolOffPeriod = time.Now().Before(coolOffEndTime)

	if isWithinCoolOffPeriod {
		// If within cool-off period, return only this request
		return latestLoanRequest, nil
	}

	// If not within cool-off period, return empty slice
	// which indicates no active loan requests
	return nil, nil
}

// handleNoLoanRequests handles the flow when the user has no loan requests
// This is typically for new users or users who haven't started the eligibility process
func (s *Service) handleNoLoanRequests(ctx context.Context, actorId string) (*preEPb.GetLandingInfoResponse, error) {
	// Step 1: Check if user has a savings account
	savingsAccount, err := s.getSavingsAccount(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "failed to get savings account", zap.Error(err))
		return s.createErrorResponse(rpcPb.StatusInternal()), nil
	}

	// If no savings account, user is new and can start eligibility
	if savingsAccount == nil {
		return s.createEvaluationDetailsResponse(
			preEPb.EvaluationDetails_EVALUATION_STATUS_NEW_USER,
		), nil
	}

	// Step 2: Check operational status
	return s.checkOperationalStatus(ctx, actorId, savingsAccount)
}

// checkOperationalStatus checks the operational status of the user's account
// and returns the appropriate response based on the status
func (s *Service) checkOperationalStatus(ctx context.Context, actorId string, savingsAccount *savingsPb.SavingsAccountEssentials) (*preEPb.GetLandingInfoResponse, error) {
	opStatusRes, err := s.opStatusClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
		DataFreshness: operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN,
		AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: savingsAccount.GetId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(opStatusRes, err); rpcErr != nil {
		// Fallback: If operational status is not found, check for loan offers.
		// Reason: Last known operational status is only fetched from DB, not from vendor.
		// If not found, we assume the account is active and proceed to check offers.
		if opStatusRes.GetStatus().IsRecordNotFound() {
			return s.checkLoanOffers(ctx, actorId)
		}
		logger.Error(ctx, "error in getting operational status", zap.Error(rpcErr))
		return s.createErrorResponse(rpcPb.StatusInternal()), nil
	}

	// Check if user is risky (has freeze status)
	if opStatusRes.GetOperationalStatusInfo().GetFreezeStatus() != enums2.FreezeStatus_FREEZE_STATUS_UNSPECIFIED {
		return s.createEvaluationDetailsResponse(
			preEPb.EvaluationDetails_EVALUATION_STATUS_NOT_ELIGIBLE,
		), nil
	}

	// Check if account is active
	if opStatusRes.GetOperationalStatusInfo().GetOperationalStatus() != enums2.OperationalStatus_OPERATIONAL_STATUS_ACTIVE {
		return s.createEvaluationDetailsResponse(
			preEPb.EvaluationDetails_EVALUATION_STATUS_NEW_USER,
		), nil
	}

	// Account is active and not risky, check for loan offers
	return s.checkLoanOffers(ctx, actorId)
}

// checkLoanOffers checks if the user has any loan offers
// and returns the appropriate response based on the offers
func (s *Service) checkLoanOffers(ctx context.Context, actorId string) (*preEPb.GetLandingInfoResponse, error) {
	homeRes, err := s.preApprovedLoanClient.GetLoanSummaryForHome(ctx, &palPb.GetLoanSummaryForHomeRequest{
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "failed to get loan summary for home", zap.Error(err))
		return s.createErrorResponse(rpcPb.StatusInternal()), nil
	}

	switch homeRes.GetHomeCardType() {
	case palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_LOAN_OFFER:
		return s.createLoanOfferResponse(homeRes.GetCardLoanOffer().GetLoanOffer()), nil

	case palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_CHECK_ELIGIBILITY:
		return s.createDefaultOfferResponse(actorId), nil

	default:
		return s.createEvaluationDetailsResponse(
			preEPb.EvaluationDetails_EVALUATION_STATUS_GO_TO_APP_TO_CHECK_LATEST_STATUS,
		), nil
	}
}

// handleExistingLoanRequest handles the flow when the user has existing loan requests
func (s *Service) handleExistingLoanRequest(ctx context.Context, actorId string, loanRequest *palPb.LoanRequest) (*preEPb.GetLandingInfoResponse, error) {
	// Check if evaluation is in progress (no completion timestamp)
	isEvaluationInProgress := loanRequest.GetCompletedAt() == nil

	if isEvaluationInProgress {
		// Evaluation is in progress, get current status
		return s.getInProgressEvaluationStatus(ctx, actorId, loanRequest.GetId())
	}

	return s.createEvaluationDetailsResponse(
		preEPb.EvaluationDetails_EVALUATION_STATUS_NOT_ELIGIBLE,
	), nil

}

// getInProgressEvaluationStatus gets the current status of an in-progress evaluation
func (s *Service) getInProgressEvaluationStatus(ctx context.Context, actorId string, requestId string) (*preEPb.GetLandingInfoResponse, error) {
	getEvaluationStatusResponse, getEvaluationStatusErr := s.GetEvaluationStatus(ctx, &preEPb.GetEvaluationStatusRequest{
		ActorId:   actorId,
		RequestId: requestId,
	})
	if getEvaluationStatusErr = epifigrpc.RPCError(getEvaluationStatusResponse, getEvaluationStatusErr); getEvaluationStatusErr != nil {
		logger.Error(ctx, "failed to get the new evaluation status", zap.Error(getEvaluationStatusErr))
		return s.createErrorResponse(getEvaluationStatusResponse.GetStatus()), nil
	}

	return s.createEvaluationDetailsResponseWithDetails(getEvaluationStatusResponse.GetEvaluationDetails()), nil
}

// Helper methods to create standardized responses

// createErrorResponse creates a response with an error status
func (s *Service) createErrorResponse(status *rpcPb.Status) *preEPb.GetLandingInfoResponse {
	return &preEPb.GetLandingInfoResponse{
		Status: status,
	}
}

// createPreEligibilityOfferResponse creates a response with a pre-eligibility offer
func (s *Service) createPreEligibilityOfferResponse(offer *palPb.PreEligibilityOffer) *preEPb.GetLandingInfoResponse {
	return &preEPb.GetLandingInfoResponse{
		Status: rpcPb.StatusOk(),
		Data: &preEPb.GetLandingInfoResponse_PreEligibilityOffer{
			PreEligibilityOffer: offer,
		},
	}
}

// createDefaultOfferResponse creates a response with a default offer
func (s *Service) createDefaultOfferResponse(actorId string) *preEPb.GetLandingInfoResponse {
	return &preEPb.GetLandingInfoResponse{
		Status: rpcPb.StatusOk(),
		Data: &preEPb.GetLandingInfoResponse_LoanOffer{
			LoanOffer: &palPb.LoanOffer{
				ActorId: actorId,
				OfferConstraints: &palPb.OfferConstraints{
					MaxLoanAmount: money.AmountINR(5_00_000).GetPb(),
				},
			},
		},
	}
}

// createLoanOfferResponse creates a response with a loan offer
func (s *Service) createLoanOfferResponse(offer *palPb.LoanOffer) *preEPb.GetLandingInfoResponse {
	return &preEPb.GetLandingInfoResponse{
		Status: rpcPb.StatusOk(),
		Data: &preEPb.GetLandingInfoResponse_LoanOffer{
			LoanOffer: offer,
		},
	}
}

// createEvaluationDetailsResponse creates a response with evaluation details
func (s *Service) createEvaluationDetailsResponse(status preEPb.EvaluationDetails_EvaluationStatus) *preEPb.GetLandingInfoResponse {
	return &preEPb.GetLandingInfoResponse{
		Status: rpcPb.StatusOk(),
		Data: &preEPb.GetLandingInfoResponse_EvaluationDetails{
			EvaluationDetails: &preEPb.EvaluationDetails{
				EvaluationStatus: status,
			},
		},
	}
}

// createEvaluationDetailsResponseWithDetails creates a response with detailed evaluation details
func (s *Service) createEvaluationDetailsResponseWithDetails(details *preEPb.EvaluationDetails) *preEPb.GetLandingInfoResponse {
	return &preEPb.GetLandingInfoResponse{
		Status: rpcPb.StatusOk(),
		Data: &preEPb.GetLandingInfoResponse_EvaluationDetails{
			EvaluationDetails: details,
		},
	}
}

func (s *Service) GetEvaluationStatus(ctx context.Context, req *preEPb.GetEvaluationStatusRequest) (*preEPb.GetEvaluationStatusResponse, error) {

	var (
		res              = &preEPb.GetEvaluationStatusResponse{}
		evaluationStatus = preEPb.EvaluationDetails_EVALUATION_STATUS_IN_PROGRESS
	)

	ctx = epificontext.WithOwnership(ctx, common.Ownership_EPIFI_TECH_V2)

	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetRequestId())
	if err != nil {
		logger.Error(ctx, "failed to create loan request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	switch loanRequest.GetStatus() {
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS:
		preEligibilityOffer, daoErr := s.preEligibilityDao.GetByLoanRequestId(ctx, req.GetRequestId())
		if daoErr != nil && !errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "Unable to check offer", zap.Error(daoErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		res.Status = rpcPb.StatusOk()
		res.EvaluationDetails = &preEPb.EvaluationDetails{
			EvaluationStatus:    preEPb.EvaluationDetails_EVALUATION_STATUS_ELIGIBLE,
			PreEligibilityOffer: preEligibilityOffer,
		}
		return res, nil
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION:
		res.Status = rpcPb.StatusOk()
		res.EvaluationDetails = &preEPb.EvaluationDetails{
			EvaluationStatus: preEPb.EvaluationDetails_EVALUATION_STATUS_NOT_ELIGIBLE,
		}
		return res, nil
	default:
		break // continue with evaluation
	}

	execDetails, err := s.orchestrator.Perform(ctx, &OrchestratorReq{
		ReqId:   req.GetRequestId(),
		ActorId: req.GetActorId(),
	})
	if err != nil {
		if errors.Is(err, epifierrors.ErrAlreadyExists) {
			res.Status = rpcPb.StatusAlreadyExists()
		} else {
			res.Status = rpcPb.StatusInternal()
		}
		logger.Error(ctx, "failed to execute step", zap.Error(err))
		return res, nil
	}

	// Evaluate the current status after orchestration perform
	switch execDetails.GetLoanRequest().GetStatus() {
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS:
		preEligibilityOffer, daoErr := s.preEligibilityDao.GetByLoanRequestId(ctx, req.GetRequestId())
		if daoErr != nil && !errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "Unable to check offer", zap.Error(daoErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		res.Status = rpcPb.StatusOk()
		res.EvaluationDetails = &preEPb.EvaluationDetails{
			EvaluationStatus:    preEPb.EvaluationDetails_EVALUATION_STATUS_ELIGIBLE,
			PreEligibilityOffer: preEligibilityOffer,
		}
		return res, nil
	case palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED, palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION:
		res.Status = rpcPb.StatusOk()
		res.EvaluationDetails = &preEPb.EvaluationDetails{
			EvaluationStatus: preEPb.EvaluationDetails_EVALUATION_STATUS_NOT_ELIGIBLE,
		}
		return res, nil
	default:
		// continue with evaluation
		res.Status = rpcPb.StatusOk()
		res.EvaluationDetails = &preEPb.EvaluationDetails{
			RequestId:        execDetails.GetLoanRequest().GetId(),
			EvaluationStatus: evaluationStatus,
			NextAction:       execDetails.GetLoanRequest().GetNextAction(),
		}
		return res, nil
	}
}

func (s *Service) InitiateEvaluation(ctx context.Context, req *preEPb.InitiateEvaluationRequest) (*preEPb.InitiateEvaluationResponse, error) {

	var (
		res = &preEPb.InitiateEvaluationResponse{}
	)

	ctx = epificontext.WithOwnership(ctx, common.Ownership_EPIFI_TECH_V2)

	executionDetails, err := s.orchestrator.Start(ctx, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrAlreadyExists) {
			logger.Error(ctx, "failed to start Evaluation, already one exists", zap.Error(err))
			res.Status = rpcPb.StatusAlreadyExists() // TODO(M240: 95077) Handle StatusAlreadyExists in a better way, see if instead of error, continue the evaluation with the existing request
			return res, nil
		}
		logger.Error(ctx, "failed to to start Evaluation", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	res.EvaluationDetails = &preEPb.EvaluationDetails{
		RequestId:        executionDetails.GetLoanRequest().GetId(),
		EvaluationStatus: preEPb.EvaluationDetails_EVALUATION_STATUS_IN_PROGRESS,
		NextAction:       executionDetails.GetLoanRequest().GetNextAction(),
	}
	return res, nil
}

func (s *Service) CollectUserData(ctx context.Context, req *preEPb.CollectUserDataRequest) (*preEPb.CollectUserDataResponse, error) {
	res := &preEPb.CollectUserDataResponse{}

	ctx = epificontext.WithOwnership(ctx, common.Ownership_EPIFI_TECH_V2)
	// Execute current step using orchestrator with the details
	// BasicUserDetails & EmploymentDetails are stored in the user service
	// ConsentDetails are stored in the consent service
	execDetails, err := s.orchestrator.Perform(ctx, &OrchestratorReq{
		ReqId:             req.GetRequestId(),
		BasicUserDetails:  req.GetBasicUserDetails(),
		EmploymentDetails: req.GetEmploymentDetails(),
		ConsentDetails:    req.GetConsentDetails(),
		ActorId:           req.GetActorId(),
	})
	if err != nil {
		if errors.Is(err, epifierrors.ErrAlreadyExists) {
			logger.Error(ctx, "failed to execute step, err already exists", zap.Error(err))
			res.Status = rpcPb.StatusAlreadyExists() // TODO(M240: 95077) Handle StatusAlreadyExists in a better way, see if instead of error, continue the evaluation with the existing request
			return res, nil
		}
		logger.Error(ctx, "failed to execute step,", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	res.NextAction = execDetails.GetLoanRequest().GetNextAction()
	return res, nil
}

func (s *Service) getSavingsAccount(ctx context.Context, actorId string) (*savingsPb.SavingsAccountEssentials, error) {
	savResp, err := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})

	if savResp != nil && savResp.GetStatus().IsRecordNotFound() {
		return nil, nil
	}

	if grpcErr := epifigrpc.RPCError(savResp, err); grpcErr != nil {
		logger.Error(ctx, "error fetching savings account for actor", zap.Error(grpcErr))
		return nil, grpcErr
	}
	return savResp.GetAccount(), nil
}
