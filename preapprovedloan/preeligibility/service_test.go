package preeligibility

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"

	enums2 "github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	operationalStatusMocks "github.com/epifi/gamma/api/accounts/operstatus/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palMocks "github.com/epifi/gamma/api/preapprovedloan/mocks"
	preEPb "github.com/epifi/gamma/api/preapprovedloan/pre_eligibility"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	dao_mocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
)

// MockOrchestrator is a mock implementation of the IOrchestrator interface
type MockOrchestrator struct {
	StartFunc   func(ctx context.Context, actorId string) (*OrchestratorResp, error)
	PerformFunc func(ctx context.Context, req *OrchestratorReq) (*OrchestratorResp, error)
}

func (m *MockOrchestrator) Start(ctx context.Context, actorId string) (*OrchestratorResp, error) {
	if m.StartFunc != nil {
		return m.StartFunc(ctx, actorId)
	}
	return nil, errors.New("StartFunc not implemented")
}

func (m *MockOrchestrator) Perform(ctx context.Context, req *OrchestratorReq) (*OrchestratorResp, error) {
	if m.PerformFunc != nil {
		return m.PerformFunc(ctx, req)
	}
	return nil, errors.New("PerformFunc not implemented")
}

func TestService_GetLandingInfo(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name         string
		args         *preEPb.GetLandingInfoRequest
		setupMocks   func(ctrl *gomock.Controller) *Service
		wantStatus   uint32
		wantErr      bool
		validateResp func(t *testing.T, resp *preEPb.GetLandingInfoResponse)
	}{
		{
			name: "user with existing active offer",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)

				// Mock existing pre-eligibility offer
				offer := &palPb.PreEligibilityOffer{
					Id:      "offer-123",
					ActorId: "actor-123",
				}
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(offer, nil)

				service := &Service{
					preEligibilityDao: mockPreEligibilityDao,
				}
				return service
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				// Should return pre-eligibility offer
				preEligibilityOffer := resp.GetPreEligibilityOffer()
				assert.NotNil(t, preEligibilityOffer)
				assert.Equal(t, "offer-123", preEligibilityOffer.GetId())
				assert.Equal(t, "actor-123", preEligibilityOffer.GetActorId())

				// Should not have other response types
				assert.Nil(t, resp.GetLoanOffer())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "new user with no savings account",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock no loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{}, nil)

				// Mock no savings account (new user)
				mockSavingsClient.EXPECT().
					GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
						Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
							ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
								ActorId:     "actor-123",
								PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							},
						},
					}).
					Return(&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					savingsClient:     mockSavingsClient,
				}
				return service
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				// Should return evaluation details for new user
				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_NEW_USER, evaluationDetails.GetEvaluationStatus())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetLoanOffer())
			},
		},
		{
			name: "user with savings account but frozen status",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
				mockOpStatusClient := operationalStatusMocks.NewMockOperationalStatusServiceClient(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock no loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{}, nil)

				// Mock savings account exists
				mockSavingsClient.EXPECT().
					GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).
					Return(&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpcPb.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							Id: "savings-123",
						},
					}, nil)

				// Mock operational status with freeze
				mockOpStatusClient.EXPECT().
					GetOperationalStatus(gomock.Any(), gomock.Any()).
					Return(&operationalStatusPb.GetOperationalStatusResponse{
						Status: rpcPb.StatusOk(),
						OperationalStatusInfo: &operationalStatusPb.OperationalStatusInfo{
							FreezeStatus: enums2.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE,
						},
					}, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					savingsClient:     mockSavingsClient,
					opStatusClient:    mockOpStatusClient,
				}
				return service
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				// Should return evaluation details for not eligible user
				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_NOT_ELIGIBLE, evaluationDetails.GetEvaluationStatus())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetLoanOffer())
			},
		},
		{
			name: "user with active account and loan offer",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
				mockOpStatusClient := operationalStatusMocks.NewMockOperationalStatusServiceClient(ctrl)
				mockPreApprovedLoanClient := palMocks.NewMockPreApprovedLoanClient(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock no loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{}, nil)

				// Mock savings account exists
				mockSavingsClient.EXPECT().
					GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).
					Return(&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpcPb.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							Id: "savings-123",
						},
					}, nil)

				// Mock operational status active
				mockOpStatusClient.EXPECT().
					GetOperationalStatus(gomock.Any(), gomock.Any()).
					Return(&operationalStatusPb.GetOperationalStatusResponse{
						Status: rpcPb.StatusOk(),
						OperationalStatusInfo: &operationalStatusPb.OperationalStatusInfo{
							FreezeStatus:      enums2.FreezeStatus_FREEZE_STATUS_UNSPECIFIED,
							OperationalStatus: enums2.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
						},
					}, nil)

				// Mock loan summary with offer
				loanOffer := &palPb.LoanOffer{
					ActorId: "actor-123",
					OfferConstraints: &palPb.OfferConstraints{
						MaxLoanAmount: money.AmountINR(100000).GetPb(),
					},
				}
				mockPreApprovedLoanClient.EXPECT().
					GetLoanSummaryForHome(gomock.Any(), &palPb.GetLoanSummaryForHomeRequest{
						ActorId: "actor-123",
					}).
					Return(&palPb.GetLoanSummaryForHomeResponse{
						HomeCardType: palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_LOAN_OFFER,
						HomeCardData: &palPb.GetLoanSummaryForHomeResponse_CardLoanOffer_{
							CardLoanOffer: &palPb.GetLoanSummaryForHomeResponse_CardLoanOffer{
								LoanOffer: loanOffer,
							},
						},
					}, nil)

				service := &Service{
					loanRequestsDao:       mockLoanRequestsDao,
					preEligibilityDao:     mockPreEligibilityDao,
					savingsClient:         mockSavingsClient,
					opStatusClient:        mockOpStatusClient,
					preApprovedLoanClient: mockPreApprovedLoanClient,
				}
				return service
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				// Should return loan offer
				loanOffer := resp.GetLoanOffer()
				assert.NotNil(t, loanOffer)
				assert.Equal(t, "actor-123", loanOffer.GetActorId())
				assert.NotNil(t, loanOffer.GetOfferConstraints())
				assert.Equal(t, int64(100000), loanOffer.GetOfferConstraints().GetMaxLoanAmount().GetUnits())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "database error when getting pre-eligibility offer",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)

				// Mock database error
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, errors.New("database error"))

				service := &Service{
					preEligibilityDao: mockPreEligibilityDao,
				}
				return service
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetLoanOffer())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "user with existing loan request in progress",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock existing loan request in progress (no completed_at)
				loanRequest := &palPb.LoanRequest{
					Id:        "req-123",
					Status:    palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
					CreatedAt: timestamppb.New(time.Now()),
					// CompletedAt is nil, indicating in progress
				}
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{loanRequest}, nil)

				// Mock GetById call for GetEvaluationStatus
				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				// Mock orchestrator perform call
				mockOrchestrator.EXPECT().
					Perform(gomock.Any(), &OrchestratorReq{
						ReqId:   "req-123",
						ActorId: "actor-123",
					}).
					Return(&OrchestratorResp{
						lr: &palPb.LoanRequest{
							Id:         "req-123",
							Status:     palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
							NextAction: &deeplink.Deeplink{},
						},
					}, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service
			},
			wantStatus: 0, // OK (GetEvaluationStatus should succeed)
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				// Should return evaluation details for in progress loan request
				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_IN_PROGRESS, evaluationDetails.GetEvaluationStatus())
				assert.Equal(t, "req-123", evaluationDetails.GetRequestId())
				assert.Nil(t, evaluationDetails.GetPreEligibilityOffer())
			},
		},
		{
			name: "database error when getting loan requests",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock database error when getting loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return(nil, errors.New("database error"))

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
				}
				return service
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetLoanOffer())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "savings client error",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock no loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{}, nil)

				// Mock savings client error
				mockSavingsClient.EXPECT().
					GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("savings client error"))

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					savingsClient:     mockSavingsClient,
				}
				return service
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetLoanOffer())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "operational status client error",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
				mockOpStatusClient := operationalStatusMocks.NewMockOperationalStatusServiceClient(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock no loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{}, nil)

				// Mock savings account exists
				mockSavingsClient.EXPECT().
					GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).
					Return(&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpcPb.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							Id: "savings-123",
						},
					}, nil)

				// Mock operational status client error
				mockOpStatusClient.EXPECT().
					GetOperationalStatus(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("op status client error"))

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					savingsClient:     mockSavingsClient,
					opStatusClient:    mockOpStatusClient,
				}
				return service
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetLoanOffer())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "user with inactive account",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
				mockOpStatusClient := operationalStatusMocks.NewMockOperationalStatusServiceClient(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock no loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{}, nil)

				// Mock savings account exists
				mockSavingsClient.EXPECT().
					GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).
					Return(&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpcPb.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							Id: "savings-123",
						},
					}, nil)

				// Mock operational status inactive
				mockOpStatusClient.EXPECT().
					GetOperationalStatus(gomock.Any(), gomock.Any()).
					Return(&operationalStatusPb.GetOperationalStatusResponse{
						Status: rpcPb.StatusOk(),
						OperationalStatusInfo: &operationalStatusPb.OperationalStatusInfo{
							FreezeStatus:      enums2.FreezeStatus_FREEZE_STATUS_UNSPECIFIED,
							OperationalStatus: enums2.OperationalStatus_OPERATIONAL_STATUS_INACTIVE,
						},
					}, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					savingsClient:     mockSavingsClient,
					opStatusClient:    mockOpStatusClient,
				}
				return service
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				// Should return evaluation details for inactive user (returns NEW_USER for inactive accounts)
				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_NEW_USER, evaluationDetails.GetEvaluationStatus())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetLoanOffer())
			},
		},
		{
			name: "user with active account and check eligibility flow",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
				mockOpStatusClient := operationalStatusMocks.NewMockOperationalStatusServiceClient(ctrl)
				mockPreApprovedLoanClient := palMocks.NewMockPreApprovedLoanClient(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock no loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{}, nil)

				// Mock savings account exists
				mockSavingsClient.EXPECT().
					GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).
					Return(&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpcPb.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							Id: "savings-123",
						},
					}, nil)

				// Mock operational status active
				mockOpStatusClient.EXPECT().
					GetOperationalStatus(gomock.Any(), gomock.Any()).
					Return(&operationalStatusPb.GetOperationalStatusResponse{
						Status: rpcPb.StatusOk(),
						OperationalStatusInfo: &operationalStatusPb.OperationalStatusInfo{
							FreezeStatus:      enums2.FreezeStatus_FREEZE_STATUS_UNSPECIFIED,
							OperationalStatus: enums2.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
						},
					}, nil)

				// Mock loan summary with check eligibility
				mockPreApprovedLoanClient.EXPECT().
					GetLoanSummaryForHome(gomock.Any(), &palPb.GetLoanSummaryForHomeRequest{
						ActorId: "actor-123",
					}).
					Return(&palPb.GetLoanSummaryForHomeResponse{
						HomeCardType: palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_CHECK_ELIGIBILITY,
					}, nil)

				service := &Service{
					loanRequestsDao:       mockLoanRequestsDao,
					preEligibilityDao:     mockPreEligibilityDao,
					savingsClient:         mockSavingsClient,
					opStatusClient:        mockOpStatusClient,
					preApprovedLoanClient: mockPreApprovedLoanClient,
				}
				return service
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				// Should return loan offer (default offer for check eligibility flow)
				loanOffer := resp.GetLoanOffer()
				assert.NotNil(t, loanOffer)
				assert.Equal(t, "actor-123", loanOffer.GetActorId())
				assert.NotNil(t, loanOffer.GetOfferConstraints())
				assert.Equal(t, int64(500000), loanOffer.GetOfferConstraints().GetMaxLoanAmount().GetUnits())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "preapproved loan client error",
			args: &preEPb.GetLandingInfoRequest{
				ActorId: "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) *Service {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
				mockOpStatusClient := operationalStatusMocks.NewMockOperationalStatusServiceClient(ctrl)
				mockPreApprovedLoanClient := palMocks.NewMockPreApprovedLoanClient(ctrl)

				// Mock no existing pre-eligibility offer
				mockPreEligibilityDao.EXPECT().
					GetActiveOfferByActorId(gomock.Any(), "actor-123").
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock no loan requests
				mockLoanRequestsDao.EXPECT().
					GetByActorIdTypesStatusAndLoanProgram(
						gomock.Any(),
						"actor-123",
						[]palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_PRE_ELIGIBILITY},
						[]palPb.LoanRequestStatus{},
						[]palPb.LoanProgram{},
						nil, nil,
					).
					Return([]*palPb.LoanRequest{}, nil)

				// Mock savings account exists
				mockSavingsClient.EXPECT().
					GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).
					Return(&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpcPb.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							Id: "savings-123",
						},
					}, nil)

				// Mock operational status active
				mockOpStatusClient.EXPECT().
					GetOperationalStatus(gomock.Any(), gomock.Any()).
					Return(&operationalStatusPb.GetOperationalStatusResponse{
						Status: rpcPb.StatusOk(),
						OperationalStatusInfo: &operationalStatusPb.OperationalStatusInfo{
							FreezeStatus:      enums2.FreezeStatus_FREEZE_STATUS_UNSPECIFIED,
							OperationalStatus: enums2.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
						},
					}, nil)

				// Mock preapproved loan client error
				mockPreApprovedLoanClient.EXPECT().
					GetLoanSummaryForHome(gomock.Any(), &palPb.GetLoanSummaryForHomeRequest{
						ActorId: "actor-123",
					}).
					Return(nil, errors.New("preapproved loan client error"))

				service := &Service{
					loanRequestsDao:       mockLoanRequestsDao,
					preEligibilityDao:     mockPreEligibilityDao,
					savingsClient:         mockSavingsClient,
					opStatusClient:        mockOpStatusClient,
					preApprovedLoanClient: mockPreApprovedLoanClient,
				}
				return service
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetLandingInfoResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())

				// Should not have other response types
				assert.Nil(t, resp.GetPreEligibilityOffer())
				assert.Nil(t, resp.GetLoanOffer())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			service := tt.setupMocks(ctrl)

			got, err := service.GetLandingInfo(context.Background(), tt.args)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetLandingInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			assert.Equal(t, tt.wantStatus, got.GetStatus().GetCode())
			tt.validateResp(t, got)
		})
	}
}

func TestService_GetEvaluationStatus(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name         string
		args         *preEPb.GetEvaluationStatusRequest
		setupMocks   func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator)
		wantStatus   uint32
		wantErr      bool
		validateResp func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse)
	}{
		{
			name: "database error when getting loan request",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(nil, errors.New("database error"))

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "loan request status SUCCESS with offer",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
				}

				offer := &palPb.PreEligibilityOffer{
					Id:      "offer-123",
					ActorId: "actor-123",
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				mockPreEligibilityDao.EXPECT().
					GetByLoanRequestId(gomock.Any(), "req-123").
					Return(offer, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_ELIGIBLE, evaluationDetails.GetEvaluationStatus())

				preEligibilityOffer := evaluationDetails.GetPreEligibilityOffer()
				assert.NotNil(t, preEligibilityOffer)
				assert.Equal(t, "offer-123", preEligibilityOffer.GetId())
				assert.Equal(t, "actor-123", preEligibilityOffer.GetActorId())
			},
		},
		{
			name: "loan request status SUCCESS with dao error",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				mockPreEligibilityDao.EXPECT().
					GetByLoanRequestId(gomock.Any(), "req-123").
					Return(nil, errors.New("database error"))

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "loan request status FAILED",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_NOT_ELIGIBLE, evaluationDetails.GetEvaluationStatus())
				assert.Nil(t, evaluationDetails.GetPreEligibilityOffer())
			},
		},
		{
			name: "loan request status MANUAL_INTERVENTION",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION,
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_NOT_ELIGIBLE, evaluationDetails.GetEvaluationStatus())
				assert.Nil(t, evaluationDetails.GetPreEligibilityOffer())
			},
		},
		{
			name: "orchestrator error - already exists",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				mockOrchestrator.EXPECT().
					Perform(gomock.Any(), &OrchestratorReq{
						ReqId:   "req-123",
						ActorId: "actor-123",
					}).
					Return(nil, epifierrors.ErrAlreadyExists)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 6, // ALREADY_EXISTS
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(6), resp.GetStatus().GetCode())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "orchestrator error - internal error",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				mockOrchestrator.EXPECT().
					Perform(gomock.Any(), &OrchestratorReq{
						ReqId:   "req-123",
						ActorId: "actor-123",
					}).
					Return(nil, errors.New("internal error"))

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
		{
			name: "orchestrator perform succeeds and returns SUCCESS status",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
				}

				execDetails := &OrchestratorResp{
					lr: &palPb.LoanRequest{
						Id:     "req-123",
						Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
					},
				}

				offer := &palPb.PreEligibilityOffer{
					Id:      "offer-123",
					ActorId: "actor-123",
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				mockOrchestrator.EXPECT().
					Perform(gomock.Any(), &OrchestratorReq{
						ReqId:   "req-123",
						ActorId: "actor-123",
					}).
					Return(execDetails, nil)

				mockPreEligibilityDao.EXPECT().
					GetByLoanRequestId(gomock.Any(), "req-123").
					Return(offer, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_ELIGIBLE, evaluationDetails.GetEvaluationStatus())

				preEligibilityOffer := evaluationDetails.GetPreEligibilityOffer()
				assert.NotNil(t, preEligibilityOffer)
				assert.Equal(t, "offer-123", preEligibilityOffer.GetId())
				assert.Equal(t, "actor-123", preEligibilityOffer.GetActorId())
			},
		},
		{
			name: "orchestrator perform succeeds and returns FAILED status",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
				}

				execDetails := &OrchestratorResp{
					lr: &palPb.LoanRequest{
						Id:     "req-123",
						Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
					},
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				mockOrchestrator.EXPECT().
					Perform(gomock.Any(), &OrchestratorReq{
						ReqId:   "req-123",
						ActorId: "actor-123",
					}).
					Return(execDetails, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_NOT_ELIGIBLE, evaluationDetails.GetEvaluationStatus())
				assert.Nil(t, evaluationDetails.GetPreEligibilityOffer())
			},
		},
		{
			name: "orchestrator perform succeeds and returns IN_PROGRESS status",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
				}

				execDetails := &OrchestratorResp{
					lr: &palPb.LoanRequest{
						Id:         "req-123",
						Status:     palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
						NextAction: &deeplink.Deeplink{},
					},
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				mockOrchestrator.EXPECT().
					Perform(gomock.Any(), &OrchestratorReq{
						ReqId:   "req-123",
						ActorId: "actor-123",
					}).
					Return(execDetails, nil)

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 0, // OK
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(0), resp.GetStatus().GetCode())

				evaluationDetails := resp.GetEvaluationDetails()
				assert.NotNil(t, evaluationDetails)
				assert.Equal(t, preEPb.EvaluationDetails_EVALUATION_STATUS_IN_PROGRESS, evaluationDetails.GetEvaluationStatus())
				assert.Equal(t, "req-123", evaluationDetails.GetRequestId())
				assert.Nil(t, evaluationDetails.GetPreEligibilityOffer())
			},
		},
		{
			name: "orchestrator perform succeeds with SUCCESS but dao error",
			args: &preEPb.GetEvaluationStatusRequest{
				RequestId: "req-123",
				ActorId:   "actor-123",
			},
			setupMocks: func(ctrl *gomock.Controller) (*Service, *MockIOrchestrator) {
				mockLoanRequestsDao := dao_mocks.NewMockLoanRequestsDao(ctrl)
				mockPreEligibilityDao := dao_mocks.NewMockPreEligibilityOfferDao(ctrl)
				mockOrchestrator := NewMockIOrchestrator(ctrl)

				loanRequest := &palPb.LoanRequest{
					Id:     "req-123",
					Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
				}

				execDetails := &OrchestratorResp{
					lr: &palPb.LoanRequest{
						Id:     "req-123",
						Status: palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
					},
				}

				mockLoanRequestsDao.EXPECT().
					GetById(gomock.Any(), "req-123").
					Return(loanRequest, nil)

				mockOrchestrator.EXPECT().
					Perform(gomock.Any(), &OrchestratorReq{
						ReqId:   "req-123",
						ActorId: "actor-123",
					}).
					Return(execDetails, nil)

				mockPreEligibilityDao.EXPECT().
					GetByLoanRequestId(gomock.Any(), "req-123").
					Return(nil, errors.New("database error"))

				service := &Service{
					loanRequestsDao:   mockLoanRequestsDao,
					preEligibilityDao: mockPreEligibilityDao,
					orchestrator:      mockOrchestrator,
				}
				return service, mockOrchestrator
			},
			wantStatus: 13, // INTERNAL
			wantErr:    false,
			validateResp: func(t *testing.T, resp *preEPb.GetEvaluationStatusResponse) {
				assert.NotNil(t, resp)
				assert.Equal(t, uint32(13), resp.GetStatus().GetCode())
				assert.Nil(t, resp.GetEvaluationDetails())
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			service, _ := tt.setupMocks(ctrl)

			got, err := service.GetEvaluationStatus(context.Background(), tt.args)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetEvaluationStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			assert.Equal(t, tt.wantStatus, got.GetStatus().GetCode())
			tt.validateResp(t, got)
		})
	}
}

func TestService_InitiateEvaluation(t *testing.T) {
	t.Parallel()
	type args struct {
		req *preEPb.InitiateEvaluationRequest
	}

	type mocks struct {
		orchestrator *MockOrchestrator
	}

	tests := []struct {
		name           string
		args           args
		setupMocks     func(m *mocks)
		wantStatusCode uint32
		wantEvalStatus preEPb.EvaluationDetails_EvaluationStatus
		wantErr        bool
	}{
		{
			name: "when orchestrator start succeeds",
			args: args{
				req: &preEPb.InitiateEvaluationRequest{
					ActorId: "actor-123",
				},
			},
			setupMocks: func(m *mocks) {
				// Mock orchestrator.Start returning a loan request
				m.orchestrator.StartFunc = func(ctx context.Context, actorId string) (*OrchestratorResp, error) {
					return &OrchestratorResp{
						lr: &palPb.LoanRequest{
							Id:         "request-123",
							Status:     palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
							NextAction: &deeplink.Deeplink{},
						},
					}, nil
				}
			},
			wantStatusCode: rpcPb.StatusOk().GetCode(),
			wantEvalStatus: preEPb.EvaluationDetails_EVALUATION_STATUS_IN_PROGRESS,
			wantErr:        false,
		},
		{
			name: "when orchestrator start returns already exists error",
			args: args{
				req: &preEPb.InitiateEvaluationRequest{
					ActorId: "actor-123",
				},
			},
			setupMocks: func(m *mocks) {
				// Mock orchestrator.Start returning already exists error
				m.orchestrator.StartFunc = func(ctx context.Context, actorId string) (*OrchestratorResp, error) {
					return nil, epifierrors.ErrAlreadyExists
				}
			},
			wantStatusCode: rpcPb.StatusAlreadyExists().GetCode(),
			wantEvalStatus: preEPb.EvaluationDetails_EVALUATION_STATUS_UNDEFINED,
			wantErr:        false,
		},
		{
			name: "when orchestrator start returns other error",
			args: args{
				req: &preEPb.InitiateEvaluationRequest{
					ActorId: "actor-123",
				},
			},
			setupMocks: func(m *mocks) {
				// Mock orchestrator.Start returning other error
				m.orchestrator.StartFunc = func(ctx context.Context, actorId string) (*OrchestratorResp, error) {
					return nil, errors.New("internal error")
				}
			},
			wantStatusCode: rpcPb.StatusInternal().GetCode(),
			wantEvalStatus: preEPb.EvaluationDetails_EVALUATION_STATUS_UNDEFINED,
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDeps := &mocks{
				orchestrator: &MockOrchestrator{},
			}

			// Setup mock functions
			if tt.setupMocks != nil {
				tt.setupMocks(mockDeps)
			}

			// Initialize service
			service := &Service{
				orchestrator: mockDeps.orchestrator,
			}

			// Set ownership in context
			ctx := epificontext.WithOwnership(context.Background(), common.Ownership_EPIFI_TECH_V2)

			// Call the method
			resp, err := service.InitiateEvaluation(ctx, tt.args.req)

			// Assertions
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.wantStatusCode, resp.GetStatus().GetCode())

				if tt.wantStatusCode == rpcPb.StatusOk().GetCode() {
					assert.NotNil(t, resp.GetEvaluationDetails())
					assert.Equal(t, tt.wantEvalStatus, resp.GetEvaluationDetails().GetEvaluationStatus())
				}
			}
		})
	}
}

func TestService_CollectUserData(t *testing.T) {
	t.Parallel()
	type args struct {
		req *preEPb.CollectUserDataRequest
	}

	type mocks struct {
		orchestrator *MockOrchestrator
	}

	tests := []struct {
		name           string
		args           args
		setupMocks     func(m *mocks)
		wantStatusCode uint32
		wantNextAction *deeplink.Deeplink
		wantErr        bool
	}{
		{
			name: "when orchestrator perform succeeds",
			args: args{
				req: &preEPb.CollectUserDataRequest{
					ActorId:   "actor-123",
					RequestId: "request-123",
				},
			},
			setupMocks: func(m *mocks) {
				// Mock orchestrator.Perform returning updated loan request
				m.orchestrator.PerformFunc = func(ctx context.Context, req *OrchestratorReq) (*OrchestratorResp, error) {
					return &OrchestratorResp{
						lr: &palPb.LoanRequest{
							Id:         "request-123",
							Status:     palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
							NextAction: &deeplink.Deeplink{},
						},
					}, nil
				}
			},
			wantStatusCode: rpcPb.StatusOk().GetCode(),
			wantNextAction: &deeplink.Deeplink{},
			wantErr:        false,
		},
		{
			name: "when orchestrator perform returns already exists error",
			args: args{
				req: &preEPb.CollectUserDataRequest{
					ActorId:   "actor-123",
					RequestId: "request-123",
				},
			},
			setupMocks: func(m *mocks) {
				// Mock orchestrator.Perform returning already exists error
				m.orchestrator.PerformFunc = func(ctx context.Context, req *OrchestratorReq) (*OrchestratorResp, error) {
					return nil, epifierrors.ErrAlreadyExists
				}
			},
			wantStatusCode: rpcPb.StatusAlreadyExists().GetCode(),
			wantNextAction: nil,
			wantErr:        false,
		},
		{
			name: "when orchestrator perform returns other error",
			args: args{
				req: &preEPb.CollectUserDataRequest{
					ActorId:   "actor-123",
					RequestId: "request-123",
				},
			},
			setupMocks: func(m *mocks) {
				// Mock orchestrator.Perform returning other error
				m.orchestrator.PerformFunc = func(ctx context.Context, req *OrchestratorReq) (*OrchestratorResp, error) {
					return nil, errors.New("internal error")
				}
			},
			wantStatusCode: rpcPb.StatusInternal().GetCode(),
			wantNextAction: nil,
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockDeps := &mocks{
				orchestrator: &MockOrchestrator{},
			}

			// Setup mock functions
			if tt.setupMocks != nil {
				tt.setupMocks(mockDeps)
			}

			// Initialize service
			service := &Service{
				orchestrator: mockDeps.orchestrator,
			}

			// Set ownership in context
			ctx := epificontext.WithOwnership(context.Background(), common.Ownership_EPIFI_TECH_V2)

			// Call the method
			resp, err := service.CollectUserData(ctx, tt.args.req)

			// Assertions
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.wantStatusCode, resp.GetStatus().GetCode())

				if tt.wantStatusCode == rpcPb.StatusOk().GetCode() {
					if tt.wantNextAction == nil {
						assert.Nil(t, resp.GetNextAction())
					} else {
						assert.NotNil(t, resp.GetNextAction())
					}
				}
			}
		})
	}
}
