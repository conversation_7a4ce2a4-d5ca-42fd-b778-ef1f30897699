package epifitech

import (
	"time"

	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/helper"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

const (
	maxReviewDetailsWaitTimeHrs = 120
)

// ReviewDetails represents the stage which where we show user all the form fields they had filled till now
type ReviewDetails struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
	*helper.ActivityHelper
}

func NewReviewDetails() *ReviewDetails {
	return &ReviewDetails{}
}

var _ stages.IStage = &ReviewDetails{}

// nolint: funlen
func (ca *ReviewDetails) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: deeplink.GetApplicationReviewDetailsScreenDeeplink(&palFeEnumsPb.LoanHeader{
			LoanProgram: deeplink.GetFeLoanProgramFromBe(req.LoanProgram),
			Vendor:      deeplink.GetPalFeVendorFromBe(req.Vendor),
		}, req.Request.GetLoanStep().GetRefId()),
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	err := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
	if preapprovedloan.IsActivityError(updateRes.GetLoanStep(), res, err, false) {
		workflow.GetLogger(ctx).Error("error in updating next action to PL_APPLICATION_REVIEW_DETAILS_SCREEN", err)
		return res, err
	}

	// Step 2: Verify if user has entered data and continued
	actReq := ca.GetActivityRequest(req, req.LoanProgram)
	actRes := &palActivityPb.PalActivityResponse{}
	var getReviewDetailsFuture epifitemporal.AsyncFuture[*palActivityPb.PalActivityResponse]
	if v := workflow.GetVersion(ctx, "use-AsyncStepTerminalStatusCheckV2-in-realtime-etb-flow", workflow.DefaultVersion, 1); v == 1 {
		getReviewDetailsFuture, err = activityPkg.ExecuteAsync(ctx, palNs.AsyncStepTerminalStatusCheckV2, actReq, actRes)
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), nil, res, err, true) {
			return res, err
		}
	} else {
		getReviewDetailsFuture, err = activityPkg.ExecuteAsync(ctx, palNs.AsyncStepTerminalStatusCheck, actReq, actRes)
		if err != nil {
			return nil, err
		}
	}

	var errorToReturn error
	getReviewDetailsFuture.AddFutureHandler(func(asyncStepTerminalStatusCheckErr error, actRes *palActivityPb.PalActivityResponse) {
		workflow.GetLogger(ctx).Info("AsyncStepTerminalStatusCheck future finished")
		if preapprovedloan.IsActivityError(actRes.GetLoanStep(), res, asyncStepTerminalStatusCheckErr, false) {
			workflow.GetLogger(ctx).Info("AsyncStepTerminalStatusCheck activity async processing failed", zap.Error(asyncStepTerminalStatusCheckErr))
			errorToReturn = asyncStepTerminalStatusCheckErr
		}
	})
	signalPayload := &emptyPb.Empty{}

	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoanEligibilityReviewDetailsCompletedSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		workflow.GetLogger(ctx).Info("LoanEligibilityReviewDetailsCompletedSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("LoanEligibilityReviewDetailsCompletedSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, getReviewDetailsFuture, sigChannel, maxReviewDetailsWaitTimeHrs*time.Hour)
	if err != nil {
		if epifitemporal.HasSignalReceivedTimedOut(err) {
			workflow.GetLogger(ctx).Info("ReceiveSignalWithFuture receive signal timed out", zap.Error(err))
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		workflow.GetLogger(ctx).Info("LoanEligibilityReviewDetailsCompletedSignal receive signal with future processing failed", zap.Error(err))
		return res, err
	}

	if errorToReturn != nil {
		if epifitemporal.IsRetryableError(errorToReturn) && workflow.GetVersion(ctx, "review-details-expiry-check", workflow.DefaultVersion, 1) == 1 {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return res, errorToReturn
	}

	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (ca *ReviewDetails) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REVIEW_DETAILS
}

func (ca *ReviewDetails) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageReviewDetails
}
