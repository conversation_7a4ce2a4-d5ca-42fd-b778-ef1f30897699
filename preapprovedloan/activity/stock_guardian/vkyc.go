package stock_guardian

import (
	"context"
	"fmt"

	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/nulltypes"

	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"

	empPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	vkyc2 "github.com/epifi/gamma/api/kyc/vkyc"
	omeglePb "github.com/epifi/gamma/api/omegle"
	"github.com/epifi/gamma/api/omegle/enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	sgApplicationPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	sKycPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/matrix"
	types "github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/vkyc"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	deeplinkPal "github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
)

// GetApplicationFormDataForNonFiCoreRequest contains all the parameters needed for getApplicationFormDataForNonFiCore function
type GetApplicationFormDataForNonFiCoreRequest struct {
	ActorId               string
	UserName              *common.Name
	UserDob               *common.Date
	CorrespondenceAddress *common.PostalAddress
	PermanentAddress      *common.PostalAddress
	UserImage             *common.Image
	OvdId                 string
}

// nolint:funlen,gocritic,dupl
func (p *Processor) SgInitiateVkyc(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		kycData := lr.GetDetails().GetApplicationDetails().GetKycDetails()
		kycApplicationId := kycData.GetVendorKycRequestId()
		// if the kyc was already done, then skipping the VKYC stage as well
		if kycData.GetIsKycAlreadyDonePreviously() {
			lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
			if updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			}); updateErr != nil {
				lg.Error("failed to update lse", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse, err: %v", updateErr))
			}
			return res, nil
		}
		gcasResp, gcasErr := p.sgMatrixApiGateway.GetCustomerApplicationStatus(ctx, &matrix.GetCustomerApplicationStatusRequest{
			ApplicationIdentifier: &matrix.GetCustomerApplicationStatusRequest_ApplicationId{
				ApplicationId: kycApplicationId,
			},
		})
		if te := epifigrpc.RPCError(gcasResp, gcasErr); te != nil {
			lg.Error("failed to get status from GetCustomerApplicationStatus", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get status from GetCustomerApplicationStatus")
		}

		isNextActionVkyc := gcasResp.GetNextAction() == matrix.Action_ACTION_VKYC
		vkycApplicationId := gcasResp.GetActionMetadata().GetVkycMetadata().GetApplicationId()
		vkycCallId := gcasResp.GetActionMetadata().GetVkycMetadata().GetCallId()

		// if we hit the below scenerio, then that means KYC is doing some data
		// redaction in background so showing the following screen in this stage
		if gcasResp.GetCurrentStage() == matrix.Stage_STAGE_VKYC && vkycApplicationId == "" && vkycCallId == "" && gcasResp.GetStageStatus() == matrix.StageStatus_STAGE_STATUS_IN_PROGRESS {
			deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinkPal.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
			res.NextAction = deeplinkProvider.GetManualReviewWaitScreen(ctx, &provider.GetManualReviewWaitScreenRequest{
				LoanHeader:     deeplinkProvider.GetLoanHeader(),
				LoanRequestId:  lr.GetId(),
				CenterImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/timingMach.png",
				Title:          "Your documents are under review",
				Subtitle:       "This may take up to 2 hours. Your Video-KYC will start after that",
				BottomCard: &provider.ReviewScreenBottomCard{
					Title:    "In progress: Documents review",
					Subtitle: "Our best agents are on this. We’ll notify you once its done",
				},
			})

			lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_IN_PROGRESS
			updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS})
			if updateErr != nil {
				lg.Info("error in updating sub status in lse", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, "error in sub status updating in lse")
			}

			lg.Info("vkyc is not yet started, KYC performing some background checks")
			return res, errors.Wrap(epifierrors.ErrTransient, "vkyc is not yet started, KYC performing some background checks")
		}

		// if next action is false, that means we can check the overall kyc status and move to the next step
		if isNextActionVkyc == false {
			stageStatus, stageErr := p.getKycStageStatusFromVendor(ctx, kycApplicationId, matrix.Stage_STAGE_VKYC)
			if stageErr != nil {
				lg.Error("got a failure stage status at kyc", zap.Error(stageErr))
				return nil, stageErr
			}
			if stageStatus == matrix.StageStatus_STAGE_STATUS_SUCCESS {
				// get overall kyc stage status from vendor
				kycStageStatus, kycStageErr := p.getStageStatusFromVendor(ctx, lr.GetVendorRequestId(), sgApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_KYC)
				if kycStageErr != nil {
					lg.Error("got a failure stage status at kyc stage", zap.Error(kycStageErr))
					return nil, kycStageErr
				}

				if kycStageStatus == sgApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_SUCCESS {
					// mark LSE success
					palActivity.MarkLoanStepSuccess(lse)
					return res, nil
				}
				lg.Info("application level kyc stage is not in success stage yet", zap.String(logger.STAGE_STATUS, kycStageStatus.String()))
				return nil, errors.Wrap(epifierrors.ErrTransient, "application level kyc stage is not in success stage yet")
			} else {
				// todo(anupam): check to give manual intervention from here
				lg.Info("vkyc stage is not in success stage yet", zap.String(logger.STAGE_STATUS, stageStatus.String()))
				return nil, errors.Wrap(epifierrors.ErrTransient, "vkyc stage is not in success stage yet!")
			}
		}

		lseFieldMasks := []palPb.LoanStepExecutionFieldMask{}
		// update the lse status and vkyc application id
		if vkycApplicationId != lse.GetDetails().GetVkycStepData().GetApplicationId() {
			lse.Details = &palPb.LoanStepExecutionDetails{
				Details: &palPb.LoanStepExecutionDetails_VkycStepData{
					VkycStepData: &palPb.VkycStepData{
						ApplicationId: vkycApplicationId,
					},
				},
			}
			lseFieldMasks = append(lseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS)
		}
		if lse.GetSubStatus() != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_DONE {
			// after reaching at this stage, our data redaction is done and we are good to start with VKYC
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_DONE
			lseFieldMasks = append(lseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
		}
		if lse.GetStatus() != palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS {
			lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			lseFieldMasks = append(lseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
		}
		if len(lseFieldMasks) != 0 {
			if updateErr := p.loanStepExecutionDao.Update(ctx, lse, lseFieldMasks); updateErr != nil {
				lg.Error("failed to update lse", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse, err: %v", updateErr))
			}
		}

		// register customer for vkyc if not already done
		getApplicantDetails, getApplicantDetailsErr := p.omegleClient.GetApplicantDetails(
			ctx, &omeglePb.GetApplicantDetailsRequest{
				ApplicationId: vkycApplicationId,
			},
		)
		if rpcErr := epifigrpc.RPCError(getApplicantDetails, getApplicantDetailsErr); rpcErr != nil {
			if rpc.StatusFromError(rpcErr).IsRecordNotFound() {
				// register user
				registerUserErr := p.registerUserAtOmegle(ctx, vkycApplicationId, lse.GetActorId(), kycApplicationId)
				if registerUserErr != nil {
					lg.Error("unable to register the user at omegle", zap.Error(registerUserErr))
					return nil, errors.Wrap(epifierrors.ErrTransient, registerUserErr.Error())
				}
				return res, nil
			}
			lg.Error("error in getting applicant details", zap.Error(rpcErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting applicant details: %v", rpcErr))
		}

		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) registerUserAtOmegle(ctx context.Context, vkycApplicationId string, actorId string, kycApplicationId string) error {
	kycDataResp, kycDataErr := p.sgKycApiGateway.GetKYCData(ctx, &sKycPb.GetKYCDataRequest{
		Identifier: &sKycPb.KYCRequestIdentifier{
			Identifier: &sKycPb.KYCRequestIdentifier_ApplicationId{
				ApplicationId: kycApplicationId,
			},
		},
	})
	if te := epifigrpc.RPCError(kycDataResp, kycDataErr); te != nil {
		return errors.Wrap(te, "failed to get kyc data")
	}

	// Use the new KycData structure
	kycData := kycDataResp.GetKycData()
	if kycData == nil {
		return errors.New("kyc data is nil")
	}

	personalData := kycData.GetPersonalData()
	userName := personalData.GetName()
	userDob := personalData.GetDob()
	correspondenceAddress := personalData.GetCorrespondenceAddress()
	permanentAddress := personalData.GetPermanentAddress()

	userImage := kycData.GetUserImage()
	ovdId := kycData.GetKycDocumentNumber()

	// Create document details from the new structure
	var documentDetails []*types.DocumentDetails
	switch kycDataResp.GetDataType() {
	case sKycPb.KycVendorDataType_KYC_VENDOR_DATA_TYPE_CKYC_DOWNLOAD:
		ckycDocumentDetails := getCkycDocumentDetailsFromKycData(kycData)
		documentDetails = append(documentDetails, ckycDocumentDetails)
	case sKycPb.KycVendorDataType_KYC_VENDOR_DATA_TYPE_DIGILOCKER_AADHAR_XML:
		digilockerDocumentDetails := getDigilockerDocumentDetailsFromKycData(kycData)
		documentDetails = append(documentDetails, digilockerDocumentDetails)
	}

	userFeatProp, err := helper.GetUserFeatureProperty(ctx, actorId, p.onbClient, p.savingsClient)
	if err != nil {
		return errors.Wrap(err, "error while checking if user is non fi core user")
	}
	var applicationFormDetails *omeglePb.ApplicationFormData
	var panDetails *types.PanDocumentDetails
	if !userFeatProp.IsFiSAHolder || (p.config.SgEtbNewEligibilityFlow() != nil && p.config.SgEtbNewEligibilityFlow().IsAllowed()) {
		applicationFormDetails, panDetails, err = p.getApplicationFormDataForNonFiCore(ctx, &GetApplicationFormDataForNonFiCoreRequest{
			ActorId:               actorId,
			UserName:              userName,
			UserDob:               userDob,
			CorrespondenceAddress: correspondenceAddress,
			PermanentAddress:      permanentAddress,
			UserImage:             userImage,
			OvdId:                 ovdId,
		})
	} else {
		applicationFormDetails, panDetails, err = p.getApplicationDetails(ctx, actorId, correspondenceAddress, permanentAddress, userImage, ovdId)
	}
	if err != nil {
		return errors.Wrap(err, "error while getting application details")
	}
	documentDetails = append(documentDetails, &types.DocumentDetails{
		DocumentType: types.DocumentType_DOCUMENT_TYPE_PAN,
		DocumentTypeDetails: &types.DocumentDetails_PanDetails{
			PanDetails: panDetails,
		},
	})

	registerUserResp, registerUserErr := p.omegleClient.RegisterUser(ctx, &omeglePb.RegisterUserRequest{
		ApplicationId:       vkycApplicationId,
		ApplicationFormData: applicationFormDetails,
		Client:              vgPb.Vendor_STOCK_GUARDIAN_LSP,
		Channel:             enums.Channel_CHANNEL_NBFC_ONBOARDING,
		DocumentDetails:     documentDetails,
	})
	if te := epifigrpc.RPCError(registerUserResp, registerUserErr); te != nil {
		return errors.Wrap(te, "unable to register the user at omegle")
	}
	return nil
}

func getCkycDocumentDetailsFromKycData(kycData *sKycPb.KycData) *types.DocumentDetails {
	var ckycIdentityData []*types.Identity
	for _, data := range kycData.GetIdentityData() {
		identityData := &types.Identity{
			IdProof: &types.IdProof{
				Type:          convertIdentityTypeFromCkycToOmegle(data.GetType()),
				IdValue:       data.GetIdValue(),
				DocumentImage: data.GetDocumentImage(),
			},
			IsIdProofSubmitted: true,
			IsIdVerified:       true,
		}
		ckycIdentityData = append(ckycIdentityData, identityData)
	}

	personalData := kycData.GetPersonalData()
	return &types.DocumentDetails{
		DocumentType: types.DocumentType_DOCUMENT_TYPE_CKYC_RECORD,
		DocumentTypeDetails: &types.DocumentDetails_CkycData{
			CkycData: &types.CKYCDocumentDetails{
				Payload: &types.CKYCDownloadPayload{
					IdentityData: ckycIdentityData,
					PersonalData: &types.PersonalDetails{
						CkycNo:           kycData.GetKycDocumentNumber(),
						Name:             personalData.GetName(),
						BirthDate:        ConvertCommonToDate(personalData.GetDob()),
						CorresAddress:    personalData.GetCorrespondenceAddress(),
						PermanentAddress: personalData.GetPermanentAddress(),
					},
					UserImage: kycData.GetUserImage(),
				},
			},
		},
	}
}

func getDigilockerDocumentDetailsFromKycData(kycData *sKycPb.KycData) *types.DocumentDetails {
	return &types.DocumentDetails{
		DocumentType: types.DocumentType_DOCUMENT_TYPE_AADHAAR,
		DocumentTypeDetails: &types.DocumentDetails_AadhaarDigilockerData{
			AadhaarDigilockerData: &types.AadhaarDigilockerData{
				MaskedAadhaarNumber: kycData.GetKycDocumentNumber(),
				Name:                kycData.GetPersonalData().GetName(),
				Gender:              kycData.GetPersonalData().GetGender(),
				DateOfBirth:         ConvertCommonToDate(kycData.GetPersonalData().GetDob()),
				PermanentAddress:    kycData.GetPersonalData().GetPermanentAddress(),
				UserImage:           kycData.GetUserImage(),
			},
		},
	}
}

func (p *Processor) getApplicationFormDataForNonFiCore(ctx context.Context, req *GetApplicationFormDataForNonFiCoreRequest) (*omeglePb.ApplicationFormData, *types.PanDocumentDetails, error) {
	userDataRes, err := p.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: req.ActorId})
	if err != nil {
		return nil, nil, errors.Wrap(err, "error in fetching default user data")
	}
	var name *common.Name
	var dob *date.Date
	if req.UserName != nil {
		name = req.UserName
	} else {
		name = userDataRes.GetBestName()
	}
	if req.UserDob != nil {
		dob = &date.Date{
			Year:  req.UserDob.GetYear(),
			Month: req.UserDob.GetMonth(),
			Day:   req.UserDob.GetDay(),
		}
	} else {
		dob = userDataRes.GetGivenDateOfBirth()
	}
	// TODO: check if all the omegle fields are being filled correctly
	// MotherName, FatherName are mandatory and cannot be left empty
	// IncomeDetails has to be salary range and not absolute value according to the current validations implemented in vkyc service: https://github.com/epiFi/gamma/blob/master/vkyccall/serviceprovider/sg_matrix_service_provider.go#L556
	return &omeglePb.ApplicationFormData{
			ApplicantName:        name, // has to be kyc name
			PhoneNumber:          userDataRes.GetMobileNumber(),
			FatherName:           name, // not needed
			MotherName:           name, // not needed
			Dob:                  dob,  // kyc dob
			Email:                userDataRes.GetEmail(),
			Gender:               userDataRes.GetGivenGender(), // kyc gender
			CommunicationAddress: ConvertTypesV2AddressIntoTypesV1(req.CorrespondenceAddress),
			PermanentAddress:     ConvertTypesV2AddressIntoTypesV1(req.PermanentAddress),
			IncomeDetails: &omeglePb.IncomeDetails{
				SalaryRange: &types.SalaryRange{
					MinValue: &types.Money{
						CurrencyCode: userDataRes.GetEmploymentDetails().GetMonthlyIncome().GetCurrencyCode(),
						Units:        userDataRes.GetEmploymentDetails().GetMonthlyIncome().GetUnits(),
					},
					MaxValue: &types.Money{
						CurrencyCode: userDataRes.GetEmploymentDetails().GetMonthlyIncome().GetCurrencyCode(),
						Units:        userDataRes.GetEmploymentDetails().GetMonthlyIncome().GetUnits() + 1,
					},
				},
			},
			Image: &common.Image{
				ImageDataBase64: req.UserImage.GetImageDataBase64(),
			},
			EmploymentDetails: &omeglePb.EmploymentDetails{
				EmploymentTypeStr: userDataRes.GetEmploymentDetails().GetEmploymentType().String(),
				OccupationTypeStr: "",
			},
			ApplicationType: enums.ApplicationType_APPLICATION_TYPE_INDIVIDUAL,
			ApplicantType:   enums.ApplicantType_APPLICANT_TYPE_APPLICANT,
			OvdId:           req.OvdId,
		}, &types.PanDocumentDetails{
			Id:          userDataRes.GetPan(),
			Name:        name, // has to be pan name, but given name can be used assuming that it is validated against pan
			DateOfBirth: dob,  // has to be pan dob, but given dob can be used assuming that it is validated against pan
		}, nil
}

func (p *Processor) getApplicationDetails(ctx context.Context, actorId string, correspondenceAddress *common.PostalAddress, permanentAddress *common.PostalAddress, userImage *common.Image, ovdId string) (*omeglePb.ApplicationFormData, *types.PanDocumentDetails, error) {
	empResp, respErr := p.empClient.GetEmploymentInfo(ctx, &empPb.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(empResp, respErr); rpcErr != nil {
		return nil, nil, errors.Wrap(rpcErr, "error in fetching employment data")
	}

	loanApplicant, loanApplicantErr := p.loanApplicantDao.GetByActorId(ctx, actorId)
	if loanApplicantErr != nil {
		return nil, nil, errors.Wrap(loanApplicantErr, "error in fetching loan account")
	}
	employmentDetails := loanApplicant.GetEmploymentDetails()

	userResp, respErr := p.userClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, respErr); rpcErr != nil {
		return nil, nil, errors.Wrap(rpcErr, "error in getting user details")
	}
	profile := userResp.GetUser().GetProfile()
	panDetails := &types.PanDocumentDetails{
		Id:          profile.GetPAN(),
		Name:        profile.GetPanName(),
		DateOfBirth: profile.GetDateOfBirth(),
	}
	employmentMonthlyIncome := employmentDetails.GetStatedIncome()

	// Todo(Anupam: Revisit Before prod): ask and clear the below hardcoded values
	return &omeglePb.ApplicationFormData{
		ApplicantName:        profile.GetKycName(),
		PhoneNumber:          profile.GetPhoneNumber(),
		FatherName:           profile.GetFatherName(),
		MotherName:           profile.GetMotherName(),
		Dob:                  profile.GetDateOfBirth(),
		Email:                profile.GetEmail(),
		Gender:               profile.GetKycGender(),
		CommunicationAddress: ConvertTypesV2AddressIntoTypesV1(correspondenceAddress),
		PermanentAddress:     ConvertTypesV2AddressIntoTypesV1(permanentAddress),
		IncomeDetails: &omeglePb.IncomeDetails{
			SalaryRange: &types.SalaryRange{
				MinValue: &types.Money{
					CurrencyCode: employmentMonthlyIncome.GetCurrencyCode(),
					Units:        employmentMonthlyIncome.GetUnits(),
				},
				MaxValue: &types.Money{
					CurrencyCode: employmentMonthlyIncome.GetCurrencyCode(),
					// after discussion, we finalised to send the value as +1
					// in max value to support the salary range
					Units: employmentMonthlyIncome.GetUnits() + 1,
				},
			},
		},
		Image: &common.Image{
			ImageDataBase64: userImage.GetImageDataBase64(),
		},
		EmploymentDetails: &omeglePb.EmploymentDetails{
			EmploymentTypeStr: employmentDetails.GetOccupation().String(),
			OccupationTypeStr: empResp.GetEmploymentData().GetOccupationType().String(),
		},
		ApplicationType: enums.ApplicationType_APPLICATION_TYPE_INDIVIDUAL,
		ApplicantType:   enums.ApplicantType_APPLICANT_TYPE_APPLICANT,
		OvdId:           ovdId,
	}, panDetails, nil
}

// this converter function will be converting the identity proof type
// from ckyc data we receive to the omegle service we need to send
func convertIdentityTypeFromCkycToOmegle(proofType sKycPb.IdProofType) types.IdProof_IdProofType {
	switch proofType {
	case sKycPb.IdProofType_ID_PROOF_TYPE_CKYC_RECORD:
		return types.IdProof_ID_PROOF_TYPE_CKYC_RECORD
	case sKycPb.IdProofType_ID_PROOF_TYPE_DRIVING_LICENSE:
		return types.IdProof_ID_PROOF_TYPE_DRIVING_LICENSE
	case sKycPb.IdProofType_ID_PROOF_TYPE_PAN:
		return types.IdProof_ID_PROOF_TYPE_PAN
	case sKycPb.IdProofType_ID_PROOF_TYPE_VOTER_ID:
		return types.IdProof_ID_PROOF_TYPE_VOTER_ID
	case sKycPb.IdProofType_ID_PROOF_TYPE_PASSPORT:
		return types.IdProof_ID_PROOF_TYPE_PASSPORT
	case sKycPb.IdProofType_ID_PROOF_TYPE_NREGA_JOB_CARD:
		return types.IdProof_ID_PROOF_TYPE_NREGA_JOB_CARD
	case sKycPb.IdProofType_ID_PROOF_TYPE_UID:
		return types.IdProof_ID_PROOF_TYPE_UID
	case sKycPb.IdProofType_ID_PROOF_TYPE_NATIONAL_POPULATION_REGISTER_LETTER:
		return types.IdProof_ID_PROOF_TYPE_NATIONAL_POPULATION_REGISTER_LETTER
	case sKycPb.IdProofType_ID_PROOF_TYPE_EKYC_AUTHENTICATION:
		return types.IdProof_ID_PROOF_TYPE_EKYC_AUTHENTICATION
	case sKycPb.IdProofType_ID_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION:
		return types.IdProof_ID_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION
	case sKycPb.IdProofType_ID_PROOF_TYPE_OFFLINE_OTHERS:
		return types.IdProof_ID_PROOF_TYPE_OFFLINE_OTHERS
	case sKycPb.IdProofType_ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED:
		return types.IdProof_ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED
	case sKycPb.IdProofType_ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT:
		return types.IdProof_ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT
	default:
		return types.IdProof_ID_PROOF_TYPE_UNSPECIFIED
	}
}

// nolint:funlen, dupl
func (p *Processor) SgCheckVkycStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		kycData := lr.GetDetails().GetApplicationDetails().GetKycDetails()
		kycApplicationId := kycData.GetVendorKycRequestId()

		// get the next action of the customer's kyc application
		gcasResp, gcasErr := p.sgMatrixApiGateway.GetCustomerApplicationStatus(ctx, &matrix.GetCustomerApplicationStatusRequest{
			ApplicationIdentifier: &matrix.GetCustomerApplicationStatusRequest_ApplicationId{
				ApplicationId: kycApplicationId,
			},
		})
		if te := epifigrpc.RPCError(gcasResp, gcasErr); te != nil {
			errString := "error in sgMatrixApiGateway.GetCustomerApplicationStatus call"
			lg.Error(errString, zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get customer application status, err: %v", te))
		}

		switch {
		case gcasResp.GetApplicationStatus() == matrix.ApplicationStatus_APPLICATION_STATUS_COMPLETED:
			// get overall kyc stage status from vendor
			kycStageStatus, kycStageErr := p.getStageStatusFromVendor(ctx, lr.GetVendorRequestId(), sgApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_KYC)
			if kycStageErr != nil {
				errString := "error in getting kyc application stage status"
				lg.Error(errString, zap.Error(kycStageErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("%s, err: %v", errString, kycStageErr))
			}
			if kycStageStatus == sgApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_SUCCESS {
				palActivity.MarkLoanStepSuccessV2(lse, res)
				return res, nil
			}
			errString := "application level kyc stage is not in success stage yet"
			lg.Info(errString)
			return nil, errors.Wrap(epifierrors.ErrTransient, errString)
		case gcasResp.GetApplicationStatus() == matrix.ApplicationStatus_APPLICATION_STATUS_FAILED:
			errString := "KYC failed at vendor end"
			lg.Error(errString)
			// TODO(vikas): show customer KYC failed screen?
			// res.NextAction =
			return res, errors.Wrap(epifierrors.ErrPermanent, errString)
		default:
			// continue check vkyc status
		}

		// if next action is not vkyc then check the overall KYC application status
		if gcasResp.GetCurrentStage() != matrix.Stage_STAGE_VKYC {
			// this will be the case when VKYC is done but some step are pending at vendor's end do complete for KYC
			errString := "current stage not VKYC and non terminal"
			lg.Error(errString)
			// TODO(vikas): check DL to return
			return nil, errors.Wrap(epifierrors.ErrTransient, errString)
		}

		callStatusString := gcasResp.GetActionMetadata().GetVkycMetadata().GetCallStatus()
		callStatus := enums.OverallStatus(enums.OverallStatus_value[callStatusString])

		// kyc stage is vkyc
		switch callStatus {
		case enums.OverallStatus_OVERALL_STATUS_CALL_APPROVED, enums.OverallStatus_OVERALL_STATUS_CALL_COMPLETED:
			// update LSE sub status if not already update to call in review
			if lse.GetSubStatus() != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_IN_REVIEW {
				lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_IN_REVIEW
				if updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
				}); updateErr != nil {
					errString := "failed to update sub status in lse"
					lg.Error(errString, zap.Error(updateErr), zap.String(logger.SUB_STATUS, lse.GetSubStatus().String()))
					return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("%s, err: %v", errString, updateErr))
				}
			}

			deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinkPal.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})

			res.NextAction = deeplinkProvider.GetManualReviewWaitScreen(ctx, &provider.GetManualReviewWaitScreenRequest{
				LoanHeader:     deeplinkProvider.GetLoanHeader(),
				LoanRequestId:  lr.GetId(),
				CenterImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/vkycInReview.png",
				Title:          "Your KYC documents are in review",
				Subtitle:       "Once document verification is complete, we'll notify you. Typically, 90% of applications are reviewed within 8 hours.",
				BottomCard: &provider.ReviewScreenBottomCard{
					Title:    "In progress: Official's approval",
					Subtitle: "90% of applications are reviewed within 8 hours",
				},
			})
			logString := "vkyc call in review"
			lg.Info(logString, zap.String(logger.STATUS, callStatusString))

			return res, errors.Wrap(epifierrors.ErrTransient, logString)
		case enums.OverallStatus_OVERALL_STATUS_CALL_FAILED:
			// TODO(vikas): show context that your last call failed?
			// res.NextAction = next action DL
			errString := "vkyc call failed"
			lg.Error(errString, zap.String(logger.STATUS, callStatusString))
			return res, errors.Wrap(epifierrors.ErrPermanent, errString)
		default:
			// Customer has not completed the call yet. Show screen to complete the call.
		}

		vkycApplicationId := gcasResp.GetActionMetadata().GetVkycMetadata().GetApplicationId()
		vkycCallId := gcasResp.GetActionMetadata().GetVkycMetadata().GetCallId()

		if vkycApplicationId == "" || vkycCallId == "" {
			// mark application manual intervention
			errString := "vkyc application or call id cannot be nil"
			lg.Error(errString, zap.String("vkyc_application_id", vkycApplicationId), zap.String("vkyc_call_id", vkycCallId))
			return res, errors.Wrap(epifierrors.ErrPermanent, errString)
		}

		// update call id in lse details
		if lse.GetDetails().GetVkycStepData().GetCallId() != vkycCallId {
			lse.GetDetails().GetVkycStepData().CallId = vkycCallId
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_INITIATED
			if updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			}); updateErr != nil {
				lg.Error("failed to update call id in lse", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update call id in lse, err: %v", updateErr))
			}
		}

		// generate redirect dl to initiate vkyc call
		pollingDeeplink := deeplinkPal.GetApplicationStatusPollDeeplinkWithParams(&palFeEnumsPb.LoanHeader{
			LoanProgram: deeplinkPal.GetFeLoanProgramFromBe(lr.GetLoanProgram()),
			Vendor:      deeplinkPal.GetPalFeVendorFromBe(lr.GetVendor()),
		}, req.GetLoanStep().GetRefId(), &provider.ApplicationStatusPollDeeplinkParams{
			GetNextActionInSync: true,
			Title:               nulltypes.NewNullString("Please wait"),
		})

		// fetch vkyc next action deeplink
		vkycNextActionDl, vkycNextActionDlErr := vkyc.GetVKYCNextActionDeeplinkForInHouseVKYC(&deeplink.GetVKYCNextActionApiScreenOptions{
			ClientLastState: vkyc2.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
			EntryPoint:      vkyc2.EntryPoint_ENTRY_POINT_STOCKGUARDIAN_LOANS.String(),
			ShowCtaLoader:   true,
		}, &vkyc.InHouseVkycNextActionBlob{
			ApplicationId:         vkycApplicationId,
			CallId:                vkycCallId,
			VideoCallExitDeeplink: pollingDeeplink,
		})
		if vkycNextActionDlErr != nil {
			errString := "failed to get vkyc next action deeplink"
			lg.Error(errString, zap.Error(vkycNextActionDlErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("%s, err: %v", errString, vkycNextActionDlErr))
		}
		res.GetLseFieldMasks()
		res.NextAction = vkycNextActionDl
		logString := "waiting for customer to finish the vkyc call"
		lg.Info(logString, zap.String(logger.STAGE_STATUS, gcasResp.GetStageStatus().String()))
		return res, errors.Wrap(epifierrors.ErrTransient, logString)
	})
	return actRes, actErr
}
