package model

import (
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

// Credit Card Statement Version Constants
//
// These constants define the different versions of credit card statement templates
// used during the Fi-Coins to Fi-Points migration and beyond. Each version controls
// how rewards are displayed in PDF statements based on the statement period's
// relationship to the migration timeline.

const (
	// CREDIT_CARD_STATEMENT_VERSION_V0 represents the legacy statement format
	// used for statement periods that occur entirely before the Fi-Coins to Fi-Points migration.
	//
	// Characteristics:
	// - Displays only Fi-Coins reward columns
	// - Uses Fi-Coins branding and terminology
	// - Maintains historical consistency for pre-migration statements
	// - Template shows: "Fi-Coins" column only
	//
	// Usage: Applied when statement.toDate < migrationTime
	CREDIT_CARD_STATEMENT_VERSION_V0 = "V0"

	// CREDIT_CARD_STATEMENT_VERSION_V1 represents the transition statement format
	// used for statement periods that span across the Fi-Coins to Fi-Points migration boundary.
	//
	// Characteristics:
	// - Displays both Fi-Coins and Fi-Points reward columns
	// - Shows "NEW" tag next to Fi-Points column header
	// - Handles mixed reward data (Fi-Coins for pre-migration, Fi-Points for post-migration)
	// - Template shows: "Fi-Points [NEW]" and "Fi-Coins" columns
	// - Ensures complete transaction visibility during migration period
	//
	// Usage: Applied when statement period overlaps with migrationTime
	//        (fromDate <= migrationTime <= toDate)
	CREDIT_CARD_STATEMENT_VERSION_V1 = "V1"

	// CREDIT_CARD_STATEMENT_VERSION_V2 represents the current statement format
	// used for statement periods that occur entirely after the Fi-Coins to Fi-Points migration.
	//
	// Characteristics:
	// - Displays only Fi-Points reward columns
	// - Uses Fi-Points branding and terminology
	// - Reflects the current reward system post-migration
	// - Template shows: "Fi-Points" column only (no NEW tag)
	// - Optimized for the new reward structure
	//
	// Usage: Applied when statement.fromDate > migrationTime
	CREDIT_CARD_STATEMENT_VERSION_V2 = "V2"
)

type StatementPdf struct {
	UserDetails *StatementUserDetail
	// date from which the statement has been fetched
	FromDate *date.Date
	// date upto which the statement has been fetched
	ToDate                    *date.Date
	Summary                   *StatementSummary
	TransactionDetails        []*StatementTransaction
	RewardsSummary            *RewardsSummary
	ExtraRewardsInfo          []*ExtraRewardsInfo
	TipsAndInformationSection *TipsAndInformationSection
	FeeBreakDown              *FeeBreakDown
	ContactUsDetails          *ContactUsDetails
	EmiSummary                *EmiSummary
	CreditCardProgramLogo     string
	FiBrandLogo               string
	PartnerBankLogo           string
	TransactionModelVersion   string
}

type StatementSummary struct {
	// date on which the statement has been created
	StatementDate *date.Date
	// date on which the payment is due
	PaymentDueDate *date.Date
	// amount of money available
	AvailableLimit *money.Money
	// total amount due
	TotalAmountDue *money.Money
	// minimum amount due
	MinAmountDue *money.Money
	// Opening balance on the date from which statement begins
	OpeningBalance *money.Money
	// summation of all expense transactions
	Spends *money.Money
	// interest accrued
	Interest *money.Money
	// total fees
	Fees *money.Money
	// repayments and returns for the statement period
	RepaymentsAndRefunds *money.Money
	// spent amount converted to emi
	SpendsConvertedToEmi *money.Money
}

type StatementUserDetail struct {
	// name of the user
	Name string
	// contact number of the user
	PhoneNumber uint64
	// email id of the user
	Email string
	// permanent address of the user
	Address string
	// masked credit card number of the user
	MaskedCreditCardNumber string
}

type StatementTransaction struct {
	// amount of money transacted (in INR)
	Amount *money.Money
	// date of transaction
	Date         *timestampPb.Timestamp
	MerchantName string
	// location where the transaction happened
	MerchantLocation  string
	TransactionOrigin string
	Category          string
	// Type of transaction - debit or credit
	TransactionType string
	PaymentMethod   string
	// Reward Coins
	RewardCoins int32
	// Reward Points
	RewardPoints int32
}

type RewardsSummary struct {
	// Whether the reward is 2x/5x
	Title                  string
	MerchantWiseRewardInfo []*MerchantWiseRewardInfo
	BottomText             string
	RewardCoinsSummary     *RewardCoinsSummary
}

type MerchantWiseRewardInfo struct {
	MerchantIconUrl        string
	MerchantDisplayName    string
	TotalAmountSpent       *money.Money
	TotalRewardCoinsEarned int32
}

type RewardCoinsSummary struct {
	Title                  string
	TotalRewardCoinsEarned int32
	TotalFiCoinsText       string
	TotalFiCoins           int32
}

type TipsAndInformationSection struct {
	IconUrl         string
	Title           string
	InformationText []InformationText
}

type InformationText struct {
	Title                 string
	TextWithLinkAndValues []*TextWithLinkAndValue
}

type FeeBreakDown struct {
	Title                  string
	FeeBreakDownComponents []*FeeBreakDownComponents
}

type FeeBreakDownComponents struct {
	FeeType string
	Amount  *money.Money
	// PLUS OR MINUS
	FeeAmountType string
}

type ContactUsDetails struct {
	Title               string
	CustomerCareDetails []*CustomerCareDetails
	PostalAddress       string
	TnCSection          *TnCSection
	IssuerDetails       *IssuerDetails
}

type CustomerCareDetails struct {
	Title                    string
	CustomerCareOrganisation string
	Email                    string
	Phone                    string
	Recommended              bool
	WebText                  string
	WebLinkText              string
	WebLink                  string
	TextWithLinkAndValues    []*TextWithLinkAndValue
}

type ExtraRewardsInfo struct {
	Text              string
	RewardCoinsEarned int32
	RewardTypeLogo    string
}

type EmiSummary struct {
	NoOfActiveEmi int32
	DueAmount     *money.Money
	EmiDetails    []*EmiDetail
}

type EmiDetail struct {
	MerchantName      string
	InstallmentNumber int32
	TotalInstallment  int32
	Amount            *money.Money
}

type TnCSection struct {
	Text                  string
	TextWithLinkAndValues []*TextWithLinkAndValue
}

type IssuerDetails struct {
	Title                      string
	LeftTextWithLinkAndValues  []*LeftTextWithLinkAndValue
	RightTextWithLinkAndValues []*RightTextWithLinkAndValue
}

type TextWithLinkAndValue struct {
	Title string
	Value string
	Link  string
	Type  string
}

// LeftTextWithLinkAndValue for positioning content on left in html
type LeftTextWithLinkAndValue struct {
	Title string
	Value string
	Link  string
}

// RightTextWithLinkAndValue for positioning content on right in html
type RightTextWithLinkAndValue struct {
	Title string
	Value string
	Link  string
}
