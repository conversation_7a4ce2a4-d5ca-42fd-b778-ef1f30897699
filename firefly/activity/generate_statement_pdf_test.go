package activity

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/date"

	ccFireflyBillingModel "github.com/epifi/gamma/firefly/billing/model"
)

func TestDetermineStatementVersion(t *testing.T) {
	// Helper function to create date
	createDate := func(year int32, month int32, day int32) *date.Date {
		return &date.Date{
			Year:  year,
			Month: month,
			Day:   day,
		}
	}

	tests := []struct {
		name            string
		fromDate        *date.Date
		toDate          *date.Date
		expectedVersion string
		description     string
	}{
		// Basic Cases - Migration date is 2025-08-01
		{
			name:            "V0 - Statement period completely before migration",
			fromDate:        createDate(2025, 6, 15),
			toDate:          createDate(2025, 7, 14), // Statement window ends before migration (Aug 1)
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V0,
			description:     "Statement period ends before migration - should use V0 (Fi-Coins only)",
		},
		{
			name:            "V2 - Statement period completely after migration",
			fromDate:        createDate(2025, 8, 2),
			toDate:          createDate(2025, 8, 31), // Statement window starts after migration (Aug 1)
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V2,
			description:     "Statement period starts after migration - should use V2 (Fi-Points only)",
		},
		{
			name:            "V1 - Statement period spans migration",
			fromDate:        createDate(2025, 7, 15),
			toDate:          createDate(2025, 8, 14), // Statement window spans migration (Aug 1)
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V1,
			description:     "Statement period spans migration - should use V1 (both Fi-Coins and Fi-Points)",
		},

		// Edge Cases - Migration boundary testing
		{
			name:            "V0 - Statement ends exactly on migration day",
			fromDate:        createDate(2025, 7, 2),
			toDate:          createDate(2025, 8, 1), // Statement window ends exactly on migration day
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V1,
			description:     "Statement ends exactly on migration day - should use V1",
		},
		{
			name:            "V1 - Statement starts exactly on migration day",
			fromDate:        createDate(2025, 8, 1),
			toDate:          createDate(2025, 8, 31), // Statement window starts exactly on migration day
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V1,
			description:     "Statement starts exactly on migration day - should use V1",
		},
		{
			name:            "V0 - Statement ends day before migration",
			fromDate:        createDate(2025, 7, 1),
			toDate:          createDate(2025, 7, 31), // Statement window ends day before migration
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V0,
			description:     "Statement ends day before migration - should use V0",
		},
		{
			name:            "V2 - Statement starts day after migration",
			fromDate:        createDate(2025, 8, 2),
			toDate:          createDate(2025, 9, 1), // Statement window starts day after migration
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V2,
			description:     "Statement starts day after migration - should use V2",
		},

		// Edge Cases - Same months
		{
			name:            "V0 - Statement in month before migration",
			fromDate:        createDate(2025, 6, 1),
			toDate:          createDate(2025, 6, 30), // Complete month before migration
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V0,
			description:     "Complete month before migration - should use V0",
		},
		{
			name:            "V2 - Statement in month after migration",
			fromDate:        createDate(2025, 9, 1),
			toDate:          createDate(2025, 9, 30), // Complete month after migration
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V2,
			description:     "Complete month after migration - should use V2",
		},

		// Edge Cases - Year boundaries
		{
			name:            "V0 - Statement in previous year",
			fromDate:        createDate(2024, 12, 1),
			toDate:          createDate(2024, 12, 31), // Previous year, well before migration
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V0,
			description:     "Statement in year before migration - should use V0",
		},
		{
			name:            "V2 - Statement in year after migration",
			fromDate:        createDate(2026, 1, 1),
			toDate:          createDate(2026, 1, 31), // Next year, well after migration
			expectedVersion: ccFireflyBillingModel.CREDIT_CARD_STATEMENT_VERSION_V2,
			description:     "Statement in year after migration - should use V2",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := determineStatementVersion(tt.fromDate, tt.toDate)
			require.Equal(t, tt.expectedVersion, result,
				"Test: %s\nDescription: %s\nFrom: %v, To: %v",
				tt.name, tt.description, tt.fromDate, tt.toDate)
		})
	}
}
