// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	awswire "github.com/epifi/be-common/pkg/aws/v2/wire"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire2 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	temporalcl "github.com/epifi/be-common/pkg/epifitemporal/client"
	namespace "github.com/epifi/be-common/pkg/epifitemporal/namespace"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	operationalstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	actor "github.com/epifi/gamma/api/actor"
	auth "github.com/epifi/gamma/api/auth"
	bankcustpb "github.com/epifi/gamma/api/bankcust"
	cardpb "github.com/epifi/gamma/api/card/provisioning"
	categorizerpb "github.com/epifi/gamma/api/categorizer"
	comms "github.com/epifi/gamma/api/comms"
	inapptargetedcommspb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	connectedaccountpb "github.com/epifi/gamma/api/connected_account"
	caanalytics "github.com/epifi/gamma/api/connected_account/analytics"
	caconsumer "github.com/epifi/gamma/api/connected_account/consumer"
	dataanalytics2 "github.com/epifi/gamma/api/connected_account/data_analytics"
	cadevpb "github.com/epifi/gamma/api/connected_account/developer"
	securities2 "github.com/epifi/gamma/api/connected_account/securities"
	consent "github.com/epifi/gamma/api/consent"
	creditreportv2pb "github.com/epifi/gamma/api/creditreportv2"
	watsonpb "github.com/epifi/gamma/api/cx/watson"
	datasharing "github.com/epifi/gamma/api/datasharing"
	docs "github.com/epifi/gamma/api/docs"
	esignpb "github.com/epifi/gamma/api/docs/esign"
	emppb "github.com/epifi/gamma/api/employment"
	aggregator "github.com/epifi/gamma/api/investment/aggregator"
	nudgepb "github.com/epifi/gamma/api/nudge"
	orderpb "github.com/epifi/gamma/api/order"
	aaorderpb "github.com/epifi/gamma/api/order/aa"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	reconpb "github.com/epifi/gamma/api/order/recon"
	parserpb "github.com/epifi/gamma/api/parser"
	pay "github.com/epifi/gamma/api/pay"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
	productpb "github.com/epifi/gamma/api/product"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	recurringpaymentpb "github.com/epifi/gamma/api/recurringpayment"
	salaryestimationpb "github.com/epifi/gamma/api/salaryestimation"
	salestdevpb "github.com/epifi/gamma/api/salaryestimation/developer"
	salaryprogrampb "github.com/epifi/gamma/api/salaryprogram"
	consumer4 "github.com/epifi/gamma/api/salaryprogram/consumer"
	salaryprogramcxpb "github.com/epifi/gamma/api/salaryprogram/cx"
	cxconsumer "github.com/epifi/gamma/api/salaryprogram/cx/consumer"
	salaryprogramdevpb "github.com/epifi/gamma/api/salaryprogram/developer"
	dynamicuielementpb "github.com/epifi/gamma/api/salaryprogram/dynamic_ui_element"
	healthinsurancepb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	consumer5 "github.com/epifi/gamma/api/salaryprogram/healthinsurance/consumer"
	salaryreferralspb "github.com/epifi/gamma/api/salaryprogram/referrals"
	savingspb "github.com/epifi/gamma/api/savings"
	consumer6 "github.com/epifi/gamma/api/savings/consumer"
	savingsdeveloper "github.com/epifi/gamma/api/savings/developer"
	extacct "github.com/epifi/gamma/api/savings/extacct"
	extacccopb "github.com/epifi/gamma/api/savings/extacct/consumer"
	savingswatsonclientpb "github.com/epifi/gamma/api/savings/watson"
	screener "github.com/epifi/gamma/api/screener"
	searchpb "github.com/epifi/gamma/api/search"
	segmentpb "github.com/epifi/gamma/api/segment"
	tieringpb "github.com/epifi/gamma/api/tiering"
	tieringcopb "github.com/epifi/gamma/api/tiering/consumer"
	tieringdevpb "github.com/epifi/gamma/api/tiering/developer"
	tieringpinotpb "github.com/epifi/gamma/api/tiering/pinot"
	timeline "github.com/epifi/gamma/api/timeline"
	upipb "github.com/epifi/gamma/api/upi"
	upionboardingpb "github.com/epifi/gamma/api/upi/onboarding"
	user "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	onboardingpb "github.com/epifi/gamma/api/user/onboarding"
	userintelpb "github.com/epifi/gamma/api/userintel"
	ussaccountpb "github.com/epifi/gamma/api/usstocks/account"
	ussorderpb "github.com/epifi/gamma/api/usstocks/order"
	vgaapb "github.com/epifi/gamma/api/vendorgateway/aa"
	vgbcpb "github.com/epifi/gamma/api/vendorgateway/bouncycastle"
	vgemploymentpb "github.com/epifi/gamma/api/vendorgateway/employment"
	extvalidate "github.com/epifi/gamma/api/vendorgateway/extvalidate"
	fennelvgpb "github.com/epifi/gamma/api/vendorgateway/fennel"
	riskcovryvgpb "github.com/epifi/gamma/api/vendorgateway/healthinsurance/riskcovry"
	incomeestimator "github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	ncpb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	employernamecategoriser "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	employernamematchvgpb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	onsurityvgpb "github.com/epifi/gamma/api/vendorgateway/onsurity"
	vgaccpb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	ovgsavings "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	vgparserpb "github.com/epifi/gamma/api/vendorgateway/parser"
	vgscienapticpb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	vmpb "github.com/epifi/gamma/api/vendormapping"
	wopb "github.com/epifi/gamma/api/wealthonboarding"
	hook "github.com/epifi/gamma/cmd/servers/qa/central-growth/hook"
	connectedaccountconf "github.com/epifi/gamma/connectedaccount/config"
	genconf2 "github.com/epifi/gamma/connectedaccount/config/genconf"
	connectedaccountsecuritiesconf "github.com/epifi/gamma/connectedaccount/securities/config"
	wire2 "github.com/epifi/gamma/connectedaccount/securities/wire"
	wire "github.com/epifi/gamma/connectedaccount/wire"
	catypes "github.com/epifi/gamma/connectedaccount/wire/types"
	datasharingconf "github.com/epifi/gamma/datasharing/config"
	wire6 "github.com/epifi/gamma/datasharing/wire"
	docstypes "github.com/epifi/gamma/docs/wire/types"
	dynamicelewiretypes "github.com/epifi/gamma/dynamicelements/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	customdelayqueue "github.com/epifi/gamma/pkg/customdelayqueue"
	customqueuewire "github.com/epifi/gamma/pkg/customdelayqueue/wire"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	salaryestimationconf "github.com/epifi/gamma/salaryestimation/config"
	genconf5 "github.com/epifi/gamma/salaryestimation/config/genconf"
	wire5 "github.com/epifi/gamma/salaryestimation/wire"
	salaryprogramconf "github.com/epifi/gamma/salaryprogram/config"
	genconf4 "github.com/epifi/gamma/salaryprogram/config/genconf"
	wire4 "github.com/epifi/gamma/salaryprogram/wire"
	salarytypes "github.com/epifi/gamma/salaryprogram/wire/types"
	savingsconf "github.com/epifi/gamma/savings/config"
	genconf6 "github.com/epifi/gamma/savings/config/genconf"
	wire7 "github.com/epifi/gamma/savings/wire"
	wiretypes "github.com/epifi/gamma/savings/wire/types"
	tieringconf "github.com/epifi/gamma/tiering/config"
	genconf3 "github.com/epifi/gamma/tiering/config/genconf"
	wire3 "github.com/epifi/gamma/tiering/wire"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.CENTRAL_GROWTH_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.CENTRAL_GROWTH_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.CENTRAL_GROWTH_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.CENTRAL_GROWTH_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	var dbConnTeardown func()
	crdbResourceMap, crdbTxnResourceMap, dbConnTeardown, err = storage2.NewDBResourceProviderV2(conf.DBConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to get db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		dbConnTeardown()
	}()

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()

	connectedAccountPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["ConnectedAccountPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "ConnectedAccountPGDB"))
		return err
	}
	connectedAccountPGDBSqlDb, err := connectedAccountPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "ConnectedAccountPGDB"))
		return err
	}
	defer func() { _ = connectedAccountPGDBSqlDb.Close() }()
	featureEngineeringPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["FeatureEngineeringPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "FeatureEngineeringPGDB"))
		return err
	}
	featureEngineeringPGDBSqlDb, err := featureEngineeringPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "FeatureEngineeringPGDB"))
		return err
	}
	defer func() { _ = featureEngineeringPGDBSqlDb.Close() }()
	tieringPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["TieringPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "TieringPGDB"))
		return err
	}
	tieringPGDBSqlDb, err := tieringPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "TieringPGDB"))
		return err
	}
	defer func() { _ = tieringPGDBSqlDb.Close() }()
	salaryprogramPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["SalaryprogramPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "SalaryprogramPGDB"))
		return err
	}
	salaryprogramPGDBSqlDb, err := salaryprogramPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "SalaryprogramPGDB"))
		return err
	}
	defer func() { _ = salaryprogramPGDBSqlDb.Close() }()

	attributeRateLimiter := ratelimiter.NewAttributeRateLimiter(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcAttributeRatelimiterParams().Namespace())
	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	growthinfraConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	managerClient := managerpb.NewManagerClient(growthinfraConn)
	authConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.AUTH_SERVER)
	defer epifigrpc.CloseConn(authConn)
	groupClient := usergrouppb.NewGroupClient(authConn)
	usersClient := user.NewUsersClient(authConn)
	actorConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.ACTOR_SERVER)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actor.NewActorClient(actorConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	accountAggregatorClient := vgaapb.NewAccountAggregatorClient(vendorgatewayConn)
	bouncyCastleClient := vgbcpb.NewBouncyCastleClient(vendorgatewayConn)
	growthinfraConnVar11ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewConnectedAccountRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		growthinfraConnVar11ClientInterceptors = append(growthinfraConnVar11ClientInterceptors, unaryClientInterceptor)
	}
	growthinfraConnVar11 := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar11ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar11)
	commsClient := comms.NewCommsClient(growthinfraConnVar11)
	docsConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.DOCS_SERVER)
	defer epifigrpc.CloseConn(docsConn)
	docsClient := docs.NewDocsClient(docsConn)
	connectedAccountRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["ConnectedAccountRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = connectedAccountRedisStore.Close() }()
	onboardingClient := onboardingpb.NewOnboardingClient(authConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	salaryProgramClient := salaryprogrampb.NewSalaryProgramClient(centralgrowthConn)
	uNNameCheckClient := ncpb.NewUNNameCheckClient(vendorgatewayConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	wealthOnboardingClient := wopb.NewWealthOnboardingClient(wealthdmfConn)
	incomeEstimatorClient := incomeestimator.NewIncomeEstimatorClient(vendorgatewayConn)
	parserClient := parserpb.NewParserClient(docsConn)
	piClient := pipb.NewPiClient(actorConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(actorConn)
	orderConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.ORDER_SERVER)
	defer epifigrpc.CloseConn(orderConn)
	accountAggregatorClientVar2 := aaorderpb.NewAccountAggregatorClient(orderConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	userIntelServiceClient := userintelpb.NewUserIntelServiceClient(onboardingConn)
	screenerClient := screener.NewScreenerClient(onboardingConn)
	lendingConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	preApprovedLoanClient := preapprovedloan.NewPreApprovedLoanClient(lendingConn)
	creditReportManagerClient := creditreportv2pb.NewCreditReportManagerClient(lendingConn)
	connectedAccountClient := connectedaccountpb.NewConnectedAccountClient(centralgrowthConn)
	analyticsClient := caanalytics.NewAnalyticsClient(centralgrowthConn)
	vendormappingConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vmpb.NewVendorMappingServiceClient(vendormappingConn)
	bankCustomerServiceClient := bankcustpb.NewBankCustomerServiceClient(onboardingConn)
	tieringActorRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["TieringActorRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = tieringActorRedisStore.Close() }()
	tieringConnectedAccountRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["TieringConnectedAccountRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = tieringConnectedAccountRedisStore.Close() }()
	commsClientVar3 := comms.NewCommsClient(growthinfraConn)
	authClient := auth.NewAuthClient(authConn)
	balanceClient := accountbalancepb.NewBalanceClient(actorConn)
	eODBalanceClient := tieringpinotpb.NewEODBalanceClient(centralgrowthConn)
	payClient := pay.NewPayClient(orderConn)
	orderManagerClient := ussorderpb.NewOrderManagerClient(wealthdmfConn)
	accountManagerClient := ussaccountpb.NewAccountManagerClient(wealthdmfConn)
	investmentAggregatorClient := aggregator.NewInvestmentAggregatorClient(wealthdmfConn)
	salaryProgramRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["SalaryProgramRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = salaryProgramRedisStore.Close() }()
	employmentClient := emppb.NewEmploymentClient(onboardingConn)
	orderServiceClient := orderpb.NewOrderServiceClient(orderConn)
	txnCategorizerClient := categorizerpb.NewTxnCategorizerClient(wealthdmfConn)
	employerNameMatchClient := employernamematchvgpb.NewEmployerNameMatchClient(vendorgatewayConn)
	paymentClient := vgpaymentpb.NewPaymentClient(vendorgatewayConn)
	nudgeServiceClient := nudgepb.NewNudgeServiceClient(growthinfraConn)
	employmentClientVar2 := vgemploymentpb.NewEmploymentClient(vendorgatewayConn)
	recurringPaymentServiceClient := recurringpaymentpb.NewRecurringPaymentServiceClient(orderConn)
	timelineServiceClient := timeline.NewTimelineServiceClient(actorConn)
	employerNameCategoriserClient := employernamecategoriser.NewEmployerNameCategoriserClient(vendorgatewayConn)
	operationalStatusServiceClient := operationalstatuspb.NewOperationalStatusServiceClient(actorConn)
	parserClientVar2 := vgparserpb.NewParserClient(vendorgatewayConn)
	inAppTargetedCommsClient := inapptargetedcommspb.NewInAppTargetedCommsClient(growthinfraConn)
	fennelFeatureStoreClient := fennelvgpb.NewFennelFeatureStoreClient(vendorgatewayConn)
	scienapticClient := vgscienapticpb.NewScienapticClient(vendorgatewayConn)
	tieringClient := tieringpb.NewTieringClient(centralgrowthConn)
	riskcovryClient := riskcovryvgpb.NewRiskcovryClient(vendorgatewayConn)
	onSurityClient := onsurityvgpb.NewOnSurityClient(vendorgatewayConn)
	dataAnalyticsClient := dataanalytics2.NewDataAnalyticsClient(centralgrowthConn)
	consentClient := consent.NewConsentClient(authConn)
	dataSharingClient := datasharing.NewDataSharingClient(centralgrowthConn)
	salaryEstimationClient := salaryestimationpb.NewSalaryEstimationClient(centralgrowthConn)
	savingsClientVar8 := ovgsavings.NewSavingsClient(vendorgatewayConn)
	actionBarClient := searchpb.NewActionBarClient(wealthdmfConn)
	ledgerReconciliationClient := reconpb.NewLedgerReconciliationClient(orderConn)
	userSavingsRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserSavingsRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userSavingsRedisStore.Close() }()
	growthinfraConnVar26ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar4 := servergenwire.NewSavingsRequestClientInterceptor()
	if unaryClientInterceptorVar4 != nil {
		growthinfraConnVar26ClientInterceptors = append(growthinfraConnVar26ClientInterceptors, unaryClientInterceptorVar4)
	}
	growthinfraConnVar26 := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar26ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar26)
	inAppTargetedCommsClientVar3 := inapptargetedcommspb.NewInAppTargetedCommsClient(growthinfraConnVar26)
	balHistoryRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["BalHistoryRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = balHistoryRedisStore.Close() }()
	docsConnVar3ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar3 := servergenwire.NewSavingsRequestClientInterceptor()
	if unaryClientInterceptorVar3 != nil {
		docsConnVar3ClientInterceptors = append(docsConnVar3ClientInterceptors, unaryClientInterceptorVar3)
	}
	docsConnVar3 := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.DOCS_SERVER, docsConnVar3ClientInterceptors...)
	defer epifigrpc.CloseConn(docsConnVar3)
	eSignClient := esignpb.NewESignClient(docsConnVar3)
	savingsRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["SavingsRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for SavingsRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { savingsRueidisRedisStore.Close() }()
	savingsRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(savingsRueidisRedisStore), gconf.RueidisRedisClients()["SavingsRueidisRedisStore"].Hystrix)
	paymentClientVar3 := paymentpb.NewPaymentClient(orderConn)
	externalAccountsClient := extacct.NewExternalAccountsClient(centralgrowthConn)
	externalValidateClient := extvalidate.NewExternalValidateClient(vendorgatewayConn)
	cxConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	watsonClient := watsonpb.NewWatsonClient(cxConn)
	cardConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	cardProvisioningClient := cardpb.NewCardProvisioningClient(cardConn)
	growthinfraConnVar29ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar5 := servergenwire.NewSavingsRequestClientInterceptor()
	if unaryClientInterceptorVar5 != nil {
		growthinfraConnVar29ClientInterceptors = append(growthinfraConnVar29ClientInterceptors, unaryClientInterceptorVar5)
	}
	growthinfraConnVar29 := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar29ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar29)
	commsClientVar7 := comms.NewCommsClient(growthinfraConnVar29)
	docsConnVar4ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire.NewSavingsRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		docsConnVar4ClientInterceptors = append(docsConnVar4ClientInterceptors, unaryClientInterceptorVar2)
	}
	docsConnVar4 := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.DOCS_SERVER, docsConnVar4ClientInterceptors...)
	defer epifigrpc.CloseConn(docsConnVar4)
	docsClientVar2 := docs.NewDocsClient(docsConnVar4)
	uPIClient := upipb.NewUPIClient(orderConn)
	upiOnboardingClient := upionboardingpb.NewUpiOnboardingClient(orderConn)
	productClient := productpb.NewProductClient(onboardingConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.CENTRAL_GROWTH_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	accountsClient := vgaccpb.NewAccountsClient(vendorgatewayConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire2.RateLimitServerInterceptorV2WithDefaultKeyGen(gconf, rateLimiter, attributeRateLimiter)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3, err := servergenwire2.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	err = setupConnectedaccount(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, connectedAccountPGDB, accountAggregatorClient, bouncyCastleClient, commsClient, docsClient, connectedAccountRedisStore, onboardingClient, savingsClient, salaryProgramClient, uNNameCheckClient, wealthOnboardingClient, incomeEstimatorClient, parserClient, piClient, accountPIRelationClient, accountAggregatorClientVar2, userIntelServiceClient, screenerClient, preApprovedLoanClient, creditReportManagerClient, connectedAccountClient, featureEngineeringPGDB, analyticsClient, vendorMappingServiceClient)
	if err != nil {
		return err
	}
	err = setupConnectedaccountsecurities(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, connectedAccountPGDB)
	if err != nil {
		return err
	}
	err = setupTiering(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, tieringPGDB, savingsClient, bankCustomerServiceClient, salaryProgramClient, tieringActorRedisStore, tieringConnectedAccountRedisStore, commsClientVar3, authClient, balanceClient, eODBalanceClient, onboardingClient, payClient, orderManagerClient, accountManagerClient, investmentAggregatorClient, piClient, accountPIRelationClient)
	if err != nil {
		return err
	}
	err = setupSalaryprogram(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, salaryprogramPGDB, salaryProgramRedisStore, piClient, employmentClient, orderServiceClient, txnCategorizerClient, employerNameMatchClient, savingsClient, paymentClient, nudgeServiceClient, employmentClientVar2, bankCustomerServiceClient, recurringPaymentServiceClient, timelineServiceClient, employerNameCategoriserClient, onboardingClient, commsClientVar3, operationalStatusServiceClient, connectedAccountClient, parserClientVar2, inAppTargetedCommsClient, fennelFeatureStoreClient, scienapticClient, tieringClient, salaryProgramClient, riskcovryClient, onSurityClient, vendorMappingServiceClient)
	if err != nil {
		return err
	}
	err = setupSalaryestimation(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, featureEngineeringPGDB, analyticsClient, dataAnalyticsClient, consentClient, dataSharingClient, connectedAccountClient, preApprovedLoanClient)
	if err != nil {
		return err
	}
	err = setupDatasharing(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, salaryEstimationClient, connectedAccountClient)
	if err != nil {
		return err
	}
	err = setupSavings(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, usersClient, actorClient, savingsClientVar8, authClient, actionBarClient, accountPIRelationClient, ledgerReconciliationClient, groupClient, userSavingsRedisStore, inAppTargetedCommsClientVar3, balHistoryRedisStore, bankCustomerServiceClient, payClient, operationalStatusServiceClient, eSignClient, orderServiceClient, balanceClient, nudgeServiceClient, savingsRueidisCacheStorage, onboardingClient, paymentClientVar3, externalAccountsClient, uNNameCheckClient, externalValidateClient, savingsClient, watsonClient, cardProvisioningClient, commsClientVar7, docsClientVar2, tieringClient, consentClient, piClient, uPIClient, upiOnboardingClient, productClient, celestialClient, salaryProgramClient, accountsClient)
	if err != nil {
		return err
	}
	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitSavingsServer(epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitSavingsServer"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := servergenwire2.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupConnectedaccount(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	connectedAccountPGDB types.ConnectedAccountPGDB,
	accountAggregatorClient vgaapb.AccountAggregatorClient,
	bouncyCastleClient vgbcpb.BouncyCastleClient,
	commsClient catypes.CaCommsClientWithInterceptors,
	docsClient docs.DocsClient,
	connectedAccountRedisStore types.ConnectedAccountRedisStore,
	onboardingClient onboardingpb.OnboardingClient,
	savingsClient savingspb.SavingsClient,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	uNNameCheckClient ncpb.UNNameCheckClient,
	wealthOnboardingClient wopb.WealthOnboardingClient,
	incomeEstimatorClient incomeestimator.IncomeEstimatorClient,
	parserClient parserpb.ParserClient,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	accountAggregatorClientVar2 aaorderpb.AccountAggregatorClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	screenerClient screener.ScreenerClient,
	preApprovedLoanClient preapprovedloan.PreApprovedLoanClient,
	creditReportManagerClient creditreportv2pb.CreditReportManagerClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	featureEngineeringPGDB types.FeatureEngineeringPGDB,
	analyticsClient caanalytics.AnalyticsClient,
	vendorMappingServiceClient vmpb.VendorMappingServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	connectedaccountConf, err := connectedaccountconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CONNECTED_ACC_SERVICE))
		return err
	}
	_ = connectedaccountConf

	connectedaccountGenConf, err := dynconf.LoadConfigWithQuestConfig(connectedaccountconf.Load, genconf2.NewConfigWithQuest, cfg.CONNECTED_ACC_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CONNECTED_ACC_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		connectedaccountGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: connectedaccountGenConf, SdkConfig: connectedaccountGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{connectedaccountGenConfAppConfig}, string(cfg.CENTRAL_GROWTH_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = connectedaccountGenConf

	processConsentSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.ProcessConsentSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	fetchDataSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.FetchDataSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	transactionBatchProcessDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.TransactionBatchProcessPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	createAttemptSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.CreateAttemptSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	processConsentSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.ProcessConsentSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	sendNotificationSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.SendNotificationSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	fetchDataSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.FetchDataSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	processConsentDataRefreshSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.ProcessConsentDataRefreshSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	captureColumnUpdateSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.CaptureColumnUpdateSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	captureHeartbeatAndSendNotificationSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.CaptureHeartbeatAndSendNotificationSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	firstDataPullSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.FirstDataPullSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	firstDataPullSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, connectedaccountGenConf.FirstDataPullSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	transactionEventExternalPublisher, err := sns.NewSnsPublisherWithConfig(ctx, connectedaccountGenConf.TransactionEventExternalPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	accountUpdateEventExternalPublisher, err := sns.NewSnsPublisherWithConfig(ctx, connectedaccountGenConf.AccountUpdateEventExternalPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	aaTxnBackfillPublisher, err := sns.NewSnsPublisherWithConfig(ctx, connectedaccountGenConf.AaTxnBackfillPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	accountDataSyncExternalPublisher, err := sns.NewSnsPublisherWithConfig(ctx, connectedaccountGenConf.AccountDataSyncExternalPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	cATxnBackfillS3Client := s3pkg.NewClient(awsConf, connectedaccountGenConf.CATxnBackfillBucketName())
	docsS3Client := s3pkg.NewClient(awsConf, connectedaccountGenConf.DocsBucketName())
	caUserTxnDataS3Client := s3pkg.NewClient(awsConf, connectedaccountGenConf.CaUserTxnDataBucketName())
	caAnalyticsS3Client := s3pkg.NewClient(awsConf, connectedaccountGenConf.CaAnalyticsBucketName())

	connectedAccountClientVar2, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.ConnectedAccount, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	connectedAccountClientVar3, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.ConnectedAccount, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	caService := wire.InitializeCaService(connectedAccountPGDB, accountAggregatorClient, connectedaccountGenConf, processConsentSqsPublisher, fetchDataSqsDelayPublisher, bouncyCastleClient, transactionBatchProcessDelayPublisher, transactionEventExternalPublisher, accountUpdateEventExternalPublisher, createAttemptSqsPublisher, processConsentSqsDelayPublisher, broker, actorClient, usersClient, groupClient, commsClient, sendNotificationSqsDelayPublisher, fetchDataSqsPublisher, processConsentDataRefreshSqsPublisher, captureColumnUpdateSqsPublisher, captureHeartbeatAndSendNotificationSqsPublisher, docsClient, firstDataPullSqsDelayPublisher, connectedAccountRedisStore, firstDataPullSqsPublisher, onboardingClient, savingsClient, salaryProgramClient, uNNameCheckClient, wealthOnboardingClient, segmentationServiceClient, incomeEstimatorClient, parserClient, piClient, accountPIRelationClient, aaTxnBackfillPublisher, cATxnBackfillS3Client, accountAggregatorClientVar2, userIntelServiceClient, screenerClient, docsS3Client, preApprovedLoanClient, accountDataSyncExternalPublisher, crdbResourceMap, creditReportManagerClient, connectedAccountClient)

	connectedaccountpb.RegisterConnectedAccountServer(s, caService)

	caDevService := wire.InitializeCaDevService(connectedAccountPGDB, connectedAccountRedisStore, connectedaccountGenConf, featureEngineeringPGDB, crdbResourceMap)

	cadevpb.RegisterDevConnectedAccServer(s, caDevService)

	caConsumer := wire.InitializeCaConsumerService(ctx, connectedAccountPGDB, accountAggregatorClient, connectedaccountGenConf, awsConf, connectedaccountConf, bouncyCastleClient, transactionBatchProcessDelayPublisher, transactionEventExternalPublisher, accountUpdateEventExternalPublisher, createAttemptSqsPublisher, fetchDataSqsDelayPublisher, broker, actorClient, usersClient, groupClient, commsClient, sendNotificationSqsDelayPublisher, fetchDataSqsPublisher, onboardingClient, savingsClient, salaryProgramClient, processConsentDataRefreshSqsPublisher, captureColumnUpdateSqsPublisher, captureHeartbeatAndSendNotificationSqsPublisher, firstDataPullSqsDelayPublisher, connectedAccountRedisStore, firstDataPullSqsPublisher, accountDataSyncExternalPublisher, caUserTxnDataS3Client, uNNameCheckClient)

	caconsumer.RegisterConnectedAccountConsumerServer(s, caConsumer)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.ProcessConsentSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessConsentMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.ProcessConsentCallbackSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessConsentCallbackMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.FetchDataSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterFetchDataMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = awswire.InitializeExtendedSubscriberWithGenConf(ctx, connectedaccountGenConf.DecryptDataSqsSubscriber(), awsConf, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterDecryptDataMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors())
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = awswire.InitializeExtendedSubscriberWithGenConf(ctx, connectedaccountGenConf.ProcessDataSqsSubscriber(), awsConf, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessDataMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors())
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.ProcessFICallbackSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessFICallbackMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.TransactionBatchProcessSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessTransactionsBatchMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.PurgeDataSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterPurgeDataMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.ProcessAccountStatusCallbackSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessAccountStatusCallbackMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.CreateAttemptSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterCreateAttemptMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.SendNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessNotificationMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.ProcessConsentDataRefreshSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessConsentDataRefreshMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.CaptureColumnUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterCaptureColumnUpdateMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.CaptureHeartbeatAndSendNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterCaptureHeartbeatAndSendNotificationMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.FirstDataPullSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterFetchDataMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, connectedaccountGenConf.ProcessAuthTokenCreationEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		caconsumer.RegisterProcessAuthTokenCreationEventMethodToSubscriber(subscriber, caConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	service := wire.InitialiseDataAnalyticsService(featureEngineeringPGDB, crdbResourceMap, connectedAccountClientVar2, analyticsClient)

	dataanalytics2.RegisterDataAnalyticsServer(s, service)

	serviceVar2 := wire.InitiateAaAnalyserService(connectedAccountPGDB, crdbResourceMap, connectedAccountClientVar3, vendorMappingServiceClient, caAnalyticsS3Client, connectedAccountClient)

	caanalytics.RegisterAnalyticsServer(s, serviceVar2)

	configNameToConfMap[cfg.ConfigName(cfg.CONNECTED_ACC_SERVICE)] = &commonexplorer.Config{StaticConf: &connectedaccountconf.Config{}, QuestIntegratedConfig: connectedaccountGenConf}

	return nil

}

// nolint: funlen
func setupConnectedaccountsecurities(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	connectedAccountPGDB types.ConnectedAccountPGDB) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	connected_account_securitiesConf, err := connectedaccountsecuritiesconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CONNECTED_ACCOUNT_SECURITIES_SERVICE))
		return err
	}
	_ = connected_account_securitiesConf

	serviceVar3 := wire2.InitializeConnectedAccountSecuritiesService(connectedAccountPGDB)

	securities2.RegisterSecuritiesServer(s, serviceVar3)

	configNameToConfMap[cfg.ConfigName(cfg.CONNECTED_ACCOUNT_SECURITIES_SERVICE)] = &commonexplorer.Config{StaticConf: &connectedaccountsecuritiesconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupTiering(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	tieringPGDB types.TieringPGDB,
	savingsClient savingspb.SavingsClient,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	tieringActorRedisStore types.TieringActorRedisStore,
	tieringConnectedAccountRedisStore types.TieringConnectedAccountRedisStore,
	commsClientVar3 comms.CommsClient,
	authClient auth.AuthClient,
	balanceClient accountbalancepb.BalanceClient,
	eODBalanceClient tieringpinotpb.EODBalanceClient,
	onboardingClient onboardingpb.OnboardingClient,
	payClient pay.PayClient,
	orderManagerClient ussorderpb.OrderManagerClient,
	accountManagerClient ussaccountpb.AccountManagerClient,
	investmentAggregatorClient aggregator.InvestmentAggregatorClient,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	tieringConf, err := tieringconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.TIERING_SERVICE))
		return err
	}
	_ = tieringConf

	tieringGenConf, err := dynconf.LoadConfigWithQuestConfig(tieringconf.Load, genconf3.NewConfigWithQuest, cfg.TIERING_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.TIERING_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		tieringGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: tieringGenConf, SdkConfig: tieringGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{tieringGenConfAppConfig}, string(cfg.CENTRAL_GROWTH_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = tieringGenConf

	tierUpdateEventExternalPublisher, err := sns.NewSnsPublisherWithConfig(ctx, tieringGenConf.TierUpdateEventExternalPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	serviceVar4 := wire3.InitializeService(tieringPGDB, tieringGenConf, savingsClient, actorClient, usersClient, groupClient, bankCustomerServiceClient, salaryProgramClient, tieringActorRedisStore, tieringConnectedAccountRedisStore, commsClientVar3, segmentationServiceClient, authClient, broker, tierUpdateEventExternalPublisher, balanceClient, eODBalanceClient, onboardingClient, payClient, orderManagerClient, accountManagerClient, investmentAggregatorClient)

	tieringpb.RegisterTieringServer(s, serviceVar4)

	tieringConsumer := wire3.InitializeTieringConsumerService(tieringPGDB, tieringGenConf, savingsClient, actorClient, usersClient, groupClient, bankCustomerServiceClient, salaryProgramClient, commsClientVar3, tieringActorRedisStore, segmentationServiceClient, authClient, broker, tierUpdateEventExternalPublisher, balanceClient, eODBalanceClient, onboardingClient, orderManagerClient, accountManagerClient, investmentAggregatorClient, piClient, accountPIRelationClient)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		tieringcopb.RegisterTieringConsumerServer(s, tieringConsumer)
	}

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, tieringGenConf.TierReEvaluationEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			tieringcopb.RegisterProcessTierReEvaluationEventMethodToSubscriber(subscriber, tieringConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, tieringGenConf.ProcessKycUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			tieringcopb.RegisterProcessKycUpdateEventMethodToSubscriber(subscriber, tieringConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, tieringGenConf.ProcessSalaryUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			tieringcopb.RegisterProcessSalaryUpdateEventMethodToSubscriber(subscriber, tieringConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, tieringGenConf.ProcessBalanceUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			tieringcopb.RegisterProcessBalanceUpdateEventMethodToSubscriber(subscriber, tieringConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, tieringGenConf.ProcessBalanceUpdateEventMarketingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			tieringcopb.RegisterProcessBalanceUpdateMarketingEventMethodToSubscriber(subscriber, tieringConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, tieringGenConf.ProcessAddFundsOrderEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			tieringcopb.RegisterProcessAddFundsOrderEventMethodToSubscriber(subscriber, tieringConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, tieringGenConf.ProcessInvestmentEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			tieringcopb.RegisterProcessInvestmentEventMethodToSubscriber(subscriber, tieringConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, tieringGenConf.ProcessUsStocksWalletOrderEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			tieringcopb.RegisterProcessUsStocksWalletOrderEventMethodToSubscriber(subscriber, tieringConsumer)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	tieringDevService := wire3.InitializeDevService(tieringPGDB, tieringConf, tieringGenConf, tieringActorRedisStore, tieringConnectedAccountRedisStore)

	tieringdevpb.RegisterTieringDevServiceServer(s, tieringDevService)

	serviceVar5 := wire3.InitializeEODBalanceService(tieringConf)

	tieringpinotpb.RegisterEODBalanceServer(s, serviceVar5)

	configNameToConfMap[cfg.ConfigName(cfg.TIERING_SERVICE)] = &commonexplorer.Config{StaticConf: &tieringconf.Config{}, QuestIntegratedConfig: tieringGenConf}

	return nil

}

// nolint: funlen
func setupSalaryprogram(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	salaryprogramPGDB types.SalaryprogramPGDB,
	salaryProgramRedisStore salarytypes.SalaryProgramRedisStore,
	piClient pipb.PiClient,
	employmentClient emppb.EmploymentClient,
	orderServiceClient orderpb.OrderServiceClient,
	txnCategorizerClient categorizerpb.TxnCategorizerClient,
	employerNameMatchClient employernamematchvgpb.EmployerNameMatchClient,
	savingsClient savingspb.SavingsClient,
	paymentClient vgpaymentpb.PaymentClient,
	nudgeServiceClient nudgepb.NudgeServiceClient,
	employmentClientVar2 vgemploymentpb.EmploymentClient,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	recurringPaymentServiceClient recurringpaymentpb.RecurringPaymentServiceClient,
	timelineServiceClient timeline.TimelineServiceClient,
	employerNameCategoriserClient employernamecategoriser.EmployerNameCategoriserClient,
	onboardingClient onboardingpb.OnboardingClient,
	commsClientVar3 comms.CommsClient,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	parserClientVar2 vgparserpb.ParserClient,
	inAppTargetedCommsClient inapptargetedcommspb.InAppTargetedCommsClient,
	fennelFeatureStoreClient fennelvgpb.FennelFeatureStoreClient,
	scienapticClient vgscienapticpb.ScienapticClient,
	tieringClient tieringpb.TieringClient,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	riskcovryClient riskcovryvgpb.RiskcovryClient,
	onSurityClient onsurityvgpb.OnSurityClient,
	vendorMappingServiceClient vmpb.VendorMappingServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	salaryprogramConf, err := salaryprogramconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SALARY_PROGRAM_SERVICE))
		return err
	}
	_ = salaryprogramConf

	salaryprogramGenConf, err := dynconf.LoadConfigWithQuestConfig(salaryprogramconf.Load, genconf4.NewConfigWithQuest, cfg.SALARY_PROGRAM_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SALARY_PROGRAM_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		salaryprogramGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: salaryprogramGenConf, SdkConfig: salaryprogramGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{salaryprogramGenConfAppConfig}, string(cfg.CENTRAL_GROWTH_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = salaryprogramGenConf

	healthInsurancePollPolicyPurchaseStatusEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, salaryprogramGenConf.HealthInsurancePollPolicyPurchaseStatusEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	salaryIdentificationOrderUpdateEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, salaryprogramGenConf.SalaryIdentificationOrderUpdateEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	salaryIdentificationOrderUpdateEventSqsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, salaryprogramGenConf.SalaryIdentificationOrderUpdateEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	salaryProgramNotificationCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		salaryprogramGenConf.SalaryProgramNotificationCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(salaryprogramGenConf.SalaryProgramNotificationCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return err
	}
	salaryStatusUpdateEventSqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		salaryprogramGenConf.SalaryStatusUpdateEventSqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(salaryprogramGenConf.SalaryStatusUpdateEventSqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return err
	}

	salaryTxnDetectionEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, salaryprogramGenConf.SalaryTxnDetectionEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	salaryProgramStatusUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, salaryprogramGenConf.SalaryProgramStatusUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	salaryProgramS3Client := s3pkg.NewClient(awsConf, salaryprogramGenConf.SalaryProgramAwsBucket().BucketName)
	salaryProgramS3ClientVar2 := s3pkg.NewClient(awsConf, salaryprogramGenConf.SalaryProgramAwsBucket().BucketName)

	serviceVar6 := wire4.InitializeSalaryProgramService(salaryprogramPGDB, salaryProgramS3Client, salaryProgramRedisStore, usersClient, actorClient, piClient, employmentClient, orderServiceClient, txnCategorizerClient, employerNameMatchClient, savingsClient, paymentClient, nudgeServiceClient, salaryTxnDetectionEventPublisher, salaryProgramStatusUpdateEventPublisher, salaryProgramNotificationCustomDelayPublisher, broker, employmentClientVar2, salaryprogramConf, bankCustomerServiceClient, recurringPaymentServiceClient, timelineServiceClient, employerNameCategoriserClient, salaryprogramGenConf, segmentationServiceClient, onboardingClient, commsClientVar3, operationalStatusServiceClient, connectedAccountClient, parserClientVar2, inAppTargetedCommsClient, fennelFeatureStoreClient, scienapticClient, tieringClient)

	salaryprogrampb.RegisterSalaryProgramServer(s, serviceVar6)

	serviceVar7 := wire4.InitializeSalaryProgramReferralsService(salaryprogramPGDB)

	salaryreferralspb.RegisterReferralsServer(s, serviceVar7)

	salaryProgramDevService := wire4.InitializeSalaryProgramDevService(salaryprogramPGDB)

	salaryprogramdevpb.RegisterSalaryProgramDevServer(s, salaryProgramDevService)

	serviceVar8 := wire4.InitializeHealthInsuranceService(salaryprogramPGDB, salaryProgramClient, salaryProgramRedisStore, usersClient, riskcovryClient, onSurityClient, vendorMappingServiceClient, healthInsurancePollPolicyPurchaseStatusEventSqsPublisher, salaryprogramConf, actorClient, savingsClient, bankCustomerServiceClient, employmentClient)

	healthinsurancepb.RegisterHealthInsuranceServer(s, serviceVar8)

	salaryProgramCxService := wire4.InitializeSalaryProgramCxService(salaryprogramPGDB, salaryProgramClient, salaryprogramConf)

	salaryprogramcxpb.RegisterCxServer(s, salaryProgramCxService)

	serviceVar9 := wire4.InitialiseDynamicUIElementService(salaryprogramPGDB)

	dynamicuielementpb.RegisterDynamicUIElementServiceServer(s, serviceVar9)

	salaryProgramCxConsumerService := wire4.InitializeSalaryProgramCxConsumerService(salaryprogramPGDB, txnCategorizerClient, employmentClient, salaryprogramConf, salaryIdentificationOrderUpdateEventSqsPublisher, orderServiceClient, salaryProgramClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.SalaryCxOpsSalaryVerEligibilityRefreshOrderUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		cxconsumer.RegisterProcessOrderEventForOpsSalaryVerEligibilityRefreshMethodToSubscriber(subscriber, salaryProgramCxConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.SalaryCxSalaryDetectionEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		cxconsumer.RegisterProcessSalaryDetectionEventMethodToSubscriber(subscriber, salaryProgramCxConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.EmployerPiMappingUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		cxconsumer.RegisterProcessEmployerPiMappingUpdateEventMethodToSubscriber(subscriber, salaryProgramCxConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	consumerService := wire4.InitializeSalaryProgramConsumerService(salaryprogramPGDB, salaryProgramS3ClientVar2, salaryProgramRedisStore, orderServiceClient, actorClient, usersClient, savingsClient, salaryProgramClient, commsClientVar3, piClient, employmentClient, txnCategorizerClient, employerNameMatchClient, paymentClient, nudgeServiceClient, salaryTxnDetectionEventPublisher, salaryProgramStatusUpdateEventPublisher, salaryProgramNotificationCustomDelayPublisher, salaryIdentificationOrderUpdateEventSqsDelayPublisher, broker, employmentClientVar2, salaryprogramConf, bankCustomerServiceClient, recurringPaymentServiceClient, timelineServiceClient, employerNameCategoriserClient, salaryprogramGenConf, segmentationServiceClient, onboardingClient, salaryStatusUpdateEventSqsCustomDelayPublisher, operationalStatusServiceClient, connectedAccountClient, parserClientVar2, inAppTargetedCommsClient, tieringClient, fennelFeatureStoreClient, scienapticClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.SalaryIdentificationOrderUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessOrderEventsForSalaryTxnIdentificationMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.EmploymentUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessEmploymentUpdateEventMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.SalaryProgramNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessSalaryProgramNotificationEventMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.SalaryLiteRecurringPaymentActionUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessRecurringPaymentActionUpdateEventMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.OnboardingStageUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessOnboardingStageUpdateEventMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.SalaryStatusUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessSalaryProgramStatusUpdateEventMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.AaSalaryOrderUpdateEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessAaSalaryOrderUpdateEventMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar10 := wire4.InitializeHealthInsuranceConsumerService(salaryprogramPGDB, usersClient, riskcovryClient, onSurityClient, vendorMappingServiceClient, broker, salaryprogramConf)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.HealthInsurancePollPolicyPurchaseStatusEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer5.RegisterProcessPollPolicyPurchaseStatusEventMethodToSubscriber(subscriber, serviceVar10)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, salaryprogramGenConf.HealthInsurancePolicyIssuanceCompletionEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer5.RegisterProcessPolicyIssuanceCompletedEventMethodToSubscriber(subscriber, serviceVar10)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.SALARY_PROGRAM_SERVICE)] = &commonexplorer.Config{StaticConf: &salaryprogramconf.Config{}, QuestIntegratedConfig: salaryprogramGenConf}

	return nil

}

// nolint: funlen
func setupSalaryestimation(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	featureEngineeringPGDB types.FeatureEngineeringPGDB,
	analyticsClient caanalytics.AnalyticsClient,
	dataAnalyticsClient dataanalytics2.DataAnalyticsClient,
	consentClient consent.ConsentClient,
	dataSharingClient datasharing.DataSharingClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	preApprovedLoanClient preapprovedloan.PreApprovedLoanClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	salaryestimationConf, err := salaryestimationconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SALARY_ESTIMATION_SERVICE))
		return err
	}
	_ = salaryestimationConf

	salaryestimationGenConf, err := dynconf.LoadConfigWithQuestConfig(salaryestimationconf.Load, genconf5.NewConfigWithQuest, cfg.SALARY_ESTIMATION_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SALARY_ESTIMATION_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		salaryestimationGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: salaryestimationGenConf, SdkConfig: salaryestimationGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{salaryestimationGenConfAppConfig}, string(cfg.CENTRAL_GROWTH_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = salaryestimationGenConf

	serviceVar11 := wire5.InitialiseSalaryEstimationService(salaryestimationGenConf, featureEngineeringPGDB, analyticsClient, dataAnalyticsClient, consentClient, dataSharingClient, connectedAccountClient, preApprovedLoanClient, broker)

	salaryestimationpb.RegisterSalaryEstimationServer(s, serviceVar11)

	salaryEstimationDevEntity := wire5.InitialiseSalaryEstimationDevService(salaryestimationGenConf, featureEngineeringPGDB)

	salestdevpb.RegisterDevSalaryEstimationServer(s, salaryEstimationDevEntity)

	configNameToConfMap[cfg.ConfigName(cfg.SALARY_ESTIMATION_SERVICE)] = &commonexplorer.Config{StaticConf: &salaryestimationconf.Config{}, QuestIntegratedConfig: salaryestimationGenConf}

	return nil

}

// nolint: funlen
func setupDatasharing(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	salaryEstimationClient salaryestimationpb.SalaryEstimationClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	datasharingConf, err := datasharingconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.DATA_SHARING_SERVICE))
		return err
	}
	_ = datasharingConf

	sharedDataStorageS3Client := s3pkg.NewClient(awsConf, datasharingConf.SharedDataStorageBucketName)

	serviceVar12 := wire6.InitialiseDataSharingService(salaryEstimationClient, connectedAccountClient, sharedDataStorageS3Client, broker)

	datasharing.RegisterDataSharingServer(s, serviceVar12)

	configNameToConfMap[cfg.ConfigName(cfg.DATA_SHARING_SERVICE)] = &commonexplorer.Config{StaticConf: &datasharingconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupSavings(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	savingsClientVar8 ovgsavings.SavingsClient,
	authClient auth.AuthClient,
	actionBarClient searchpb.ActionBarClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	ledgerReconciliationClient reconpb.LedgerReconciliationClient,
	groupClient usergrouppb.GroupClient,
	userSavingsRedisStore wiretypes.UserSavingsRedisStore,
	inAppTargetedCommsClientVar3 dynamicelewiretypes.InAppTargetedCommsClientWithInterceptors,
	balHistoryRedisStore wiretypes.BalHistoryRedisStore,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	payClient pay.PayClient,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	eSignClient docstypes.ESignClientWithInterceptors,
	orderServiceClient orderpb.OrderServiceClient,
	balanceClient accountbalancepb.BalanceClient,
	nudgeServiceClient nudgepb.NudgeServiceClient,
	savingsRueidisCacheStorage wiretypes.SavingsRueidisCacheStorage,
	onboardingClient onboardingpb.OnboardingClient,
	paymentClientVar3 paymentpb.PaymentClient,
	externalAccountsClient extacct.ExternalAccountsClient,
	uNNameCheckClient ncpb.UNNameCheckClient,
	externalValidateClient extvalidate.ExternalValidateClient,
	savingsClient savingspb.SavingsClient,
	watsonClient watsonpb.WatsonClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	commsClientVar7 wiretypes.SavingsCommsClientWithInterceptors,
	docsClientVar2 docstypes.DocsClientWithInterceptors,
	tieringClient tieringpb.TieringClient,
	consentClient consent.ConsentClient,
	piClient pipb.PiClient,
	uPIClient upipb.UPIClient,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	productClient productpb.ProductClient,
	celestialClient celestialpb.CelestialClient,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	accountsClient vgaccpb.AccountsClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	savingsConf, err := savingsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SAVINGS_SERVICE))
		return err
	}
	_ = savingsConf

	savingsGenConf, err := dynconf.LoadConfig(savingsconf.Load, genconf6.NewConfig, cfg.SAVINGS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SAVINGS_SERVICE))
		return err
	}

	_ = savingsGenConf

	savingsCreationPublisher, err := sqs.NewPublisherWithConfig(ctx, savingsGenConf.SavingsCreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	thirdPartyAccountSharingPublisher, err := sqs.NewPublisherWithConfig(ctx, savingsGenConf.ThirdPartyAccountSharingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	createVPAPublisher, err := sqs.NewPublisherWithConfig(ctx, savingsGenConf.CreateVPAPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	savingsAccountPICreationPublisher, err := sqs.NewPublisherWithConfig(ctx, savingsGenConf.SavingsAccountPICreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	eventAfPurchasePublisher, err := sqs.NewPublisherWithConfig(ctx, savingsGenConf.EventAfPurchasePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	balanceUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, savingsGenConf.BalanceUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	balanceChangeEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, savingsGenConf.BalanceChangeEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	accountStatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, savingsGenConf.AccountStatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	savingsService := wire7.InitializeService(epifiCRDB, savingsCreationPublisher, usersClient, actorClient, savingsClientVar8, authClient, actionBarClient, accountPIRelationClient, savingsConf, ledgerReconciliationClient, groupClient, balanceUpdateEventPublisher, userSavingsRedisStore, inAppTargetedCommsClientVar3, balHistoryRedisStore, bankCustomerServiceClient, payClient, savingsGenConf, operationalStatusServiceClient, eSignClient, orderServiceClient, balanceChangeEventPublisher, broker, balanceClient, nudgeServiceClient, savingsRueidisCacheStorage, onboardingClient, paymentClientVar3, externalAccountsClient)

	savingspb.RegisterSavingsServer(s, savingsService)

	savingsDbStatesService := wire7.InitializeDevSavingsService(epifiCRDB, savingsConf, savingsGenConf, userSavingsRedisStore, broker, nudgeServiceClient, savingsRueidisCacheStorage)

	savingsdeveloper.RegisterSavingsDbStatesServer(s, savingsDbStatesService)

	serviceVar13 := wire7.InitialiseExternalAccountsService(epifiCRDB, uNNameCheckClient, usersClient, actorClient, savingsGenConf, externalValidateClient, savingsClient, watsonClient, payClient, thirdPartyAccountSharingPublisher)

	extacct.RegisterExternalAccountsServer(s, serviceVar13)

	serviceVar14 := wire7.InitialiseWatsonClientService(actorClient, savingsClient)

	savingswatsonclientpb.RegisterWatsonServer(s, serviceVar14)

	savingsConsumerService, err := wire7.InitializeSavingsConsumerService(epifiCRDB, savingsClientVar8, cardProvisioningClient, actorClient, authClient, createVPAPublisher, accountStatePublisher, savingsCreationPublisher, usersClient, broker, savingsConf, savingsAccountPICreationPublisher, eventAfPurchasePublisher, savingsGenConf, userSavingsRedisStore, bankCustomerServiceClient, operationalStatusServiceClient, commsClientVar7, docsClientVar2, savingsRueidisCacheStorage, tieringClient, consentClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	if func(conf *config.Config) bool {
		savingsConf, err := savingsconf.Load()
		if err != nil {
			panic(err)
		}
		return savingsConf.Server.EnablePoller
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.SavingsCreationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			savingspb.RegisterProcessAccountCreationMethodToSubscriber(subscriber, savingsConsumerService)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	callbackAccountCreationConsumer := wire7.InitialiseSavingsCallbackConsumer(epifiCRDB, actorClient, createVPAPublisher, cardProvisioningClient, usersClient, authClient, accountStatePublisher, savingsConf, broker, savingsAccountPICreationPublisher, eventAfPurchasePublisher, savingsGenConf, userSavingsRedisStore, bankCustomerServiceClient, savingsRueidisCacheStorage, operationalStatusServiceClient, commsClientVar7, docsClientVar2, tieringClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.SavingsCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		savingspb.RegisterProcessAccountCreationCallbackMethodToSubscriber(subscriber, callbackAccountCreationConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	pIConsumerService := wire7.InitialiseCreateVPAConsumer(epifiCRDB, piClient, accountPIRelationClient, actorClient, uPIClient, usersClient, authClient, upiOnboardingClient, savingsConf, userSavingsRedisStore, savingsGenConf, savingsRueidisCacheStorage)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.CreateVPASubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		savingspb.RegisterCreateVPAMethodToSubscriber(subscriber, pIConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.SavingsAccountPICreationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		savingspb.RegisterProcessSavingsAccountPICreationMethodToSubscriber(subscriber, pIConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	balanceUpdateConsumer := wire7.InitialiseBalanceUpdateConsumerService(commsClientVar7, savingsConf, actorClient, onboardingClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.BalanceUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterNotifyLowBalanceUsersMethodToSubscriber(subscriber, balanceUpdateConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	operStatusUpdateConsumer := wire7.InitialiseOperStatusUpdateConsumerService(savingsConf, savingsGenConf, savingsClient, actorClient, accountStatePublisher, broker, nudgeServiceClient, epifiCRDB, userSavingsRedisStore, savingsRueidisCacheStorage, usersClient, productClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.OperationalStatusUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterUpdateSavingsAccountStatusMethodToSubscriber(subscriber, operStatusUpdateConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	eventSubscriber := wire7.InitialiseEventsSubscriberService(celestialClient, savingsGenConf, savingsClient, bankCustomerServiceClient, salaryProgramClient, tieringClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.ProcessTierUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessTierUpdateEventConsumerMethodToSubscriber(subscriber, eventSubscriber)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	eventSubscriberVar2 := wire7.InitialiseEventsSubscriberService(celestialClient, savingsGenConf, savingsClient, bankCustomerServiceClient, salaryProgramClient, tieringClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.ProcessKycUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessKycUpdateEventMethodToSubscriber(subscriber, eventSubscriberVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	saClosureClosureConsumerService := wire7.InitialiseSaClosureClosureConsumerService(epifiCRDB, savingsConf, savingsGenConf, userSavingsRedisStore, broker, nudgeServiceClient, savingsRueidisCacheStorage)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.ProcessBalanceUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer6.RegisterProcessBalanceUpdateEventMethodToSubscriber(subscriber, saClosureClosureConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar15 := wire7.InitialiseExtAcctConsumerService(accountsClient, bankCustomerServiceClient, usersClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, savingsGenConf.ThirdPartyAccountSharingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		extacccopb.RegisterProcessThirdPartyAccountSharingEventMethodToSubscriber(subscriber, serviceVar15)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.SAVINGS_SERVICE)] = &commonexplorer.Config{StaticConf: &savingsconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.CENTRAL_GROWTH_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
